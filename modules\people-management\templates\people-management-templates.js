/**
 * People Management Templates
 * Main templates for people management views following the standard pattern
 */

export const peopleManagementTemplates = {
    /**
     * People management main view (following address/organization pattern)
     */
    peopleListView: (people) => {
        return `
            <style>
                .people-list-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .people-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .people-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .people-controls {
                    display: flex;
                    gap: 1rem;
                }
                .people-list {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    margin-bottom: 2rem;
                }
                .people-list-header {
                    background-color: rgba(255, 0, 0, 0.1);
                    border-bottom: 1px solid #ff0000;
                    padding: 0.75rem;
                    display: grid;
                    grid-template-columns: 2fr 1.5fr 1fr 1fr 120px;
                    gap: 1rem;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.8rem;
                    color: #ff0000;
                    text-transform: uppercase;
                }
                .people-list-item {
                    padding: 0.75rem;
                    border-bottom: 1px solid rgba(255, 0, 0, 0.2);
                    display: grid;
                    grid-template-columns: 2fr 1.5fr 1fr 1fr 120px;
                    gap: 1rem;
                    align-items: center;
                    transition: all 0.2s ease;
                }
                .people-list-item:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .people-list-item:last-child {
                    border-bottom: none;
                }
                .person-name {
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.9rem;
                    color: #ff0000;
                    text-transform: uppercase;
                }
                .person-contact {
                    font-family: 'Courier New', monospace;
                    font-size: 0.75rem;
                    color: #cccccc;
                    line-height: 1.2;
                }
                .person-housing {
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    color: #ffaaaa;
                }
                .person-created {
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    color: #ffaaaa;
                }
                .person-actions {
                    display: flex;
                    gap: 0.3rem;
                }
                .person-actions .secondary-button,
                .person-actions .action-button {
                    font-size: 0.7rem;
                    padding: 0.25rem 0.5rem;
                    text-transform: uppercase;
                    min-width: 45px;
                }
                .no-people {
                    text-align: center;
                    padding: 3rem;
                    color: #ff6666;
                    font-family: 'Courier New', monospace;
                }
                .search-container {
                    margin-bottom: 1.5rem;
                }
                .search-input {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    margin-bottom: 0.5rem;
                }
                .search-input::placeholder {
                    color: #ff6666;
                }
                .secondary-button {
                    background-color: transparent;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .secondary-button:hover {
                    background-color: #ff0000;
                    color: #000000;
                }
                .action-button {
                    background-color: #ff0000;
                    border: 1px solid #ff0000;
                    color: #000000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .action-button:hover {
                    background-color: transparent;
                    color: #ff0000;
                }
                .action-button.primary {
                    font-weight: bold;
                }
            </style>

            <div class="people-list-container">
                <div class="people-header">
                    <div class="people-title">PEOPLE DIRECTORY</div>
                    <div class="people-controls">
                        <button class="secondary-button" data-action="back-to-records">← Back to Records</button>
                        <button class="action-button primary" data-action="add-person">+ Add Person</button>
                    </div>
                </div>

                <div class="search-container">
                    <input type="text"
                           class="search-input"
                           placeholder="Search people by name, email, phone, or housing status..."
                           data-action="search-people-input">
                </div>

                <div class="people-list" id="people-list">
                    ${people && people.length > 0
                        ? `<div class="people-list-header">
                            <div>Name</div>
                            <div>Contact Info</div>
                            <div>Housing Status</div>
                            <div>Created</div>
                            <div>Actions</div>
                           </div>
                           ${people.map(person => peopleManagementTemplates.peopleListItem(person)).join('')}`
                        : '<div class="no-people">NO PEOPLE FOUND<br><br>Click "Add Person" to create the first entry.</div>'
                    }
                </div>
            </div>
        `;
    },

    /**
     * Individual person list item (following address/organization pattern)
     */
    peopleListItem: (person) => {
        const personName = `${person.first_name || ''} ${person.last_name || ''}`.trim() || 'UNNAMED PERSON';
        const contactInfo = [
            person.email ? `Email: ${person.email}` : '',
            person.phone ? `Phone: ${person.phone}` : ''
        ].filter(Boolean).join('<br>');

        const housingStatus = person.housing_status || 'Unknown';
        const createdDate = person.created_at ? new Date(person.created_at).toLocaleDateString() : 'Unknown';

        return `
            <div class="people-list-item" data-person-id="${person.id}">
                <div class="person-name">${personName}</div>
                <div class="person-contact">${contactInfo || 'No contact info'}</div>
                <div class="person-housing">${housingStatus}</div>
                <div class="person-created">${createdDate}</div>
                <div class="person-actions">
                    <button class="secondary-button" data-action="view-person-detail" data-person-id="${person.id}">View</button>
                    <button class="action-button" data-action="edit-person" data-person-id="${person.id}">Edit</button>
                </div>
            </div>
        `;
    },

    /**
     * Person search modal template
     */
    peopleSearchModal: () => {
        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔍 SEARCH PEOPLE</h3>
                    <button class="close-btn" data-action="close-modal">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <label>First Name:</label>
                            <input type="text" id="search-first-name" placeholder="Enter first name...">
                        </div>
                        <div class="form-row">
                            <label>Last Name:</label>
                            <input type="text" id="search-last-name" placeholder="Enter last name...">
                        </div>
                        <div class="form-row">
                            <label>Email:</label>
                            <input type="text" id="search-email" placeholder="Enter email address...">
                        </div>
                        <div class="form-row">
                            <label>Phone:</label>
                            <input type="text" id="search-phone" placeholder="Enter phone number...">
                        </div>
                        <div class="form-row">
                            <label>Housing Status:</label>
                            <select id="search-housing-status">
                                <option value="">All Statuses</option>
                                <option value="Housed">Housed</option>
                                <option value="Temporarily Housed">Temporarily Housed</option>
                                <option value="Unsheltered">Unsheltered</option>
                                <option value="Emergency Shelter">Emergency Shelter</option>
                                <option value="Transitional Housing">Transitional Housing</option>
                                <option value="Unknown">Unknown</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="primary-button" id="execute-people-search">Search</button>
                        <button class="secondary-button" data-action="close-modal">Cancel</button>
                    </div>
                    <div id="people-search-results" class="search-results"></div>
                </div>
            </div>
        `;
    },

    /**
     * Person search result item
     */
    personSearchResultItem: (person) => {
        const personName = `${person.first_name || ''} ${person.last_name || ''}`.trim() || 'Unknown Person';
        
        return `
            <div class="search-result-item" data-person-id="${person.id}">
                <div class="result-header">
                    <h3 class="person-name">${personName}</h3>
                    <div class="result-actions">
                        <button class="action-btn view-btn" data-action="view-person-detail" data-person-id="${person.id}">
                            👁️ View
                        </button>
                        <button class="action-btn edit-btn" data-action="edit-person" data-person-id="${person.id}">
                            ✏️ Edit
                        </button>
                    </div>
                </div>
                <div class="result-details">
                    ${person.email ? `<div class="detail-item"><span class="icon">📧</span> ${person.email}</div>` : ''}
                    ${person.phone ? `<div class="detail-item"><span class="icon">📱</span> ${person.phone}</div>` : ''}
                    ${person.housing_status ? `<div class="detail-item"><span class="icon">🏠</span> ${person.housing_status}</div>` : ''}
                    ${person.date_of_birth ? `<div class="detail-item"><span class="icon">🎂</span> ${new Date(person.date_of_birth).toLocaleDateString()}</div>` : ''}
                </div>
            </div>
        `;
    },

    /**
     * Empty search results template
     */
    noSearchResults: () => {
        return `
            <div class="no-results">
                <div class="no-results-icon">🔍</div>
                <h3>No People Found</h3>
                <p>No people match your search criteria. Try adjusting your search terms.</p>
            </div>
        `;
    }
};
