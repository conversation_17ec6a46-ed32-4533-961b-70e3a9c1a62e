import { BaseManager } from '../../shared/base-manager.js';
import { Logger } from '../../shared/logger.js';

// Comprehensive Outreach Manager - Handles both transaction management and list view
export class OutreachManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, locationManager = null) {
        super(dataManager, authManager, 'outreach', uiManager);
        this.locationManager = locationManager;
        this.logger = Logger.forModule('Outreach');
        
        // Transaction creation state
        this.selectedPerson = null;
        this.transactionItems = [];
        this.currentLocation = null;
        
        // List management state
        this.selectedOutreachTransaction = null;
        this.outreachUpdateInterval = null;
        this.outreachEventHandlers = [];
        
        this.logger.info('Outreach Manager initialized');
    }

    init() {
        // Set up event listeners
        this.setupEventListeners();
        
        // Initialize search functionality
        this.initializeSearch();
    }

    setupEventListeners() {
        // Person search input
        const personSearch = document.getElementById('person-search');
        if (personSearch) {
            personSearch.addEventListener('input', this.debounce(() => {
                this.searchPerson();
            }, 300));
            
            personSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchPerson();
                }
            });
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    initializeSearch() {
        // Focus on person search input
        const personSearch = document.getElementById('person-search');
        if (personSearch) {
            personSearch.focus();
        }
    }

    async searchPerson() {
        const searchInput = document.getElementById('person-search');
        const resultsContainer = document.getElementById('person-results');
        
        if (!searchInput || !resultsContainer) return;
        
        const query = searchInput.value.trim();
        
        if (query.length < 2) {
            resultsContainer.innerHTML = '<div class="no-selection">Enter at least 2 characters to search</div>';
            return;
        }

        try {
            resultsContainer.innerHTML = '<div class="loading">Searching...</div>';
            
            // Search people by name, phone, or other identifiers
            const people = await this.data.search('people', {});
            
            // Filter results based on query
            const filteredPeople = people.filter(person => {
                const searchFields = [
                    person.first_name,
                    person.last_name,
                    person.phone,
                    person.email,
                    `${person.first_name} ${person.last_name}`
                ].filter(Boolean).map(field => field.toLowerCase());
                
                return searchFields.some(field => field.includes(query.toLowerCase()));
            });

            if (filteredPeople.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <p>No people found matching "${query}"</p>
                        <button class="primary-button" onclick="outreachTransaction.showAddPersonModal('${query}')">
                            Create New Person
                        </button>
                    </div>
                `;
                return;
            }

            // Display search results
            const resultsHTML = filteredPeople.map(person => `
                <div class="person-result" onclick="outreachTransaction.selectPerson('${person.id}')">
                    <div class="person-name">${person.first_name} ${person.last_name}</div>
                    <div class="person-details">
                        ${person.phone ? `📞 ${person.phone}` : ''}
                        ${person.email ? `📧 ${person.email}` : ''}
                    </div>
                    <div class="person-id">ID: ${String(person.id).substring(0, 8)}...</div>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;

        } catch (error) {
            console.error('Error searching people:', error);
            resultsContainer.innerHTML = '<div class="error">Error searching people. Please try again.</div>';
        }
    }

    async selectPerson(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (!person) {
                this.ui.showDialog('Error', 'Person not found', 'error');
                return;
            }

            this.selectedPerson = person;
            
            // Update UI to show selected person
            const selectedPersonContainer = document.getElementById('selected-person');
            const personResults = document.getElementById('person-results');
            const addItemBtn = document.getElementById('add-item-btn');
            
            if (selectedPersonContainer && personResults) {
                selectedPersonContainer.style.display = 'block';
                personResults.style.display = 'none';

                const personInfoElement = selectedPersonContainer.querySelector('.person-info');
                if (personInfoElement) {
                    personInfoElement.innerHTML = `
                        <div class="selected-person-info">
                            <h4>${person.first_name} ${person.last_name}</h4>
                            <div class="person-contact">
                                ${person.phone ? `📞 ${person.phone}` : ''}
                                ${person.email ? `📧 ${person.email}` : ''}
                            </div>
                            <div class="person-id">ID: ${String(person.id).substring(0, 8)}...</div>
                        </div>
                    `;
                }
            }

            // Enable add item button
            if (addItemBtn) {
                addItemBtn.disabled = false;
            }

            // Enable completion buttons
            this.updateCompletionButtons();
            
            this.ui.setStatus(`Selected person: ${person.first_name} ${person.last_name}`, 'success');

        } catch (error) {
            console.error('Error selecting person:', error);
            this.ui.showDialog('Error', `Failed to select person: ${error.message}`, 'error');
        }
    }

    clearPersonSelection() {
        this.selectedPerson = null;
        
        const selectedPersonContainer = document.getElementById('selected-person');
        const personResults = document.getElementById('person-results');
        const addItemBtn = document.getElementById('add-item-btn');
        const personSearch = document.getElementById('person-search');
        
        if (selectedPersonContainer) selectedPersonContainer.style.display = 'none';
        if (personResults) personResults.style.display = 'block';
        if (addItemBtn) addItemBtn.disabled = true;
        if (personSearch) {
            personSearch.value = '';
            personSearch.focus();
        }

        // Clear items list
        this.transactionItems = [];
        this.updateItemsList();
        this.updateCompletionButtons();
        
        this.ui.setStatus('Person selection cleared', 'info');
    }

    async showAddPersonModal(prefillName = '') {
        // This will be implemented to show a modal for creating a new person
        // For now, show a placeholder
        this.ui.showDialog(
            'Add New Person',
            'Person creation modal will be implemented here.\n\n' +
            `Pre-filled name: ${prefillName}`,
            'info'
        );
    }

    async showItemCatalog() {
        if (!this.selectedPerson) {
            this.ui.showDialog('Error', 'Please select a person first', 'warning');
            return;
        }

        try {
            // Force refresh items cache to ensure we have latest data
            await this.forceRefreshItems();
            
            // Get available items from database - try multiple methods
            console.log('🔍 Attempting to load items for catalog...');
            
            // First try with active filter
            let items = await this.data.search('items', { active: true });
            console.log(`📦 Found ${items.length} active items with filter`);
            
            // If no items found, try getting all items
            if (items.length === 0) {
                console.log('⚠️ No active items found, trying to get all items...');
                items = await this.data.getAll('items');
                console.log(`📦 Found ${items.length} total items`);
                
                // Filter active items manually if needed
                if (items.length > 0) {
                    items = items.filter(item => item.active === true || item.active === 1);
                    console.log(`📦 Filtered to ${items.length} active items`);
                }
            }
            
            if (items.length === 0) {
                console.error('❌ No items found at all - this indicates a cache sync issue');
                this.ui.showDialog('No Items', 
                    'No active items found in inventory. This may indicate a sync issue. Please try refreshing the application.', 
                    'warning');
                return;
            }

            console.log(`✅ Successfully loaded ${items.length} items for catalog`);
            // Create item catalog modal
            this.showItemCatalogModal(items);

        } catch (error) {
            console.error('Error loading item catalog:', error);
            this.ui.showDialog('Error', `Failed to load item catalog: ${error.message}`, 'error');
        }
    }

    async forceRefreshItems() {
        try {
            console.log('🔄 Forcing refresh of items cache...');
            
            // Clear any cached data that might have invalid IDs
            this.data.clearCache();
            
            // Force refresh the items table from Supabase
            const supabase = this.data.getSupabaseClient();
            if (supabase && this.data.isOnline) {
                console.log('📡 Online: Refreshing items cache from Supabase...');
                await this.data.refreshTableCache('items', supabase);
                console.log('✅ Items cache refreshed from Supabase for outreach transactions');
                
                // Verify the refresh worked
                const refreshedItems = await this.data.getAll('items');
                console.log(`✅ Verification: ${refreshedItems.length} items now in cache`);
            } else {
                console.log('📴 Offline: Using existing cached items');
            }
        } catch (error) {
            console.warn('⚠️ Warning: Could not force refresh items cache:', error);
        }
    }

    showItemCatalogModal(items) {
        // Group items by category
        const itemsByCategory = items.reduce((acc, item) => {
            const category = item.category || 'other';
            if (!acc[category]) acc[category] = [];
            acc[category].push(item);
            return acc;
        }, {});

        const categoriesHTML = Object.entries(itemsByCategory).map(([category, categoryItems]) => {
            const itemsHTML = categoryItems.map(item => `
                <div class="catalog-item ${item.current_stock <= 0 ? 'out-of-stock' : ''}"
                     onclick="outreachTransaction.selectCatalogItem('${item.id}')">
                    <div class="item-name">${item.name}</div>
                    <div class="item-description">${item.description || ''}</div>
                    <div class="item-stock">Stock: ${item.current_stock} ${item.unit_type}</div>
                    ${item.current_stock <= 0 ? '<div class="stock-warning">Out of Stock</div>' : ''}
                </div>
            `).join('');

            return `
                <div class="catalog-category">
                    <h4 class="category-title">${category.replace('_', ' ').toUpperCase()}</h4>
                    <div class="category-items">
                        ${itemsHTML}
                    </div>
                </div>
            `;
        }).join('');

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>📦 Select Item to Add</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="item-catalog">
                        ${categoriesHTML}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    async selectCatalogItem(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            if (item.current_stock <= 0) {
                this.ui.showDialog('Out of Stock', `${item.name} is currently out of stock`, 'warning');
                return;
            }

            // Close catalog modal
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();

            // Show quantity input modal
            this.showQuantityInputModal(item);

        } catch (error) {
            console.error('Error selecting catalog item:', error);
            this.ui.showDialog('Error', `Failed to select item: ${error.message}`, 'error');
        }
    }

    showQuantityInputModal(item) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>📦 Add ${item.name}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="quantity-input-form">
                        <div class="item-info">
                            <div class="item-name">${item.name}</div>
                            <div class="item-description">${item.description || ''}</div>
                            <div class="item-stock">Available: ${item.current_stock} ${item.unit_type}</div>
                        </div>
                        <div class="form-group">
                            <label for="item-quantity">Quantity to distribute:</label>
                            <input type="number" id="item-quantity" min="1" max="${item.current_stock}" value="1" class="quantity-input">
                            <span class="unit-label">${item.unit_type}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="primary-button" onclick="outreachTransaction.addItemToTransaction('${item.id}')">Add to Transaction</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Focus on quantity input
        const quantityInput = modal.querySelector('#item-quantity');
        if (quantityInput) {
            quantityInput.focus();
            quantityInput.select();
        }
    }

    async addItemToTransaction(itemId) {
        try {
            const quantityInput = document.getElementById('item-quantity');
            if (!quantityInput) return;

            const quantity = parseInt(quantityInput.value);
            if (isNaN(quantity) || quantity <= 0) {
                this.ui.showDialog('Invalid Quantity', 'Please enter a valid quantity', 'warning');
                return;
            }

            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            if (quantity > item.current_stock) {
                this.ui.showDialog('Insufficient Stock', `Only ${item.current_stock} ${item.unit_type} available`, 'warning');
                return;
            }

            // Check if item already exists in transaction
            const existingItemIndex = this.transactionItems.findIndex(transItem => transItem.item_id === itemId);

            if (existingItemIndex >= 0) {
                // Update existing item quantity
                const newQuantity = this.transactionItems[existingItemIndex].quantity + quantity;
                if (newQuantity > item.current_stock) {
                    this.ui.showDialog('Insufficient Stock',
                        `Total quantity would be ${newQuantity}, but only ${item.current_stock} ${item.unit_type} available`,
                        'warning');
                    return;
                }
                this.transactionItems[existingItemIndex].quantity = newQuantity;
            } else {
                // Add new item to transaction
                this.transactionItems.push({
                    item_id: itemId,
                    item_name: item.name,
                    unit_type: item.unit_type,
                    quantity: quantity,
                    category: item.category
                });
            }

            // Close modal
            const modal = document.querySelector('.modal-overlay');
            if (modal) modal.remove();

            // Update items list display
            this.updateItemsList();
            this.updateCompletionButtons();

            this.ui.setStatus(`Added ${quantity} ${item.unit_type} of ${item.name}`, 'success');

        } catch (error) {
            console.error('Error adding item to distribution:', error);
            this.ui.showDialog('Error', `Failed to add item: ${error.message}`, 'error');
        }
    }

    updateItemsList() {
        const itemsList = document.getElementById('items-list');
        const itemsTotal = document.getElementById('items-total');
        
        if (!itemsList) return;

        if (this.transactionItems.length === 0) {
            itemsList.innerHTML = '<div class="no-items">No items added yet</div>';
            if (itemsTotal) itemsTotal.style.display = 'none';
            return;
        }

        const itemsHTML = this.transactionItems.map((item, index) => `
            <div class="transaction-item">
                <div class="item-details">
                    <div class="item-name">${item.item_name}</div>
                    <div class="item-category">${item.category.replace('_', ' ')}</div>
                </div>
                <div class="item-quantity">
                    <span class="quantity">${item.quantity}</span>
                    <span class="unit">${item.unit_type}</span>
                </div>
                <div class="item-actions">
                    <button class="edit-quantity-btn" onclick="outreachTransaction.editItemQuantity(${index})" title="Edit quantity">✏️</button>
                    <button class="remove-item-btn" onclick="outreachTransaction.removeItem(${index})" title="Remove item">🗑️</button>
                </div>
            </div>
        `).join('');

        itemsList.innerHTML = itemsHTML;

        // Update total
        if (itemsTotal) {
            const totalItems = this.transactionItems.reduce((sum, item) => sum + item.quantity, 0);
            document.getElementById('total-item-count').textContent = totalItems;
            itemsTotal.style.display = 'block';
        }
    }

    editItemQuantity(index) {
        const item = this.transactionItems[index];
        if (!item) return;

        const newQuantity = prompt(`Enter new quantity for ${item.item_name}:`, item.quantity);
        if (newQuantity === null) return;

        const quantity = parseInt(newQuantity);
        if (isNaN(quantity) || quantity <= 0) {
            this.ui.showDialog('Invalid Quantity', 'Please enter a valid quantity', 'warning');
            return;
        }

        // TODO: Check against current stock
        item.quantity = quantity;
        this.updateItemsList();
        this.ui.setStatus(`Updated quantity for ${item.item_name}`, 'success');
    }

    removeItem(index) {
        const item = this.transactionItems[index];
        if (!item) return;

        if (confirm(`Remove ${item.item_name} from transaction?`)) {
            this.transactionItems.splice(index, 1);
            this.updateItemsList();
            this.updateCompletionButtons();
            this.ui.setStatus(`Removed ${item.item_name} from transaction`, 'info');
        }
    }

    updateCompletionButtons() {
        const completeBtn = document.getElementById('complete-btn');
        const completeNewBtn = document.getElementById('complete-new-btn');

        const canComplete = this.selectedPerson && this.transactionItems.length > 0;

        if (completeBtn) completeBtn.disabled = !canComplete;
        if (completeNewBtn) completeNewBtn.disabled = !canComplete;
    }

    async getCurrentLocation() {
        try {
            const locationInput = document.getElementById('transaction-location');
            if (!locationInput) return;

            locationInput.value = 'Getting location...';
            locationInput.disabled = true;

            // Use LocationManager if available
            if (this.locationManager) {
                console.log('📍 OutreachTransaction: Using LocationManager for GPS location');
                
                try {
                    // Get current position from LocationManager
                    const position = await this.locationManager.getCurrentPosition();
                    
                    if (position && position.latitude !== undefined && position.longitude !== undefined) {
                        const { latitude, longitude, source } = position;
                        console.log(`📍 OutreachTransaction: Got position from ${source}: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                        
                        // Use our own more precise geocoding instead of LocationManager's
                        try {
                            const preciseAddress = await this.geocodeCoordinates(latitude, longitude);
                            locationInput.value = preciseAddress;
                            
                            this.currentLocation = {
                                address: preciseAddress,
                                coordinates: `${latitude}, ${longitude}`,
                                latitude: latitude,
                                longitude: longitude,
                                source: source
                            };
                        } catch (geocodeError) {
                            console.warn('Precise geocoding failed:', geocodeError);
                            // Fall back to coordinates display
                            locationInput.value = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
                            this.currentLocation = {
                                address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                                coordinates: `${latitude}, ${longitude}`,
                                latitude: latitude,
                                longitude: longitude,
                                source: source
                            };
                        }
                        
                        this.ui.setStatus(`Location updated from ${source}`, 'success');
                        return;
                    } else {
                        console.warn('📍 OutreachTransaction: LocationManager returned invalid position:', position);
                    }
                } catch (locationManagerError) {
                    console.warn('LocationManager failed:', locationManagerError);
                    // Fall through to legacy geolocation
                }
            } else {
                console.log('📍 OutreachTransaction: LocationManager not available, using legacy methods');
            }

            // Fallback to legacy geolocation if LocationManager not available or failed
            console.log('📍 OutreachTransaction: Falling back to legacy geolocation');
            
            if (!navigator.geolocation) {
                throw new Error('Geolocation not supported');
            }

            // Try multiple geolocation strategies
            let position = null;
            
            // Strategy 1: GPS only (high accuracy)
            try {
                position = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => reject(new Error('GPS timeout')), 10000);
                    navigator.geolocation.getCurrentPosition(
                        (pos) => {
                            clearTimeout(timeout);
                            resolve(pos);
                        },
                        (err) => {
                            clearTimeout(timeout);
                            reject(err);
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 8000,
                            maximumAge: 0
                        }
                    );
                });
            } catch (gpsError) {
                console.warn('GPS failed, trying cached location:', gpsError);
                
                // Strategy 2: Use cached location
                try {
                    position = await new Promise((resolve, reject) => {
                        const timeout = setTimeout(() => reject(new Error('Cached location timeout')), 5000);
                        navigator.geolocation.getCurrentPosition(
                            (pos) => {
                                clearTimeout(timeout);
                                resolve(pos);
                            },
                            (err) => {
                                clearTimeout(timeout);
                                reject(err);
                            },
                            {
                                enableHighAccuracy: false,
                                timeout: 3000,
                                maximumAge: 300000 // 5 minutes
                            }
                        );
                    });
                } catch (cachedError) {
                    console.warn('Cached location failed:', cachedError);
                    throw new Error('All location methods failed');
                }
            }

            if (!position) {
                throw new Error('No position obtained');
            }

            const { latitude, longitude } = position.coords;
            
            // Try to geocode the coordinates
            try {
                const address = await this.geocodeCoordinates(latitude, longitude);
                locationInput.value = address;
                this.currentLocation = {
                    address: address,
                    coordinates: `${latitude}, ${longitude}`,
                    latitude: latitude,
                    longitude: longitude,
                    source: 'legacy_geolocation'
                };
            } catch (geocodeError) {
                console.warn('Geocoding failed:', geocodeError);
                locationInput.value = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
                this.currentLocation = {
                    address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                    coordinates: `${latitude}, ${longitude}`,
                    latitude: latitude,
                    longitude: longitude,
                    source: 'legacy_geolocation'
                };
            }

            this.ui.setStatus('Location updated', 'success');

        } catch (error) {
            console.error('Error getting location:', error);
            const locationInput = document.getElementById('transaction-location');
            if (locationInput) {
                locationInput.value = '';
                // Show more helpful error message
                const errorMsg = error.message.includes('denied') || error.code === 1 
                    ? 'Location access denied. Please enable location permissions and try again, or enter location manually.'
                    : 'Could not get current location. Please check location services are enabled or enter manually.';
                this.ui.showDialog('Location Error', errorMsg, 'warning');
            }
        } finally {
            const locationInput = document.getElementById('transaction-location');
            if (locationInput) locationInput.disabled = false;
        }
    }

    async geocodeCoordinates(lat, lng) {
        try {
            // Get Google API key from config (same method as LocationManager)
            let apiKey = null;
            if (window.app && window.app.config) {
                apiKey = await window.app.config.getGoogleApiKey();
            }
            
            // Fallback: try to get it from the vault manager directly
            if (!apiKey && window.electronAPI && window.electronAPI.invoke) {
                try {
                    const result = await window.electronAPI.invoke('vault-get-secret', 'google_api_key');
                    apiKey = result.success ? result.value : null;
                } catch (error) {
                    console.warn('Could not get Google API key from vault:', error.message);
                }
            }
            
            if (!apiKey) {
                console.warn('Google API key not available for geocoding');
                return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            }

            const response = await fetch(
                `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
            );

            if (!response.ok) {
                throw new Error(`Geocoding request failed: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'OK' && data.results.length > 0) {
                // Find the most precise address (street address level)
                let bestResult = null;
                let bestScore = 0;
                
                for (const result of data.results) {
                    let score = 0;
                    const types = result.types;
                    
                    // Prefer results with street address or precise location info
                    if (types.includes('street_address')) score += 10;
                    if (types.includes('premise')) score += 8;
                    if (types.includes('point_of_interest')) score += 6;
                    if (types.includes('route')) score += 4;
                    if (types.includes('intersection')) score += 7;
                    if (types.includes('establishment')) score += 5;
                    
                    // Avoid very broad results
                    if (types.includes('political') && types.length === 1) score -= 5;
                    if (types.includes('administrative_area_level_1') && types.length === 1) score -= 5;
                    if (types.includes('country') && types.length === 1) score -= 10;
                    
                    if (score > bestScore) {
                        bestScore = score;
                        bestResult = result;
                    }
                }
                
                // If no good result found, use the first one
                if (!bestResult) {
                    bestResult = data.results[0];
                }
                
                // Try to construct a more precise address from components
                const components = bestResult.address_components;
                let streetNumber = '';
                let route = '';
                let neighborhood = '';
                let city = '';
                let province = '';
                
                for (const component of components) {
                    if (component.types.includes('street_number')) {
                        streetNumber = component.long_name;
                    } else if (component.types.includes('route')) {
                        route = component.long_name;
                    } else if (component.types.includes('neighborhood') || component.types.includes('sublocality')) {
                        neighborhood = component.long_name;
                    } else if (component.types.includes('locality')) {
                        city = component.long_name;
                    } else if (component.types.includes('administrative_area_level_1')) {
                        province = component.short_name;
                    }
                }
                
                // Build precise address
                let preciseAddress = '';
                if (streetNumber && route) {
                    preciseAddress = `${streetNumber} ${route}`;
                } else if (route) {
                    preciseAddress = route;
                } else if (neighborhood) {
                    preciseAddress = neighborhood;
                }
                
                if (preciseAddress && city) {
                    preciseAddress += `, ${city}`;
                } else if (city) {
                    preciseAddress = city;
                }
                
                if (province) {
                    preciseAddress += `, ${province}`;
                }
                
                // If we couldn't build a precise address, use the formatted one
                if (!preciseAddress.trim()) {
                    preciseAddress = bestResult.formatted_address;
                }
                
                console.log(`📍 Geocoded to: ${preciseAddress} (from ${data.results.length} results)`);
                return preciseAddress;
            } else {
                console.warn('Geocoding failed:', data.status);
                return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            }
        } catch (error) {
            console.error('Error geocoding coordinates:', error);
            return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
    }

    async completeTransaction() {
        if (!this.selectedPerson || this.transactionItems.length === 0) {
            this.ui.showDialog('Incomplete', 'Please select a person and add items before completing', 'warning');
            return;
        }

        try {
            await this.saveTransaction();
            this.ui.showDialog('Success', 'Outreach transaction completed successfully!', 'success');

            // Return to outreach transactions list
            setTimeout(() => {
                window.app.loadTabContent('outreach');
            }, 2000);

        } catch (error) {
            console.error('Error completing transaction:', error);
            this.ui.showDialog('Error', `Failed to complete transaction: ${error.message}`, 'error');
        }
    }

    async completeAndNew() {
        if (!this.selectedPerson || this.transactionItems.length === 0) {
            this.ui.showDialog('Incomplete', 'Please select a person and add items before completing', 'warning');
            return;
        }

        try {
            await this.saveTransaction();
            this.ui.showDialog('Success', 'Outreach transaction completed! Ready for next transaction.', 'success');

            // Reset form for new transaction
            this.resetForm();

        } catch (error) {
            console.error('Error completing transaction:', error);
            this.ui.showDialog('Error', `Failed to complete transaction: ${error.message}`, 'error');
        }
    }

    async saveTransaction() {
        // CRITICAL FIX: Get current user properly and ensure data types match database
        const currentUser = window.app?.auth?.getCurrentUser();
        const staffMember = currentUser?.email || currentUser?.user_metadata?.full_name || 'Unknown User';

        // Create activity record (people_activities table uses TEXT for person_id)
        const activityData = {
            person_id: String(this.selectedPerson.id), // Convert to string for people_activities table
            activity_type: 'supply_provision',
            title: 'Outreach Transaction',
            description: document.getElementById('transaction-notes')?.value || '',
            location: document.getElementById('transaction-location')?.value || '',
            coordinates: this.currentLocation?.coordinates || null,
            activity_date: new Date().toISOString().split('T')[0],
            staff_member: staffMember,
            created_by: staffMember,
            created_at: new Date().toISOString()
        };

        const activity = await this.data.insert('people_activities', activityData);

        // Create supply provision records and update inventory
        const itemsForTransaction = [];
        for (const item of this.transactionItems) {
            // Create supply provision record
            console.log('🔍 DEBUG: supply_provisions insert data:', {
                activity_id: activity.id,
                item_id: item.item_id,
                activity_type: typeof activity.id,
                item_type: typeof item.item_id,
                activity_full: activity,
                item_full: item
            });
            
            await this.data.insert('supply_provisions', {
                activity_id: activity.id,
                item_id: item.item_id,
                quantity_provided: item.quantity,
                notes: activityData.description,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });

            // Update item stock
            const currentItem = await this.data.get('items', item.item_id);
            const newStock = currentItem.current_stock - item.quantity;

            await this.data.update('items', item.item_id, {
                current_stock: newStock,
                updated_at: new Date().toISOString()
            });

            // Prepare item data for outreach transaction record
            itemsForTransaction.push({
                item_id: item.item_id,
                item_name: item.item_name,
                category: item.category,
                quantity: item.quantity,
                unit_type: item.unit_type
            });
        }

        // Create outreach transaction record for UI display (uses BIGINT for person_id)
        const personName = `${this.selectedPerson.first_name} ${this.selectedPerson.last_name}`.trim();
        const totalItems = this.transactionItems.reduce((sum, item) => sum + item.quantity, 0);
        
        await this.data.insert('outreach_transactions', {
            person_id: parseInt(this.selectedPerson.id), // Convert to integer for outreach_transactions table
            person_name: personName,
            staff_member: staffMember,
            location: activityData.location,
            coordinates: activityData.coordinates,
            latitude: this.currentLocation?.latitude || null,
            longitude: this.currentLocation?.longitude || null,
            location_source: this.currentLocation?.source || 'manual',
            notes: activityData.description,
            total_items: totalItems,
            items: JSON.stringify(itemsForTransaction),
            activity_date: activityData.activity_date,
            created_by: staffMember,
            created_at: new Date().toISOString()
        });
    }

    resetForm() {
        // Clear person selection
        this.clearPersonSelection();
        
        // Clear location and notes
        const locationInput = document.getElementById('transaction-location');
        const notesInput = document.getElementById('transaction-notes');

        if (locationInput) locationInput.value = '';
        if (notesInput) notesInput.value = '';

        // Reset current location
        this.currentLocation = null;

        this.ui.setStatus('Form reset for new transaction', 'info');
    }

    // === OUTREACH LIST MANAGEMENT METHODS ===
    // Methods extracted from app.js for outreach list view functionality

    /**
     * Load outreach content HTML
     */
    async loadOutreachContent() {
        const { outreachListTemplates } = await import('../templates/index.js');
        return outreachListTemplates.outreachDashboard();
    }

    /**
     * Initialize outreach list view
     */
    async initializeOutreach() {
        try {
            this.logger.info('Initializing outreach interface...');

            // Initialize state
            this.selectedOutreachTransaction = null;
            window.selectedOutreachId = null;
            this.outreachUpdateInterval = null;

            // Set up event handlers
            this.setupOutreachEventHandlers();
            
            // Load initial data
            await this.loadOutreachData();

            // Start real-time updates
            this.startOutreachUpdates();

            this.logger.info('Outreach interface initialized');

        } catch (error) {
            this.logger.error('Error initializing outreach', error);
        }
    }

    /**
     * Set up event handlers for outreach list view
     */
    setupOutreachEventHandlers() {
        // Clear any existing event handlers first
        const oldHandlers = this.outreachEventHandlers || [];
        oldHandlers.forEach(handler => {
            handler.element.removeEventListener(handler.event, handler.function);
        });
        this.outreachEventHandlers = [];

        // Refresh button
        const refreshBtn = document.getElementById('refresh-outreach');
        if (refreshBtn) {
            const handler = () => this.loadOutreachData();
            refreshBtn.addEventListener('click', handler);
            this.outreachEventHandlers.push({element: refreshBtn, event: 'click', function: handler});
        }

        // Search functionality
        const searchInput = document.getElementById('outreach-search-input');
        const clearSearchBtn = document.getElementById('clear-outreach-search');

        if (searchInput) {
            const searchHandler = (e) => {
                if (e.key === 'Enter' || e.type === 'input') {
                    this.filterOutreachList();
                }
            };
            searchInput.addEventListener('input', searchHandler);
            searchInput.addEventListener('keypress', searchHandler);
            this.outreachEventHandlers.push({element: searchInput, event: 'input', function: searchHandler});
            this.outreachEventHandlers.push({element: searchInput, event: 'keypress', function: searchHandler});
        }

        if (clearSearchBtn) {
            const clearHandler = () => this.clearOutreachSearch();
            clearSearchBtn.addEventListener('click', clearHandler);
            this.outreachEventHandlers.push({element: clearSearchBtn, event: 'click', function: clearHandler});
        }

        // Filter dropdowns
        const dateFilter = document.getElementById('outreach-date-filter');
        const staffFilter = document.getElementById('outreach-staff-filter');

        if (dateFilter) {
            const dateHandler = () => this.filterOutreachList();
            dateFilter.addEventListener('change', dateHandler);
            this.outreachEventHandlers.push({element: dateFilter, event: 'change', function: dateHandler});
        }

        if (staffFilter) {
            const staffHandler = () => this.filterOutreachList();
            staffFilter.addEventListener('change', staffHandler);
            this.outreachEventHandlers.push({element: staffFilter, event: 'change', function: staffHandler});
        }

        // Real-time data updates - listen for changes to outreach transactions
        const dataChangeHandler = (event) => {
            const { table, operation } = event.detail;
            if (table === 'outreach_transactions' && operation !== 'refresh') {
                this.loadOutreachData();
            }
        };
        document.addEventListener('dataChange', dataChangeHandler);
        this.outreachEventHandlers.push({element: document, event: 'dataChange', function: dataChangeHandler});
    }

    /**
     * Load outreach data with filtering
     */
    async loadOutreachData() {
        try {
            this.logger.info('Loading outreach data...');

            // Get filter values
            const dateFilter = document.getElementById('outreach-date-filter')?.value || '';
            const staffFilter = document.getElementById('outreach-staff-filter')?.value || '';
            const searchInput = document.getElementById('outreach-search-input')?.value?.trim() || '';

            // Fetch all outreach transactions
            let transactions = await this.data.search('outreach_transactions', {}) || [];

            // Apply date filter
            if (dateFilter) {
                const today = new Date();
                let filterDate;

                switch (dateFilter) {
                    case 'today':
                        filterDate = today.toISOString().split('T')[0];
                        transactions = transactions.filter(t => t.created_at?.startsWith(filterDate));
                        break;
                    case 'week':
                        const weekAgo = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
                        transactions = transactions.filter(t => new Date(t.created_at) >= weekAgo);
                        break;
                    case 'month':
                        const monthAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
                        transactions = transactions.filter(t => new Date(t.created_at) >= monthAgo);
                        break;
                }
            }

            // Apply staff filter
            if (staffFilter) {
                transactions = transactions.filter(t => t.created_by === staffFilter);
            }

            // Apply search filter
            if (searchInput) {
                const searchTerm = searchInput.toLowerCase();
                transactions = transactions.filter(t => {
                    const searchableText = [
                        t.person_name,
                        t.location,
                        t.notes,
                        t.items_summary
                    ].filter(Boolean).join(' ').toLowerCase();
                    return searchableText.includes(searchTerm);
                });
            }

            // Sort by created_at desc
            transactions.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            // Update the list
            this.updateOutreachList(transactions);

            // Update counts
            this.updateOutreachStats(transactions);

            this.logger.info(`Loaded ${transactions.length} outreach transactions`);

        } catch (error) {
            this.logger.error('Error loading outreach data', error);
            this.showOutreachError();
        }
    }

    /**
     * Start real-time updates for outreach data
     */
    startOutreachUpdates() {
        // Clear existing interval
        if (this.outreachUpdateInterval) {
            clearInterval(this.outreachUpdateInterval);
        }

        // Set up new interval
        this.outreachUpdateInterval = setInterval(async () => {
            if (window.app && window.app.currentTab === 'outreach') {
                await this.loadOutreachData();
            }
        }, 30000);
    }

    /**
     * Update outreach list display
     */
    updateOutreachList(transactions) {
        const outreachList = document.getElementById('outreach-list');
        if (!outreachList) return;

        if (transactions.length === 0) {
            const { outreachListTemplates } = require('../templates/index.js');
            outreachList.innerHTML = outreachListTemplates.createNoTransactionsFound();
            return;
        }

        // Generate transaction HTML
        const transactionsHtml = transactions.map(transaction => {
            const { outreachListTemplates } = require('../templates/index.js');
            return outreachListTemplates.createTransactionItem(transaction);
        }).join('');

        outreachList.innerHTML = transactionsHtml;
    }

    /**
     * Update outreach statistics display
     */
    updateOutreachStats(transactions) {
        const countDisplay = document.getElementById('outreach-count-display');
        const lastUpdateDisplay = document.getElementById('outreach-last-update');

        if (countDisplay) {
            countDisplay.textContent = transactions.length;
        }

        if (lastUpdateDisplay) {
            lastUpdateDisplay.textContent = new Date().toLocaleTimeString();
        }

        // Update stats breakdown
        const todayCount = document.getElementById('today-count');
        const weekCount = document.getElementById('week-count');
        const totalItems = document.getElementById('total-items');

        if (todayCount || weekCount || totalItems) {
            const today = new Date().toISOString().split('T')[0];
            const weekAgo = new Date(Date.now() - (7 * 24 * 60 * 60 * 1000));

            const todayTransactions = transactions.filter(t => t.created_at?.startsWith(today));
            const weekTransactions = transactions.filter(t => new Date(t.created_at) >= weekAgo);
            const totalItemsCount = transactions.reduce((sum, t) => {
                if (t.items && Array.isArray(t.items)) {
                    return sum + t.items.reduce((itemSum, item) => itemSum + (item.quantity || 0), 0);
                }
                return sum;
            }, 0);

            if (todayCount) todayCount.textContent = todayTransactions.length;
            if (weekCount) weekCount.textContent = weekTransactions.length;
            if (totalItems) totalItems.textContent = totalItemsCount;
        }
    }

    /**
     * Filter outreach list
     */
    filterOutreachList() {
        this.loadOutreachData();
    }

    /**
     * Clear outreach search and filters
     */
    clearOutreachSearch() {
        const searchInput = document.getElementById('outreach-search-input');
        const dateFilter = document.getElementById('outreach-date-filter');
        const staffFilter = document.getElementById('outreach-staff-filter');

        if (searchInput) searchInput.value = '';
        if (dateFilter) dateFilter.value = '';
        if (staffFilter) staffFilter.value = '';

        this.loadOutreachData();
    }

    /**
     * Show outreach error state
     */
    showOutreachError() {
        const outreachList = document.getElementById('outreach-list');
        if (outreachList) {
            outreachList.innerHTML = '<div class="error">Error loading outreach data. Please try refreshing.</div>';
        }
    }

    /**
     * Select outreach transaction
     */
    selectOutreachTransaction(transactionId) {
        // Update selection state
        window.selectedOutreachId = transactionId;
        this.selectedOutreachTransaction = transactionId;

        // Update visual selection in list
        const items = document.querySelectorAll('.outreach-item');
        items.forEach(item => {
            if (item.dataset.transactionId == transactionId) {
                item.classList.add('selected');
            } else {
                item.classList.remove('selected');
            }
        });

        // Load transaction details
        this.loadOutreachTransactionDetail(transactionId);
    }

    /**
     * Load outreach transaction detail
     */
    async loadOutreachTransactionDetail(transactionId) {
        try {
            const transaction = await this.data.get('outreach_transactions', transactionId);
            if (!transaction) {
                this.logger.error('Transaction not found:', transactionId);
                return;
            }

            // Process items data
            if (transaction.items) {
                if (typeof transaction.items === 'string') {
                    try {
                        transaction.items = JSON.parse(transaction.items);
                    } catch (e) {
                        this.logger.warn('Failed to parse items JSON:', e);
                        transaction.items = [];
                    }
                }

                if (!Array.isArray(transaction.items)) {
                    transaction.items = [];
                }
            } else {
                transaction.items = [];
            }

            // Update details section
            const detailsSection = document.getElementById('outreach-details');
            if (detailsSection) {
                const { outreachDetailTemplates } = await import('../templates/index.js');
                detailsSection.innerHTML = outreachDetailTemplates.transactionDetail(transaction);
            }

        } catch (error) {
            this.logger.error('Error loading transaction detail', error);
        }
    }

    /**
     * Refresh outreach list
     */
    refreshOutreachList() {
        this.loadOutreachData();
    }

    /**
     * Export outreach data
     */
    exportOutreachData() {
        // Placeholder for export functionality
        this.logger.info('Export outreach data functionality to be implemented');
    }

    /**
     * Cleanup outreach resources
     */
    async cleanup() {
        // Clear update interval
        if (this.outreachUpdateInterval) {
            clearInterval(this.outreachUpdateInterval);
            this.outreachUpdateInterval = null;
        }

        // Remove event handlers
        if (this.outreachEventHandlers && Array.isArray(this.outreachEventHandlers)) {
            this.outreachEventHandlers.forEach(handler => {
                if (handler.element && handler.event && handler.function) {
                    handler.element.removeEventListener(handler.event, handler.function);
                }
            });
            this.outreachEventHandlers = [];
        }

        this.logger.info('Outreach manager cleanup completed');
    }
}
