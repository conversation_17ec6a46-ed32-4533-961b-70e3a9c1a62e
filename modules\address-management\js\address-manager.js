import { BaseCrudManager } from '../../shared/base-crud-manager.js';

export class AddressManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager = null) {
        super(dataManager, authManager, 'addresses', 'address', uiManager);
        this.activitiesTable = 'address_activities';
    }

    /**
     * Load addresses management view with search functionality
     * @returns {Promise<string>} - HTML content for addresses management
     */
    async loadAddressesManagementContent() {
        // Get all addresses from the database
        const addresses = await this.data.search('addresses', {});

        // Import the address management templates
        const { addressManagementTemplates } = await import('../templates/address-management-templates.js');

        // Set up event handlers after content is loaded
        setTimeout(() => {
            this.setupAddressListEventHandlers(addresses);
        }, 100);

        // Use the compact list template
        return addressManagementTemplates.addressListView(addresses);
    }


    /**
     * Show address detail view
     * @param {Object} address - Address object to display
     * @returns {Promise<string>} - HTML content for address detail view
     */
    async showAddressDetailView(address) {
        // Show address detail view
        
        const { addressDetailTemplates } = await import('../templates/address-detail-templates.js');
        
        // Set up tab switching after content loads
        setTimeout(() => {
            this.setupAddressDetailEventHandlers(address);
        }, 100);

        return addressDetailTemplates.addressDetailContent(address);
    }

    /**
     * Set up event handlers for address list view
     * @param {Array} addresses - Array of address objects
     */
    setupAddressListEventHandlers(addresses) {
        // Set up address list event handlers

        // Search functionality
        const searchInput = document.querySelector('input[data-action="search-addresses-input"]');
        
        if (searchInput) {
            const performSearch = async () => {
                const searchTerm = searchInput.value.toLowerCase().trim();
                await this.filterAddresses(addresses, searchTerm);
            };

            searchInput.addEventListener('input', performSearch);
        }

        // Set up click handlers for view and edit buttons - these are handled by the command system via data-action attributes
        // No additional click handlers needed as the template uses data-action attributes
    }

    /**
     * Set up event handlers for address detail view
     * @param {Object} address - Address object
     */
    setupAddressDetailEventHandlers(address) {
        // Set up address detail event handlers

        // Tab switching (using person detail tab classes for consistency)
        const tabs = document.querySelectorAll('.person-detail-tab');
        const tabContents = document.querySelectorAll('.person-detail-content .tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const targetTab = e.target.dataset.tab;
                
                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
                
                // Update active content
                tabContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === `${targetTab}-tab`) {
                        content.classList.add('active');
                    }
                });

                // Load activities if activities tab is clicked
                if (targetTab === 'activities') {
                    this.loadAddressActivities(address.id);
                }
            });
        });

        // Load activities initially if activities tab is visible
        setTimeout(() => {
            const activitiesTab = document.querySelector('#activities-tab');
            if (activitiesTab && activitiesTab.classList.contains('active')) {
                this.loadAddressActivities(address.id);
            }
        }, 100);
    }

    /**
     * Load activities for an address
     * @param {string} addressId - Address ID
     */
    async loadAddressActivities(addressId) {
        // Load activities for address - both address_activities and people_activities

        const activitiesList = document.getElementById('activities-list');
        if (!activitiesList) return;

        try {
            activitiesList.innerHTML = '<div class="loading">Loading activities...</div>';

            // Fetch both types of activities in parallel
            const [addressActivities, personActivities] = await Promise.all([
                this.data.search('address_activities', { address_id: addressId }),
                this.loadPersonActivitiesForAddress(addressId)
            ]);

            // Combine and sort activities by date (newest first)
            const allActivities = [];

            // Add address activities with type marker
            if (addressActivities && addressActivities.length > 0) {
                addressActivities.forEach(activity => {
                    allActivities.push({
                        ...activity,
                        activity_source: 'address',
                        sort_date: new Date(activity.activity_date || activity.created_at)
                    });
                });
            }

            // Add person activities with type marker
            if (personActivities && personActivities.length > 0) {
                personActivities.forEach(activity => {
                    allActivities.push({
                        ...activity,
                        activity_source: 'person',
                        sort_date: new Date(activity.activity_date || activity.created_at)
                    });
                });
            }

            // Sort by date (newest first)
            allActivities.sort((a, b) => b.sort_date - a.sort_date);

            if (allActivities.length > 0) {
                const { addressDetailTemplates } = await import('../templates/address-detail-templates.js');
                activitiesList.innerHTML = allActivities.map(activity =>
                    this.renderActivityItem(activity, addressDetailTemplates)
                ).join('');
            } else {
                const { addressDetailTemplates } = await import('../templates/address-detail-templates.js');
                activitiesList.innerHTML = addressDetailTemplates.noActivitiesMessage();
            }
        } catch (error) {
            console.error('❌ Error loading address activities:', error);
            activitiesList.innerHTML = '<div class="error">Error loading activities</div>';
        }
    }

    /**
     * Load person activities that are associated with this address
     */
    async loadPersonActivitiesForAddress(addressId) {
        try {
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                console.warn('Supabase client not available for person activities');
                return [];
            }

            const { data: activities, error } = await supabase
                .schema('core')
                .from('people_activities')
                .select(`
                    *,
                    people:person_id (
                        first_name,
                        last_name
                    )
                `)
                .eq('address_id', String(addressId))
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error loading person activities for address:', error);
                return [];
            }

            return activities || [];
        } catch (error) {
            console.error('Error in loadPersonActivitiesForAddress:', error);
            return [];
        }
    }

    /**
     * Render activity item based on its source type
     */
    renderActivityItem(activity, addressDetailTemplates) {
        if (activity.activity_source === 'person') {
            // Render person activity with additional context
            return this.renderPersonActivityItem(activity);
        } else {
            // Render address activity using existing template
            return addressDetailTemplates.activityItem(activity);
        }
    }

    /**
     * Render person activity item for address view
     */
    renderPersonActivityItem(activity) {
        const activityDate = activity.activity_date ? new Date(activity.activity_date).toLocaleDateString() : 'No date';
        const activityTime = activity.activity_time || '';
        const dateTimeDisplay = activityTime ? `${activityDate} at ${activityTime}` : activityDate;

        // Get person name from the joined data
        const personName = activity.people ?
            `${activity.people.first_name || ''} ${activity.people.last_name || ''}`.trim() || 'Unknown Person' :
            'Unknown Person';

        // Format location information
        const locationInfo = activity.location ?
            `<div class="activity-location">
                <i class="fas fa-location-dot"></i>
                <span>${activity.location}</span>
            </div>` : '';

        // Format follow-up information
        const followUpInfo = activity.follow_up_required ?
            `<div class="activity-follow-up">
                <i class="fas fa-calendar-check"></i>
                <span>Follow-up required${activity.follow_up_date ? ` by ${new Date(activity.follow_up_date).toLocaleDateString()}` : ''}</span>
            </div>` : '';

        // Format priority badge
        const priorityBadge = activity.priority ?
            `<span class="priority-badge priority-${activity.priority.toLowerCase()}">${activity.priority}</span>` : '';

        return `
            <div class="activity-item person-activity" data-activity-id="${activity.id}">
                <div class="activity-header">
                    <div class="activity-title-section">
                        <h4 class="activity-title">${activity.title || activity.activity_type}</h4>
                        <span class="activity-source-badge">Person Activity</span>
                        ${priorityBadge}
                    </div>
                    <div class="activity-meta">
                        <span class="activity-date">${dateTimeDisplay}</span>
                        <span class="activity-staff">by ${activity.staff_member}</span>
                    </div>
                </div>

                <div class="activity-content">
                    <div class="activity-person">
                        <strong>Person:</strong> ${personName}
                    </div>

                    <div class="activity-type">
                        <strong>Type:</strong> ${activity.activity_type}
                    </div>

                    ${activity.description ? `
                        <div class="activity-description">
                            <strong>Description:</strong> ${activity.description}
                        </div>
                    ` : ''}

                    ${locationInfo}

                    ${activity.outcome ? `
                        <div class="activity-outcome">
                            <strong>Outcome:</strong> ${activity.outcome}
                        </div>
                    ` : ''}

                    ${followUpInfo}
                </div>

                <div class="activity-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewPersonDetail('${activity.person_id}')">
                        <i class="fas fa-user"></i> View Person
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editPersonActivity('${activity.id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Filter addresses based on search term
     * @param {Array} addresses - Array of address objects
     * @param {string} searchTerm - Search term to filter by
     */
    async filterAddresses(addresses, searchTerm) {
        // Filter addresses
        
        const addressList = document.getElementById('address-list');
        
        if (!addressList) return;

        let filteredAddresses = addresses;
        
        if (searchTerm) {
            filteredAddresses = addresses.filter(address => {
                const searchableText = [
                    address.street_address,
                    address.city,
                    address.province,
                    address.postal_code,
                    address.address_type,
                    address.unit_number,
                    address.notes
                ].filter(Boolean).join(' ').toLowerCase();
                
                return searchableText.includes(searchTerm);
            });
        }

        // Import templates and update the display
        const { addressManagementTemplates } = await import('../templates/address-management-templates.js');
        
        // Update just the list content, not the entire template
        if (filteredAddresses && filteredAddresses.length > 0) {
            addressList.innerHTML = `
                <div class="address-list-header">
                    <div>Street Address</div>
                    <div>City</div>
                    <div>Province</div>
                    <div>Type</div>
                    <div>Actions</div>
                </div>
                ${filteredAddresses.map(address => addressManagementTemplates.addressListItem(address)).join('')}
            `;
        } else {
            addressList.innerHTML = '<div class="no-addresses">NO ADDRESSES FOUND<br><br>Try adjusting your search terms.</div>';
        }

        // Re-setup event handlers for the filtered results
        setTimeout(() => {
            this.setupAddressListEventHandlers(addresses); // Pass original array for full search capability
        }, 50);
    }

    /**
     * Search addresses with specific criteria
     * @param {Object} criteria - Search criteria
     * @returns {Promise<Array>} - Array of matching addresses
     */
    async searchAddresses(criteria = {}) {
        // Search addresses
        return await this.data.search('addresses', criteria);
    }

    /**
     * Get address by ID
     * @param {string} addressId - Address ID
     * @returns {Promise<Object|null>} - Address object or null
     */
    async getAddress(addressId) {
        // Get address by ID
        return await this.data.get('addresses', addressId);
    }

    /**
     * Create new address
     * @param {Object} addressData - Address data
     * @returns {Promise<Object>} - Created address object
     */
    async createAddress(addressData) {
        // Create new address
        return await this.data.create('addresses', {
            ...addressData,
            created_by: this.auth.getCurrentUser()?.email,
            created_at: new Date().toISOString()
        });
    }

    /**
     * Update existing address
     * @param {string} addressId - Address ID
     * @param {Object} addressData - Updated address data
     * @returns {Promise<Object>} - Updated address object
     */
    async updateAddress(addressId, addressData) {
        // Update address
        return await this.data.update('addresses', addressId, {
            ...addressData,
            updated_by: this.auth.getCurrentUser()?.email,
            updated_at: new Date().toISOString()
        });
    }

    /**
     * Delete address
     * @param {string} addressId - Address ID
     * @returns {Promise<boolean>} - Success status
     */
    async deleteAddress(addressId) {
        // Delete address
        return await this.data.delete('addresses', addressId);
    }
}