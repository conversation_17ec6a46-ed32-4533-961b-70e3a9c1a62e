/**
 * Incident Search Manager
 * Handles incident search, filtering, and advanced search operations
 * Extracted from app.js - manages search and filtering functionality
 */

import { BaseManager } from '../../shared/base-manager.js';
import { incidentDetailTemplates } from '../templates/index.js';

export class IncidentSearchManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    async loadIncidentSearchContent() {
        return incidentDetailTemplates.incidentSearchView();
    }

    setupIncidentSearch() {
        console.log('Setting up incident search functionality...');
        
        // Search button functionality
        const searchBtn = document.getElementById('incident-search-btn');
        const searchInput = document.getElementById('incident-search-input');
        const clearBtn = document.getElementById('incident-search-clear');

        if (searchBtn && searchInput) {
            searchBtn.addEventListener('click', () => {
                this.performIncidentSearch();
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performIncidentSearch();
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearIncidentSearch();
            });
        }

        // Advanced search filters
        const statusFilter = document.getElementById('incident-status-filter');
        const priorityFilter = document.getElementById('incident-priority-filter');
        const typeFilter = document.getElementById('incident-type-filter');
        const dateFromFilter = document.getElementById('incident-date-from');
        const dateToFilter = document.getElementById('incident-date-to');

        [statusFilter, priorityFilter, typeFilter, dateFromFilter, dateToFilter].forEach(filter => {
            if (filter) {
                filter.addEventListener('change', () => {
                    this.performIncidentSearch();
                });
            }
        });
    }

    async performIncidentSearch() {
        console.log('Performing incident search...');
        
        const resultsContainer = document.getElementById('incident-search-results');
        if (!resultsContainer) return;

        try {
            // Show loading state
            resultsContainer.innerHTML = '<div class="loading">Searching incidents...</div>';

            // Get search criteria
            const searchInput = document.getElementById('incident-search-input');
            const statusFilter = document.getElementById('incident-status-filter');
            const priorityFilter = document.getElementById('incident-priority-filter');
            const typeFilter = document.getElementById('incident-type-filter');
            const dateFromFilter = document.getElementById('incident-date-from');
            const dateToFilter = document.getElementById('incident-date-to');

            const searchTerm = searchInput ? searchInput.value.trim().toLowerCase() : '';
            const statusValue = statusFilter ? statusFilter.value : '';
            const priorityValue = priorityFilter ? priorityFilter.value : '';
            const typeValue = typeFilter ? typeFilter.value : '';
            const dateFrom = dateFromFilter ? dateFromFilter.value : '';
            const dateTo = dateToFilter ? dateToFilter.value : '';

            // Get all incidents
            const incidents = await this.data.search('incidents', {});

            // Apply search filters
            const filteredIncidents = incidents.filter(incident => {
                // Text search across multiple fields
                if (searchTerm) {
                    const searchableText = [
                        incident.incident_number,
                        incident.description,
                        incident.narrative,
                        incident.location,
                        incident.incident_type,
                        incident.assigned_ranger,
                        incident.reporter_name
                    ].join(' ').toLowerCase();

                    if (!searchableText.includes(searchTerm)) {
                        return false;
                    }
                }

                // Status filter
                if (statusValue && (incident.status || 'open').toLowerCase() !== statusValue.toLowerCase()) {
                    return false;
                }

                // Priority filter
                if (priorityValue && (incident.priority || 'medium').toLowerCase() !== priorityValue.toLowerCase()) {
                    return false;
                }

                // Type filter
                if (typeValue && (incident.incident_type || '').toLowerCase() !== typeValue.toLowerCase()) {
                    return false;
                }

                // Date range filter
                if (dateFrom || dateTo) {
                    const incidentDate = new Date(incident.incident_time || incident.created_at);
                    
                    if (dateFrom) {
                        const fromDate = new Date(dateFrom);
                        if (incidentDate < fromDate) {
                            return false;
                        }
                    }
                    
                    if (dateTo) {
                        const toDate = new Date(dateTo);
                        toDate.setHours(23, 59, 59, 999); // End of day
                        if (incidentDate > toDate) {
                            return false;
                        }
                    }
                }

                return true;
            });

            // Sort results by relevance and date
            filteredIncidents.sort((a, b) => {
                // If there's a search term, prioritize exact matches in incident number
                if (searchTerm) {
                    const aExactMatch = (a.incident_number || '').toLowerCase().includes(searchTerm);
                    const bExactMatch = (b.incident_number || '').toLowerCase().includes(searchTerm);
                    
                    if (aExactMatch && !bExactMatch) return -1;
                    if (!aExactMatch && bExactMatch) return 1;
                }
                
                // Then sort by date (newest first)
                return new Date(b.created_at) - new Date(a.created_at);
            });

            // Display results
            if (filteredIncidents.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <h4>No incidents found</h4>
                        <p>Try adjusting your search criteria or clear filters to see all incidents.</p>
                    </div>
                `;
                return;
            }

            const resultsHTML = `
                <div class="search-results-header">
                    <h4>Found ${filteredIncidents.length} incident(s)</h4>
                </div>
                <div class="search-results-list">
                    ${filteredIncidents.map(incident => `
                        <div class="search-result-item" data-incident-id="${incident.id}">
                            <div class="result-header">
                                <span class="incident-number">#${incident.incident_number}</span>
                                <span class="incident-priority priority-${(incident.priority || 'medium').toLowerCase()}">${incident.priority || 'Medium'}</span>
                                <span class="incident-status status-${(incident.status || 'open').toLowerCase()}">${incident.status || 'Open'}</span>
                            </div>
                            <div class="result-type">${incident.incident_type || 'General Incident'}</div>
                            <div class="result-location">📍 ${incident.location || 'No location specified'}</div>
                            <div class="result-description">${(incident.description || incident.narrative || '').substring(0, 100)}${(incident.description || incident.narrative || '').length > 100 ? '...' : ''}</div>
                            <div class="result-meta">
                                <span class="result-date">🕒 ${this.uiUtilities.formatDateTime(incident.incident_time || incident.created_at)}</span>
                                <span class="result-ranger">${incident.assigned_ranger ? `👤 ${incident.assigned_ranger}` : '👤 Unassigned'}</span>
                            </div>
                            <div class="result-actions">
                                <button class="view-incident-btn" data-action="view-incident-from-search" data-incident-id="${incident.id}">View Details</button>
                                <button class="edit-incident-btn" data-action="edit-incident" data-incident-id="${incident.id}">Edit</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            resultsContainer.innerHTML = resultsHTML;

            // Add click handlers for result items
            resultsContainer.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', (e) => {
                    // Don't trigger if clicking on action buttons
                    if (e.target.tagName === 'BUTTON') return;
                    
                    const incidentId = item.dataset.incidentId;
                    this.viewIncidentFromSearch(incidentId);
                });
            });

        } catch (error) {
            console.error('Error performing incident search:', error);
            resultsContainer.innerHTML = `
                <div class="search-error">
                    <h4>Search Error</h4>
                    <p>Failed to search incidents: ${error.message}</p>
                </div>
            `;
        }
    }

    async viewIncidentFromSearch(incidentId) {
        try {
            console.log('Viewing incident from search:', incidentId);

            // Get incident data
            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Close search view and switch to incident detail view
            const searchView = document.querySelector('.incident-search-content');
            const incidentsContent = document.querySelector('.incidents-content');
            
            if (searchView) {
                searchView.style.display = 'none';
            }
            
            if (incidentsContent) {
                incidentsContent.style.display = 'block';
                
                // Show incident details
                const detailView = document.getElementById('incident-detail-view');
                if (detailView) {
                    detailView.style.display = 'block';
                    detailView.innerHTML = incidentDetailTemplates.incidentDetailView(incident);
                }
            }

            // Update app state if available
            if (window.app && window.app.selectIncident) {
                window.app.selectIncident(incidentId);
            }

        } catch (error) {
            console.error('Error viewing incident from search:', error);
            this.ui.showDialog('Error', `Failed to view incident: ${error.message}`, 'error');
        }
    }

    clearIncidentSearch() {
        // Clear all search inputs
        const searchInput = document.getElementById('incident-search-input');
        const statusFilter = document.getElementById('incident-status-filter');
        const priorityFilter = document.getElementById('incident-priority-filter');
        const typeFilter = document.getElementById('incident-type-filter');
        const dateFromFilter = document.getElementById('incident-date-from');
        const dateToFilter = document.getElementById('incident-date-to');
        const resultsContainer = document.getElementById('incident-search-results');

        [searchInput, statusFilter, priorityFilter, typeFilter, dateFromFilter, dateToFilter].forEach(input => {
            if (input) {
                input.value = '';
            }
        });

        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="no-search">Enter search criteria and click "Search" to find incidents.</div>';
        }

        console.log('Incident search cleared');
    }

    // Advanced search for people in incidents
    async searchPeopleForIncident() {
        const searchInput = document.getElementById('person-search-input');
        const resultsContainer = document.getElementById('person-search-results');
        
        if (!searchInput || !resultsContainer) return;

        const searchTerm = searchInput.value.trim().toLowerCase();
        
        if (searchTerm.length < 2) {
            resultsContainer.innerHTML = '<div class="search-hint">Enter at least 2 characters to search</div>';
            return;
        }

        try {
            // Search people
            const people = await this.data.search('people', {});
            
            const filteredPeople = people.filter(person => {
                const searchableText = [
                    person.first_name,
                    person.last_name,
                    person.preferred_name,
                    person.date_of_birth
                ].join(' ').toLowerCase();
                
                return searchableText.includes(searchTerm);
            });

            if (filteredPeople.length === 0) {
                resultsContainer.innerHTML = '<div class="no-results">No people found matching your search</div>';
                return;
            }

            const resultsHTML = filteredPeople.map(person => `
                <div class="person-search-result" data-person-id="${person.id}">
                    <div class="person-name">${person.first_name} ${person.last_name}</div>
                    <div class="person-details">
                        ${person.preferred_name ? `Preferred: ${person.preferred_name}` : ''}
                        ${person.date_of_birth ? `DOB: ${this.uiUtilities.formatDate(person.date_of_birth)}` : ''}
                    </div>
                    <button class="add-to-incident-btn" data-person-id="${person.id}">Add to Incident</button>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;

            // Add click handlers
            resultsContainer.querySelectorAll('.add-to-incident-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const personId = btn.dataset.personId;
                    this.addPersonToIncidentFromSearch(personId);
                });
            });

        } catch (error) {
            console.error('Error searching people for incident:', error);
            resultsContainer.innerHTML = '<div class="search-error">Error searching people</div>';
        }
    }

    async addPersonToIncidentFromSearch(personId) {
        // This would integrate with the incident people manager
        console.log('Adding person to incident from search:', personId);
        
        // Emit event for incident people manager to handle
        const event = new CustomEvent('addPersonToIncident', {
            detail: { personId }
        });
        
        window.dispatchEvent(event);
    }

    // Quick filters for common search patterns
    applyQuickFilter(filterType) {
        const searchInput = document.getElementById('incident-search-input');
        const statusFilter = document.getElementById('incident-status-filter');
        const priorityFilter = document.getElementById('incident-priority-filter');

        // Clear existing filters
        this.clearIncidentSearch();

        switch (filterType) {
            case 'open-high':
                if (statusFilter) statusFilter.value = 'open';
                if (priorityFilter) priorityFilter.value = 'high';
                break;
            case 'closed-today':
                if (statusFilter) statusFilter.value = 'closed';
                const today = new Date().toISOString().split('T')[0];
                const dateFromFilter = document.getElementById('incident-date-from');
                const dateToFilter = document.getElementById('incident-date-to');
                if (dateFromFilter) dateFromFilter.value = today;
                if (dateToFilter) dateToFilter.value = today;
                break;
            case 'unassigned':
                if (searchInput) searchInput.value = 'unassigned';
                break;
            case 'medical':
                if (searchInput) searchInput.value = 'medical';
                break;
            case 'overdose':
                if (searchInput) searchInput.value = 'overdose';
                break;
            case 'welfare-check':
                if (searchInput) searchInput.value = 'welfare';
                break;
        }

        // Perform search with applied filter
        this.performIncidentSearch();
    }

    // Export search results
    async exportSearchResults() {
        try {
            const resultsContainer = document.getElementById('incident-search-results');
            if (!resultsContainer) return;

            const incidents = [];
            const resultItems = resultsContainer.querySelectorAll('.search-result-item');
            
            for (const item of resultItems) {
                const incidentId = item.dataset.incidentId;
                const allIncidents = await this.data.search('incidents', {});
                const incident = allIncidents.find(i => i.id == incidentId);
                if (incident) {
                    incidents.push(incident);
                }
            }

            if (incidents.length === 0) {
                this.ui.showDialog('Info', 'No search results to export.', 'info');
                return;
            }

            // Create CSV data
            const csvHeader = ['Incident Number', 'Type', 'Status', 'Priority', 'Location', 'Date/Time', 'Assigned Ranger', 'Description'];
            const csvRows = incidents.map(incident => [
                incident.incident_number || '',
                incident.incident_type || '',
                incident.status || 'open',
                incident.priority || 'medium',
                incident.location || '',
                this.uiUtilities.formatDateTime(incident.incident_time || incident.created_at),
                incident.assigned_ranger || 'Unassigned',
                (incident.description || '').replace(/"/g, '""') // Escape quotes for CSV
            ]);

            const csvContent = [csvHeader, ...csvRows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');

            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `incident_search_results_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.uiUtilities.showToast('Search results exported successfully', 'success');

        } catch (error) {
            console.error('Error exporting search results:', error);
            this.ui.showDialog('Error', `Failed to export search results: ${error.message}`, 'error');
        }
    }
}