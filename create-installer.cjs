const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

console.log('🚀 Creating S.T.E.V.I Retro Installer...');

async function createInstaller() {
    try {
        // Step 1: Build the application
        console.log('📦 Building application...');
        execSync('node build-custom.cjs', { stdio: 'inherit' });
        
        // Step 2: Create simple license file if it doesn't exist
        const licensePath = path.join(__dirname, 'LICENSE');
        if (!fs.existsSync(licensePath)) {
            console.log('📄 Creating LICENSE file...');
            fs.writeFileSync(licensePath, `MIT License

Copyright (c) 2024 I.H.A.R.C

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.`);
        }
        
        // Step 3: Check for NSIS
        console.log('🔍 Checking for NSIS...');
        let nsisPath = '';
        const possiblePaths = [
            'C:\\Program Files (x86)\\NSIS\\makensis.exe',
            'C:\\Program Files\\NSIS\\makensis.exe',
            'makensis.exe' // In case it's in PATH
        ];
        
        for (const p of possiblePaths) {
            try {
                if (p === 'makensis.exe') {
                    execSync('where makensis.exe', { stdio: 'pipe' });
                    nsisPath = p;
                    break;
                } else if (fs.existsSync(p)) {
                    nsisPath = p;
                    break;
                }
            } catch (e) {
                // Continue checking
            }
        }
        
        if (!nsisPath) {
            console.log('⚠️  NSIS not found. Creating portable ZIP instead...');
            return createPortableZip();
        }
        
        // Step 4: Create installer with NSIS
        console.log('🔨 Creating installer with NSIS...');
        const nsisScript = path.join(__dirname, 'build', 'installer.nsi');
        execSync(`"${nsisPath}" "${nsisScript}"`, { stdio: 'inherit' });
        
        console.log('✅ Installer created successfully!');
        console.log('📍 Installer location: S.T.E.V.I-Retro-Installer.exe');
        
        return true;
        
    } catch (error) {
        console.error('❌ Failed to create installer:', error.message);
        console.log('📦 Creating portable ZIP as fallback...');
        return createPortableZip();
    }
}

function createPortableZip() {
    try {
        console.log('📦 Creating portable ZIP package...');
        
        // Create a portable version
        const portableDir = path.join(__dirname, 'dist', 'S.T.E.V.I-Retro-Portable');
        fs.ensureDirSync(portableDir);
        
        // Copy the built application
        fs.copySync(
            path.join(__dirname, 'dist', 'win-unpacked'), 
            portableDir
        );
        
        // Create a readme for portable version
        fs.writeFileSync(path.join(portableDir, 'README.txt'), `S.T.E.V.I Retro - Portable Version
======================================

This is a portable version of S.T.E.V.I Retro.

To run the application:
1. Double-click "S.T.E.V.I Retro.exe"
2. The application will create its data folders in your user profile

To uninstall:
1. Simply delete this folder
2. Optionally, delete the data folder at: %APPDATA%\\steviretro

Version: 1.3.0
Copyright © 2024 I.H.A.R.C. All rights reserved.
`);
        
        // Try to create ZIP if possible
        try {
            console.log('🗜️  Creating ZIP archive...');
            const archiver = require('archiver');
            const archive = archiver('zip', { zlib: { level: 9 } });
            const output = fs.createWriteStream(path.join(__dirname, 'S.T.E.V.I-Retro-Portable.zip'));
            
            archive.pipe(output);
            archive.directory(portableDir, 'S.T.E.V.I-Retro-Portable');
            archive.finalize();
            
            console.log('✅ Portable ZIP created: S.T.E.V.I-Retro-Portable.zip');
            
        } catch (zipError) {
            console.log('⚠️  Could not create ZIP. Portable folder available at:', portableDir);
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ Failed to create portable version:', error.message);
        return false;
    }
}

// Run the installer creation
createInstaller().then(success => {
    if (success) {
        console.log('🎉 Build process completed!');
    } else {
        console.log('💥 Build process failed!');
        process.exit(1);
    }
}).catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
});