# S.T.E.V.I Retro Professional Installer Builder (PowerShell)
# Creates a comprehensive Windows installer with all dependencies

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Professional Installer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found." -ForegroundColor Red
    Write-Host "Please run this script from the S.T.E.V.I Retro project directory." -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Node.js and npm
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: npm not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Stop any running electron processes
Write-Host "Stopping any running processes..." -ForegroundColor Yellow
Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "S.T.E.V.I Retro" -ErrorAction SilentlyContinue | Stop-Process -Force

# Clean up dist directory
Write-Host "Cleaning up build directories..." -ForegroundColor Yellow
if (Test-Path "dist") {
    Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
}

# Check if node_modules is corrupted
Write-Host "Checking node_modules integrity..." -ForegroundColor Yellow
$electronPath = "node_modules\electron\dist\electron.exe"
$needsCleanInstall = $false

if (-not (Test-Path $electronPath)) {
    Write-Host "Electron not found in node_modules, clean install needed" -ForegroundColor Yellow
    $needsCleanInstall = $true
}

# Check for locked files
if (Test-Path "node_modules") {
    try {
        Get-ChildItem "node_modules\electron" -ErrorAction Stop | Out-Null
    } catch {
        Write-Host "Node modules appear to be locked or corrupted" -ForegroundColor Yellow
        $needsCleanInstall = $true
    }
}

if ($needsCleanInstall) {
    Write-Host "Performing clean install of dependencies..." -ForegroundColor Yellow
    
    # Remove node_modules with retry logic
    if (Test-Path "node_modules") {
        Write-Host "Removing node_modules..." -ForegroundColor Yellow
        $retryCount = 0
        $maxRetries = 3
        
        do {
            try {
                Remove-Item -Path "node_modules" -Recurse -Force -ErrorAction Stop
                Write-Host "Successfully removed node_modules" -ForegroundColor Green
                break
            } catch {
                $retryCount++
                Write-Host "Attempt $retryCount failed: $($_.Exception.Message)" -ForegroundColor Yellow
                if ($retryCount -lt $maxRetries) {
                    Write-Host "Waiting 5 seconds before retry..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 5
                } else {
                    Write-Host "ERROR: Could not remove node_modules after $maxRetries attempts" -ForegroundColor Red
                    Write-Host "Please close all applications and try again manually" -ForegroundColor Red
                    Read-Host "Press Enter to exit"
                    exit 1
                }
            }
        } while ($retryCount -lt $maxRetries)
    }
    
    # Install dependencies
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    try {
        npm install
        if ($LASTEXITCODE -ne 0) {
            throw "npm install failed with exit code $LASTEXITCODE"
        }
        Write-Host "Dependencies installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "ERROR: Failed to install dependencies: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Check system dependencies
Write-Host "Checking system dependencies..." -ForegroundColor Yellow
try {
    npm run check-deps
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Warning: Dependency check failed, but continuing..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Warning: Could not run dependency check, continuing..." -ForegroundColor Yellow
}

Write-Host ""

# Build the installer
Write-Host "Building S.T.E.V.I Retro installer..." -ForegroundColor Cyan
Write-Host "This may take several minutes..." -ForegroundColor Yellow
Write-Host ""

try {
    # Use npx to ensure we get the latest electron-builder
    npx electron-builder --win --config.electronVersion=37.2.3
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "SUCCESS: Professional installer created!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "The installer includes:" -ForegroundColor Green
        Write-Host "- Program Files installation" -ForegroundColor Green
        Write-Host "- Automatic dependency management" -ForegroundColor Green
        Write-Host "- Uninstall and repair options" -ForegroundColor Green
        Write-Host "- Windows integration" -ForegroundColor Green
        Write-Host ""
        
        # Find the installer file
        $installerFiles = Get-ChildItem -Path "dist" -Filter "*.exe" -ErrorAction SilentlyContinue
        if ($installerFiles) {
            foreach ($file in $installerFiles) {
                Write-Host "Location: $($file.FullName)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "Location: dist\" -ForegroundColor Cyan
        }
        
        Write-Host ""
        Write-Host "Ready for distribution on fresh Windows systems!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Opening dist folder..." -ForegroundColor Yellow
        Start-Process "dist"
    } else {
        throw "electron-builder failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "ERROR: Installer creation failed!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Run: npm install" -ForegroundColor Yellow
    Write-Host "2. Delete node_modules and run: npm install" -ForegroundColor Yellow
    Write-Host "3. Check that all dependencies are installed" -ForegroundColor Yellow
    Write-Host "4. Ensure no antivirus is blocking the build" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
