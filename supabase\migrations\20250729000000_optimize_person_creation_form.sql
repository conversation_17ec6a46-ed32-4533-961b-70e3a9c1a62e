-- Migration: Optimize Person Creation Form
-- Date: 2025-07-29
-- Description: Remove redundant fields, add "Unknown" options, and set user-friendly defaults

BEGIN;

-- Remove redundant currently_homeless field since housing_status already captures this information
ALTER TABLE core.people DROP COLUMN IF EXISTS currently_homeless;

-- Set default values for better user experience
ALTER TABLE core.people ALTER COLUMN housing_status SET DEFAULT 'Unknown';
ALTER TABLE core.people ALTER COLUMN income_source SET DEFAULT 'Unknown';

-- Convert boolean fields to text fields with Yes/No/Unknown options for better UX
-- Convert has_id_documents from boolean to text
ALTER TABLE core.people 
ALTER COLUMN has_id_documents TYPE TEXT 
USING CASE 
    WHEN has_id_documents IS TRUE THEN 'Yes' 
    WHEN has_id_documents IS FALSE THEN 'No' 
    ELSE 'Unknown' 
END;

-- Set default and add constraint for has_id_documents
ALTER TABLE core.people 
ALTER COLUMN has_id_documents SET DEFAULT 'Unknown';

ALTER TABLE core.people 
ADD CONSTRAINT check_has_id_documents 
CHECK (has_id_documents IN ('Yes', 'No', 'Unknown'));

-- Convert veteran_status from boolean to text
ALTER TABLE core.people 
ALTER COLUMN veteran_status TYPE TEXT 
USING CASE 
    WHEN veteran_status IS TRUE THEN 'Yes' 
    WHEN veteran_status IS FALSE THEN 'No' 
    ELSE 'Unknown' 
END;

-- Set default and add constraint for veteran_status
ALTER TABLE core.people 
ALTER COLUMN veteran_status SET DEFAULT 'Unknown';

ALTER TABLE core.people 
ADD CONSTRAINT check_veteran_status 
CHECK (veteran_status IN ('Yes', 'No', 'Unknown'));

-- Add comments for documentation
COMMENT ON COLUMN core.people.housing_status IS 'Housing status with Unknown as default for better UX';
COMMENT ON COLUMN core.people.has_id_documents IS 'Whether person has ID documents: Yes/No/Unknown';
COMMENT ON COLUMN core.people.veteran_status IS 'Veteran status: Yes/No/Unknown';
COMMENT ON COLUMN core.people.income_source IS 'Primary income source with Unknown as default';

COMMIT;
