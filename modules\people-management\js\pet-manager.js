/**
 * Pet Manager
 * Handles pet management for people
 */

import { BaseManager } from '../../shared/base-manager.js';

export class PetManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.onPetChanged = null; // Callback for when pets change
    }

    /**
     * Add pet to person
     */
    async addPet(personId) {
        try {
            const fields = [
                { name: 'name', label: 'Pet Name', type: 'text', required: true },
                {
                    name: 'species',
                    label: 'Species',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select species...' },
                        { value: 'Dog', label: 'Dog' },
                        { value: 'Cat', label: 'Cat' },
                        { value: 'Bird', label: 'Bird' },
                        { value: 'Rabbit', label: 'Rabbit' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'breed', label: 'Breed', type: 'text' },
                { name: 'age', label: 'Age (years)', type: 'number' },
                { name: 'color', label: 'Color', type: 'text' },
                { name: 'description', label: 'Description', type: 'textarea' },
                { name: 'microchip_number', label: 'Microchip Number', type: 'text' },
                {
                    name: 'vaccination_status',
                    label: 'Vaccination Status',
                    type: 'select',
                    options: [
                        { value: '', label: 'Select status...' },
                        { value: 'Up to date', label: 'Up to date' },
                        { value: 'Overdue', label: 'Overdue' },
                        { value: 'Unknown', label: 'Unknown' }
                    ]
                },
                { name: 'medical_notes', label: 'Medical Notes', type: 'textarea' }
            ];

            this.ui.showForm('Add Pet', fields, async (formData) => {
                try {
                    const petData = {
                        ...formData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('pets', petData);

                    this.ui.showDialog(
                        'Pet Added',
                        `Pet "${formData.name}" has been added.`,
                        'success'
                    );

                    // Refresh the pets list
                    this.loadPersonPets(personId);

                    // Trigger change callback
                    if (this.onPetChanged) {
                        this.onPetChanged();
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add pet: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error adding pet:', error);
            this.ui.showDialog('Error', 'Failed to add pet', 'error');
        }
    }

    /**
     * Edit existing pet
     */
    async editPet(petId) {
        try {
            const pet = await this.data.get('pets', petId);
            if (!pet) {
                this.ui.showDialog('Error', 'Pet not found', 'error');
                return;
            }

            const fields = [
                { name: 'name', label: 'Pet Name', type: 'text', required: true, value: pet.name },
                {
                    name: 'species',
                    label: 'Species',
                    type: 'select',
                    value: pet.species,
                    options: [
                        { value: '', label: 'Select species...' },
                        { value: 'Dog', label: 'Dog' },
                        { value: 'Cat', label: 'Cat' },
                        { value: 'Bird', label: 'Bird' },
                        { value: 'Rabbit', label: 'Rabbit' },
                        { value: 'Other', label: 'Other' }
                    ]
                },
                { name: 'breed', label: 'Breed', type: 'text', value: pet.breed },
                { name: 'age', label: 'Age (years)', type: 'number', value: pet.age },
                { name: 'color', label: 'Color', type: 'text', value: pet.color },
                { name: 'description', label: 'Description', type: 'textarea', value: pet.description },
                { name: 'microchip_number', label: 'Microchip Number', type: 'text', value: pet.microchip_number },
                {
                    name: 'vaccination_status',
                    label: 'Vaccination Status',
                    type: 'select',
                    value: pet.vaccination_status,
                    options: [
                        { value: '', label: 'Select status...' },
                        { value: 'Up to date', label: 'Up to date' },
                        { value: 'Overdue', label: 'Overdue' },
                        { value: 'Unknown', label: 'Unknown' }
                    ]
                },
                { name: 'medical_notes', label: 'Medical Notes', type: 'textarea', value: pet.medical_notes }
            ];

            this.ui.showForm('Edit Pet', fields, async (formData) => {
                try {
                    const updatedData = {
                        ...formData,
                        updated_by: this.auth.getCurrentUser()?.email,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('pets', petId, updatedData);

                    this.ui.showDialog(
                        'Pet Updated',
                        `Pet "${formData.name}" has been updated.`,
                        'success'
                    );

                    // Refresh the pets list
                    this.loadPersonPets(pet.person_id);

                    // Trigger change callback
                    if (this.onPetChanged) {
                        this.onPetChanged();
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update pet: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error editing pet:', error);
            this.ui.showDialog('Error', 'Failed to edit pet', 'error');
        }
    }

    /**
     * Delete pet
     */
    async deletePet(petId) {
        try {
            const pet = await this.data.get('pets', petId);
            if (!pet) {
                this.ui.showDialog('Error', 'Pet not found', 'error');
                return;
            }

            this.ui.showConfirmDialog(
                'Delete Pet',
                `Are you sure you want to delete the pet "${pet.name}"? This action cannot be undone.`,
                async () => {
                    try {
                        await this.data.delete('pets', petId);

                        this.ui.showDialog(
                            'Pet Deleted',
                            `Pet "${pet.name}" has been deleted.`,
                            'success'
                        );

                        // Refresh the pets list
                        this.loadPersonPets(pet.person_id);

                        // Trigger change callback
                        if (this.onPetChanged) {
                            this.onPetChanged();
                        }
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to delete pet: ${error.message}`, 'error');
                    }
                }
            );
        } catch (error) {
            console.error('Error deleting pet:', error);
            this.ui.showDialog('Error', 'Failed to delete pet', 'error');
        }
    }

    /**
     * Load pets for a specific person
     */
    async loadPersonPets(personId) {
        try {
            const pets = await this.data.search('pets', { person_id: personId });
            
            const petsContainer = document.getElementById('person-pets');
            if (petsContainer) {
                if (pets.length === 0) {
                    petsContainer.innerHTML = `
                        <div class="no-pets">
                            <p>No pets registered for this person.</p>
                            <button class="primary-button" data-action="add-pet" data-person-id="${personId}">
                                <span class="button-icon">🐕</span>
                                Add Pet
                            </button>
                        </div>
                    `;
                } else {
                    petsContainer.innerHTML = `
                        <div class="pets-header">
                            <h4>Pets (${pets.length})</h4>
                            <button class="primary-button" data-action="add-pet" data-person-id="${personId}">
                                <span class="button-icon">🐕</span>
                                Add Pet
                            </button>
                        </div>
                        <div class="pets-list">
                            ${pets.map(pet => this.renderPetCard(pet)).join('')}
                        </div>
                    `;
                }

                // Set up event handlers for pet actions
                this.setupPetEventHandlers(petsContainer);
            }

            return pets;
        } catch (error) {
            console.error('Error loading person pets:', error);
            const petsContainer = document.getElementById('person-pets');
            if (petsContainer) {
                petsContainer.innerHTML = '<p class="error">Failed to load pets.</p>';
            }
            return [];
        }
    }

    /**
     * Render pet card HTML
     */
    renderPetCard(pet) {
        return `
            <div class="pet-card" data-pet-id="${pet.id}">
                <div class="pet-header">
                    <h5 class="pet-name">${pet.name}</h5>
                    <div class="pet-actions">
                        <button class="action-btn edit-btn" data-action="edit-pet" data-pet-id="${pet.id}" title="Edit Pet">✏️</button>
                        <button class="action-btn delete-btn" data-action="delete-pet" data-pet-id="${pet.id}" title="Delete Pet">🗑️</button>
                    </div>
                </div>
                <div class="pet-details">
                    ${pet.species ? `<div class="pet-detail"><strong>Species:</strong> ${pet.species}</div>` : ''}
                    ${pet.breed ? `<div class="pet-detail"><strong>Breed:</strong> ${pet.breed}</div>` : ''}
                    ${pet.age ? `<div class="pet-detail"><strong>Age:</strong> ${pet.age} years</div>` : ''}
                    ${pet.color ? `<div class="pet-detail"><strong>Color:</strong> ${pet.color}</div>` : ''}
                    ${pet.microchip_number ? `<div class="pet-detail"><strong>Microchip:</strong> ${pet.microchip_number}</div>` : ''}
                    ${pet.vaccination_status ? `<div class="pet-detail"><strong>Vaccinations:</strong> ${pet.vaccination_status}</div>` : ''}
                    ${pet.description ? `<div class="pet-detail"><strong>Description:</strong> ${pet.description}</div>` : ''}
                    ${pet.medical_notes ? `<div class="pet-detail"><strong>Medical Notes:</strong> ${pet.medical_notes}</div>` : ''}
                </div>
                <div class="pet-meta">
                    <small>Added: ${this.uiUtilities.formatDate(pet.created_at)}</small>
                    ${pet.updated_at ? `<small>Updated: ${this.uiUtilities.formatDate(pet.updated_at)}</small>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Set up event handlers for pet actions
     */
    setupPetEventHandlers(container) {
        // Add pet buttons
        const addPetButtons = container.querySelectorAll('[data-action="add-pet"]');
        addPetButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const personId = btn.getAttribute('data-person-id');
                if (personId) {
                    this.addPet(personId);
                }
            });
        });

        // Edit pet buttons
        const editPetButtons = container.querySelectorAll('[data-action="edit-pet"]');
        editPetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const petId = btn.getAttribute('data-pet-id');
                if (petId) {
                    this.editPet(petId);
                }
            });
        });

        // Delete pet buttons
        const deletePetButtons = container.querySelectorAll('[data-action="delete-pet"]');
        deletePetButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const petId = btn.getAttribute('data-pet-id');
                if (petId) {
                    this.deletePet(petId);
                }
            });
        });
    }

    /**
     * Get pets for a person
     */
    async getPersonPets(personId) {
        try {
            return await this.data.search('pets', { person_id: personId });
        } catch (error) {
            console.error('Error getting person pets:', error);
            return [];
        }
    }

    /**
     * Get pet by ID
     */
    async getPet(petId) {
        try {
            return await this.data.get('pets', petId);
        } catch (error) {
            console.error('Error getting pet:', error);
            return null;
        }
    }

    /**
     * Search pets
     */
    async searchPets(criteria) {
        try {
            return await this.data.search('pets', criteria);
        } catch (error) {
            console.error('Error searching pets:', error);
            return [];
        }
    }

    /**
     * Update pet
     */
    async updatePet(petId, petData) {
        try {
            const updatedData = {
                ...petData,
                updated_by: this.auth.getCurrentUser()?.email,
                updated_at: new Date().toISOString()
            };

            const result = await this.data.update('pets', petId, updatedData);

            // Trigger change callback
            if (this.onPetChanged) {
                this.onPetChanged();
            }

            return result;
        } catch (error) {
            console.error('Error updating pet:', error);
            throw error;
        }
    }

    /**
     * Create new pet
     */
    async createPet(petData) {
        try {
            const fullPetData = {
                ...petData,
                created_by: this.auth.getCurrentUser()?.email,
                created_at: new Date().toISOString()
            };

            const result = await this.data.insert('pets', fullPetData);

            // Trigger change callback
            if (this.onPetChanged) {
                this.onPetChanged();
            }

            return result;
        } catch (error) {
            console.error('Error creating pet:', error);
            throw error;
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners if needed
    }
}