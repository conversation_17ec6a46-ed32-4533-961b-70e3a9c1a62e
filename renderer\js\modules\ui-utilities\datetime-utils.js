/**
 * DateTime Utilities
 * 
 * Handles all date and time formatting operations.
 * Extracted from app.js formatDate(), formatDateTime(), formatTime(), and updateDateTime() methods.
 */

export class DateTimeUtils {
    
    /**
     * Format a date string for display
     * @param {string} dateString - ISO date string
     * @returns {string} Formatted date string
     */
    static formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            return dateString;
        }
    }

    /**
     * Format a datetime string for display
     * @param {string} dateString - ISO datetime string
     * @returns {string} Formatted datetime string
     */
    static formatDateTime(dateString) {
        if (!dateString) return 'Unknown';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return dateString;
        }
    }

    /**
     * Format a time string for display
     * @param {string} timeString - Time string or ISO datetime string
     * @returns {string} Formatted time string (HH:MM format)
     */
    static formatTime(timeString) {
        if (!timeString) return '--:--';
        try {
            // Handle both time strings and full datetime strings
            const time = timeString.includes('T') ?
                new Date(timeString).toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }) :
                timeString.substring(0, 5); // Just take HH:MM
            return time;
        } catch (error) {
            return '--:--';
        }
    }

    /**
     * Update the datetime display in the UI
     */
    static updateDateTime() {
        const datetimeElement = document.getElementById('datetime');
        const now = new Date();
        const formatted = now.toLocaleString('en-CA', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        if (datetimeElement) {
            datetimeElement.textContent = formatted;
        }
    }

    /**
     * Instance methods for non-static usage
     */
    formatDate(dateString) {
        return DateTimeUtils.formatDate(dateString);
    }

    formatDateTime(dateString) {
        return DateTimeUtils.formatDateTime(dateString);
    }

    formatTime(timeString) {
        return DateTimeUtils.formatTime(timeString);
    }

    updateDateTime() {
        return DateTimeUtils.updateDateTime();
    }
}