// Import sub-template modules from their new locations
export * from '../people-management/templates/people-list-templates.js';
export * from '../organizations-management/templates/organization-list-templates.js';
export * from '../vehicle-management/templates/vehicle-list-templates.js';
export * from '../address-management/templates/address-list-templates.js';

export const RecordsTemplates = {
    getMainView() {
        return `
            <style>
                .record-category {
                    margin-bottom: 1rem;
                    border: 1px solid #ff0000;
                    background-color: rgba(255, 0, 0, 0.05);
                    padding: 0.75rem;
                    border-radius: 4px;
                }
                .category-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 0.95rem;
                    font-weight: bold;
                    margin: 0 0 0.75rem 0;
                    padding-bottom: 0.4rem;
                    border-bottom: 1px dashed #ff0000;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .category-grid {
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    gap: 0.5rem;
                }
                .record-category .menu-item {
                    background-color: rgba(0, 0, 0, 0.8);
                    border: 1px solid #ff0000;
                    padding: 0.5rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    min-height: 70px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                }
                .record-category .menu-item:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                    border-color: #ff6666;
                }
                .record-category .menu-icon {
                    font-size: 1.2rem;
                    margin-bottom: 0.25rem;
                    text-align: center;
                }
                .record-category .menu-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.8rem;
                    text-align: center;
                    margin-bottom: 0.2rem;
                }
                .record-category .menu-desc {
                    color: #cccccc;
                    font-family: 'Courier New', monospace;
                    font-size: 0.7rem;
                    text-align: center;
                    line-height: 1.1;
                }
            </style>
            <div class="content-section">
                <h2>RECORDS MANAGEMENT</h2>
                
                <!-- People & Organizations -->
                <div class="record-category">
                    <h3 class="category-title">👥 PEOPLE & ORGANIZATIONS</h3>
                    <div class="category-grid">
                        <div class="menu-item" data-action="manage-people">
                            <div class="menu-icon">👥</div>
                            <div class="menu-title">Manage People</div>
                            <div class="menu-desc">View person records</div>
                        </div>
                        <div class="menu-item" data-action="add-person">
                            <div class="menu-icon">👤</div>
                            <div class="menu-title">Add Person</div>
                            <div class="menu-desc">Create new person</div>
                        </div>
                        <div class="menu-item" data-action="list-organizations">
                            <div class="menu-icon">📋</div>
                            <div class="menu-title">Manage Organizations</div>
                            <div class="menu-desc">View organization directory</div>
                        </div>
                        <div class="menu-item" data-action="add-organization">
                            <div class="menu-icon">🏢</div>
                            <div class="menu-title">Add Organization</div>
                            <div class="menu-desc">Create new organization</div>
                        </div>
                        <div class="menu-item" data-action="search-organizations">
                            <div class="menu-icon">🔍</div>
                            <div class="menu-title">Search Organizations</div>
                            <div class="menu-desc">Find existing organizations</div>
                        </div>
                        <div class="menu-item" data-action="manage-addresses">
                            <div class="menu-icon">📋</div>
                            <div class="menu-title">Manage Addresses</div>
                            <div class="menu-desc">View address directory</div>
                        </div>
                        <div class="menu-item" data-action="add-address">
                            <div class="menu-icon">🏠</div>
                            <div class="menu-title">Add Address</div>
                            <div class="menu-desc">Create new address</div>
                        </div>
                        <div class="menu-item" data-action="search-addresses">
                            <div class="menu-icon">🔍</div>
                            <div class="menu-title">Search Addresses</div>
                            <div class="menu-desc">Find existing addresses</div>
                        </div>
                    </div>
                </div>

                <!-- Vehicles & Property -->
                <div class="record-category">
                    <h3 class="category-title">🚗 VEHICLES & PROPERTY</h3>
                    <div class="category-grid">
                        <div class="menu-item" data-action="manage-bikes">
                            <div class="menu-icon">🚲</div>
                            <div class="menu-title">Bikes</div>
                            <div class="menu-desc">Registration & theft</div>
                        </div>
                        <div class="menu-item" data-action="manage-vehicles">
                            <div class="menu-icon">📋</div>
                            <div class="menu-title">Manage Vehicles</div>
                            <div class="menu-desc">View vehicle directory</div>
                        </div>
                        <div class="menu-item" data-action="add-vehicle">
                            <div class="menu-icon">🚗</div>
                            <div class="menu-title">Add Vehicle</div>
                            <div class="menu-desc">Register new vehicle</div>
                        </div>
                        <div class="menu-item" data-action="search-vehicles">
                            <div class="menu-icon">🔍</div>
                            <div class="menu-title">Search Vehicles</div>
                            <div class="menu-desc">Look up existing</div>
                        </div>
                    </div>
                </div>

                <!-- Incidents & Search -->
                <div class="record-category">
                    <h3 class="category-title">📋 INCIDENTS & SEARCH</h3>
                    <div class="category-grid">
                        <div class="menu-item" data-action="search-incidents">
                            <div class="menu-icon">📋</div>
                            <div class="menu-title">Search Incidents</div>
                            <div class="menu-desc">Historical incidents</div>
                        </div>
                        <div class="menu-item" data-action="search-records">
                            <div class="menu-icon">🔍</div>
                            <div class="menu-title">Search Records</div>
                            <div class="menu-desc">Find existing records</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
};