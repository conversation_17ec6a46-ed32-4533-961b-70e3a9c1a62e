// Vehicle Detail Templates
// Comprehensive vehicle detail screen with tabs for basic info, activities, and actions

export const vehicleDetailTemplates = {
    // Main vehicle detail content (like person detail - not a modal)
    vehicleDetailContent: (vehicle) => {
        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>VEHICLE DETAILS</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="edit-vehicle" data-vehicle-id="${vehicle.id}">
                            <span class="button-icon">✏️</span>
                            Edit Vehicle
                        </button>
                        <button class="secondary-button" data-action="back-to-vehicle-list">
                            <span class="button-icon">←</span>
                            Back to Vehicles
                        </button>
                    </div>
                </div>

                <div class="vehicle-detail-container">
                    <div class="vehicle-detail-header">
                        <div class="vehicle-avatar-large">
                            <div class="avatar-placeholder-large vehicle-avatar">
                                🚗
                            </div>
                        </div>
                        <div class="vehicle-title-info">
                            <h3 class="vehicle-identifier">
                                ${vehicle.plate_number || 'Unknown Plate'} - ${vehicle.province || 'Unknown Province'}
                            </h3>
                            <div class="vehicle-description">
                                ${vehicle.vehicle_year || ''} ${vehicle.vehicle_make || ''} ${vehicle.vehicle_model || ''}
                            </div>
                            <div class="vehicle-id">ID: ${vehicle.id}</div>
                            <div class="vehicle-created">Created: ${new Date(vehicle.created_at).toLocaleString()}</div>
                        </div>
                    </div>

                    ${vehicleDetailTemplates.vehicleDetailTabs(vehicle)}
                </div>
            </div>
        `;
    },

    // Vehicle detail tabs (like person detail tabs)
    vehicleDetailTabs: (vehicle) => {
        return `
            <div class="vehicle-detail-tabs">
                <button class="vehicle-detail-tab active" data-tab="basic">Basic Info</button>
                <button class="vehicle-detail-tab" data-tab="activities">Activities</button>
            </div>

            <div class="vehicle-detail-tab-content">
                <div class="tab-content active" data-tab="basic">
                    ${vehicleDetailTemplates.basicInfoTab(vehicle)}
                </div>
                <div class="tab-content" data-tab="activities">
                    ${vehicleDetailTemplates.activitiesTab(vehicle)}
                </div>
            </div>
        `;
    },

    // Basic info tab content (following person detail style)
    basicInfoTab: (vehicle) => {
        return `
            <div class="detail-sections">
                <div class="detail-section">
                    <h4>Vehicle Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>License Plate:</label>
                            <span>${vehicle.plate_number || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Province/State:</label>
                            <span>${vehicle.province || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Make:</label>
                            <span>${vehicle.vehicle_make || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Model:</label>
                            <span>${vehicle.vehicle_model || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Year:</label>
                            <span>${vehicle.vehicle_year || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Color:</label>
                            <span>${vehicle.vehicle_color || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Type:</label>
                            <span>${vehicle.vehicle_type || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Owner Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Owner Name:</label>
                            <span>${vehicle.owner_name || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Record Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Created:</label>
                            <span>${new Date(vehicle.created_at).toLocaleString()}</span>
                        </div>
                        <div class="detail-item">
                            <label>Created By:</label>
                            <span>${vehicle.created_by || 'Unknown'}</span>
                        </div>
                        ${vehicle.updated_at ? `
                        <div class="detail-item">
                            <label>Last Updated:</label>
                            <span>${new Date(vehicle.updated_at).toLocaleString()}</span>
                        </div>
                        ` : ''}
                        ${vehicle.updated_by ? `
                        <div class="detail-item">
                            <label>Updated By:</label>
                            <span>${vehicle.updated_by}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                ${vehicle.notes ? `
                <div class="detail-section">
                    <h4>Notes</h4>
                    <div class="notes-content">
                        ${vehicle.notes}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    },

    // Activities tab content (following person detail style)
    activitiesTab: (vehicle) => {
        return `
            <div class="detail-sections">
                <div class="detail-section">
                    <div class="section-header">
                        <h4>Vehicle Activities</h4>
                        <button class="primary-button" data-action="add-vehicle-activity" data-vehicle-id="${vehicle.id}">
                            <span class="button-icon">+</span>
                            Add Activity
                        </button>
                    </div>
                    <div class="activities-list" id="activities-list">
                        <div class="loading">Loading activities...</div>
                    </div>
                </div>
            </div>
        `;
    },

    // Activity item template
    activityItem: (activity) => {
        return `
            <div class="activity-item" data-activity-id="${activity.id}">
                <div class="activity-header">
                    <div class="activity-title">
                        <strong>${activity.title || activity.activity_type}</strong>
                        <span class="activity-type-badge">${activity.activity_type}</span>
                    </div>
                    <div class="activity-date">
                        ${activity.activity_date ? new Date(activity.activity_date).toLocaleDateString() : 'No date'}
                    </div>
                </div>
                <div class="activity-content">
                    ${activity.description ? `<p class="activity-description">${activity.description}</p>` : ''}
                    ${activity.location ? `<p class="activity-location"><strong>Location:</strong> ${activity.location}</p>` : ''}
                    ${activity.staff_member ? `<p class="activity-staff"><strong>Staff:</strong> ${activity.staff_member}</p>` : ''}
                </div>
                <div class="activity-meta">
                    <span class="activity-created">Created: ${new Date(activity.created_at).toLocaleString()}</span>
                    ${activity.created_by ? `<span class="activity-created-by">by ${activity.created_by}</span>` : ''}
                </div>
            </div>
        `;
    },

    // No activities message
    noActivitiesMessage: () => {
        return `
            <div class="no-activities">
                <div class="no-activities-icon">📝</div>
                <div class="no-activities-text">No activities recorded yet.</div>
                <div class="no-activities-subtext">Click "Add Activity" to record the first activity for this vehicle.</div>
            </div>
        `;
    }
};
