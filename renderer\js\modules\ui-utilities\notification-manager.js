/**
 * Notification Manager
 * 
 * Manages all notification and toast systems for user feedback.
 * Extracted from app.js showNotification() and showToast() methods.
 */

export class NotificationManager {
    
    /**
     * Show a notification popup
     * @param {string} message - Message to display
     * @param {string} type - Notification type (info, success, error, warning)
     */
    static showNotification(message, type = 'info') {
        // Create a simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: var(--bg-color);
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    /**
     * Show a toast message (simpler notification)
     * @param {string} message - Message to display
     * @param {string} type - Toast type (info, success, error, warning)
     */
    static showToast(message, type = 'info') {
        // Remove existing toast
        const existingToast = document.querySelector('.dispatch-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Create new toast
        const toast = document.createElement('div');
        toast.className = `dispatch-toast ${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }

    /**
     * Instance methods for non-static usage
     */
    showNotification(message, type = 'info') {
        return NotificationManager.showNotification(message, type);
    }

    showToast(message, type = 'info') {
        return NotificationManager.showToast(message, type);
    }
}