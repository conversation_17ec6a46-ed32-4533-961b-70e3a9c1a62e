// Court Appearance Event Modal
// Handles adding court appearance events with outcome tracking

import { addEventTemplates } from '../../../templates/add-event-templates.js';

export class CourtAppearanceModal {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.episodeId = null;
    }
    
    async show(episodeId) {
        this.episodeId = episodeId;
        
        try {
            // Create modal
            this.modal = this.justice.ui.createModal({
                id: 'add-court-appearance-modal',
                title: 'Add Court Appearance',
                size: 'medium'
            });
            
            // Render form
            this.render();
            
            // Show modal
            this.justice.ui.showModal(this.modal);
            
            // Setup event handlers
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to show court appearance modal:', error);
            this.justice.ui.showDialog('Error', `Failed to open court appearance form: ${error.message}`, 'error');
        }
    }
    
    render() {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = addEventTemplates.courtAppearanceForm({
            episodeId: this.episodeId,
            defaultDateTime: new Date().toISOString().slice(0, 16)
        });
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#court-appearance-form');
        const outcomeSelect = this.modal.querySelector('#appearance-outcome');
        const nextDateSection = this.modal.querySelector('#next-date-section');
        
        // Show/hide next date based on outcome
        if (outcomeSelect && nextDateSection) {
            outcomeSelect.addEventListener('change', (e) => {
                const outcome = e.target.value;
                const showNextDate = ['ADJOURNED', 'REMANDED', 'CONTINUED'].includes(outcome);
                nextDateSection.style.display = showNextDate ? 'block' : 'none';
            });
        }
        
        // Save button
        const saveBtn = this.modal.querySelector('#save-court-appearance-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveCourtAppearance(form);
            });
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-court-appearance-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }
    
    async saveCourtAppearance(form) {
        try {
            const formData = new FormData(form);
            
            const eventData = {
                event_dt: formData.get('appearance_dt'),
                payload: {
                    court_name: formData.get('court_name'),
                    address: formData.get('court_address'),
                    appearance_type: formData.get('appearance_type'),
                    outcome: formData.get('outcome'),
                    next_date: formData.get('next_date') || null,
                    notes: formData.get('notes')
                }
            };
            
            // Validate required fields
            if (!eventData.event_dt) {
                this.justice.ui.showDialog('Error', 'Appearance date/time is required.', 'error');
                return;
            }
            
            if (!eventData.payload.court_name) {
                this.justice.ui.showDialog('Error', 'Court name is required.', 'error');
                return;
            }
            
            if (!eventData.payload.appearance_type) {
                this.justice.ui.showDialog('Error', 'Appearance type is required.', 'error');
                return;
            }
            
            // Show loading state
            const saveBtn = this.modal.querySelector('#save-court-appearance-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
            
            // Create the event
            const newEvent = await this.justice.api.addEvent(
                this.episodeId,
                'COURT_APPEARANCE',
                eventData.event_dt,
                eventData.payload
            );
            
            // Update state
            this.justice.state.addEvent(newEvent);
            
            // Close modal
            this.close();
            
            // Show success
            this.justice.ui.showDialog('Success', 'Court appearance added successfully!', 'success');
            
            // Refresh views
            await this.refreshViews();
            
        } catch (error) {
            console.error('Failed to save court appearance:', error);
            this.justice.ui.showDialog('Error', `Failed to save court appearance: ${error.message}`, 'error');
            
            // Reset button
            const saveBtn = this.modal.querySelector('#save-court-appearance-btn');
            if (saveBtn) {
                saveBtn.textContent = 'Save Court Appearance';
                saveBtn.disabled = false;
            }
        }
    }
    
    async refreshViews() {
        // Refresh timeline if visible
        if (this.justice.timelineView && this.justice.timelineView.container) {
            await this.justice.timelineView.refresh();
        }
        
        // Refresh status ribbon if visible
        if (this.justice.statusRibbon && this.justice.statusRibbon.container) {
            await this.justice.statusRibbon.refresh();
        }
    }
    
    close() {
        if (this.modal) {
            this.justice.ui.closeModal(this.modal.id);
            this.modal = null;
        }
        this.episodeId = null;
    }
}
