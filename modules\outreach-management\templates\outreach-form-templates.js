// Outreach Transaction Form Templates
// Extracted from commands.js for better maintainability

export const outreachFormTemplates = {
    // Main outreach transaction screen template
    createOutreachTransactionScreen: (location = { address: '', coordinates: null }) => `
        <div class="outreach-transaction-screen">
            <div class="outreach-header">
                <h2>📦 Outreach Transaction</h2>
                <button class="close-screen-btn" onclick="app.loadTabContent('outreach')">← Back to Outreach</button>
            </div>
            <div class="outreach-content">
                <!-- Person Selection Section -->
                <div class="outreach-section person-section">
                    <div class="section-header">
                        <h3>👤 Select Person</h3>
                    </div>
                    <div class="person-search-container">
                        <div class="search-input-group">
                            <input type="text" id="person-search" placeholder="Search by name, phone, or ID..." class="person-search-input">
                            <button class="search-btn" onclick="supplyDistribution.searchPerson()">🔍</button>
                            <button class="add-person-btn" onclick="supplyDistribution.showAddPersonModal()">+ New Person</button>
                        </div>
                        <div class="person-results" id="person-results">
                            <div class="no-selection">Search for a person or create a new one</div>
                        </div>
                        <div class="selected-person" id="selected-person" style="display: none;">
                            <div class="person-info"></div>
                            <button class="change-person-btn" onclick="outreachTransaction.clearPersonSelection()">Change Person</button>
                        </div>
                    </div>
                </div>
                <!-- Items Section -->
                <div class="outreach-section items-section">
                    <div class="section-header">
                        <h3>📋 Items to Provide</h3>
                        <button class="add-item-btn" onclick="outreachTransaction.showItemCatalog()" disabled id="add-item-btn">+ Add Item</button>
                    </div>
                    <div class="items-list" id="items-list">
                        <div class="no-items">Select a person first, then add items to provide</div>
                    </div>
                    <div class="items-total" id="items-total" style="display: none;">
                        <div class="total-items">Total Items: <span id="total-item-count">0</span></div>
                    </div>
                </div>
                <!-- Details Section -->
                <div class="outreach-section details-section">
                    <div class="section-header">
                        <h3>📝 Transaction Details</h3>
                    </div>
                    <div class="details-form">
                        <div class="form-group">
                            <label for="transaction-location">Location:</label>
                            <input type="text" id="transaction-location" value="${location.address || ''}" placeholder="Transaction location...">
                            <button class="location-btn" onclick="outreachTransaction.getCurrentLocation()" title="Get current location">📍</button>
                        </div>
                        <div class="form-group">
                            <label for="transaction-notes">Notes:</label>
                            <textarea id="transaction-notes" placeholder="Additional notes about this transaction..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Action Buttons -->
            <div class="outreach-actions">
                <button class="secondary-button" onclick="app.loadTabContent('outreach')">Cancel</button>
                <button class="primary-button" onclick="outreachTransaction.completeTransaction()" disabled id="complete-btn">Complete Transaction</button>
                <button class="primary-button" onclick="outreachTransaction.completeAndNew()" disabled id="complete-new-btn">Complete & New</button>
            </div>
        </div>
    `,

    // Person search result template (individual result item)
    createPersonResultItem: (person) => `
        <div class="person-result" onclick="outreachTransaction.selectPerson('${person.id}')">
            <div class="person-name">${person.first_name} ${person.last_name}</div>
            <div class="person-details">
                ${person.phone ? `📞 ${person.phone}` : ''}
                ${person.email ? `📧 ${person.email}` : ''}
            </div>
            <div class="person-id">ID: ${String(person.id).substring(0, 8)}...</div>
        </div>
    `,

    // Selected person display template
    createSelectedPersonInfo: (person) => `
        <div class="selected-person-info">
            <h4>${person.first_name} ${person.last_name}</h4>
            <div class="person-contact">
                ${person.phone ? `📞 ${person.phone}` : ''}
                ${person.email ? `📧 ${person.email}` : ''}
            </div>
            <div class="person-id">ID: ${String(person.id).substring(0, 8)}...</div>
        </div>
    `,

    // No results template for person search
    createNoPersonResults: (query) => `
        <div class="no-results">
            <p>No people found matching "${query}"</p>
            <button class="primary-button" onclick="outreachTransaction.showAddPersonModal('${query}')">
                Create New Person
            </button>
        </div>
    `,

    // Loading state template for person search
    createPersonSearchLoading: () => `
        <div class="loading">Searching...</div>
    `,

    // Error state template for person search
    createPersonSearchError: () => `
        <div class="error">Error searching people. Please try again.</div>
    `,

    // No selection state for person search
    createNoPersonSelection: () => `
        <div class="no-selection">Enter at least 2 characters to search</div>
    `,

    // Item catalog category template
    createItemCatalogCategory: (category, categoryItems) => {
        const itemsHTML = categoryItems.map(item => `
            <div class="catalog-item ${item.current_stock <= 0 ? 'out-of-stock' : ''}"
                 onclick="outreachTransaction.selectCatalogItem('${item.id}')">
                <div class="item-name">${item.name}</div>
                <div class="item-description">${item.description || ''}</div>
                <div class="item-stock">Stock: ${item.current_stock} ${item.unit_type}</div>
                ${item.current_stock <= 0 ? '<div class="stock-warning">Out of Stock</div>' : ''}
            </div>
        `).join('');

        return `
            <div class="catalog-category">
                <h4 class="category-title">${category.replace('_', ' ').toUpperCase()}</h4>
                <div class="category-items">
                    ${itemsHTML}
                </div>
            </div>
        `;
    },

    // Item catalog modal template
    createItemCatalogModal: (categoriesHTML) => `
        <div class="modal-dialog large-modal">
            <div class="modal-header">
                <h3>📦 Select Item to Add</h3>
                <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
            </div>
            <div class="modal-body">
                <div class="item-catalog">
                    ${categoriesHTML}
                </div>
            </div>
            <div class="modal-footer">
                <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
            </div>
        </div>
    `,

    // Quantity input modal template
    createQuantityInputModal: (item) => `
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>📦 Add ${item.name}</h3>
                <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
            </div>
            <div class="modal-body">
                <div class="quantity-input-form">
                    <div class="item-info">
                        <div class="item-name">${item.name}</div>
                        <div class="item-description">${item.description || ''}</div>
                        <div class="item-stock">Available: ${item.current_stock} ${item.unit_type}</div>
                    </div>
                    <div class="form-group">
                        <label for="item-quantity">Quantity to distribute:</label>
                        <input type="number" id="item-quantity" min="1" max="${item.current_stock}" value="1" class="quantity-input">
                        <span class="unit-label">${item.unit_type}</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                <button class="primary-button" onclick="outreachTransaction.addItemToTransaction('${item.id}')">Add to Transaction</button>
            </div>
        </div>
    `,

    // Transaction item template (for items list)
    createTransactionItem: (item, index) => `
        <div class="transaction-item">
            <div class="item-details">
                <div class="item-name">${item.item_name}</div>
                <div class="item-category">${item.category.replace('_', ' ')}</div>
            </div>
            <div class="item-quantity">
                <span class="quantity">${item.quantity}</span>
                <span class="unit">${item.unit_type}</span>
            </div>
            <div class="item-actions">
                <button class="edit-quantity-btn" onclick="outreachTransaction.editItemQuantity(${index})" title="Edit quantity">✏️</button>
                <button class="remove-item-btn" onclick="outreachTransaction.removeItem(${index})" title="Remove item">🗑️</button>
            </div>
        </div>
    `,

    // No items selected state
    createNoItemsSelected: () => `
        <div class="no-items">No items added yet</div>
    `
};