/* Justice Mo<PERSON>le Styles */

/* Main Justice Container */
.justice-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.justice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e0e0e0;
}

.justice-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.5em;
}

.justice-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Status Ribbon */
.justice-status-ribbon {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 20px;
}

.justice-status-ribbon.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.status-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.status-label {
    font-size: 0.85em;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-value {
    font-size: 1em;
    font-weight: 500;
    color: #333;
}

.status-section.warning .status-value {
    color: #dc3545;
    font-weight: 600;
}

.status-actions {
    margin-left: auto;
}

.refresh-btn {
    background: none;
    border: none;
    font-size: 1.2em;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.refresh-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

/* Episode Cards */
.episodes-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.episode-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background: white;
    transition: box-shadow 0.2s;
}

.episode-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.episode-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.episode-title {
    display: flex;
    gap: 10px;
    align-items: center;
}

.episode-origin {
    font-weight: 600;
    color: #333;
}

.episode-date {
    color: #666;
    font-size: 0.9em;
}

.episode-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
}

.status-active { background: #d4edda; color: #155724; }
.status-in_custody { background: #f8d7da; color: #721c24; }
.status-on_bail { background: #fff3cd; color: #856404; }
.status-released { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #e2e3e5; color: #383d41; }

.episode-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 10px;
}

.episode-agency, .episode-jurisdiction {
    font-size: 0.9em;
    color: #666;
}

.episode-actions {
    display: flex;
    gap: 10px;
}

.no-episodes {
    text-align: center;
    padding: 40px;
    color: #666;
}

/* Timeline Styles */
.justice-timeline {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.add-event-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.add-event-section h5 {
    margin: 0 0 10px 0;
    color: #333;
}

.add-event-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.event-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.9em;
}

.event-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.event-icon {
    font-size: 1.1em;
}

/* Timeline Events */
.timeline-events {
    position: relative;
}

.timeline-events::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e0e0e0;
}

.timeline-event {
    position: relative;
    margin-bottom: 20px;
    padding-left: 50px;
}

.timeline-event::before {
    content: '';
    position: absolute;
    left: 14px;
    top: 8px;
    width: 12px;
    height: 12px;
    background: #007bff;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 2px #007bff;
}

.event-header {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.event-header:hover {
    background: #e9ecef;
}

.event-info {
    flex: 1;
}

.event-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.event-datetime {
    font-size: 0.9em;
    color: #666;
}

.event-toggle {
    font-size: 0.8em;
    color: #666;
    transition: transform 0.2s;
}

.event-details {
    margin-top: 10px;
    padding: 15px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
}

.event-details-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-row {
    display: flex;
    gap: 10px;
}

.detail-label {
    font-weight: 600;
    color: #666;
    min-width: 120px;
}

.detail-value {
    color: #333;
    flex: 1;
}

.conditions-list {
    margin: 0;
    padding-left: 20px;
}

.conditions-list li {
    margin-bottom: 4px;
}

/* Wizard Styles */
.episode-wizard {
    max-width: 600px;
}

.wizard-step {
    margin-bottom: 20px;
}

.wizard-step h4 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 8px;
}

.form-row {
    margin-bottom: 15px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-row input,
.form-row select,
.form-row textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
}

.form-row input:focus,
.form-row select:focus,
.form-row textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.wizard-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

/* Charges List */
.charges-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.charge-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f8f9fa;
}

.charge-details {
    flex: 1;
}

.charge-code {
    font-weight: 600;
    color: #007bff;
    font-size: 0.9em;
}

.charge-label {
    color: #333;
    margin: 2px 0;
}

.charge-severity {
    font-size: 0.8em;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
}

.severity-summary { background: #d4edda; color: #155724; }
.severity-indictable { background: #f8d7da; color: #721c24; }
.severity-hybrid { background: #fff3cd; color: #856404; }
.severity-other { background: #e2e3e5; color: #383d41; }

.remove-charge-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 1.1em;
    line-height: 1;
}

.remove-charge-btn:hover {
    background: #c82333;
}

.no-charges {
    text-align: center;
    padding: 20px;
    color: #666;
    font-style: italic;
}

/* Error States */
.justice-error {
    text-align: center;
    padding: 40px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    color: #721c24;
}

.justice-error h4 {
    margin: 0 0 10px 0;
    color: #721c24;
}

.timeline-error,
.event-detail-error {
    text-align: center;
    padding: 20px;
    color: #dc3545;
    background: #f8d7da;
    border-radius: 4px;
    margin: 10px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .justice-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .justice-status-ribbon {
        flex-direction: column;
        gap: 10px;
    }
    
    .episode-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .add-event-buttons {
        flex-direction: column;
    }
    
    .event-btn {
        justify-content: center;
    }
    
    .timeline-event {
        padding-left: 30px;
    }
    
    .timeline-events::before {
        left: 10px;
    }
    
    .timeline-event::before {
        left: 4px;
    }
}
