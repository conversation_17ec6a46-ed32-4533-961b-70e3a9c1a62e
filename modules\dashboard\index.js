import { FeatureModuleInterface } from '../shared/module-interface.js';
import { Logger } from '../shared/logger.js';
import { DashboardManager } from './js/dashboard-manager.js';

/**
 * Dashboard Module - Main dashboard functionality and widget management
 * Implements FeatureModuleInterface for standardized module lifecycle
 */
export class DashboardModule extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, weatherService) {
        super('Dashboard', '1.0.0', [], ['dashboard']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.weather = weatherService;
        this.logger = Logger.forModule('Dashboard');
        
        // Initialize dashboard manager
        this.dashboardManager = new DashboardManager(dataManager, authManager, uiManager, weatherService);
        
        this.logger.info('Dashboard Module initialized');
    }

    // REQUIRED: Initialize module resources
    async initialize() {
        this.logger.info('Initializing Dashboard Module');
        try {
            // Dashboard initialization is handled when content is loaded
            this.logger.info('Dashboard Module initialization complete');
        } catch (error) {
            this.logger.error('Dashboard Module initialization failed', error);
            throw error;
        }
    }

    // REQUIRED: Cleanup module resources
    async cleanup() {
        this.logger.info('Cleaning up Dashboard Module');
        // Dashboard doesn't have persistent resources to clean up
    }

    // REQUIRED: Provide commands for command system
    getCommands(commandManager) {
        // Dashboard doesn't provide commands currently
        return new Map();
    }

    // REQUIRED for FeatureModuleInterface: Provide workflows
    getWorkflows() {
        return {
            loadDashboard: () => this.loadDashboardWorkflow(),
            initializeDashboard: () => this.initializeDashboardWorkflow(),
            refreshDashboardData: () => this.refreshDashboardDataWorkflow()
        };
    }

    // REQUIRED for FeatureModuleInterface: Provide templates
    getTemplates() {
        return {
            dashboard: () => this.dashboardManager.loadDashboardContent()
        };
    }

    // REQUIRED for FeatureModuleInterface: Provide API endpoints
    getApiEndpoints() {
        return [];
    }

    // REQUIRED for FeatureModuleInterface: Provide statistics
    getStatistics() {
        return {
            name: 'Dashboard',
            loaded: true,
            components: ['weather', 'incidents', 'stats', 'alerts']
        };
    }

    // Workflow Methods
    async loadDashboardWorkflow() {
        return await this.dashboardManager.loadDashboardContent();
    }

    async initializeDashboardWorkflow() {
        return await this.dashboardManager.initializeDashboard();
    }

    async refreshDashboardDataWorkflow() {
        await Promise.all([
            this.dashboardManager.loadDashboardIncidents(),
            this.dashboardManager.loadDashboardStats(),
            this.dashboardManager.initializeWeatherWidget()
        ]);
    }

    // Public API Methods
    async loadDashboardContent() {
        return await this.dashboardManager.loadDashboardContent();
    }

    async initializeDashboard() {
        return await this.dashboardManager.initializeDashboard();
    }

    async loadDashboardIncidents() {
        return await this.dashboardManager.loadDashboardIncidents();
    }

    async loadDashboardStats() {
        return await this.dashboardManager.loadDashboardStats();
    }

    async initializeWeatherWidget() {
        return await this.dashboardManager.initializeWeatherWidget();
    }

    createWeatherDisplay(formattedData, rawData) {
        return this.dashboardManager.createWeatherDisplay(formattedData, rawData);
    }
}