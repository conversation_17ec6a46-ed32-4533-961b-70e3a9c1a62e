/**
 * People List Manager
 * Handles people list display and management
 */

import { BaseManager } from '../../shared/base-manager.js';
import { peopleManagementTemplates } from '../templates/people-management-templates.js';
import { peopleListTemplates } from '../templates/people-list-templates.js';

export class PeopleListManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    /**
     * Load people data
     */
    async loadPeopleData() {
        try {
            const people = await this.data.search('people', {});
            console.log(`📊 Found ${people.length} people records for display:`, people.map(p => `${p.first_name} ${p.last_name} (${p.id})`));
            return people || [];
        } catch (error) {
            console.error('Error loading people data:', error);
            throw error;
        }
    }

    /**
     * Load people management content (main screen)
     */
    async loadPeopleManagementContent() {
        console.log('🔍 Loading people management content...');
        const people = await this.loadPeopleData();

        // Set up event handlers after content is loaded
        setTimeout(() => {
            this.setupPeopleListEventHandlers(people);
        }, 100);

        // Use the standardized template
        return peopleManagementTemplates.peopleListView(people);
    }

    /**
     * Render people list HTML (legacy method - kept for compatibility)
     */
    renderPeopleList(people) {
        if (!people || people.length === 0) {
            return '<p class="no-results">No people found.</p>';
        }

        return people.map(person => `
            <div class="person-card" data-person-id="${person.id}" data-action="view-person-detail">
                <div class="person-header">
                    <h3 class="person-name">${person.first_name} ${person.last_name}</h3>
                    <div class="person-actions">
                        <button class="action-btn edit-btn" data-action="edit-person" data-person-id="${person.id}" title="Edit Person">✏️</button>
                    </div>
                </div>
                <div class="person-details">
                    ${person.email ? `<div class="detail-item"><span class="label">📧</span> ${person.email}</div>` : ''}
                    ${person.phone ? `<div class="detail-item"><span class="label">📱</span> ${person.phone}</div>` : ''}
                    ${person.housing_status ? `<div class="detail-item"><span class="label">🏠</span> ${person.housing_status}</div>` : ''}
                    ${person.date_of_birth ? `<div class="detail-item"><span class="label">🎂</span> ${new Date(person.date_of_birth).toLocaleDateString()}</div>` : ''}
                </div>
                <div class="person-meta">
                    <small>Created: ${new Date(person.created_at).toLocaleDateString()}</small>
                </div>
            </div>
        `).join('');
    }

    /**
     * Set up event handlers for people list
     */
    setupPeopleListEventHandlers(people) {
        // Search functionality
        const searchInput = document.getElementById('people-search');
        const searchBtn = document.getElementById('search-people-btn');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterPeople(people, e.target.value);
            });
        }

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                const query = searchInput ? searchInput.value : '';
                this.filterPeople(people, query);
            });
        }

        // Set up person card click handlers
        this.setupPersonCardHandlers();
    }

    /**
     * Set up person list item click handlers (simplified - relies on global event delegation)
     */
    setupPersonCardHandlers() {
        // The app's global event delegation system handles data-action attributes
        // We only need to handle card clicks for the legacy card layout
        const personCards = document.querySelectorAll('.person-card');
        personCards.forEach(card => {
            // Handle card click for detail view (only for legacy cards)
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on action buttons
                if (e.target.closest('.action-btn, .secondary-button, .action-button, [data-action]')) {
                    return;
                }

                const personId = card.getAttribute('data-person-id');
                if (personId) {
                    this.showPersonDetail(personId);
                }
            });
        });

        // Note: View and Edit buttons with data-action attributes are handled by app's global event delegation
        console.log('✅ Person list event handlers set up - relying on global event delegation for data-action buttons');
    }

    /**
     * Filter people based on search term
     */
    filterPeople(people, searchTerm) {
        const filteredPeople = people.filter(person => {
            const searchLower = searchTerm.toLowerCase();
            return (
                (person.first_name && person.first_name.toLowerCase().includes(searchLower)) ||
                (person.last_name && person.last_name.toLowerCase().includes(searchLower)) ||
                (person.email && person.email.toLowerCase().includes(searchLower)) ||
                (person.phone && person.phone.toLowerCase().includes(searchLower)) ||
                (person.housing_status && person.housing_status.toLowerCase().includes(searchLower))
            );
        });

        this.updatePeopleList(filteredPeople);
    }

    /**
     * Update people list with new data
     */
    updatePeopleList(people) {
        const listContainer = document.getElementById('people-list');
        const countContainer = document.getElementById('people-count');

        if (listContainer) {
            // Use the standardized template pattern
            if (people && people.length > 0) {
                listContainer.innerHTML = `
                    <div class="people-list-header">
                        <div>Name</div>
                        <div>Contact Info</div>
                        <div>Housing Status</div>
                        <div>Created</div>
                        <div>Actions</div>
                    </div>
                    ${people.map(person => peopleManagementTemplates.peopleListItem(person)).join('')}
                `;
            } else {
                listContainer.innerHTML = '<div class="no-people">NO PEOPLE FOUND<br><br>Click "Add Person" to create the first entry.</div>';
            }
            this.setupPersonCardHandlers();
        }

        if (countContainer) {
            countContainer.textContent = people.length;
        }
    }

    /**
     * Refresh people list
     */
    async refreshPeopleList() {
        try {
            console.log(`🔄 refreshPeopleList called`);
            const people = await this.loadPeopleData();
            this.updatePeopleList(people);
            return people;
        } catch (error) {
            console.error('Error refreshing people list:', error);
            throw error;
        }
    }

    /**
     * Show person detail (delegates to app's command system)
     */
    async showPersonDetail(personId) {
        try {
            // Use the app's command system instead of custom events
            if (window.app && window.app.commands) {
                await window.app.commands.executeCommand('view-person-detail', { personId: personId });
            } else {
                console.warn('App command system not available, falling back to direct call');
                if (window.app && window.app.peopleManagement) {
                    await window.app.peopleManagement.viewPersonDetail(personId);
                }
            }
        } catch (error) {
            console.error('Error showing person detail:', error);
            this.ui.showDialog('Error', `Failed to show person details: ${error.message}`, 'error');
        }
    }

    /**
     * Edit person (delegates to app's command system)
     */
    async editPerson(personId) {
        try {
            // Use the app's command system instead of custom events
            if (window.app && window.app.commands) {
                await window.app.commands.executeCommand('edit-person', { personId: personId });
            } else {
                console.warn('App command system not available, falling back to direct call');
                if (window.app && window.app.peopleManagement) {
                    await window.app.peopleManagement.editPerson(personId);
                }
            }
        } catch (error) {
            console.error('Error editing person:', error);
            this.ui.showDialog('Error', `Failed to edit person: ${error.message}`, 'error');
        }
    }

    /**
     * Show people list view (modal version)
     */
    async showPeopleListView(people) {
        try {
            // Create modal overlay if needed
            const listModal = document.createElement('div');
            listModal.className = 'modal-overlay people-list-overlay';
            listModal.innerHTML = peopleListTemplates.peopleListModal(people);

            document.body.appendChild(listModal);

            // Set up event handlers
            this.setupListModalHandlers(listModal, people);

        } catch (error) {
            console.error('Error showing people list view:', error);
            throw error;
        }
    }

    /**
     * Set up event handlers for list modal
     */
    setupListModalHandlers(modal, people) {
        // Back button
        const backBtn = modal.querySelector('#back-to-records-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        }

        // Filter functionality
        const filterInput = modal.querySelector('#people-filter');
        if (filterInput) {
            filterInput.addEventListener('input', () => {
                this.filterPeopleInModal(modal, people, filterInput.value);
            });
        }

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Filter people in modal
     */
    filterPeopleInModal(modal, people, searchTerm) {
        const filteredPeople = people.filter(person => {
            const searchLower = searchTerm.toLowerCase();
            return (
                (person.first_name && person.first_name.toLowerCase().includes(searchLower)) ||
                (person.last_name && person.last_name.toLowerCase().includes(searchLower)) ||
                (person.email && person.email.toLowerCase().includes(searchLower)) ||
                (person.phone && person.phone.toLowerCase().includes(searchLower))
            );
        });

        const gridContainer = modal.querySelector('.people-list-grid');
        if (gridContainer) {
            gridContainer.innerHTML = filteredPeople.map(person => 
                peopleListTemplates.personCard(person)
            ).join('');
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners or intervals if needed
    }
}