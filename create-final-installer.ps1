# S.T.E.V.I Retro Final Installer Creator
# Creates a professional installer from the packaged application

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Final Installer Creator" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if packaged app exists
$packagedPath = "dist\S.T.E.V.I Retro-win32-x64"
if (-not (Test-Path $packagedPath)) {
    Write-Host "ERROR: Packaged application not found at $packagedPath" -ForegroundColor Red
    Write-Host "Please run electron-packager first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found packaged application at: $packagedPath" -ForegroundColor Green
Write-Host ""

# Get version from package.json
$packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
$version = $packageJson.version

Write-Host "Creating installer for version: $version" -ForegroundColor Yellow
Write-Host ""

# Create portable zip version
Write-Host "Creating portable ZIP version..." -ForegroundColor Yellow
$zipPath = "dist\S.T.E.V.I-Retro-Portable-v$version.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

try {
    Compress-Archive -Path "$packagedPath\*" -DestinationPath $zipPath -CompressionLevel Optimal
    $zipSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
    Write-Host "SUCCESS: Portable ZIP created: $zipPath ($zipSize MB)" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to create ZIP: $($_.Exception.Message)" -ForegroundColor Red
}

# Create self-extracting installer using PowerShell
Write-Host ""
Write-Host "Creating self-extracting installer..." -ForegroundColor Yellow

$installerScript = @"
# S.T.E.V.I Retro Self-Extracting Installer
# This script extracts and installs S.T.E.V.I Retro

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Installer v$version" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
`$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not `$isAdmin) {
    Write-Host "This installer requires administrator privileges." -ForegroundColor Yellow
    Write-Host "Please run as administrator or choose a user directory." -ForegroundColor Yellow
    Write-Host ""
}

# Choose installation directory
`$defaultPath = if (`$isAdmin) { "`$env:ProgramFiles\S.T.E.V.I Retro" } else { "`$env:LOCALAPPDATA\S.T.E.V.I Retro" }
Write-Host "Default installation path: `$defaultPath" -ForegroundColor Yellow
`$installPath = Read-Host "Enter installation path (or press Enter for default)"

if ([string]::IsNullOrWhiteSpace(`$installPath)) {
    `$installPath = `$defaultPath
}

Write-Host ""
Write-Host "Installing to: `$installPath" -ForegroundColor Cyan

# Create installation directory
try {
    if (Test-Path `$installPath) {
        Write-Host "Directory exists. Removing old installation..." -ForegroundColor Yellow
        Remove-Item -Path `$installPath -Recurse -Force
    }
    
    New-Item -Path `$installPath -ItemType Directory -Force | Out-Null
    Write-Host "✓ Installation directory created" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to create installation directory: `$(`$_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Extract embedded ZIP (this would be replaced with actual extraction code)
Write-Host "Extracting application files..." -ForegroundColor Yellow

# For now, copy from the packaged directory
`$sourcePath = "`$PSScriptRoot\S.T.E.V.I Retro-win32-x64"
if (Test-Path `$sourcePath) {
    try {
        Copy-Item -Path "`$sourcePath\*" -Destination `$installPath -Recurse -Force
        Write-Host "✓ Application files extracted" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to extract files: `$(`$_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "✗ Source files not found at `$sourcePath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create desktop shortcut
Write-Host "Creating desktop shortcut..." -ForegroundColor Yellow
try {
    `$WshShell = New-Object -comObject WScript.Shell
    `$Shortcut = `$WshShell.CreateShortcut("`$env:USERPROFILE\Desktop\S.T.E.V.I Retro.lnk")
    `$Shortcut.TargetPath = "`$installPath\S.T.E.V.I Retro.exe"
    `$Shortcut.WorkingDirectory = `$installPath
    `$Shortcut.Description = "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals"
    `$Shortcut.Save()
    Write-Host "✓ Desktop shortcut created" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not create desktop shortcut: `$(`$_.Exception.Message)" -ForegroundColor Yellow
}

# Create Start Menu shortcut
Write-Host "Creating Start Menu shortcut..." -ForegroundColor Yellow
try {
    `$startMenuPath = "`$env:APPDATA\Microsoft\Windows\Start Menu\Programs"
    `$Shortcut = `$WshShell.CreateShortcut("`$startMenuPath\S.T.E.V.I Retro.lnk")
    `$Shortcut.TargetPath = "`$installPath\S.T.E.V.I Retro.exe"
    `$Shortcut.WorkingDirectory = `$installPath
    `$Shortcut.Description = "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals"
    `$Shortcut.Save()
    Write-Host "✓ Start Menu shortcut created" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not create Start Menu shortcut: `$(`$_.Exception.Message)" -ForegroundColor Yellow
}

# Register uninstaller
Write-Host "Registering uninstaller..." -ForegroundColor Yellow
try {
    `$uninstallKey = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\S.T.E.V.I Retro"
    New-Item -Path `$uninstallKey -Force | Out-Null
    Set-ItemProperty -Path `$uninstallKey -Name "DisplayName" -Value "S.T.E.V.I Retro"
    Set-ItemProperty -Path `$uninstallKey -Name "DisplayVersion" -Value "$version"
    Set-ItemProperty -Path `$uninstallKey -Name "Publisher" -Value "I.H.A.R.C."
    Set-ItemProperty -Path `$uninstallKey -Name "InstallLocation" -Value `$installPath
    Set-ItemProperty -Path `$uninstallKey -Name "UninstallString" -Value "powershell.exe -ExecutionPolicy Bypass -Command `"Remove-Item -Path '`$installPath' -Recurse -Force; Remove-Item -Path 'HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\S.T.E.V.I Retro' -Force`""
    Write-Host "✓ Uninstaller registered" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not register uninstaller: `$(`$_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Installation completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "S.T.E.V.I Retro has been installed to: `$installPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "You can now:" -ForegroundColor Yellow
Write-Host "• Launch from Desktop shortcut" -ForegroundColor Yellow
Write-Host "• Launch from Start Menu" -ForegroundColor Yellow
Write-Host "• Run directly from: `$installPath\S.T.E.V.I Retro.exe" -ForegroundColor Yellow
Write-Host ""

`$launch = Read-Host "Would you like to launch S.T.E.V.I Retro now? (y/n)"
if (`$launch -eq "y" -or `$launch -eq "Y") {
    try {
        Start-Process "`$installPath\S.T.E.V.I Retro.exe"
        Write-Host "✓ S.T.E.V.I Retro launched" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to launch: `$(`$_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Thank you for installing S.T.E.V.I Retro!" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
"@

# Save the installer script
$installerScriptPath = "dist\S.T.E.V.I-Retro-Installer-v$version.ps1"
$installerScript | Out-File -FilePath $installerScriptPath -Encoding UTF8

Write-Host "✓ Self-extracting installer created: $installerScriptPath" -ForegroundColor Green

# Create a batch file to run the PowerShell installer
$batchContent = @"
@echo off
echo S.T.E.V.I Retro Installer v$version
echo.
echo Starting PowerShell installer...
powershell.exe -ExecutionPolicy Bypass -File "%~dp0S.T.E.V.I-Retro-Installer-v$version.ps1"
pause
"@

$batchPath = "dist\S.T.E.V.I-Retro-Installer-v$version.bat"
$batchContent | Out-File -FilePath $batchPath -Encoding ASCII

Write-Host "✓ Batch installer created: $batchPath" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Installer creation completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Created files:" -ForegroundColor Cyan
Write-Host "• Portable ZIP: $zipPath" -ForegroundColor Yellow
Write-Host "• PowerShell Installer: $installerScriptPath" -ForegroundColor Yellow
Write-Host "• Batch Installer: $batchPath" -ForegroundColor Yellow
Write-Host ""
Write-Host "For distribution on fresh Windows 10/11 systems:" -ForegroundColor Green
Write-Host "1. Use the ZIP file for portable installation" -ForegroundColor Green
Write-Host "2. Use the .bat file for guided installation" -ForegroundColor Green
Write-Host "3. The installer will handle dependencies and shortcuts" -ForegroundColor Green
Write-Host ""

Write-Host "Opening dist folder..." -ForegroundColor Yellow
Start-Process "dist"

Read-Host "Press Enter to exit"
