// Contacts Templates
// Templates for justice-related contacts management

export const contactsTemplates = {
    contactsPanel(data) {
        const { episode, contacts } = data;
        const contactsByRole = contactsTemplates.groupContactsByRole(contacts);
        
        return `
            <div class="contacts-panel">
                <div class="contacts-header">
                    <h4>Justice Contacts</h4>
                    <div class="contacts-actions">
                        <button class="secondary-button" data-action="refresh-contacts">
                            <span class="button-icon">🔄</span>
                            Refresh
                        </button>
                    </div>
                </div>
                
                <div class="contacts-content">
                    ${contactsTemplates.renderContactSections(contactsByRole)}
                </div>
            </div>
        `;
    },
    
    renderContactSections(contactsByRole) {
        const roles = ['LAWYER', 'DUTY_COUNSEL', 'PROBATION_OFFICER', 'SURETY', 'BAIL_SUPERVISOR', 'CASE_WORKER', 'OTHER'];
        
        return roles.map(role => `
            <div class="contact-section" data-role="${role}">
                <div class="contact-section-header">
                    <h5>
                        <span class="role-icon">${contactsTemplates.getRoleIcon(role)}</span>
                        ${contactsTemplates.formatRole(role)}
                    </h5>
                    <button class="add-contact-btn" data-role="${role}">
                        <span class="button-icon">+</span>
                        Add
                    </button>
                </div>
                
                <div class="contact-section-content">
                    ${contactsByRole[role] && contactsByRole[role].length > 0 
                        ? contactsTemplates.renderContactsList(contactsByRole[role])
                        : contactsTemplates.renderNoContacts(role)
                    }
                </div>
            </div>
        `).join('');
    },
    
    renderContactsList(contacts) {
        return `
            <div class="contacts-list">
                ${contacts.map(contact => contactsTemplates.renderContactItem(contact)).join('')}
            </div>
        `;
    },
    
    renderContactItem(contact) {
        return `
            <div class="contact-item" data-contact-id="${contact.id}">
                <div class="contact-header">
                    <div class="contact-info">
                        <div class="contact-name">${contact.name}</div>
                        ${contact.org ? `<div class="contact-org">${contact.org}</div>` : ''}
                    </div>
                    <div class="contact-controls">
                        <button class="contact-toggle">▼</button>
                        <button class="action-button edit-contact-btn" 
                                data-contact-id="${contact.id}" title="Edit Contact">
                            ✏️
                        </button>
                        <button class="action-button deactivate-contact-btn" 
                                data-contact-id="${contact.id}" title="Deactivate Contact">
                            ⏹️
                        </button>
                    </div>
                </div>
                
                <div class="contact-details" style="display: none;">
                    ${contactsTemplates.renderContactDetails(contact)}
                </div>
            </div>
        `;
    },
    
    renderContactDetails(contact) {
        return `
            <div class="contact-details-content">
                ${contact.phone ? `
                    <div class="detail-row">
                        <span class="detail-label">Phone:</span>
                        <span class="detail-value">
                            <a href="tel:${contact.phone}">${contact.phone}</a>
                        </span>
                    </div>
                ` : ''}
                
                ${contact.email ? `
                    <div class="detail-row">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value">
                            <a href="mailto:${contact.email}">${contact.email}</a>
                        </span>
                    </div>
                ` : ''}
                
                ${contact.started_at ? `
                    <div class="detail-row">
                        <span class="detail-label">Started:</span>
                        <span class="detail-value">${contactsTemplates.formatDateTime(contact.started_at)}</span>
                    </div>
                ` : ''}
                
                ${contact.ended_at ? `
                    <div class="detail-row">
                        <span class="detail-label">Ended:</span>
                        <span class="detail-value">${contactsTemplates.formatDateTime(contact.ended_at)}</span>
                    </div>
                ` : ''}
                
                ${contact.notes ? `
                    <div class="detail-row">
                        <span class="detail-label">Notes:</span>
                        <span class="detail-value">${contact.notes}</span>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderNoContacts(role) {
        return `
            <div class="no-contacts">
                <p>No ${contactsTemplates.formatRole(role).toLowerCase()} contacts added.</p>
            </div>
        `;
    },
    
    addContactForm(role) {
        return `
            <form id="add-contact-form">
                <div class="form-row">
                    <label for="name">Name:</label>
                    <input type="text" name="name" id="name" required
                           placeholder="Contact name">
                </div>
                
                <div class="form-row">
                    <label for="org">Organization:</label>
                    <input type="text" name="org" id="org"
                           placeholder="Law firm, agency, etc.">
                </div>
                
                <div class="form-row">
                    <label for="phone">Phone:</label>
                    <input type="tel" name="phone" id="phone"
                           placeholder="Phone number">
                </div>
                
                <div class="form-row">
                    <label for="email">Email:</label>
                    <input type="email" name="email" id="email"
                           placeholder="Email address">
                </div>
                
                <div class="form-row">
                    <label for="started_at">Start Date:</label>
                    <input type="datetime-local" name="started_at" id="started_at"
                           value="${new Date().toISOString().slice(0, 16)}">
                </div>
                
                <div class="form-row">
                    <label for="notes">Notes:</label>
                    <textarea name="notes" id="notes" rows="3"
                              placeholder="Additional notes about this contact..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-contact-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-contact-btn" class="primary-button">
                        Add ${contactsTemplates.formatRole(role)}
                    </button>
                </div>
            </form>
        `;
    },
    
    editContactForm(contact) {
        return `
            <form id="edit-contact-form">
                <div class="form-row">
                    <label for="name">Name:</label>
                    <input type="text" name="name" id="name" required
                           value="${contact.name || ''}"
                           placeholder="Contact name">
                </div>
                
                <div class="form-row">
                    <label for="org">Organization:</label>
                    <input type="text" name="org" id="org"
                           value="${contact.org || ''}"
                           placeholder="Law firm, agency, etc.">
                </div>
                
                <div class="form-row">
                    <label for="phone">Phone:</label>
                    <input type="tel" name="phone" id="phone"
                           value="${contact.phone || ''}"
                           placeholder="Phone number">
                </div>
                
                <div class="form-row">
                    <label for="email">Email:</label>
                    <input type="email" name="email" id="email"
                           value="${contact.email || ''}"
                           placeholder="Email address">
                </div>
                
                <div class="form-row">
                    <label for="notes">Notes:</label>
                    <textarea name="notes" id="notes" rows="3"
                              placeholder="Additional notes about this contact...">${contact.notes || ''}</textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-contact-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-contact-btn" class="primary-button">
                        Update Contact
                    </button>
                </div>
            </form>
        `;
    },
    
    deactivateContactModal(contact) {
        return `
            <div class="deactivate-contact-modal">
                <div class="modal-header">
                    <h5>Deactivate Contact</h5>
                    <p>Are you sure you want to deactivate this contact?</p>
                    <div class="contact-summary">
                        <strong>${contact.name}</strong><br>
                        ${contact.org ? `${contact.org}<br>` : ''}
                        ${contactsTemplates.formatRole(contact.role)}
                    </div>
                </div>
                
                <div class="modal-actions">
                    <button type="button" id="cancel-deactivate-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="deactivate-contact-btn" class="primary-button">
                        Deactivate
                    </button>
                </div>
            </div>
        `;
    },
    
    errorView(message) {
        return `
            <div class="contacts-error">
                <div class="error-message">
                    Failed to load contacts: ${message}
                </div>
                <button class="secondary-button" data-action="refresh-contacts">
                    Retry
                </button>
            </div>
        `;
    },
    
    groupContactsByRole(contacts) {
        const grouped = {};
        const roles = ['LAWYER', 'DUTY_COUNSEL', 'PROBATION_OFFICER', 'SURETY', 'BAIL_SUPERVISOR', 'CASE_WORKER', 'OTHER'];
        
        // Initialize all roles
        roles.forEach(role => {
            grouped[role] = [];
        });
        
        // Group contacts by role
        contacts.forEach(contact => {
            if (grouped[contact.role]) {
                grouped[contact.role].push(contact);
            } else {
                grouped['OTHER'].push(contact);
            }
        });
        
        return grouped;
    },
    
    formatRole(role) {
        const labels = {
            'LAWYER': 'Lawyer',
            'DUTY_COUNSEL': 'Duty Counsel',
            'PROBATION_OFFICER': 'Probation Officer',
            'SURETY': 'Surety',
            'BAIL_SUPERVISOR': 'Bail Supervisor',
            'CASE_WORKER': 'Case Worker',
            'OTHER': 'Other Contact'
        };
        
        return labels[role] || role;
    },
    
    getRoleIcon(role) {
        const icons = {
            'LAWYER': '⚖️',
            'DUTY_COUNSEL': '📋',
            'PROBATION_OFFICER': '👮',
            'SURETY': '🤝',
            'BAIL_SUPERVISOR': '👁️',
            'CASE_WORKER': '📝',
            'OTHER': '👤'
        };
        
        return icons[role] || '👤';
    },
    
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
};
