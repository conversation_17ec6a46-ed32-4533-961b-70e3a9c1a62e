// Charges Grid View
// Displays and manages charges with disposition tracking

import { chargesTemplates } from '../../templates/charges-templates.js';

export class ChargesGrid {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.container = null;
        this.episode = null;
        this.charges = [];
    }
    
    async mount(container, episode) {
        this.container = container;
        this.episode = episode;
        
        try {
            // Load charges for the episode
            this.charges = await this.justice.api.getEpisodeCharges(episode.id);
            
            // Update state
            this.justice.state.setCharges(this.charges);
            
            // Render charges grid
            this.render();
            
        } catch (error) {
            console.error('Failed to mount charges grid:', error);
            this.renderError(error.message);
        }
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = chargesTemplates.chargesGrid({
            episode: this.episode,
            charges: this.charges
        });
        
        this.setupEventHandlers();
    }
    
    renderError(message) {
        if (!this.container) return;
        
        this.container.innerHTML = chargesTemplates.errorView(message);
    }
    
    setupEventHandlers() {
        if (!this.container) return;
        
        // Status update dropdowns
        const statusSelects = this.container.querySelectorAll('.charge-status-select');
        statusSelects.forEach(select => {
            select.addEventListener('change', async (e) => {
                const chargeId = e.target.dataset.chargeId;
                const newStatus = e.target.value;
                await this.updateChargeStatus(chargeId, newStatus);
            });
        });
        
        // Add charge button
        const addChargeBtn = this.container.querySelector('#add-charge-btn');
        if (addChargeBtn) {
            addChargeBtn.addEventListener('click', () => {
                this.showAddChargeModal();
            });
        }
        
        // Edit charge buttons
        const editBtns = this.container.querySelectorAll('.edit-charge-btn');
        editBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chargeId = e.target.dataset.chargeId;
                this.showEditChargeModal(chargeId);
            });
        });
        
        // Refresh button
        const refreshBtn = this.container.querySelector('[data-action="refresh-charges"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.refresh();
            });
        }
    }
    
    async updateChargeStatus(chargeId, newStatus) {
        try {
            // Update via API
            const updatedCharge = await this.justice.api.updateChargeStatus(chargeId, newStatus);
            
            // Update local state
            const index = this.charges.findIndex(ch => ch.id === chargeId);
            if (index !== -1) {
                this.charges[index] = updatedCharge;
                this.justice.state.updateCharge(chargeId, updatedCharge);
            }
            
            // Check if all charges are closed
            await this.checkEpisodeCompletion();
            
            // Show success message
            this.justice.ui.showDialog('Success', 'Charge status updated successfully.', 'success');
            
        } catch (error) {
            console.error('Failed to update charge status:', error);
            this.justice.ui.showDialog('Error', `Failed to update charge status: ${error.message}`, 'error');
            
            // Revert the select value
            const select = this.container.querySelector(`[data-charge-id="${chargeId}"]`);
            if (select) {
                const originalCharge = this.charges.find(ch => ch.id === chargeId);
                if (originalCharge) {
                    select.value = originalCharge.status;
                }
            }
        }
    }
    
    async checkEpisodeCompletion() {
        // Check if all charges have a closed status
        const closedStatuses = ['WITHDRAWN', 'DISMISSED', 'CONVICTED', 'ACQUITTED', 'STAYED'];
        const allClosed = this.charges.every(charge => closedStatuses.includes(charge.status));
        
        if (allClosed && this.charges.length > 0) {
            // Episode should be marked as completed
            // This would typically be handled by database triggers
            console.log('All charges closed - episode should be marked as completed');
        }
    }
    
    showAddChargeModal() {
        const modal = this.justice.ui.createModal({
            id: 'add-charge-modal',
            title: 'Add New Charge',
            size: 'medium'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = chargesTemplates.addChargeForm();
        
        this.justice.ui.showModal(modal);
        
        // Setup form handlers
        const form = modal.querySelector('#add-charge-form');
        const saveBtn = modal.querySelector('#save-charge-btn');
        const cancelBtn = modal.querySelector('#cancel-charge-btn');
        
        saveBtn.addEventListener('click', async () => {
            await this.saveNewCharge(form, modal);
        });
        
        cancelBtn.addEventListener('click', () => {
            this.justice.ui.closeModal(modal.id);
        });
    }
    
    async saveNewCharge(form, modal) {
        try {
            const formData = new FormData(form);
            const chargeData = {
                code: formData.get('code'),
                label: formData.get('label'),
                severity: formData.get('severity') || 'OTHER'
            };
            
            // Validate
            if (!chargeData.code && !chargeData.label) {
                this.justice.ui.showDialog('Error', 'Either charge code or label is required.', 'error');
                return;
            }
            
            // Create charge
            const newCharges = await this.justice.api.createCharges(this.episode.id, [chargeData]);
            
            // Update local state
            this.charges.push(...newCharges);
            this.justice.state.setCharges(this.charges);
            
            // Re-render
            this.render();
            
            // Close modal
            this.justice.ui.closeModal(modal.id);
            
            this.justice.ui.showDialog('Success', 'Charge added successfully.', 'success');
            
        } catch (error) {
            console.error('Failed to add charge:', error);
            this.justice.ui.showDialog('Error', `Failed to add charge: ${error.message}`, 'error');
        }
    }
    
    showEditChargeModal(chargeId) {
        const charge = this.charges.find(ch => ch.id === chargeId);
        if (!charge) return;
        
        const modal = this.justice.ui.createModal({
            id: 'edit-charge-modal',
            title: 'Edit Charge',
            size: 'medium'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = chargesTemplates.editChargeForm(charge);
        
        this.justice.ui.showModal(modal);
        
        // Setup form handlers
        const form = modal.querySelector('#edit-charge-form');
        const saveBtn = modal.querySelector('#save-charge-btn');
        const cancelBtn = modal.querySelector('#cancel-charge-btn');
        
        saveBtn.addEventListener('click', async () => {
            await this.saveChargeEdit(form, modal, chargeId);
        });
        
        cancelBtn.addEventListener('click', () => {
            this.justice.ui.closeModal(modal.id);
        });
    }
    
    async saveChargeEdit(form, modal, chargeId) {
        try {
            const formData = new FormData(form);
            const updates = {
                code: formData.get('code'),
                label: formData.get('label'),
                severity: formData.get('severity')
            };
            
            // Note: In a real implementation, you'd need an API method to update charge details
            // For now, we'll just update the status
            console.log('Charge edit not fully implemented - would update:', updates);
            
            this.justice.ui.closeModal(modal.id);
            
        } catch (error) {
            console.error('Failed to edit charge:', error);
            this.justice.ui.showDialog('Error', `Failed to edit charge: ${error.message}`, 'error');
        }
    }
    
    async refresh() {
        if (!this.episode) return;
        
        try {
            // Reload charges
            this.charges = await this.justice.api.getEpisodeCharges(this.episode.id);
            this.justice.state.setCharges(this.charges);
            
            // Re-render
            this.render();
            
        } catch (error) {
            console.error('Failed to refresh charges:', error);
            this.justice.ui.showDialog('Error', `Failed to refresh charges: ${error.message}`, 'error');
        }
    }
    
    getStatusLabel(status) {
        const labels = {
            'PENDING': 'Pending',
            'WITHDRAWN': 'Withdrawn',
            'DISMISSED': 'Dismissed',
            'CONVICTED': 'Convicted',
            'ACQUITTED': 'Acquitted',
            'STAYED': 'Stayed'
        };
        
        return labels[status] || status;
    }
    
    getStatusClass(status) {
        const classes = {
            'PENDING': 'status-pending',
            'WITHDRAWN': 'status-closed',
            'DISMISSED': 'status-closed',
            'CONVICTED': 'status-convicted',
            'ACQUITTED': 'status-closed',
            'STAYED': 'status-closed'
        };
        
        return classes[status] || 'status-unknown';
    }
    
    unmount() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.container = null;
        this.episode = null;
        this.charges = [];
    }
}
