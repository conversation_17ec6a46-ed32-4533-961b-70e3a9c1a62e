// Add Event Templates
// Forms for adding various types of justice events

export const addEventTemplates = {
    bailHearingForm(data) {
        return `
            <form id="bail-hearing-form">
                <div class="form-row">
                    <label for="hearing_dt">Hearing Date & Time:</label>
                    <input type="datetime-local" name="hearing_dt" id="hearing_dt" 
                           value="${data.defaultDateTime}" required>
                </div>
                
                <div class="form-row">
                    <label for="hearing-outcome">Outcome:</label>
                    <select name="outcome" id="hearing-outcome" required>
                        <option value="">Select outcome...</option>
                        <option value="GRANTED">Granted</option>
                        <option value="DENIED">Denied</option>
                        <option value="RESERVED">Reserved</option>
                    </select>
                </div>
                
                <div id="next-date-section" class="form-row" style="display: none;">
                    <label for="next_date">Next Hearing Date:</label>
                    <input type="datetime-local" name="next_date" id="next_date">
                </div>
                
                <div class="form-row">
                    <label for="notes">Notes:</label>
                    <textarea name="notes" id="notes" rows="3" 
                              placeholder="Additional details about the hearing..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-bail-hearing-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-bail-hearing-btn" class="primary-button">
                        Save Bail Hearing
                    </button>
                </div>
            </form>
        `;
    },
    
    transferFacilityForm(data) {
        return `
            <form id="transfer-facility-form">
                <div class="form-row">
                    <label for="transfer_dt">Transfer Date & Time:</label>
                    <input type="datetime-local" name="transfer_dt" id="transfer_dt" 
                           value="${data.defaultDateTime}" required>
                </div>
                
                <div class="form-row">
                    <label for="facility_id">Destination Facility:</label>
                    <select name="facility_id" id="facility_id" required>
                        <option value="">Select facility...</option>
                        ${data.facilities.map(facility => `
                            <option value="${facility.id}">${facility.name} (${facility.type})</option>
                        `).join('')}
                    </select>
                </div>
                
                <div class="form-row">
                    <label for="reason">Reason for Transfer:</label>
                    <textarea name="reason" id="reason" rows="3" 
                              placeholder="Reason for the transfer..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-transfer-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-transfer-btn" class="primary-button">
                        Save Transfer
                    </button>
                </div>
            </form>
        `;
    },
    
    releaseConditionsForm(data) {
        return `
            <form id="release-conditions-form">
                <div class="form-row">
                    <label for="release_dt">Release Date & Time:</label>
                    <input type="datetime-local" name="release_dt" id="release_dt" 
                           value="${data.defaultDateTime}" required>
                </div>
                
                <div class="form-row">
                    <label for="condition-pack-select">Condition Pack:</label>
                    <select name="pack_id" id="condition-pack-select">
                        <option value="">No pack (custom conditions)</option>
                        ${data.conditionPacks.map(pack => `
                            <option value="${pack.id}" ${data.selectedPack?.id === pack.id ? 'selected' : ''}>
                                ${pack.name} (${pack.scope})
                            </option>
                        `).join('')}
                    </select>
                </div>
                
                <div class="form-row">
                    <label>
                        <input type="checkbox" name="replace" ${data.replace ? 'checked' : ''}>
                        Replace existing conditions
                    </label>
                </div>
                
                <div class="form-row">
                    <label for="start_dt">Conditions Start Date:</label>
                    <input type="datetime-local" name="start_dt" id="start_dt" 
                           value="${data.defaultDateTime}">
                </div>
                
                <div class="form-row">
                    <label for="end_dt">Conditions End Date (optional):</label>
                    <input type="datetime-local" name="end_dt" id="end_dt">
                </div>
                
                <div id="pack-items-container">
                    <!-- Pack items will be populated here -->
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-release-conditions-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-release-conditions-btn" class="primary-button">
                        Save Release Conditions
                    </button>
                </div>
            </form>
        `;
    },
    
    packItemsList(data) {
        if (!data.packItems || data.packItems.length === 0) {
            return '<p>Select a condition pack to see available conditions.</p>';
        }
        
        return `
            <div class="pack-items-section">
                <h5>Condition Pack Items</h5>
                <div class="pack-items-list">
                    ${data.packItems.map(item => `
                        <div class="pack-item">
                            <div class="item-header">
                                <span class="item-label">${item.label}</span>
                                ${item.required ? '<span class="required-badge">Required</span>' : ''}
                            </div>
                            <div class="item-overrides">
                                ${addEventTemplates.renderItemOverrides(item, data.overrides[item.id] || {})}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },
    
    renderItemOverrides(item, overrides) {
        switch (item.type) {
            case 'CURFEW':
                return `
                    <div class="override-row">
                        <label>Start Time:</label>
                        <input type="time" class="override-input" data-item-id="${item.id}" 
                               data-field="start_time" value="${overrides.start_time || '22:00'}">
                    </div>
                    <div class="override-row">
                        <label>End Time:</label>
                        <input type="time" class="override-input" data-item-id="${item.id}" 
                               data-field="end_time" value="${overrides.end_time || '06:00'}">
                    </div>
                `;
            case 'NO_CONTACT':
                return `
                    <div class="override-row">
                        <label>Person:</label>
                        <input type="text" class="override-input" data-item-id="${item.id}" 
                               data-field="person" value="${overrides.person || ''}" 
                               placeholder="Name of person to avoid contact with">
                    </div>
                `;
            case 'NO_GO_ZONE':
                return `
                    <div class="override-row">
                        <label>Location:</label>
                        <input type="text" class="override-input" data-item-id="${item.id}" 
                               data-field="location" value="${overrides.location || ''}" 
                               placeholder="Address or area description">
                    </div>
                `;
            case 'REPORTING':
                return `
                    <div class="override-row">
                        <label>Officer:</label>
                        <input type="text" class="override-input" data-item-id="${item.id}" 
                               data-field="officer" value="${overrides.officer || ''}" 
                               placeholder="Probation officer name">
                    </div>
                    <div class="override-row">
                        <label>Frequency:</label>
                        <select class="override-input" data-item-id="${item.id}" data-field="frequency">
                            <option value="weekly" ${overrides.frequency === 'weekly' ? 'selected' : ''}>Weekly</option>
                            <option value="biweekly" ${overrides.frequency === 'biweekly' ? 'selected' : ''}>Bi-weekly</option>
                            <option value="monthly" ${overrides.frequency === 'monthly' ? 'selected' : ''}>Monthly</option>
                        </select>
                    </div>
                `;
            case 'SURETY':
                return `
                    <div class="override-row">
                        <label>Surety Name:</label>
                        <input type="text" class="override-input" data-item-id="${item.id}" 
                               data-field="name" value="${overrides.name || ''}" 
                               placeholder="Name of surety">
                    </div>
                    <div class="override-row">
                        <label>Amount:</label>
                        <input type="number" class="override-input" data-item-id="${item.id}" 
                               data-field="amount" value="${overrides.amount || ''}" 
                               placeholder="Dollar amount">
                    </div>
                `;
            default:
                return `
                    <div class="override-row">
                        <label>Details:</label>
                        <textarea class="override-input" data-item-id="${item.id}" 
                                  data-field="description" rows="2" 
                                  placeholder="Additional details for this condition">${overrides.description || ''}</textarea>
                    </div>
                `;
        }
    },
    
    courtAppearanceForm(data) {
        return `
            <form id="court-appearance-form">
                <div class="form-row">
                    <label for="appearance_dt">Appearance Date & Time:</label>
                    <input type="datetime-local" name="appearance_dt" id="appearance_dt" 
                           value="${data.defaultDateTime}" required>
                </div>
                
                <div class="form-row">
                    <label for="court_name">Court Name:</label>
                    <input type="text" name="court_name" id="court_name" required
                           placeholder="e.g., Ontario Court of Justice - Cobourg">
                </div>
                
                <div class="form-row">
                    <label for="court_address">Court Address:</label>
                    <input type="text" name="court_address" id="court_address"
                           placeholder="Court address (optional)">
                </div>
                
                <div class="form-row">
                    <label for="appearance_type">Appearance Type:</label>
                    <select name="appearance_type" id="appearance_type" required>
                        <option value="">Select type...</option>
                        <option value="FIRST_APPEARANCE">First Appearance</option>
                        <option value="BAIL_HEARING">Bail Hearing</option>
                        <option value="PLEA">Plea</option>
                        <option value="TRIAL">Trial</option>
                        <option value="SENTENCING">Sentencing</option>
                        <option value="REVIEW">Review</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>
                
                <div class="form-row">
                    <label for="appearance-outcome">Outcome (if known):</label>
                    <select name="outcome" id="appearance-outcome">
                        <option value="">Not yet determined</option>
                        <option value="ADJOURNED">Adjourned</option>
                        <option value="REMANDED">Remanded</option>
                        <option value="CONTINUED">Continued</option>
                        <option value="RESOLVED">Resolved</option>
                        <option value="WITHDRAWN">Withdrawn</option>
                    </select>
                </div>
                
                <div id="next-date-section" class="form-row" style="display: none;">
                    <label for="next_date">Next Court Date:</label>
                    <input type="datetime-local" name="next_date" id="next_date">
                </div>
                
                <div class="form-row">
                    <label for="notes">Notes:</label>
                    <textarea name="notes" id="notes" rows="3" 
                              placeholder="Additional details about the appearance..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-court-appearance-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-court-appearance-btn" class="primary-button">
                        Save Court Appearance
                    </button>
                </div>
            </form>
        `;
    },
    
    sentenceForm(data) {
        return `
            <form id="sentence-form">
                <div class="form-row">
                    <label for="sentence_dt">Sentence Date & Time:</label>
                    <input type="datetime-local" name="sentence_dt" id="sentence_dt" 
                           value="${data.defaultDateTime}" required>
                </div>
                
                <div class="form-row">
                    <label for="sentence-mode">Sentence Mode:</label>
                    <select name="mode" id="sentence-mode" required>
                        <option value="">Select mode...</option>
                        <option value="CUSTODIAL">Custodial</option>
                        <option value="COMMUNITY">Community</option>
                    </select>
                </div>
                
                <div id="custodial-details" class="sentence-details" style="display: none;">
                    <div class="form-row">
                        <label for="length_days">Length (days):</label>
                        <input type="number" name="length_days" id="length_days" min="0">
                    </div>
                    
                    <div class="form-row">
                        <label for="credit_days">Credit (days):</label>
                        <input type="number" name="credit_days" id="credit_days" min="0">
                    </div>
                    
                    <div class="form-row">
                        <label for="facility_id">Facility:</label>
                        <select name="facility_id" id="facility_id">
                            <option value="">Select facility...</option>
                            <!-- Facilities would be populated here -->
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <label for="parole_eligibility">Parole Eligibility:</label>
                        <input type="date" name="parole_eligibility" id="parole_eligibility">
                    </div>
                </div>
                
                <div id="community-details" class="sentence-details" style="display: none;">
                    <div class="form-row">
                        <label for="service_hours">Community Service Hours:</label>
                        <input type="number" name="service_hours" id="service_hours" min="0">
                    </div>
                    
                    <div class="form-row">
                        <label for="probation_length">Probation Length (days):</label>
                        <input type="number" name="probation_length" id="probation_length" min="0">
                    </div>
                </div>
                
                <div class="form-row">
                    <label for="conditions">Conditions:</label>
                    <textarea name="conditions" id="conditions" rows="3" 
                              placeholder="Sentence conditions..."></textarea>
                </div>
                
                <div class="form-row">
                    <label for="notes">Notes:</label>
                    <textarea name="notes" id="notes" rows="3" 
                              placeholder="Additional details about the sentence..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-sentence-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-sentence-btn" class="primary-button">
                        Save Sentence
                    </button>
                </div>
            </form>
        `;
    },
    
    warrantForm(data) {
        const isExecution = data.action === 'execute';
        
        return `
            <form id="warrant-form">
                <div class="form-row">
                    <label for="warrant_dt">${isExecution ? 'Execution' : 'Issue'} Date & Time:</label>
                    <input type="datetime-local" name="warrant_dt" id="warrant_dt" 
                           value="${data.defaultDateTime}" required>
                </div>
                
                <div class="form-row">
                    <label for="warrant_type">Warrant Type:</label>
                    <select name="warrant_type" id="warrant_type" required>
                        <option value="">Select type...</option>
                        <option value="BENCH">Bench Warrant</option>
                        <option value="ARREST">Arrest Warrant</option>
                        <option value="SEARCH">Search Warrant</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>
                
                ${isExecution ? `
                    <div class="form-row">
                        <label for="original_warrant_id">Original Warrant ID:</label>
                        <input type="text" name="original_warrant_id" id="original_warrant_id"
                               placeholder="ID of the warrant being executed">
                    </div>
                    
                    <div class="form-row">
                        <label for="executing_officer">Executing Officer:</label>
                        <input type="text" name="executing_officer" id="executing_officer"
                               placeholder="Name of executing officer">
                    </div>
                    
                    <div class="form-row">
                        <label for="execution_location">Execution Location:</label>
                        <input type="text" name="execution_location" id="execution_location"
                               placeholder="Where the warrant was executed">
                    </div>
                ` : `
                    <div class="form-row">
                        <label for="issuing_court">Issuing Court:</label>
                        <input type="text" name="issuing_court" id="issuing_court"
                               placeholder="Court that issued the warrant">
                    </div>
                    
                    <div class="form-row">
                        <label for="reason">Reason:</label>
                        <textarea name="reason" id="reason" rows="3" 
                                  placeholder="Reason for issuing the warrant..."></textarea>
                    </div>
                `}
                
                <div class="form-row">
                    <label for="notes">Notes:</label>
                    <textarea name="notes" id="notes" rows="3" 
                              placeholder="Additional details..."></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-warrant-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-warrant-btn" class="primary-button">
                        ${isExecution ? 'Save Execution' : 'Issue Warrant'}
                    </button>
                </div>
            </form>
        `;
    }
};
