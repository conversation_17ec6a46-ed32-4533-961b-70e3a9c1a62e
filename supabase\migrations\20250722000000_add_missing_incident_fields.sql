-- Add missing incident fields to align with forms and SQLite schema
-- Migration: 20250722000000_add_missing_incident_fields.sql
-- Description: Adds officers_attending, fire_personnel, and agency_response_notes fields

BEGIN;

-- Add missing fields to case_mgmt.incidents table
ALTER TABLE case_mgmt.incidents 
ADD COLUMN IF NOT EXISTS officers_attending TEXT,
ADD COLUMN IF NOT EXISTS fire_personnel TEXT,
ADD COLUMN IF NOT EXISTS agency_response_notes TEXT;

-- Add comments for documentation
COMMENT ON COLUMN case_mgmt.incidents.officers_attending IS 'Names and badge numbers of attending police officers';
COMMENT ON COLUMN case_mgmt.incidents.fire_personnel IS 'Names of fire department personnel who responded';
COMMENT ON COLUMN case_mgmt.incidents.agency_response_notes IS 'Overall notes about emergency services response coordination';

COMMIT;