/**
 * Encampment Commands Factory
 * Creates modular encampment commands following the established patterns
 */

import { BaseCommand } from '../../shared/base-commands.js';

export class EncampmentCommandsFactory {
    constructor(commandManager, encampmentManagement) {
        this.commandManager = commandManager;
        this.encampmentManagement = encampmentManagement;
    }

    createCommands() {
        return new Map([
            // Main encampment management commands
            ['add-encampment', new AddEncampmentCommand(this.commandManager, this.encampmentManagement)],
            ['edit-encampment', new EditEncampmentCommand(this.commandManager, this.encampmentManagement)],
            ['delete-encampment', new DeleteEncampmentCommand(this.commandManager, this.encampmentManagement)],
            ['view-encampment-detail', new ViewEncampmentDetailCommand(this.commandManager, this.encampmentManagement)],
            ['manage-encampments', new ManageEncampmentsCommand(this.commandManager, this.encampmentManagement)],
            ['search-encampments', new SearchEncampmentsCommand(this.commandManager, this.encampmentManagement)],
            ['search-encampments-input', new SearchEncampmentsInputCommand(this.commandManager, this.encampmentManagement)],
            
            // Visit management commands
            ['add-encampment-visit', new AddEncampmentVisitCommand(this.commandManager, this.encampmentManagement)],
            ['edit-encampment-visit', new EditEncampmentVisitCommand(this.commandManager, this.encampmentManagement)],
            ['view-encampment-visits', new ViewEncampmentVisitsCommand(this.commandManager, this.encampmentManagement)],
            
            // Navigation commands
            ['back-to-encampment-list', new BackToEncampmentListCommand(this.commandManager, this.encampmentManagement)],
            ['cancel-encampment-form', new CancelEncampmentFormCommand(this.commandManager, this.encampmentManagement)],
            ['save-encampment', new SaveEncampmentCommand(this.commandManager, this.encampmentManagement)]
        ]);
    }
}

// === MAIN ENCAMPMENT MANAGEMENT COMMANDS ===

class AddEncampmentCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute() {
        try {
            await this.encampmentManagement.showAddEncampmentForm();
        } catch (error) {
            console.error('Error executing add-encampment command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show add encampment form');
        }
    }
}

class EditEncampmentCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(encampmentId) {
        try {
            await this.encampmentManagement.showEditEncampmentForm(encampmentId);
        } catch (error) {
            console.error('Error executing edit-encampment command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show edit encampment form');
        }
    }
}

class DeleteEncampmentCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(encampmentId) {
        try {
            await this.encampmentManagement.deleteEncampment(encampmentId);
        } catch (error) {
            console.error('Error executing delete-encampment command:', error);
            this.commandManager.ui.showErrorMessage('Failed to delete encampment');
        }
    }
}

class ViewEncampmentDetailCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(encampmentId) {
        try {
            await this.encampmentManagement.showEncampmentDetail(encampmentId);
        } catch (error) {
            console.error('Error executing view-encampment-detail command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show encampment detail');
        }
    }
}

class ManageEncampmentsCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute() {
        try {
            await this.encampmentManagement.showEncampmentList();
        } catch (error) {
            console.error('Error executing manage-encampments command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show encampments list');
        }
    }
}

class SearchEncampmentsCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute() {
        try {
            await this.encampmentManagement.showSearchForm();
        } catch (error) {
            console.error('Error executing search-encampments command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show search form');
        }
    }
}

class SearchEncampmentsInputCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(query) {
        try {
            await this.encampmentManagement.performSearch(query);
        } catch (error) {
            console.error('Error executing search-encampments-input command:', error);
            this.commandManager.ui.showErrorMessage('Failed to perform search');
        }
    }
}

// === VISIT MANAGEMENT COMMANDS ===

class AddEncampmentVisitCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(encampmentId) {
        try {
            await this.encampmentManagement.showAddVisitForm(encampmentId);
        } catch (error) {
            console.error('Error executing add-encampment-visit command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show add visit form');
        }
    }
}

class EditEncampmentVisitCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(visitId) {
        try {
            await this.encampmentManagement.showEditVisitForm(visitId);
        } catch (error) {
            console.error('Error executing edit-encampment-visit command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show edit visit form');
        }
    }
}

class ViewEncampmentVisitsCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute(encampmentId) {
        try {
            await this.encampmentManagement.showEncampmentVisits(encampmentId);
        } catch (error) {
            console.error('Error executing view-encampment-visits command:', error);
            this.commandManager.ui.showErrorMessage('Failed to show encampment visits');
        }
    }
}

// === NAVIGATION COMMANDS ===

class BackToEncampmentListCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute() {
        try {
            await this.encampmentManagement.showEncampmentList();
        } catch (error) {
            console.error('Error executing back-to-encampment-list command:', error);
            this.commandManager.ui.showErrorMessage('Failed to return to encampment list');
        }
    }
}

class CancelEncampmentFormCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute() {
        try {
            await this.encampmentManagement.cancelForm();
        } catch (error) {
            console.error('Error executing cancel-encampment-form command:', error);
            this.commandManager.ui.showErrorMessage('Failed to cancel form');
        }
    }
}

class SaveEncampmentCommand extends BaseCommand {
    constructor(commandManager, encampmentManagement) {
        super(commandManager);
        this.encampmentManagement = encampmentManagement;
    }

    async execute() {
        try {
            // Get form data from the form element
            const form = document.getElementById('encampment-form');
            if (!form) {
                throw new Error('Encampment form not found');
            }

            const formData = new FormData(form);
            const encampmentData = Object.fromEntries(formData.entries());
            
            await this.encampmentManagement.saveEncampment(encampmentData);
        } catch (error) {
            console.error('Error executing save-encampment command:', error);
            this.commandManager.ui.showErrorMessage('Failed to save encampment');
        }
    }
}