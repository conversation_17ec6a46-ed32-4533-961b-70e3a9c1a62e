@echo off
title S.T.E.V.I Retro - Install Missing Dependencies
echo.
echo ===============================================
echo  Installing Missing Dependencies
echo ===============================================
echo.

REM Function to install a package globally and copy it locally
:INSTALL_PACKAGE
set PACKAGE_NAME=%1
echo Installing %PACKAGE_NAME%...

REM Install globally first
npm install -g %PACKAGE_NAME%
if errorlevel 1 (
    echo ERROR: Failed to install %PACKAGE_NAME% globally
    goto :EOF
)

REM Create local directory
if not exist "node_modules\%PACKAGE_NAME%" (
    mkdir "node_modules\%PACKAGE_NAME%"
)

REM Copy from global to local
xcopy "C:\Users\<USER>\AppData\Roaming\npm\node_modules\%PACKAGE_NAME%" "node_modules\%PACKAGE_NAME%" /E /I /Y >nul
echo ✅ %PACKAGE_NAME% installed successfully
goto :EOF

REM Install required dependencies
call :INSTALL_PACKAGE semver
call :INSTALL_PACKAGE @supabase/supabase-js
call :INSTALL_PACKAGE better-sqlite3
call :INSTALL_PACKAGE fs-extra
call :INSTALL_PACKAGE node-fetch
call :INSTALL_PACKAGE uuid

echo.
echo ✅ All missing dependencies installed!
echo You can now run the application with: launch.bat
echo.
pause
