/* Incident Module Styles */
/* Extracted from main styles.css for better organization */

/* Main content area containing incidents and bottom sections */
.incidents-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #000000;
    min-height: 0;
}

.incidents-section .section-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: #000000;
    border-top: 1px solid #ff0000;
    display: flex;
    flex-direction: column;
}

.incidents-section {
    background: #000000;
    color: #ff0000;
}

.incident-count {
    color: #ff0000;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 11px;
    border: none;
    background: none;
    padding: 2px 5px;
}

.no-incidents,
.loading-incidents {
    padding: 5px;
    text-align: center;
    color: #888;
    font-style: italic;
}

.incident-item {
    border: 1px solid #444;
    padding: 5px;
    margin-bottom: 5px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.incident-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.incident-number {
    font-weight: bold;
    color: #ff0000;
}

.incident-status {
    font-size: 10px;
    padding: 2px 5px;
    border: 1px solid;
    border-radius: 3px;
}

.incident-description {
    font-size: 11px;
    color: #ccc;
    margin: 5px 0;
}

.incident-location {
    font-size: 10px;
    color: #999;
}

/* Incident Detail Tabs - Responsive */
@media (max-width: 768px) {
    .incident-detail-tabs {
        flex-wrap: wrap;
    }
    
    .incident-detail-tab {
        flex: 1;
        min-width: 100px;
    }
}

.incident-result-item {
    border: 1px solid #ff4444;
    padding: 5px;
    margin: 5px 0;
    cursor: pointer;
}

.incident-result-item:hover {
    background: rgba(255, 68, 68, 0.1);
}

.incident-result-item:last-child {
    margin-bottom: 0;
}

.incident-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.incident-number {
    font-weight: bold;
    color: #ff6666;
    font-size: 12px;
}

.incident-badges {
    display: flex;
    gap: 5px;
    align-items: center;
}

.incident-result-content {
    color: #cccccc;
}

.incident-description {
    font-size: 11px;
    margin: 5px 0;
}

.incident-details {
    display: flex;
    gap: 5px;
    font-size: 10px;
    margin: 5px 0;
}

.incident-result-actions {
    margin-top: 5px;
    display: flex;
    gap: 8px;
}

.incident-details-view {
    background: rgba(255, 68, 68, 0.05);
    border: 1px solid #ff4444;
}

.incident-details-view h4 {
    color: #ff6666;
    margin: 0 0 5px 0;
    font-size: 12px;
}

.dispatch-incident-list,
.dispatch-incident-detail {
    background: #111111;
    border: 2px solid #ff0000;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.incident-count {
    color: #ff4444;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
}

.incident-list-container,
.incident-detail-container {
    flex: 1;
    overflow-y: auto;
    padding: 5px;
}

/* Incident List Headers */
.incident-list-header {
    border-bottom: 2px solid #ff0000;
    margin-bottom: 5px;
    padding-bottom: 5px;
}

.incident-list-body {
    max-height: calc(100% - 40px);
    overflow-y: auto;
}

/* Incident List Items */
.dispatch-incident-item {
    border: 1px solid #ff4444;
    margin-bottom: 2px;
    padding: 3px 5px;
    cursor: pointer;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dispatch-incident-item:hover {
    background: rgba(255, 68, 68, 0.1);
    border-color: #ff6666;
}

.dispatch-incident-item.selected {
    background: rgba(255, 68, 68, 0.2);
    border-color: #ff0000;
}

.dispatch-incident-item.new {
    border-color: #00ff00;
    color: #00ff00;
    animation: pulse 2s infinite;
}

.incident-row {
    padding: 5px;
    border-bottom: 1px solid #333;
    display: grid;
    grid-template-columns: 50px 100px 80px 60px 80px 80px 1fr 200px;
    gap: 10px;
    align-items: center;
    font-size: 11px;
}

.incident-call-number {
    font-weight: bold;
    color: #ff6666;
}

.incident-type {
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.incident-priority {
    font-size: 10px;
    text-align: center;
    text-transform: uppercase;
}

.incident-priority.high {
    color: #ff0000;
    font-weight: bold;
}

.incident-priority.medium {
    color: #ffaa00;
}

.incident-priority.low {
    color: #00aa00;
}

.incident-status {
    font-size: 10px;
    text-transform: uppercase;
}

.incident-time {
    font-size: 10px;
    color: #ccc;
    font-family: monospace;
}

.incident-detail-tabs {
    display: flex;
    border-bottom: 2px solid #ff0000;
    margin-bottom: 5px;
    background: #000000;
}

/* Removed duplicate - using main definition below */

.incident-detail-content {
    padding: 0;
    color: #cccccc;
    font-size: 11px;
    line-height: 1.4;
    max-height: 800px;
    overflow-y: auto;
}

.incident-overview {
    margin-bottom: 0px;
}

.incident-overview .detail-row {
    display: flex;
    margin-bottom: 5px;
    font-size: 11px;
}

.incident-overview .detail-label {
    font-weight: bold;
    min-width: 80px;
    color: #ff6666;
}

.incident-overview .detail-value {
    color: #cccccc;
    flex: 1;
}

.incident-map-container {
    width: 100%;
    height: 200px;
    border: 1px solid #ff4444;
    margin: 10px 0;
    background: #000000;
}

/* Responsive Design for Incidents */
@media (max-width: 1200px) {
    .incident-row {
        grid-template-columns: 45px 90px 70px 50px 70px 70px 1fr 150px;
        gap: 8px;
        font-size: 10px;
    }

    .dispatch-incident-item {
        font-size: 10px;
        padding: 2px 4px;
    }

    /* Optimize incident detail for smaller screens */
    .incident-detail-container {
        padding: 5px;
    }

    .incident-tabs {
        flex-wrap: wrap;
    }

    .incident-tab {
        flex: 1;
        min-width: 80px;
        font-size: 9px;
        padding: 5px 5px;
    }
}

@media (max-width: 900px) {
    .incident-row {
        grid-template-columns: 40px 80px 60px 1fr 120px;
        gap: 6px;
        font-size: 9px;
    }

    .dispatch-incident-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 4px 5px;
        font-size: 9px;
    }

    .incident-map-container {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .incident-map-container {
        width: 100%;
        height: 200px;
    }

    .incident-map-container .gm-style {
        font-size: 11px;
    }

    .incident-map-container .gm-style-iw {
        max-width: 200px;
    }
}

@media (max-width: 600px) {
    .content-area.incident-creation-content {
        padding: 5px;
    }

    .incident-row {
        grid-template-columns: 1fr;
        gap: 2px;
        font-size: 10px;
        padding: 6px;
        border: 1px solid #333;
        margin-bottom: 2px;
    }

    .dispatch-incident-item {
        font-size: 10px;
        padding: 4px;
    }

    .incidents-main {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .incidents-header {
        padding: 4px;
    }

    .incidents-header h2 {
        font-size: 16px;
    }

    .incident-detail-container {
        border-top: 2px solid #ff0000;
    }

    .incident-tabs {
        flex-wrap: wrap;
    }

    .incident-tab {
        flex: 1;
        min-width: 70px;
        font-size: 8px;
    }

    .incident-map-container {
        height: 180px;
    }
}

@media (max-width: 480px) {
    .incidents-section {
        padding: 5px;
    }

    .incidents-section {
        padding: 4px;
    }
}

/* Incident Links */
.incident-links {
    margin: 5px 0;
}

.incident-links h4, .incident-links h5 {
    color: #ff6666;
    margin-bottom: 5px;
    font-size: 11px;
}

/* Incident Log */
.incident-log {
    margin: 5px 0;
}

.log-type.incident-created {
    background: #2a5934;
}

/* Incident Status */
.incident-status {
    margin: 5px 0;
}

.incident-status h4 {
    color: #ff6666;
    margin-bottom: 5px;
    font-size: 11px;
}

.incident-item {
    border: 1px solid #ff4444;
    padding: 5px;
    margin-bottom: 3px;
    cursor: pointer;
}

.incident-item:hover {
    background: rgba(255, 68, 68, 0.1);
}

.incident-type {
    font-weight: bold;
    color: #ff6666;
    font-size: 10px;
    text-transform: uppercase;
}

.incident-assigned {
    color: #cccccc;
    font-size: 10px;
    margin: 2px 0;
}

.incident-priority {
    font-size: 9px;
    padding: 1px 4px;
    border-radius: 2px;
    text-transform: uppercase;
}

.incident-priority.priority-high {
    background: #ff0000;
    color: #000000;
}

.incident-priority.priority-medium {
    background: #ffaa00;
    color: #000000;
}

.incident-priority.priority-low {
    background: #00aa00;
    color: #000000;
}

/* Focus states for accessibility */
.dispatch-incident-item:focus {
    outline: 2px solid #ff6666;
    outline-offset: 1px;
}

.incident-detail-tab:focus {
    outline: 2px solid #ff6666;
    outline-offset: 1px;
}

/* High contrast mode support */
.dispatch-incident-item[aria-selected="true"] {
    border-color: #ffff00;
    background: rgba(255, 255, 0, 0.1);
}

/* Animation for new incidents */
@keyframes pulse {
    0%, 100% { 
        border-color: #00ff00; 
        box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.4); 
    }
    50% { 
        border-color: #00aa00;
        box-shadow: 0 0 0 4px rgba(0, 255, 0, 0.1); 
    }
}

.dispatch-incident-item.new-incident {
    animation: pulse 2s infinite;
}

/* Improved scrollbar styling for dispatch areas */
.incident-list-body::-webkit-scrollbar,
.incident-detail-container::-webkit-scrollbar {
    width: 8px;
}

.incident-list-body::-webkit-scrollbar-track,
.incident-detail-container::-webkit-scrollbar-track {
    background: #000000;
}

.incident-list-body::-webkit-scrollbar-thumb,
.incident-detail-container::-webkit-scrollbar-thumb {
    background: #ff0000;
    border-radius: 4px;
}

.incident-list-body::-webkit-scrollbar-thumb:hover,
.incident-detail-container::-webkit-scrollbar-thumb:hover {
    background: #ff4444;
}

/* Incident Form Container */
.incident-form-container {
    height: 100%;
    background: #000000;
}

.incident-form-scroll {
    height: 100%;
    overflow-y: auto;
    padding: 0;
}

/* Override content-area for incident creation */
.content-area.incident-creation-content {
    padding: 0;
    overflow: hidden;
}

/* Override content-area for incidents tab */
.content-area.incidents-content {
    overflow: hidden;
    padding: 0;
}

/* Clean Incidents Interface */
.incidents-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    background: #000000;
    color: #ff0000;
}

.incidents-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 5px;
    border-bottom: 2px solid #ff0000;
    background: #000000;
    flex-shrink: 0;
}

.incidents-header h2 {
    margin: 0;
    color: #ff0000;
    font-size: 18px;
}

.incidents-status {
    font-size: 12px;
    color: #ff4444;
}

.incidents-main {
    flex: 1;
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 8px;
    padding: 0;
    overflow: hidden;
    min-height: 0;
}

.incidents-list-section {
    display: flex;
    flex-direction: column;
    border: 1px solid #ff4444;
    background: #000000;
    height: 100%;
    overflow: hidden;
}

.incidents-list-header {
    padding: 5px;
    border-bottom: 1px solid #ff4444;
    background: #000000;
    flex-shrink: 0;
}

.incidents-list-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.incidents-list {
    flex: 1;
    overflow-y: auto;
    padding: 5px;
}

.incidents-list::-webkit-scrollbar {
    width: 6px;
}

.incidents-list::-webkit-scrollbar-track {
    background: #000000;
}

.incidents-list::-webkit-scrollbar-thumb {
    background: #ff4444;
    border-radius: 3px;
}

.incidents-list::-webkit-scrollbar-thumb:hover {
    background: #ff0000;
}

.list-filters {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
    overflow: hidden;
}

.incident-item {
    border: 1px solid #ff4444;
    padding: 5px;
    margin-bottom: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(0, 0, 0, 0.8);
}

.incident-item:hover {
    background: rgba(255, 68, 68, 0.1);
    border-color: #ff6666;
    transform: translateY(-1px);
}

.incident-item.selected {
    background: rgba(255, 68, 68, 0.2);
    border-color: #ff0000;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

.incident-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.incident-number {
    font-weight: bold;
    color: #ff0000;
    font-size: 12px;
}

.incident-item.selected .incident-number {
    color: #ffffff;
}

/* Draft incident styles */
.incident-item.draft-incident {
    border-color: #ffaa00;
    background: rgba(255, 170, 0, 0.05);
}

.incident-item.draft-incident:hover {
    background: rgba(255, 170, 0, 0.1);
    border-color: #ffcc00;
}

.incident-item.draft-incident.selected {
    background: rgba(255, 170, 0, 0.2);
    border-color: #ffaa00;
    box-shadow: 0 0 5px rgba(255, 170, 0, 0.3);
}

.draft-indicator {
    font-size: 9px;
    padding: 2px 5px;
    background: #ffaa00;
    color: #000000;
    border-radius: 3px;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 5px;
}

.incident-priority {
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    text-transform: uppercase;
}

.incident-priority.high {
    background: #ff0000;
    color: #000000;
}

.incident-priority.medium {
    background: #ffaa00;
    color: #000000;
}

.incident-priority.low {
    background: #00aa00;
    color: #000000;
}

.incident-type {
    color: #ff4444;
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 4px;
}

.incident-location {
    color: #888888;
    font-size: 10px;
}

.incident-details-section {
    border: 1px solid #ff4444;
    background: #000000;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

.no-selection {
    text-align: center;
    color: #ff4444;
    padding: 50px 20px;
}

.incident-details {
    flex: 1;
    overflow: hidden;
}

.incident-details h3 {
    color: #ff0000;
    margin: 0 0 5px 0;
    padding: 5px 5px 0;
    font-size: 16px;
}

.incident-actions {
    padding: 2px 2px;
    border-top: 1px solid #ff4444;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Use main app action-button styling */

.incident-detail-tabs {
    display: flex;
    background: #000000;
    border-bottom: 2px solid #ff0000;
    overflow-x: auto;
}

/* Incident Detail Tabs - consistent with main app terminal styling */
.incident-detail-tab {
    background: #000000;
    color: #ff0000;
    border: none;
    padding: 5px 5px;
    cursor: pointer;
    font-size: 11px;
    text-transform: uppercase;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    border-right: 1px solid #ff0000;
    white-space: nowrap;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.incident-detail-tab:hover {
    background: rgba(255, 0, 0, 0.1);
    color: #ff4444;
}

.incident-detail-tab:last-child {
    border-right: none;
}

.incident-detail-tab.active {
    background: #ff0000;
    color: #000000;
    font-weight: bold;
}

.incident-detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.detail-content-scroll {
    height: 100%;
    overflow-y: auto;
}

/* Tab Content Styling */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Incident Detail Tab Panes */
.tab-pane {
    display: none;
    padding: 10px;
}

.tab-pane.active {
    display: block;
}

.detail-tab-content {
    color: #cccccc;
    font-size: 12px;
    line-height: 1.4;
    border: 1px solid #333333;
    padding: 5px;
    background: rgba(0, 0, 0, 0.3);
}

.detail-section {
    margin-bottom: 5px;
    border-bottom: 1px solid #ff0000;
    padding-bottom: 5px;
}

.detail-section:last-child {
    border-bottom: 1px solid #ff0000;
    margin-bottom: 0;
}

.detail-section h4 {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
    margin: 0 0 5px 0;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    text-transform: uppercase;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}

.detail-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.detail-item label {
    color: #ff4444;
    font-weight: bold;
    min-width: 120px;
    font-size: 11px;
    text-transform: uppercase;
}

.detail-value {
    color: #cccccc;
    flex: 1;
}

.description-display,
.observations-display,
.actions-display,
.followup-display,
.notes-display,
.location-notes-display {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #333333;
    padding: 12px;
    margin-top: 8px;
    color: #cccccc;
    font-size: 11px;
    line-height: 1.4;
    white-space: pre-wrap;
}

/* Address styling */
.address-detail {
    margin-bottom: 15px;
}

.address-detail label {
    color: #ff4444;
    font-weight: bold;
    font-size: 11px;
    text-transform: uppercase;
    display: block;
    margin-bottom: 5px;
}

.address-value {
    color: #cccccc;
    font-size: 12px;
    line-height: 1.3;
}

.coordinates-detail {
    margin-bottom: 15px;
}

.coordinates-detail label {
    color: #ff4444;
    font-weight: bold;
    font-size: 11px;
    text-transform: uppercase;
    display: block;
    margin-bottom: 5px;
}

.coordinates-value {
    color: #cccccc;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 11px;
}

.no-location {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 20px;
    border: 1px dashed #444444;
}

/* Service status indicators */
.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    margin-bottom: 5px;
    border: 1px solid #333333;
}

.service-item.active {
    border-color: #ff4444;
    background: rgba(255, 68, 68, 0.1);
}

.service-label {
    color: #ff4444;
    font-weight: bold;
    font-size: 11px;
}

.service-status {
    color: #cccccc;
    font-size: 11px;
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 5px;
    margin-bottom: 5px;
}

/* Risk indicators */
.risk-indicator {
    padding: 4px 5px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 10px;
}

.risk-indicator.risk-low {
    background: #00aa00;
    color: #000000;
}

.risk-indicator.risk-medium {
    background: #ffaa00;
    color: #000000;
}

.risk-indicator.risk-high {
    background: #ff0000;
    color: #000000;
}

/* Files list styling */
.files-list {
    min-height: 100px;
    border: 1px dashed #333333;
    padding: 20px;
    text-align: center;
    color: #888888;
    font-style: italic;
}

.files-list:after {
    content: 'No files attached';
}

/* Confidential info styling */
.confidential-display {
    background: rgba(255, 0, 0, 0.1);
    border: 2px solid #ff0000;
    padding: 15px;
    margin-top: 8px;
    color: #ffcccc;
    font-size: 11px;
    line-height: 1.4;
    position: relative;
}

.confidential-display::before {
    content: '⚠️ CONFIDENTIAL ⚠️';
    position: absolute;
    top: -8px;
    left: 15px;
    background: #000000;
    color: #ff0000;
    padding: 0 10px;
    font-weight: bold;
    font-size: 10px;
}

/* Additional content styling */
.no-services,
.no-followup,
.no-people {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 20px;
    border: 1px dashed #444444;
}

/* People display styling */
.people-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.person-item {
    border: 1px solid #333333;
    padding: 12px;
    background: rgba(255, 255, 255, 0.02);
}

.person-details {
    font-size: 11px;
    line-height: 1.4;
}

.person-details strong {
    color: #ff4444;
}

.injury-status {
    color: #ffaa00;
    font-weight: bold;
}

.person-description {
    color: #cccccc;
}

/* Service details styling */
.police-details,
.fire-details,
.ems-details,
.transport-info {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #333333;
}

/* Lists styling */
.services-list,
.resources-list,
.substance-list,
.environmental-list {
    color: #cccccc;
    font-size: 11px;
    margin-top: 5px;
}

.people-text {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #333333;
    padding: 12px;
    margin-top: 8px;
    color: #cccccc;
    font-size: 11px;
    line-height: 1.4;
}

.referrals-display,
.medical-notes-display {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #333333;
    padding: 5px;
    margin-top: 5px;
    color: #cccccc;
    font-size: 11px;
    line-height: 1.4;
}

/* Incident overview, log, links, status styles */
.incident-overview,
.incident-log,
.incident-links,
.incident-status {
    margin-bottom: 5px;
}

.incident-overview h4,
.incident-log h4,
.incident-links h4,
.incident-status h4 {
    color: #ff0000;
    margin-bottom: 5px;
    font-size: 14px;
}

/* Incident Search */
.incident-search-container {
    padding: 5px;
    background: #000000;
    border: 1px solid #ff4444;
    margin-bottom: 5px;
}

.incident-search-results {
    max-height: 400px;
    overflow-y: auto;
}

.search-result-incident {
    border: 1px solid #ff4444;
    padding: 5px;
    margin-bottom: 5px;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.8);
    transition: all 0.2s ease;
}

.search-result-incident:hover {
    background: rgba(255, 68, 68, 0.1);
    border-color: #ff6666;
}

.incident-summary {
    margin-bottom: 5px;
}

.incident-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.incident-number {
    font-weight: bold;
    color: #ff0000;
    font-size: 14px;
}

.incident-date {
    color: #888888;
    font-size: 11px;
}

.incident-priority {
    font-size: 10px;
    padding: 2px 2px;
    border-radius: 3px;
    text-transform: uppercase;
}

.incident-priority.high {
    background: #ff0000;
    color: #000000;
}

.incident-priority.medium {
    background: #ffaa00;
    color: #000000;
}

.incident-priority.low {
    background: #00aa00;
    color: #000000;
}

.incident-type {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 5px;
}

.incident-location {
    color: #888888;
    font-size: 11px;
    margin-bottom: 5px;
}

.incident-description {
    color: #cccccc;
    font-size: 11px;
    line-height: 1.4;
}

.incident-status {
    font-size: 10px;
    padding: 2px 2px;
    border-radius: 3px;
    text-transform: uppercase;
}

.incident-actions {
    margin-top: 5px;
    display: flex;
    gap: 5px;
}

/* Responsive adjustments for incidents */
@media (max-width: 1024px) {
    .incidents-main {
        grid-template-columns: 300px 1fr;
        gap: 5px;
        padding: 0;
    }
}

@media (max-width: 768px) {
    .incident-actions .action-button {
        font-size: 10px;
        padding: 2px 2px;
        flex: 1;
        min-width: 80px;
    }

    .incident-detail-tabs {
        overflow-x: auto;
    }

    .incident-detail-tab {
        font-size: 10px;
        padding: 5px 5px;
    }
}

@media (max-width: 600px) {
    .incidents-main {
        grid-template-columns: 1fr;
        gap: 5px;
    }

    .incidents-header {
        padding: 5px 5px;
    }

    .incident-detail-tabs {
        flex-wrap: wrap;
    }

    .incident-detail-tab {
        flex: 1;
        min-width: 80px;
        font-size: 9px;
    }
}

@media (max-width: 480px) {
    .incidents-main {
        padding: 0;
        gap: 5px;
    }

    .incidents-header {
        padding: 5px;
    }

    .incidents-actions {
        flex-wrap: wrap;
        gap: 5px;
    }
}

/* Incident Actions at bottom */
.incidents-actions {
    display: flex;
    gap: 5px;
    padding: 2px 2px;
    border-top: 1px solid #ff0000;
    background: #000000;
    flex-shrink: 0;
}

/* Remove duplicate action-button styles - inherit from main styles.css */

/* Incident Creation Form Styles */
.incident-creation-form .form-field {
    margin-bottom: 5px;
}

.incident-creation-form .form-field label {
    display: block;
    color: #ff4444;
    margin-bottom: 5px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.incident-creation-form .form-actions {
    display: flex;
    gap: 5px;
    padding: 5px 0;
    border-top: 1px solid #ff4444;
    margin-top: 5px;
}

.incident-creation-content .section-header {
    background: #000000;
    border-bottom: 2px solid #ff0000;
    padding: 5px 5px;
    margin-bottom: 0;
}

.incident-creation-form {
    background: #000000;
    padding: 20px;
}

.incident-creation-form .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 5px;
}

.incident-creation-form .form-group {
    flex: 1;
    min-width: 0;
}

.incident-creation-form .form-group.full-width {
    flex: none;
    width: 100%;
}

/* Incident Form Tabs */
.incident-form-tabs {
    display: flex;
    background: #000000;
    border-bottom: 2px solid #ff0000;
    overflow-x: auto;
}

.incident-tab {
    background: #000000;
    color: #ff4444;
    border: none;
    padding: 5px 5px;
    cursor: pointer;
    font-size: 11px;
    text-transform: uppercase;
    font-family: inherit;
    border-right: 1px solid #ff4444;
    white-space: nowrap;
    flex-shrink: 0;
}

.incident-tab:hover {
    background: rgba(255, 68, 68, 0.1);
    color: #ff6666;
}

.incident-tab.active {
    background: #ff4444;
    color: #000000;
    font-weight: bold;
}

/* Full width incident form layout */
.incident-creation-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #000000;
    color: #ff0000;
}

.incident-creation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 5px;
    border-bottom: 2px solid #ff0000;
    background: #000000;
    flex-shrink: 0;
}

.incident-creation-header h2 {
    margin: 0;
    color: #ff0000;
    font-size: 18px;
    text-transform: uppercase;
}

.incident-form-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.comprehensive-incident-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.incident-form-tabs {
    display: flex;
    background: #000000;
    border-bottom: 2px solid #ff0000;
    overflow-x: auto;
    flex-shrink: 0;
}

.incident-tab-back {
    margin-right: auto;
    border-right: 2px solid #ff0000;
}

.incident-form-title {
    display: flex;
    align-items: center;
    color: #ff0000;
    font-weight: bold;
    padding: 5px 5px;
    border-right: 2px solid #ff0000;
    background: #000000;
}

.incident-form-tab {
    background: #000000;
    color: #ff4444;
    border: none;
    padding: 5px 5px;
    cursor: pointer;
    font-size: 11px;
    text-transform: uppercase;
    font-family: inherit;
    border-right: 1px solid #ff4444;
    white-space: nowrap;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.incident-form-tab:hover {
    background: rgba(255, 68, 68, 0.1);
    color: #ff6666;
}

.incident-form-tab.active {
    background: #ff4444;
    color: #000000;
    font-weight: bold;
}

.incident-form-content {
    flex: 1;
    overflow: hidden;
    background: #000000;
}

.form-content-scroll {
    height: 100%;
    overflow-y: auto;
    padding: 5px;
}

.incident-form-actions {
    display: flex;
    gap: 10px;
    padding: 5px 5px;
    border-top: 2px solid #ff0000;
    background: #000000;
    flex-shrink: 0;
}

/* Use main app button styling for form actions */

/* Incident Detail Container */
.incident-detail-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #000000;
    color: #ff0000;
}

.incident-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 5px;
    border-bottom: 2px solid #ff0000;
    background: #000000;
    flex-shrink: 0;
}

.incident-detail-header h2 {
    margin: 0;
    color: #ff0000;
    font-size: 16px;
    text-transform: uppercase;
}

.header-status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.header-actions {
    display: flex;
    gap: 5px;
}

.incident-status {
    padding: 4px 5px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.incident-status.status-open { background: #ff6600; color: #000000; }
.incident-status.status-assigned { background: #0066ff; color: #ffffff; }
.incident-status.status-en_route { background: #ffff00; color: #000000; }
.incident-status.status-resolved { background: #00ff00; color: #000000; }
.incident-status.status-closed { background: #888888; color: #ffffff; }

.incident-detail-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.incident-detail-tabs {
    display: flex;
    background: #000000;
    border-bottom: 2px solid #ff0000;
    overflow-x: auto;
    flex-shrink: 0;
}

/* Removed duplicate - using main definition below */

.incident-detail-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* Enhanced People Card Styling */
.people-cards {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.person-card {
    border: 1px solid #ff4444;
    background: rgba(255, 68, 68, 0.05);
    padding: 12px;
    border-radius: 3px;
}

.person-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #333333;
}

.person-identity {
    flex: 1;
}

.person-name {
    margin-bottom: 4px;
}

.person-name strong {
    color: #ff0000;
    font-size: 13px;
}

.person-age {
    color: #cccccc;
    font-size: 11px;
    margin-left: 8px;
}

.person-type {
    margin-top: 2px;
}

.involvement-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 9px;
    font-weight: bold;
    text-transform: uppercase;
    border: 1px solid;
}

.involvement-badge.involvement-victim {
    background: #ff4444;
    color: #000000;
    border-color: #ff0000;
}

.involvement-badge.involvement-witness {
    background: #0066ff;
    color: #ffffff;
    border-color: #0044cc;
}

.involvement-badge.involvement-suspect {
    background: #ff6600;
    color: #000000;
    border-color: #ff4400;
}

.involvement-badge.involvement-complainant {
    background: #9900ff;
    color: #ffffff;
    border-color: #7700cc;
}

.involvement-badge.involvement-unknown,
.involvement-badge.involvement-other {
    background: #888888;
    color: #ffffff;
    border-color: #666666;
}

.medical-flag {
    color: #ff0000;
    font-size: 10px;
    font-weight: bold;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
}

.person-details {
    font-size: 11px;
    line-height: 1.4;
}

.detail-row {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 8px;
    padding: 4px 0;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.detail-row label {
    color: #ff4444;
    font-weight: bold;
    min-width: 140px;
    font-size: 10px;
    text-transform: uppercase;
    flex-shrink: 0;
}

.detail-row span,
.detail-row div {
    color: #cccccc;
    flex: 1;
    font-size: 11px;
}

.injury-status {
    font-weight: bold;
}

.injury-status.status-minor {
    color: #ffaa00;
}

.injury-status.status-serious {
    color: #ff4444;
}

.injury-status.status-critical {
    color: #ff0000;
}

.injury-status.status-none {
    color: #00aa00;
}

.medical-conditions,
.injuries-sustained,
.treatment-provided {
    background: rgba(255, 255, 255, 0.05);
    padding: 6px;
    border-left: 2px solid #ff4444;
    margin-top: 4px;
}

.transport-status {
    font-weight: bold;
}

.hospital-dest,
.decline-reason {
    color: #888888;
    font-style: italic;
}

.person-description,
.involvement-notes,
.medical-notes {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid #333333;
    padding: 8px;
    margin-top: 4px;
    line-height: 1.4;
    white-space: pre-wrap;
}

.medical-notes {
    border-left: 3px solid #ff4444;
    background: rgba(255, 68, 68, 0.05);
}

/* Medical Information Collapsible Section */
.medical-section {
    background: rgba(255, 68, 68, 0.03);
    border: 1px solid #ff4444;
    border-radius: 3px;
    margin: 8px 0;
}

.medical-toggle {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    background: rgba(255, 68, 68, 0.05);
    border-bottom: 1px solid #ff4444;
    transition: background-color 0.2s ease;
}

.medical-toggle:hover {
    background: rgba(255, 68, 68, 0.1);
}

.toggle-icon {
    color: #ff4444;
    font-size: 10px;
    margin-right: 12px;
    transition: transform 0.2s ease;
    font-family: monospace;
    display: inline-block;
    width: 12px;
    text-align: center;
}

.medical-toggle.collapsed .toggle-icon {
    /* No rotation needed - using different icons now */
}

.medical-toggle label {
    color: #ff4444;
    font-weight: bold;
    font-size: 11px;
    margin: 0;
    cursor: pointer;
    flex: 1;
}

.medical-summary {
    color: #888888;
    font-size: 10px;
    font-style: italic;
}

.medical-details {
    padding: 0 12px;
    max-height: 1000px;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.medical-details.collapsed {
    max-height: 0;
    padding: 0 12px;
}

.medical-field {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin: 8px 0;
    padding: 4px 0;
    border-bottom: 1px dotted #333333;
}

.medical-field:last-child {
    border-bottom: none;
    margin-bottom: 8px;
}

.medical-field.indent {
    margin-left: 20px;
    padding-left: 12px;
    border-left: 2px solid #666666;
    border-bottom: 1px dotted #444444;
}

.field-label {
    color: #ff6666;
    font-weight: bold;
    font-size: 10px;
    text-transform: capitalize;
    min-width: 160px;
    flex-shrink: 0;
}

.field-value {
    color: #cccccc;
    font-size: 11px;
    flex: 1;
}

.field-value.multiline {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid #333333;
    padding: 6px;
    border-radius: 2px;
    line-height: 1.4;
    white-space: pre-wrap;
    margin-top: 2px;
}

/* Override injury status colors in medical section */
.medical-field .injury-status.status-minor {
    color: #ffaa00;
    font-weight: bold;
}

.medical-field .injury-status.status-serious {
    color: #ff4444;
    font-weight: bold;
}

.medical-field .injury-status.status-critical {
    color: #ff0000;
    font-weight: bold;
}

.medical-field .injury-status.status-none {
    color: #00aa00;
    font-weight: bold;
}

/* Responsive adjustments for person cards */
@media (max-width: 768px) {
    .person-header {
        flex-direction: column;
        gap: 8px;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 4px;
    }
    
    .detail-row label {
        min-width: auto;
        margin-bottom: 2px;
    }
    
    .medical-field {
        flex-direction: column;
        gap: 4px;
    }
    
    .field-label {
        min-width: auto;
        margin-bottom: 2px;
    }
    
    .medical-field.indent {
        margin-left: 10px;
        padding-left: 8px;
    }
}

/* Narrative Entry Styles */
.narrative-entry {
    border: 1px solid #333;
    margin-bottom: 15px;
    background: #111111;
    border-radius: 3px;
}

.narrative-entry-header {
    background: #222222;
    padding: 8px 12px;
    border-bottom: 1px solid #333;
}

.entry-header-line {
    display: flex;
    align-items: center;
    gap: 15px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 11px;
    margin-bottom: 6px;
}

.entry-type-badge {
    background: #ff0000;
    color: #000000;
    padding: 2px 6px;
    border-radius: 2px;
    font-weight: bold;
    font-size: 10px;
    min-width: 80px;
    text-align: center;
}

.entry-user-info {
    color: #00ff00;
    flex: 1;
}

.entry-user-info strong {
    color: #ffffff;
}

/* Dual timestamp display */
.entry-timestamps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 10px;
    gap: 20px;
}

.event-time {
    color: #ffaa00;
    font-weight: normal;
}

.event-time strong {
    color: #ffffff;
    font-weight: bold;
}

.created-time {
    color: #aaaaaa;
    font-style: italic;
}

.narrative-entry-content {
    padding: 12px;
    color: #ffffff;
    line-height: 1.4;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 11px;
    background: #000000;
}

.no-entries {
    text-align: center;
    padding: 20px;
    color: #888888;
    font-style: italic;
}

.no-entries p {
    margin: 8px 0;
}

/* Narrative Container */
.narrative-container {
    background: #000000;
    padding: 10px;
}

.narrative-header {
    margin-bottom: 15px;
    text-align: right;
}

.add-narrative-btn {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 6px 12px;
    cursor: pointer;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 10px;
    font-weight: bold;
}

.add-narrative-btn:hover {
    background: #ff3333;
}