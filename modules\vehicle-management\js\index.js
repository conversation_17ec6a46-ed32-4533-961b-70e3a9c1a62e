/**
 * Vehicle Management Module - Main Entry Point
 * Modular vehicle management system following the incident management pattern
 * Handles vehicles, license plates, and vehicle activities
 */

import { BaseManager } from '../../shared/base-manager.js';
import { VehicleListManager } from './vehicle-list-manager.js';
import { VehicleSearchManager } from './vehicle-search-manager.js';
import { VehicleCrudManager } from './vehicle-crud-manager.js';
import { VehicleDetailManager } from './vehicle-detail-manager.js';
import { VehicleActivitiesManager } from './vehicle-activities-manager.js';
import { VehicleCommandsFactory } from './vehicle-commands.js';

export class VehicleManagement extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement, app = null) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.app = app;

        // Initialize all sub-managers
        this.listManager = new VehicleListManager(dataManager, authManager, uiManager, uiUtilities);
        this.searchManager = new VehicleSearchManager(dataManager, authManager, uiManager, uiUtilities);
        this.crudManager = new VehicleCrudManager(dataManager, authManager, uiManager, uiUtilities, modalManagement);
        this.detailManager = new VehicleDetailManager(dataManager, authManager, uiManager, uiUtilities);
        this.activitiesManager = new VehicleActivitiesManager(dataManager, authManager, uiManager, uiUtilities);

        // Set up cross-manager communication
        this.setupManagerCommunication();

        // Initialize commands factory
        this.commandsFactory = new VehicleCommandsFactory(null, this);
    }

    setupManagerCommunication() {
        // Connect detail manager with activities manager
        this.detailManager.activitiesManager = this.activitiesManager;
        
        // Connect activities manager with detail updates
        this.activitiesManager.onActivitiesChanged = () => {
            this.triggerDetailUpdate();
        };

        // Connect CRUD operations with list updates
        this.crudManager.onVehicleChanged = () => {
            this.triggerListUpdate();
        };
    }

    // === LIST MANAGEMENT ===
    async loadVehiclesContent() {
        return await this.listManager.loadVehiclesContent();
    }

    async loadVehiclesData() {
        return await this.listManager.loadVehiclesData();
    }

    async refreshVehicles() {
        return await this.listManager.refreshVehicles();
    }

    async showVehicleListView(vehicles) {
        return await this.listManager.showVehicleListView(vehicles);
    }

    async showVehicleManagementView(vehicles) {
        // For now, delegate to app.js which has the full page rendering logic
        // TODO: Eventually move this logic to a dedicated PageManager
        return await this.app?.showVehicleManagementView(vehicles);
    }

    // === SEARCH MANAGEMENT ===
    async searchVehicles(query) {
        return await this.searchManager.searchVehicles(query);
    }

    async showVehicleSearchInterface() {
        return await this.searchManager.showVehicleSearchInterface();
    }

    // === CRUD OPERATIONS ===
    async addVehicle(vehicleData) {
        return await this.crudManager.addVehicle(vehicleData);
    }

    async updateVehicle(vehicleId, updateData) {
        return await this.crudManager.updateVehicle(vehicleId, updateData);
    }

    async deleteVehicle(vehicleId) {
        return await this.crudManager.deleteVehicle(vehicleId);
    }

    async showAddVehicleForm() {
        return await this.crudManager.showAddVehicleForm();
    }

    async showEditVehicleForm(vehicleId) {
        return await this.crudManager.showEditVehicleForm(vehicleId);
    }

    // === DETAIL MANAGEMENT ===
    async showVehicleDetail(vehicleId) {
        return await this.detailManager.showVehicleDetail(vehicleId);
    }

    async loadVehicleDetail(vehicleId) {
        return await this.detailManager.loadVehicleDetail(vehicleId);
    }

    // === ACTIVITIES MANAGEMENT ===
    async loadVehicleActivities(vehicleId) {
        return await this.activitiesManager.loadVehicleActivities(vehicleId);
    }

    async addVehicleActivity(vehicleId, activityData) {
        return await this.activitiesManager.addVehicleActivity(vehicleId, activityData);
    }

    async showAddActivityForm(vehicleId) {
        return await this.activitiesManager.showAddActivityForm(vehicleId);
    }

    // === INTEGRATED FUNCTIONALITY ===
    
    /**
     * Complete vehicle creation workflow
     */
    async createVehicleWorkflow() {
        try {
            return await this.showAddVehicleForm();
        } catch (error) {
            console.error('Error in vehicle creation workflow:', error);
            throw error;
        }
    }

    /**
     * Complete vehicle search workflow
     */
    async searchVehicleWorkflow() {
        try {
            return await this.showVehicleSearchInterface();
        } catch (error) {
            console.error('Error in vehicle search workflow:', error);
            throw error;
        }
    }

    /**
     * Complete vehicle management workflow (full page view)
     */
    async manageVehiclesWorkflow() {
        try {
            const vehicles = await this.loadVehiclesData();
            return await this.showVehicleManagementView(vehicles);
        } catch (error) {
            console.error('Error in vehicle management workflow:', error);
            throw error;
        }
    }

    /**
     * Complete vehicle list workflow (modal view)
     */
    async listVehicleWorkflow() {
        try {
            const vehicles = await this.loadVehiclesData();
            return await this.showVehicleListView(vehicles);
        } catch (error) {
            console.error('Error in vehicle list workflow:', error);
            throw error;
        }
    }

    /**
     * Complete vehicle detail workflow
     */
    async viewVehicleWorkflow(vehicleId) {
        try {
            return await this.showVehicleDetail(vehicleId);
        } catch (error) {
            console.error('Error in vehicle detail workflow:', error);
            throw error;
        }
    }

    // === UTILITY METHODS ===
    
    triggerDetailUpdate() {
        // Emit event for detail updates
        const event = new CustomEvent('vehicleDetailUpdated', {
            detail: { timestamp: new Date() }
        });
        window.dispatchEvent(event);
    }

    triggerListUpdate() {
        // Emit event for list updates
        const event = new CustomEvent('vehicleListUpdated', {
            detail: { timestamp: new Date() }
        });
        window.dispatchEvent(event);
    }

    // === COMMAND INTEGRATION ===
    
    /**
     * Get vehicle commands for registration with command system
     */
    getCommands(commandManager) {
        // Update the factory with the actual command manager
        this.commandsFactory = new VehicleCommandsFactory(commandManager, this);
        return this.commandsFactory.createCommands();
    }

    // === CLEANUP ===
    
    cleanup() {
        console.log('Cleaning up vehicle management system...');
        
        // Cleanup all managers
        if (this.listManager.cleanup) this.listManager.cleanup();
        if (this.searchManager.cleanup) this.searchManager.cleanup();
        if (this.crudManager.cleanup) this.crudManager.cleanup();
        if (this.detailManager.cleanup) this.detailManager.cleanup();
        if (this.activitiesManager.cleanup) this.activitiesManager.cleanup();
    }

    // === STATUS AND DEBUGGING ===
    
    getSystemStatus() {
        return {
            managers: {
                list: !!this.listManager,
                search: !!this.searchManager,
                crud: !!this.crudManager,
                detail: !!this.detailManager,
                activities: !!this.activitiesManager
            }
        };
    }
}

// Export individual managers for direct access if needed
export {
    VehicleListManager,
    VehicleSearchManager,
    VehicleCrudManager,
    VehicleDetailManager,
    VehicleActivitiesManager,
    VehicleCommandsFactory
};
