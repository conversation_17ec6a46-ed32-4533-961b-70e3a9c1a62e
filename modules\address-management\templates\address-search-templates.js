export const addressSearchTemplates = {
    searchModal: () => {
        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔍 SEARCH ADDRESSES</h3>
                    <button class="close-btn" data-action="close-modal">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <label>Street Address:</label>
                            <input type="text" id="search-street" placeholder="Enter street address...">
                        </div>
                        <div class="form-row">
                            <label>City:</label>
                            <input type="text" id="search-city" placeholder="Enter city...">
                        </div>
                        <div class="form-row">
                            <label>Province:</label>
                            <input type="text" id="search-province" placeholder="Enter province...">
                        </div>
                        <div class="form-row">
                            <label>Postal Code:</label>
                            <input type="text" id="search-postal" placeholder="Enter postal code...">
                        </div>
                        <div class="form-actions">
                            <button id="execute-address-search">Search</button>
                            <button id="clear-address-search">Clear</button>
                        </div>
                    </div>
                    <div id="address-search-results" class="search-results"></div>
                </div>
            </div>
        `;
    },

    searchResults: (addresses) => {
        if (!addresses || addresses.length === 0) {
            return `
                <div class="no-results">
                    <p>No addresses found matching your search criteria.</p>
                </div>
            `;
        }

        return `
            <div class="results-header">
                <h4>Search Results (${addresses.length} found)</h4>
            </div>
            <div class="results-grid">
                ${addresses.map(address => addressSearchTemplates.addressCard(address)).join('')}
            </div>
        `;
    },

    addressCard: (address) => {
        const fullAddress = [
            address.street_address,
            address.city,
            address.province,
            address.postal_code
        ].filter(Boolean).join(', ');

        return `
            <div class="result-card" data-address-id="${address.id}">
                <div class="card-header">
                    <strong>🏠 ${address.address_type || 'Address'}</strong>
                </div>
                <div class="card-body">
                    <div class="address-line">${fullAddress}</div>
                    ${address.unit_number ? `<div class="unit-info">Unit: ${address.unit_number}</div>` : ''}
                    ${address.notes ? `<div class="notes">${address.notes}</div>` : ''}
                    <div class="metadata">
                        Created: ${new Date(address.created_at).toLocaleDateString()}
                        ${address.created_by ? ` by ${address.created_by}` : ''}
                    </div>
                </div>
                <div class="card-actions">
                    <button data-action="view-address-details" data-address-id="${address.id}">View Details</button>
                    <button data-action="edit-address" data-address-id="${address.id}">Edit</button>
                </div>
            </div>
        `;
    },

    addressDetailModal: (address) => {
        const fullAddress = [
            address.street_address,
            address.city,
            address.province,
            address.postal_code
        ].filter(Boolean).join(', ');

        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🏠 ADDRESS DETAILS</h3>
                    <button class="close-btn" data-action="close-modal">×</button>
                </div>
                <div class="modal-body">
                    <div class="detail-section">
                        <h4>Address Information</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Type:</label>
                                <span>${address.address_type || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Street Address:</label>
                                <span>${address.street_address || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Unit Number:</label>
                                <span>${address.unit_number || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>City:</label>
                                <span>${address.city || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Province:</label>
                                <span>${address.province || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Postal Code:</label>
                                <span>${address.postal_code || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Country:</label>
                                <span>${address.country || 'Not specified'}</span>
                            </div>
                            ${address.notes ? `
                            <div class="detail-item full-width">
                                <label>Notes:</label>
                                <span>${address.notes}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="detail-section">
                        <h4>Record Information</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Created:</label>
                                <span>${new Date(address.created_at).toLocaleString()}</span>
                            </div>
                            <div class="detail-item">
                                <label>Created By:</label>
                                <span>${address.created_by || 'Unknown'}</span>
                            </div>
                            ${address.updated_at ? `
                            <div class="detail-item">
                                <label>Last Updated:</label>
                                <span>${new Date(address.updated_at).toLocaleString()}</span>
                            </div>
                            ` : ''}
                            ${address.updated_by ? `
                            <div class="detail-item">
                                <label>Updated By:</label>
                                <span>${address.updated_by}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button data-action="edit-address" data-address-id="${address.id}">Edit Address</button>
                        <button data-action="close-modal">Close</button>
                    </div>
                </div>
            </div>
        `;
    }
};