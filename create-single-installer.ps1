# S.T.E.V.I Retro Single File Installer Creator
# Creates a single executable installer with embedded application files

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Single File Installer Creator" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if packaged app exists
$packagedPath = "dist\S.T.E.V.I Retro-win32-x64"
if (-not (Test-Path $packagedPath)) {
    Write-Host "ERROR: Packaged application not found at $packagedPath" -ForegroundColor Red
    Write-Host "Please run electron-packager first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found packaged application at: $packagedPath" -ForegroundColor Green

# Get version from package.json
$packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
$version = $packageJson.version

Write-Host "Creating single-file installer for version: $version" -ForegroundColor Yellow
Write-Host ""

# Create the installer script with embedded base64 data
Write-Host "Creating self-contained installer..." -ForegroundColor Yellow

# First, let's create a compressed archive in memory and convert to base64
Write-Host "Compressing application files..." -ForegroundColor Yellow

# Create a temporary zip file (we'll embed this)
$tempZip = [System.IO.Path]::GetTempFileName() + ".zip"
try {
    # Use .NET compression instead of PowerShell Compress-Archive to avoid file locks
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::CreateFromDirectory($packagedPath, $tempZip, [System.IO.Compression.CompressionLevel]::Optimal, $false)
    
    # Convert to base64
    $zipBytes = [System.IO.File]::ReadAllBytes($tempZip)
    $base64Data = [System.Convert]::ToBase64String($zipBytes)
    
    Write-Host "Application files compressed successfully" -ForegroundColor Green
    Write-Host "Compressed size: $([math]::Round($zipBytes.Length / 1MB, 2)) MB" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR: Failed to compress application files: $($_.Exception.Message)" -ForegroundColor Red
    if (Test-Path $tempZip) { Remove-Item $tempZip -Force }
    Read-Host "Press Enter to exit"
    exit 1
}

# Create the self-contained installer script
$installerContent = @"
# S.T.E.V.I Retro Self-Contained Installer v$version
# This script contains the embedded application files and provides install/uninstall/repair functionality

param(
    [string]`$Mode = "install",
    [string]`$InstallPath = "",
    [switch]`$Silent = `$false
)

# Embedded application data (base64 encoded)
`$EmbeddedAppData = @'
$base64Data
'@

function Write-Log {
    param([string]`$Message, [string]`$Color = "White")
    if (-not `$Silent) {
        Write-Host `$Message -ForegroundColor `$Color
    }
}

function Show-Header {
    if (-not `$Silent) {
        Write-Host "========================================" -ForegroundColor Cyan
        Write-Host "S.T.E.V.I Retro Installer v$version" -ForegroundColor Cyan
        Write-Host "========================================" -ForegroundColor Cyan
        Write-Host ""
    }
}

function Test-Administrator {
    return ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
}

function Get-InstallPath {
    param([string]`$ProvidedPath)
    
    if ([string]::IsNullOrWhiteSpace(`$ProvidedPath)) {
        `$isAdmin = Test-Administrator
        `$defaultPath = if (`$isAdmin) { "`$env:ProgramFiles\S.T.E.V.I Retro" } else { "`$env:LOCALAPPDATA\S.T.E.V.I Retro" }
        
        if (-not `$Silent) {
            Write-Host "Default installation path: `$defaultPath" -ForegroundColor Yellow
            `$userPath = Read-Host "Enter installation path (or press Enter for default)"
            if (-not [string]::IsNullOrWhiteSpace(`$userPath)) {
                return `$userPath
            }
        }
        return `$defaultPath
    }
    return `$ProvidedPath
}

function Install-Application {
    param([string]`$InstallPath)
    
    Write-Log "Installing S.T.E.V.I Retro to: `$InstallPath" "Cyan"
    
    # Create installation directory
    try {
        if (Test-Path `$InstallPath) {
            Write-Log "Removing existing installation..." "Yellow"
            Remove-Item -Path `$InstallPath -Recurse -Force
        }
        
        New-Item -Path `$InstallPath -ItemType Directory -Force | Out-Null
        Write-Log "Installation directory created" "Green"
    } catch {
        Write-Log "Failed to create installation directory: `$(`$_.Exception.Message)" "Red"
        return `$false
    }
    
    # Extract embedded application
    try {
        Write-Log "Extracting application files..." "Yellow"
        
        `$zipBytes = [System.Convert]::FromBase64String(`$EmbeddedAppData)
        `$tempZip = [System.IO.Path]::GetTempFileName() + ".zip"
        [System.IO.File]::WriteAllBytes(`$tempZip, `$zipBytes)
        
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        [System.IO.Compression.ZipFile]::ExtractToDirectory(`$tempZip, `$InstallPath)
        
        Remove-Item `$tempZip -Force
        Write-Log "Application files extracted successfully" "Green"
    } catch {
        Write-Log "Failed to extract application files: `$(`$_.Exception.Message)" "Red"
        return `$false
    }
    
    # Create shortcuts
    try {
        Write-Log "Creating shortcuts..." "Yellow"
        `$WshShell = New-Object -comObject WScript.Shell
        
        # Desktop shortcut
        `$desktopShortcut = `$WshShell.CreateShortcut("`$env:USERPROFILE\Desktop\S.T.E.V.I Retro.lnk")
        `$desktopShortcut.TargetPath = "`$InstallPath\S.T.E.V.I Retro.exe"
        `$desktopShortcut.WorkingDirectory = `$InstallPath
        `$desktopShortcut.Description = "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals"
        `$desktopShortcut.Save()
        
        # Start Menu shortcut
        `$startMenuPath = "`$env:APPDATA\Microsoft\Windows\Start Menu\Programs"
        `$startMenuShortcut = `$WshShell.CreateShortcut("`$startMenuPath\S.T.E.V.I Retro.lnk")
        `$startMenuShortcut.TargetPath = "`$InstallPath\S.T.E.V.I Retro.exe"
        `$startMenuShortcut.WorkingDirectory = `$InstallPath
        `$startMenuShortcut.Description = "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals"
        `$startMenuShortcut.Save()
        
        Write-Log "Shortcuts created successfully" "Green"
    } catch {
        Write-Log "Warning: Could not create shortcuts: `$(`$_.Exception.Message)" "Yellow"
    }
    
    # Register in Windows Programs list
    try {
        Write-Log "Registering application..." "Yellow"
        `$uninstallKey = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\S.T.E.V.I Retro"
        New-Item -Path `$uninstallKey -Force | Out-Null
        Set-ItemProperty -Path `$uninstallKey -Name "DisplayName" -Value "S.T.E.V.I Retro"
        Set-ItemProperty -Path `$uninstallKey -Name "DisplayVersion" -Value "$version"
        Set-ItemProperty -Path `$uninstallKey -Name "Publisher" -Value "I.H.A.R.C."
        Set-ItemProperty -Path `$uninstallKey -Name "InstallLocation" -Value `$InstallPath
        Set-ItemProperty -Path `$uninstallKey -Name "UninstallString" -Value "powershell.exe -ExecutionPolicy Bypass -File `"`$PSCommandPath`" -Mode uninstall"
        Set-ItemProperty -Path `$uninstallKey -Name "ModifyPath" -Value "powershell.exe -ExecutionPolicy Bypass -File `"`$PSCommandPath`" -Mode repair"
        Set-ItemProperty -Path `$uninstallKey -Name "NoModify" -Value 0
        Set-ItemProperty -Path `$uninstallKey -Name "NoRepair" -Value 0
        
        Write-Log "Application registered successfully" "Green"
    } catch {
        Write-Log "Warning: Could not register application: `$(`$_.Exception.Message)" "Yellow"
    }
    
    return `$true
}

function Uninstall-Application {
    Write-Log "Uninstalling S.T.E.V.I Retro..." "Yellow"
    
    # Get install path from registry
    try {
        `$uninstallKey = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\S.T.E.V.I Retro"
        `$installPath = Get-ItemProperty -Path `$uninstallKey -Name "InstallLocation" -ErrorAction Stop | Select-Object -ExpandProperty InstallLocation
        
        if (Test-Path `$installPath) {
            Remove-Item -Path `$installPath -Recurse -Force
            Write-Log "Application files removed" "Green"
        }
        
        # Remove shortcuts
        if (Test-Path "`$env:USERPROFILE\Desktop\S.T.E.V.I Retro.lnk") {
            Remove-Item "`$env:USERPROFILE\Desktop\S.T.E.V.I Retro.lnk" -Force
        }
        if (Test-Path "`$env:APPDATA\Microsoft\Windows\Start Menu\Programs\S.T.E.V.I Retro.lnk") {
            Remove-Item "`$env:APPDATA\Microsoft\Windows\Start Menu\Programs\S.T.E.V.I Retro.lnk" -Force
        }
        
        # Remove registry entry
        Remove-Item -Path `$uninstallKey -Force
        
        Write-Log "S.T.E.V.I Retro uninstalled successfully" "Green"
        return `$true
    } catch {
        Write-Log "Error during uninstall: `$(`$_.Exception.Message)" "Red"
        return `$false
    }
}

function Repair-Application {
    Write-Log "Repairing S.T.E.V.I Retro installation..." "Yellow"
    
    # Get install path from registry
    try {
        `$uninstallKey = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\S.T.E.V.I Retro"
        `$installPath = Get-ItemProperty -Path `$uninstallKey -Name "InstallLocation" -ErrorAction Stop | Select-Object -ExpandProperty InstallLocation
        
        # Reinstall to the same location
        return Install-Application `$installPath
    } catch {
        Write-Log "Could not find existing installation. Performing fresh install..." "Yellow"
        `$installPath = Get-InstallPath ""
        return Install-Application `$installPath
    }
}

# Main execution
Show-Header

switch (`$Mode.ToLower()) {
    "install" {
        `$installPath = Get-InstallPath `$InstallPath
        if (Install-Application `$installPath) {
            Write-Log ""
            Write-Log "========================================" "Green"
            Write-Log "Installation completed successfully!" "Green"
            Write-Log "========================================" "Green"
            Write-Log ""
            Write-Log "S.T.E.V.I Retro has been installed to: `$installPath" "Cyan"
            Write-Log ""
            Write-Log "You can now:" "Yellow"
            Write-Log "• Launch from Desktop shortcut" "Yellow"
            Write-Log "• Launch from Start Menu" "Yellow"
            Write-Log "• Uninstall from Windows Settings > Apps" "Yellow"
            Write-Log ""
            
            if (-not `$Silent) {
                `$launch = Read-Host "Would you like to launch S.T.E.V.I Retro now? (y/n)"
                if (`$launch -eq "y" -or `$launch -eq "Y") {
                    Start-Process "`$installPath\S.T.E.V.I Retro.exe"
                }
            }
        } else {
            Write-Log "Installation failed!" "Red"
            exit 1
        }
    }
    "uninstall" {
        if (Uninstall-Application) {
            Write-Log "Uninstall completed successfully!" "Green"
        } else {
            Write-Log "Uninstall failed!" "Red"
            exit 1
        }
    }
    "repair" {
        if (Repair-Application) {
            Write-Log "Repair completed successfully!" "Green"
        } else {
            Write-Log "Repair failed!" "Red"
            exit 1
        }
    }
    default {
        Write-Log "Invalid mode. Use: install, uninstall, or repair" "Red"
        exit 1
    }
}

if (-not `$Silent) {
    Write-Log ""
    Write-Log "Thank you for using S.T.E.V.I Retro!" "Cyan"
    Read-Host "Press Enter to exit"
}
"@

# Save the self-contained installer
$installerPath = "dist\S.T.E.V.I-Retro-Setup-v$version.ps1"
$installerContent | Out-File -FilePath $installerPath -Encoding UTF8

Write-Host "Self-contained installer created: $installerPath" -ForegroundColor Green

# Clean up temp file
if (Test-Path $tempZip) { Remove-Item $tempZip -Force }

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Single File Installer Created!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Installer file: $installerPath" -ForegroundColor Cyan
Write-Host "Size: $([math]::Round((Get-Item $installerPath).Length / 1MB, 2)) MB" -ForegroundColor Cyan
Write-Host ""
Write-Host "Usage:" -ForegroundColor Yellow
Write-Host "• Install: powershell -ExecutionPolicy Bypass -File `"$installerPath`"" -ForegroundColor Yellow
Write-Host "• Uninstall: powershell -ExecutionPolicy Bypass -File `"$installerPath`" -Mode uninstall" -ForegroundColor Yellow
Write-Host "• Repair: powershell -ExecutionPolicy Bypass -File `"$installerPath`" -Mode repair" -ForegroundColor Yellow
Write-Host "• Silent Install: powershell -ExecutionPolicy Bypass -File `"$installerPath`" -Silent" -ForegroundColor Yellow
Write-Host ""
Write-Host "This single file can be copied to any fresh Windows 10/11 system!" -ForegroundColor Green

Read-Host "Press Enter to exit"
