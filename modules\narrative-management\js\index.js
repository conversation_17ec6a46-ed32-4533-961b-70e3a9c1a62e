/**
 * Narrative Management Module
 * 
 * Centralized narrative entry management system for incident logging
 * and documentation. Provides rich text editing, entry persistence,
 * and real-time synchronization capabilities.
 * 
 * Extracted from app.js and narrative-editor.js as part of ongoing modularization.
 */

import { FeatureModuleInterface } from '../../shared/feature-module-interface.js';
import { NarrativeManager } from './narrative-manager.js';
import { NarrativeFormManager } from './narrative-form-manager.js';
import { NarrativeRenderManager } from './narrative-render-manager.js';

export class NarrativeManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager = null, uiUtilities = null, modalManagement = null, configManager = null) {
        super('NarrativeManagement', '1.0.0', [], ['narratives']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.configManager = configManager;

        // Initialize sub-managers
        this.narrativeManager = new NarrativeManager(this.data, this.auth, this.ui, this.configManager);
        this.formManager = new NarrativeFormManager(this.data, this.auth, this.ui, this.configManager);
        this.renderManager = new NarrativeRenderManager(this.data, this.auth, this.ui, this.configManager);

        // Set up cross-manager references
        this.narrativeManager.setFormManager(this.formManager);
        this.narrativeManager.setRenderManager(this.renderManager);
        this.formManager.setNarrativeManager(this.narrativeManager);
        this.renderManager.setNarrativeManager(this.narrativeManager);

        // Current state
        this.currentIncidentId = null;
        this.entries = [];
        this.isEditing = false;
        this.editingEntryId = null;
    }

    // Core narrative management
    init(incidentId, existingEntries = []) {
        this.currentIncidentId = incidentId;
        if (existingEntries && existingEntries.length > 0) {
            this.entries = existingEntries;
        }
        this.renderManager.render(this.entries, incidentId);
    }

    // Entry management
    async addEntry() {
        return this.formManager.showAddEntryForm();
    }

    async editEntry(entryId) {
        const entry = this.entries.find(e => e.id === entryId);
        if (!entry) {
            console.error('Entry not found for editing:', entryId);
            if (this.uiManager?.showToast) {
                this.uiManager.showToast('Entry not found for editing.', 'error');
            }
            return;
        }

        try {
            this.isEditing = true;
            this.editingEntryId = entryId;
            return await this.formManager.showEditEntryForm(entry);
        } catch (error) {
            console.error('Error opening edit form:', error);
            if (this.uiManager?.showToast) {
                this.uiManager.showToast('Failed to open edit form. Please try again.', 'error');
            }
            // Reset editing state on error
            this.isEditing = false;
            this.editingEntryId = null;
        }
    }

    async saveEntry(entryData) {
        const entry = {
            ...entryData,
            timestamp: this.isEditing ? 
                this.entries.find(e => e.id === this.editingEntryId)?.timestamp : 
                new Date().toISOString()
        };

        if (this.isEditing) {
            const index = this.entries.findIndex(e => e.id === this.editingEntryId);
            if (index !== -1) {
                this.entries[index] = entry;
            }
            this.isEditing = false;
            this.editingEntryId = null;
        } else {
            this.entries.push(entry);
        }

        // Save to incident
        if (this.currentIncidentId) {
            await this.saveToIncident();
        }

        // Re-render
        this.renderManager.render(this.entries, this.currentIncidentId);

        return entry;
    }

    async deleteEntry(entryId) {
        const entry = this.entries.find(e => e.id === entryId);
        if (!entry) {
            console.error('Entry not found for deletion:', entryId);
            return false;
        }

        const entryType = entry.entry_type.replace('_', ' ').toUpperCase();
        const eventTime = new Date(entry.event_time || entry.timestamp).toLocaleString();
        
        if (!confirm(`Are you sure you want to delete this narrative entry?\n\nType: ${entryType}\nTime: ${eventTime}\n\nThis action cannot be undone.`)) {
            return false;
        }

        try {
            this.entries = this.entries.filter(e => e.id !== entryId);
            
            if (this.currentIncidentId) {
                await this.saveToIncident();
            }

            this.renderManager.render(this.entries, this.currentIncidentId);
            
            if (this.uiManager?.showToast) {
                this.uiManager.showToast('Narrative entry deleted successfully', 'success');
            }

            return true;
        } catch (error) {
            console.error('Error deleting narrative entry:', error);
            if (this.uiManager?.showToast) {
                this.uiManager.showToast('Failed to delete narrative entry. Please try again.', 'error');
            }
            return false;
        }
    }

    // Data persistence
    async saveToIncident() {
        if (!this.currentIncidentId || !this.dataManager) {
            return;
        }

        try {
            const updateData = {
                log_entries: JSON.stringify(this.entries)
            };

            await this.dataManager.update('incidents', this.currentIncidentId, updateData);
        } catch (error) {
            console.error('Error saving narrative entries to incident:', error);
            throw error;
        }
    }

    // Data loading
    loadFromIncident(incident) {
        if (!incident) {
            this.entries = [];
            return;
        }

        try {
            const logEntries = incident.log_entries;

            if (!logEntries) {
                this.entries = [];
            } else if (Array.isArray(logEntries)) {
                this.entries = logEntries;
            } else if (typeof logEntries === 'object') {
                this.entries = [];
                console.warn('log_entries is an object but not an array:', logEntries);
            } else if (typeof logEntries === 'string') {
                if (logEntries.trim() === '') {
                    this.entries = [];
                } else {
                    this.entries = JSON.parse(logEntries);
                    if (!Array.isArray(this.entries)) {
                        this.entries = [];
                        console.warn('Parsed log_entries is not an array:', this.entries);
                    }
                }
            } else {
                this.entries = [];
                console.warn('Unknown log_entries type:', typeof logEntries, logEntries);
            }
        } catch (error) {
            console.error('Error parsing log entries:', error);
            this.entries = [];
        }
    }

    // Form data handling
    getEntriesForSubmission() {
        return JSON.stringify(this.entries);
    }

    // Render methods
    render() {
        this.renderManager.render(this.entries, this.currentIncidentId);
    }

    // State getters
    getCurrentIncidentId() {
        return this.currentIncidentId;
    }

    getEntries() {
        return this.entries;
    }

    isCurrentlyEditing() {
        return this.isEditing;
    }

    getEditingEntryId() {
        return this.editingEntryId;
    }

    // Manager access for external components
    getNarrativeManager() {
        return this.narrativeManager;
    }

    getFormManager() {
        return this.formManager;
    }

    getRenderManager() {
        return this.renderManager;
    }

    /**
     * Initialize the narrative management module
     * @returns {Promise<void>}
     */
    async initialize() {
        console.log('Initializing Narrative Management module');
        this.initialized = true;
    }

    /**
     * Cleanup module resources
     * @returns {Promise<void>}
     */
    async cleanup() {
        console.log('Cleaning up Narrative Management module');
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // TODO: Implement narrative management commands
        return new Map();
    }
}

// Export individual components for specific use cases
export { NarrativeManager, NarrativeFormManager, NarrativeRenderManager };