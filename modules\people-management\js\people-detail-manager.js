/**
 * People Detail Manager
 * Handles person detail view and related functionality
 */

import { BaseManager } from '../../shared/base-manager.js';

export class PeopleDetailManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.selectedPerson = null;
        
        // Cross-manager references (set by parent)
        this.petManager = null;
        this.medicalManager = null;
    }

    /**
     * View person detail - main entry point
     */
    async viewPersonDetail(personId) {
        try {
            if (!personId) {
                console.error('No person ID provided');
                this.ui.showDialog('Error', 'No person ID provided', 'error');
                return null;
            }

            const person = await this.data.get('people', personId);
            if (!person) {
                console.error('Person not found:', personId);
                this.ui.showDialog('Error', 'Person not found', 'error');
                return null;
            }

            this.selectedPerson = person;

            // Set the selected person on the app instance so it can be accessed by loadTabContent
            if (window.app) {
                window.app.selectedPerson = person;
                await window.app.loadTabContent('records', 'person-detail');

                // Set up tab navigation for person detail
                setTimeout(async () => {
                    this.setupPersonDetailTabs();
                    this.loadPersonActivities(personId);
                    if (this.petManager) {
                        this.petManager.loadPersonPets(personId);
                    }
                    if (this.medicalManager) {
                        this.medicalManager.loadPersonMedicalIssues(personId);
                    }
                    this.loadPersonDisabilities(personId);
                    this.loadPersonCaseManagement(personId);
                    this.loadPersonServiceBarriers(personId);
                    this.loadPersonSupportNetwork(personId);
                    await this.loadPersonJustice(personId);
                }, 100);
            }

            return person;
        } catch (error) {
            console.error('Error viewing person detail:', error);
            this.ui.showDialog('Error', `Failed to load person details: ${error.message}`, 'error');
            return null;
        }
    }

    /**
     * Load person detail data
     */
    async loadPersonDetail(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (person) {
                this.selectedPerson = person;
            }
            return person;
        } catch (error) {
            console.error('Error loading person detail:', error);
            throw error;
        }
    }

    /**
     * Handle person edit actions (aliases, view details, etc.)
     */
    async handlePersonEditAction(action, personRecord) {
        try {
            switch (action) {
                case 'manage-aliases':
                    await this.showAliasManagementModal(personRecord);
                    break;
                case 'view-details':
                    await this.viewPersonDetail(personRecord.id);
                    break;
                default:
                    console.warn('Unknown person edit action:', action);
            }
        } catch (error) {
            console.error('Error handling person edit action:', error);
            this.ui.showDialog('Error', `Failed to perform action: ${error.message}`, 'error');
        }
    }

    /**
     * Set up person detail tabs
     */
    setupPersonDetailTabs() {
        // This is typically handled by the main app tab system
        // But we can add person-specific tab functionality here if needed
        const tabContainer = document.querySelector('.person-detail-tabs');
        if (tabContainer) {
            const tabs = tabContainer.querySelectorAll('.person-detail-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    this.switchPersonDetailTab(e.target.dataset.tab);
                });
            });
        }
    }

    /**
     * Switch person detail tab
     */
    switchPersonDetailTab(tabName) {
        // Activate the selected tab
        const tabs = document.querySelectorAll('.person-detail-tabs .person-detail-tab');
        tabs.forEach(tab => {
            if (tab.dataset.tab === tabName) {
                tab.classList.add('active');
            } else {
                tab.classList.remove('active');
            }
        });

        // Show the selected tab content - match by ID since content uses id="tabname-tab"
        const tabContents = document.querySelectorAll('.person-detail-content .tab-content');
        tabContents.forEach(content => {
            if (content.id === `${tabName}-tab`) {
                content.style.display = 'block';
                content.classList.add('active');
            } else {
                content.style.display = 'none';
                content.classList.remove('active');
            }
        });
    }

    /**
     * Load person activities
     */
    async loadPersonActivities(personId) {
        try {
            const activities = await this.data.search('activity_logs', {
                person_id: personId
            });

            const activitiesContainer = document.getElementById('person-activities');
            if (activitiesContainer) {
                if (activities.length === 0) {
                    activitiesContainer.innerHTML = '<p class="no-data">No activities found for this person.</p>';
                } else {
                    activitiesContainer.innerHTML = activities.map(activity => `
                        <div class="activity-item">
                            <div class="activity-header">
                                <span class="activity-type">${activity.activity_type}</span>
                                <span class="activity-date">${this.uiUtilities.formatDate(activity.activity_date)}</span>
                            </div>
                            <div class="activity-description">${activity.description || 'No description'}</div>
                            ${activity.notes ? `<div class="activity-notes">${activity.notes}</div>` : ''}
                        </div>
                    `).join('');
                }
            }
        } catch (error) {
            console.error('Error loading person activities:', error);
            const activitiesContainer = document.getElementById('person-activities');
            if (activitiesContainer) {
                activitiesContainer.innerHTML = '<p class="error">Failed to load activities.</p>';
            }
        }
    }

    /**
     * Load person disabilities
     */
    async loadPersonDisabilities(personId) {
        try {
            const disabilities = await this.data.search('medical_issues', {
                person_id: personId,
                category: 'Disability'
            });

            const disabilitiesContainer = document.getElementById('person-disabilities');
            if (disabilitiesContainer) {
                if (disabilities.length === 0) {
                    disabilitiesContainer.innerHTML = '<p class="no-data">No disabilities recorded.</p>';
                } else {
                    disabilitiesContainer.innerHTML = disabilities.map(disability => `
                        <div class="disability-item">
                            <div class="disability-name">${disability.condition_name}</div>
                            ${disability.notes ? `<div class="disability-notes">${disability.notes}</div>` : ''}
                            <div class="disability-meta">
                                <small>Added: ${this.uiUtilities.formatDate(disability.created_at)}</small>
                            </div>
                        </div>
                    `).join('');
                }
            }
        } catch (error) {
            console.error('Error loading person disabilities:', error);
            const disabilitiesContainer = document.getElementById('person-disabilities');
            if (disabilitiesContainer) {
                disabilitiesContainer.innerHTML = '<p class="error">Failed to load disabilities.</p>';
            }
        }
    }

    /**
     * Load person case management info
     */
    async loadPersonCaseManagement(personId) {
        try {
            // Load case management related incidents/records
            const caseRecords = await this.data.search('incidents', {
                person_id: personId
            });

            const caseContainer = document.getElementById('person-case-management');
            if (caseContainer) {
                if (caseRecords.length === 0) {
                    caseContainer.innerHTML = '<p class="no-data">No case management records found.</p>';
                } else {
                    caseContainer.innerHTML = caseRecords.map(record => `
                        <div class="case-record-item">
                            <div class="case-header">
                                <span class="case-type">${record.incident_type}</span>
                                <span class="case-date">${this.uiUtilities.formatDate(record.incident_date)}</span>
                            </div>
                            <div class="case-description">${record.description || 'No description'}</div>
                        </div>
                    `).join('');
                }
            }
        } catch (error) {
            console.error('Error loading person case management:', error);
            const caseContainer = document.getElementById('person-case-management');
            if (caseContainer) {
                caseContainer.innerHTML = '<p class="error">Failed to load case management records.</p>';
            }
        }
    }

    /**
     * Load person service barriers
     */
    async loadPersonServiceBarriers(personId) {
        try {
            // This would load service barriers if they exist in the schema
            const barriersContainer = document.getElementById('person-service-barriers');
            if (barriersContainer) {
                barriersContainer.innerHTML = '<p class="no-data">Service barriers functionality not yet implemented.</p>';
            }
        } catch (error) {
            console.error('Error loading person service barriers:', error);
        }
    }

    /**
     * Load person support network
     */
    async loadPersonSupportNetwork(personId) {
        try {
            // This would load support network if it exists in the schema
            const supportContainer = document.getElementById('person-support-network');
            if (supportContainer) {
                supportContainer.innerHTML = '<p class="no-data">Support network functionality not yet implemented.</p>';
            }
        } catch (error) {
            console.error('Error loading person support network:', error);
        }
    }

    /**
     * Load person justice information
     */
    async loadPersonJustice(personId) {
        try {
            // This would integrate with the justice module if available
            const justiceContainer = document.getElementById('person-justice');
            if (justiceContainer) {
                if (window.app && window.app.justiceManagement) {
                    // Delegate to justice management module
                    await window.app.justiceManagement.loadPersonJusticeInfo(personId);
                } else {
                    justiceContainer.innerHTML = '<p class="no-data">Justice information not available.</p>';
                }
            }
        } catch (error) {
            console.error('Error loading person justice info:', error);
        }
    }

    /**
     * Show alias management modal
     */
    async showAliasManagementModal(personRecord) {
        try {
            const aliases = await this.data.getPersonAliases(personRecord.id);
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';

            const aliasesHTML = aliases.length > 0 
                ? aliases.map(alias => `
                    <div class="alias-item" data-alias-id="${alias.id}">
                        <div class="alias-info">
                            <span class="alias-name">${alias.alias_name}</span>
                            <small class="alias-meta">Added ${this.uiUtilities.formatDate(alias.created_at)}</small>
                        </div>
                        <div class="alias-actions">
                            <button class="action-button edit-button" data-action="edit" data-alias-id="${alias.id}" data-alias-name="${alias.alias_name}">Edit</button>
                            <button class="action-button delete-button" data-action="delete" data-alias-id="${alias.id}">Delete</button>
                        </div>
                    </div>
                `).join('')
                : '<div class="no-aliases">No aliases found for this person.</div>';

            modal.innerHTML = `
                <div class="modal-dialog large-modal">
                    <div class="modal-header">
                        <h3>Manage Aliases - ${personRecord.first_name} ${personRecord.last_name}</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="section-header">
                            <h4>Current Aliases</h4>
                            <button class="primary-button" id="add-new-alias-btn" data-person-id="${personRecord.id}">
                                <span class="button-icon">+</span>
                                Add New Alias
                            </button>
                        </div>
                        <div class="aliases-container">
                            ${aliasesHTML}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle add new alias
            const addBtn = modal.querySelector('#add-new-alias-btn');
            addBtn.addEventListener('click', async () => {
                await this.addPersonAliasInModal(personRecord.id, modal);
            });

            // Handle edit and delete actions
            modal.addEventListener('click', async (e) => {
                const button = e.target.closest('.action-button');
                if (button) {
                    const action = button.dataset.action;
                    const aliasId = button.dataset.aliasId;
                    
                    if (action === 'edit') {
                        const aliasName = button.dataset.aliasName;
                        await this.editPersonAliasInModal(aliasId, aliasName, modal);
                    } else if (action === 'delete') {
                        await this.deletePersonAliasInModal(aliasId, modal);
                    }
                }
            });

        } catch (error) {
            console.error('Error showing alias management modal:', error);
            this.ui.showDialog('Error', 'Failed to load alias management', 'error');
        }
    }

    /**
     * Add person alias in modal
     */
    async addPersonAliasInModal(personId, modal) {
        // Implementation would be here - simplified for now
        console.log('Add alias functionality not fully implemented');
    }

    /**
     * Edit person alias in modal
     */
    async editPersonAliasInModal(aliasId, currentName, modal) {
        // Implementation would be here - simplified for now
        console.log('Edit alias functionality not fully implemented');
    }

    /**
     * Delete person alias in modal
     */
    async deletePersonAliasInModal(aliasId, modal) {
        // Implementation would be here - simplified for now
        console.log('Delete alias functionality not fully implemented');
    }

    /**
     * Get selected person
     */
    getSelectedPerson() {
        return this.selectedPerson;
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.selectedPerson = null;
    }
}