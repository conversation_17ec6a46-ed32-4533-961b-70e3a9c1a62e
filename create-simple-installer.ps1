# S.T.E.V.I Retro Simple Installer Creator
# Creates distribution files from the packaged application

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Simple Installer Creator" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if packaged app exists
$packagedPath = "dist\S.T.E.V.I Retro-win32-x64"
if (-not (Test-Path $packagedPath)) {
    Write-Host "ERROR: Packaged application not found at $packagedPath" -ForegroundColor Red
    Write-Host "Please run electron-packager first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Found packaged application at: $packagedPath" -ForegroundColor Green
Write-Host ""

# Get version from package.json
$packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
$version = $packageJson.version

Write-Host "Creating installer for version: $version" -ForegroundColor Yellow
Write-Host ""

# Create portable zip version
Write-Host "Creating portable ZIP version..." -ForegroundColor Yellow
$zipPath = "dist\S.T.E.V.I-Retro-Portable-v$version.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

try {
    Compress-Archive -Path "$packagedPath\*" -DestinationPath $zipPath -CompressionLevel Optimal
    $zipSize = [math]::Round((Get-Item $zipPath).Length / 1MB, 2)
    Write-Host "SUCCESS: Portable ZIP created: $zipPath ($zipSize MB)" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to create ZIP: $($_.Exception.Message)" -ForegroundColor Red
}

# Create installation script
Write-Host ""
Write-Host "Creating installation script..." -ForegroundColor Yellow

$installScript = @'
# S.T.E.V.I Retro Installation Script
param(
    [string]$InstallPath = ""
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Installer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "This installer can run without administrator privileges." -ForegroundColor Yellow
    Write-Host "It will install to your user directory." -ForegroundColor Yellow
    Write-Host ""
}

# Choose installation directory
$defaultPath = if ($isAdmin) { "$env:ProgramFiles\S.T.E.V.I Retro" } else { "$env:LOCALAPPDATA\S.T.E.V.I Retro" }

if ([string]::IsNullOrWhiteSpace($InstallPath)) {
    Write-Host "Default installation path: $defaultPath" -ForegroundColor Yellow
    $InstallPath = Read-Host "Enter installation path (or press Enter for default)"
    
    if ([string]::IsNullOrWhiteSpace($InstallPath)) {
        $InstallPath = $defaultPath
    }
}

Write-Host ""
Write-Host "Installing to: $InstallPath" -ForegroundColor Cyan

# Create installation directory
try {
    if (Test-Path $InstallPath) {
        Write-Host "Directory exists. Removing old installation..." -ForegroundColor Yellow
        Remove-Item -Path $InstallPath -Recurse -Force
    }
    
    New-Item -Path $InstallPath -ItemType Directory -Force | Out-Null
    Write-Host "Installation directory created" -ForegroundColor Green
} catch {
    Write-Host "Failed to create installation directory: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Copy application files
Write-Host "Copying application files..." -ForegroundColor Yellow

$sourcePath = "$PSScriptRoot\S.T.E.V.I Retro-win32-x64"
if (Test-Path $sourcePath) {
    try {
        Copy-Item -Path "$sourcePath\*" -Destination $InstallPath -Recurse -Force
        Write-Host "Application files copied successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to copy files: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "Source files not found at $sourcePath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Create desktop shortcut
Write-Host "Creating desktop shortcut..." -ForegroundColor Yellow
try {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\S.T.E.V.I Retro.lnk")
    $Shortcut.TargetPath = "$InstallPath\S.T.E.V.I Retro.exe"
    $Shortcut.WorkingDirectory = $InstallPath
    $Shortcut.Description = "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals"
    $Shortcut.Save()
    Write-Host "Desktop shortcut created" -ForegroundColor Green
} catch {
    Write-Host "Could not create desktop shortcut: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Create Start Menu shortcut
Write-Host "Creating Start Menu shortcut..." -ForegroundColor Yellow
try {
    $startMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs"
    $Shortcut = $WshShell.CreateShortcut("$startMenuPath\S.T.E.V.I Retro.lnk")
    $Shortcut.TargetPath = "$InstallPath\S.T.E.V.I Retro.exe"
    $Shortcut.WorkingDirectory = $InstallPath
    $Shortcut.Description = "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals"
    $Shortcut.Save()
    Write-Host "Start Menu shortcut created" -ForegroundColor Green
} catch {
    Write-Host "Could not create Start Menu shortcut: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Installation completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "S.T.E.V.I Retro has been installed to: $InstallPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "You can now:" -ForegroundColor Yellow
Write-Host "• Launch from Desktop shortcut" -ForegroundColor Yellow
Write-Host "• Launch from Start Menu" -ForegroundColor Yellow
Write-Host "• Run directly from: $InstallPath\S.T.E.V.I Retro.exe" -ForegroundColor Yellow
Write-Host ""

$launch = Read-Host "Would you like to launch S.T.E.V.I Retro now? (y/n)"
if ($launch -eq "y" -or $launch -eq "Y") {
    try {
        Start-Process "$InstallPath\S.T.E.V.I Retro.exe"
        Write-Host "S.T.E.V.I Retro launched" -ForegroundColor Green
    } catch {
        Write-Host "Failed to launch: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Thank you for installing S.T.E.V.I Retro!" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
'@

# Save the installation script
$installScriptPath = "dist\Install-S.T.E.V.I-Retro.ps1"
$installScript | Out-File -FilePath $installScriptPath -Encoding UTF8

Write-Host "Installation script created: $installScriptPath" -ForegroundColor Green

# Create a batch file to run the PowerShell installer
$batchContent = @"
@echo off
echo S.T.E.V.I Retro Installer v$version
echo.
echo Starting installation...
powershell.exe -ExecutionPolicy Bypass -File "%~dp0Install-S.T.E.V.I-Retro.ps1"
pause
"@

$batchPath = "dist\Install-S.T.E.V.I-Retro.bat"
$batchContent | Out-File -FilePath $batchPath -Encoding ASCII

Write-Host "Batch installer created: $batchPath" -ForegroundColor Green

# Create README file
$readmeContent = @"
# S.T.E.V.I Retro v$version - Installation Guide

## Installation Options

### Option 1: Portable Installation (Recommended for testing)
1. Extract the ZIP file: S.T.E.V.I-Retro-Portable-v$version.zip
2. Run S.T.E.V.I Retro.exe directly from the extracted folder
3. No installation required - runs from any location

### Option 2: Full Installation (Recommended for production)
1. Run Install-S.T.E.V.I-Retro.bat as Administrator (for system-wide install)
   OR
   Run Install-S.T.E.V.I-Retro.bat as regular user (for user-only install)
2. Follow the prompts to choose installation location
3. Desktop and Start Menu shortcuts will be created automatically

## System Requirements
- Windows 10 or Windows 11 (64-bit)
- 4 GB RAM minimum
- 500 MB free disk space
- Internet connection for cloud features

## Features
- Offline-capable incident reporting
- Secure data synchronization with Supabase
- Professional report generation
- User management and authentication
- Comprehensive logging and audit trails

## Support
For technical support, please contact I.H.A.R.C. support team.

## Version Information
Version: $version
Build Date: $(Get-Date -Format 'yyyy-MM-dd')
Platform: Windows x64
"@

$readmePath = "dist\README.txt"
$readmeContent | Out-File -FilePath $readmePath -Encoding UTF8

Write-Host "README file created: $readmePath" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Installer creation completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Created files:" -ForegroundColor Cyan
Write-Host "• Portable ZIP: $zipPath" -ForegroundColor Yellow
Write-Host "• PowerShell Installer: $installScriptPath" -ForegroundColor Yellow
Write-Host "• Batch Installer: $batchPath" -ForegroundColor Yellow
Write-Host "• README: $readmePath" -ForegroundColor Yellow
Write-Host ""
Write-Host "For distribution on fresh Windows 10/11 systems:" -ForegroundColor Green
Write-Host "1. Use the ZIP file for portable installation" -ForegroundColor Green
Write-Host "2. Use the .bat file for guided installation with shortcuts" -ForegroundColor Green
Write-Host "3. Both options work without additional dependencies" -ForegroundColor Green
Write-Host ""

Write-Host "Opening dist folder..." -ForegroundColor Yellow
Start-Process "dist"

Read-Host "Press Enter to exit"
