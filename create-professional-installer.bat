@echo off
REM S.T.E.V.I Retro Professional Installer Builder
REM Creates a comprehensive Windows installer with all dependencies

echo ========================================
echo S.T.E.V.I Retro Professional Installer
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: package.json not found.
    echo Please run this script from the S.T.E.V.I Retro project directory.
    echo.
    pause
    exit /b 1
)

REM Check Node.js and npm
echo Checking prerequisites...
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Node.js not found. Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: npm not found. Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js and npm found - OK
echo.

REM Kill any running electron processes first
echo Stopping any running processes...
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im "S.T.E.V.I Retro.exe" >nul 2>&1

REM Clean up any locked files
echo Cleaning up temporary files...
if exist "dist" rmdir /s /q "dist" >nul 2>&1

REM Check dependencies
echo Checking system dependencies...
npm run check-deps
if %errorLevel% neq 0 (
    echo Warning: Dependency check failed, but continuing...
)
echo.

REM Clean install dependencies if needed
echo Checking if clean install is needed...
if not exist "node_modules\electron\dist\electron.exe" (
    echo Node modules appear corrupted, performing clean install...
    if exist "node_modules" (
        echo Removing corrupted node_modules...
        rmdir /s /q "node_modules" >nul 2>&1
        if exist "node_modules" (
            echo Warning: Could not remove node_modules completely
            echo Some files may be locked. Please close all applications and try again.
            pause
            exit /b 1
        )
    )

    echo Installing dependencies...
    npm install
    if %errorLevel% neq 0 (
        echo ERROR: Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
)

REM Build the installer
echo Building S.T.E.V.I Retro installer...
echo This may take several minutes...
echo.

REM Try to build with explicit configuration
echo Attempting to build installer...
npx electron-builder --win --config.electronVersion=37.2.3 --config.directories.output=dist

REM Check if build succeeded
if %errorLevel% == 0 (
    echo.
    echo ========================================
    echo SUCCESS: Professional installer created!
    echo ========================================
    echo.
    echo The installer includes:
    echo - Program Files installation
    echo - Automatic dependency management
    echo - Uninstall and repair options
    echo - Windows integration
    echo.
    echo Location: dist\S.T.E.V.I-Retro-Setup-1.3.0.exe
    echo.
    echo Ready for distribution on fresh Windows systems!
    echo.
    echo Opening dist folder...
    start "" "dist"
) else (
    echo.
    echo ========================================
    echo ERROR: Installer creation failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Common solutions:
    echo 1. Run: npm install
    echo 2. Delete node_modules and run: npm install
    echo 3. Check that all dependencies are installed
)

echo.
pause
