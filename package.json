{"name": "steviretro", "version": "1.3.0", "description": "S.T.E.V.I Retro - Supportive Technology to Enable Vulnerable Individuals - Retro Interface for I.H.A.R.C field staff", "main": "electron/main.js", "type": "module", "scripts": {"start": "npx electron .", "dev": "electron . --dev", "build": "npm run check-deps && npx electron-builder", "build-win": "npm run check-deps && npx electron-builder --win", "pack": "electron-builder --dir", "dist": "npx electron-builder", "postinstall": "electron-builder install-app-deps", "check-deps": "node build/check-dependencies.cjs", "installer": "npm run build-win", "standalone": "npm run clean && npm run check-deps && npx electron-builder --win", "clean": "rimraf dist S.T.E.V.I-Retro-Installer.exe", "rebuild": "npm run clean && npm run build"}, "keywords": ["cli", "offline", "iharc", "field-staff", "incident-reporting"], "author": "IHARC", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.50.5", "better-sqlite3": "^12.2.0", "fs-extra": "^11.3.0", "node-fetch": "^3.3.2", "semver": "^7.7.2", "uuid": "^11.1.0", "serialport": "^12.0.0", "@serialport/parser-readline": "^12.0.0", "nmea-simple": "^3.2.0"}, "devDependencies": {"archiver": "^7.0.1", "core-util-is": "^1.0.3", "electron": "^37.2.3", "electron-builder": "^26.0.12", "electron-packager": "^17.1.2", "electron-reload": "^2.0.0-alpha.1"}, "build": {"appId": "ca.iharc.steviretro", "productName": "S.T.E.V.I Retro", "copyright": "Copyright © 2024 I.H.A.R.C. All rights reserved.", "electronVersion": "37.2.3", "directories": {"output": "dist", "buildResources": "build"}, "files": ["electron/**/*", "src/**/*", "renderer/**/*", "templates/**/*", "assets/**/*"], "extraResources": [{"from": "templates", "to": "templates", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "legalTrademarks": "I.H.A.R.C. - Supportive Technology to Enable Vulnerable Individuals"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "S.T.E.V.I Retro", "runAfterFinish": true, "menuCategory": "I.H.A.R.C", "artifactName": "S.T.E.V.I-Retro-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false, "perMachine": true, "packElevateHelper": true, "unicode": true, "warningsAsErrors": false, "include": "build/installer.nsh", "displayLanguageSelector": false, "multiLanguageInstaller": false, "removeDefaultUninstallWelcomePage": false, "guid": "ca.iharc.steviretro", "differentialPackage": false}, "publish": null}}