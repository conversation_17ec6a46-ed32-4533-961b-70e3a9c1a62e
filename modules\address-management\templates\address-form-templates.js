export const addressFormTemplates = {
    addressForm: (addressData = {}) => {
        return `
            <style>
                .address-form-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                    font-family: 'Courier New', monospace;
                }
                .address-form-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 2rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .address-form-title {
                    color: #ff0000;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .address-form-actions {
                    display: flex;
                    gap: 1rem;
                }
                .address-form-content {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                }
                .address-form-section {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    padding: 1.5rem;
                }
                .address-section-title {
                    color: #ff0000;
                    font-size: 1rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    margin-bottom: 1rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 1px solid #ff0000;
                }
                .address-field-group {
                    margin-bottom: 1rem;
                }
                .address-field-label {
                    display: block;
                    color: #ff0000;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    text-transform: uppercase;
                    font-size: 0.9rem;
                }
                .address-field-input {
                    width: 100%;
                    padding: 0.75rem;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    box-sizing: border-box;
                }
                .address-field-input:focus {
                    outline: none;
                    border-color: #ffffff;
                    box-shadow: 0 0 0 1px #ffffff;
                }
                .address-field-input::placeholder {
                    color: rgba(255, 0, 0, 0.5);
                }
                .address-search-wrapper {
                    position: relative;
                }
                .address-suggestions-dropdown {
                    position: absolute;
                    top: 100%;
                    left: 0;
                    right: 0;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    border-top: none;
                    max-height: 200px;
                    overflow-y: auto;
                    z-index: 1000;
                }
                .address-suggestion-item {
                    padding: 0.75rem;
                    border-bottom: 1px dashed rgba(255, 0, 0, 0.3);
                    cursor: pointer;
                    transition: background-color 0.2s;
                }
                .address-suggestion-item:hover,
                .address-suggestion-item.active {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .suggestion-main {
                    color: #ff0000;
                    font-weight: bold;
                    margin-bottom: 0.25rem;
                }
                .suggestion-secondary {
                    color: #cccccc;
                    font-size: 0.8rem;
                }
                .coordinates-section {
                    grid-column: 1 / -1;
                }
                .coordinates-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr auto;
                    gap: 1rem;
                    align-items: end;
                }
                .geocode-button {
                    background-color: #ff0000;
                    color: #000000;
                    border: 1px solid #ff0000;
                    padding: 0.75rem 1rem;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.2s;
                    text-transform: uppercase;
                }
                .geocode-button:hover {
                    background-color: transparent;
                    color: #ff0000;
                }
                .geocode-button:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                .form-actions {
                    grid-column: 1 / -1;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 2rem;
                    padding-top: 1rem;
                    border-top: 1px solid #ff0000;
                }
                .primary-button {
                    background-color: #ff0000;
                    color: #000000;
                    border: 1px solid #ff0000;
                    padding: 0.75rem 2rem;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.2s;
                    text-transform: uppercase;
                }
                .primary-button:hover {
                    background-color: transparent;
                    color: #ff0000;
                }
                .secondary-button {
                    background-color: transparent;
                    color: #ff0000;
                    border: 1px solid #ff0000;
                    padding: 0.75rem 2rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                    text-transform: uppercase;
                }
                .secondary-button:hover {
                    background-color: #ff0000;
                    color: #000000;
                }
                .required {
                    color: #ff6666;
                }
            </style>
            
            <div class="address-form-container">
                <div class="address-form-header">
                    <div class="address-form-title">
                        ${addressData.id ? 'Edit Address' : 'Add New Address'}
                    </div>
                    <div class="address-form-actions">
                        <button class="secondary-button" data-action="cancel-address-form">
                            Cancel
                        </button>
                    </div>
                </div>
                
                <form id="address-form" class="address-form-content">
                    <div class="address-form-section">
                        <div class="address-section-title">Address Search</div>
                        
                        <div class="address-field-group">
                            <label class="address-field-label" for="address-search">
                                Search Address <span class="required">*</span>
                            </label>
                            <div id="address-search-container" class="address-search-wrapper"></div>
                            <small style="color: #999; margin-top: 0.5rem; display: block;">
                                Start typing to search for addresses using Google Maps
                            </small>
                        </div>
                    </div>
                    
                    <div class="address-form-section">
                        <div class="address-section-title">Address Details</div>
                        
                        <div class="address-field-group">
                            <label class="address-field-label" for="street_address">
                                Street Address <span class="required">*</span>
                            </label>
                            <input type="text" 
                                   id="street_address" 
                                   name="street_address" 
                                   class="address-field-input"
                                   value="${addressData.street_address || ''}"
                                   placeholder="123 Main Street"
                                   required>
                        </div>
                        
                        <div class="address-field-group">
                            <label class="address-field-label" for="city">
                                City
                            </label>
                            <input type="text" 
                                   id="city" 
                                   name="city" 
                                   class="address-field-input"
                                   value="${addressData.city || ''}"
                                   placeholder="City name">
                        </div>
                        
                        <div class="address-field-group">
                            <label class="address-field-label" for="province">
                                Province
                            </label>
                            <input type="text" 
                                   id="province" 
                                   name="province" 
                                   class="address-field-input"
                                   value="${addressData.province || 'Ontario'}"
                                   placeholder="Ontario">
                        </div>
                        
                        <div class="address-field-group">
                            <label class="address-field-label" for="postal_code">
                                Postal Code
                            </label>
                            <input type="text" 
                                   id="postal_code" 
                                   name="postal_code" 
                                   class="address-field-input"
                                   value="${addressData.postal_code || ''}"
                                   placeholder="A1A 1A1"
                                   pattern="[A-Za-z][0-9][A-Za-z] [0-9][A-Za-z][0-9]">
                        </div>
                    </div>
                    
                    <div class="address-form-section coordinates-section">
                        <div class="address-section-title">Location Coordinates</div>
                        
                        <div class="coordinates-grid">
                            <div class="address-field-group">
                                <label class="address-field-label" for="latitude">
                                    Latitude
                                </label>
                                <input type="number" 
                                       id="latitude" 
                                       name="latitude" 
                                       class="address-field-input"
                                       value="${addressData.latitude || ''}"
                                       step="any"
                                       placeholder="43.6532"
                                       min="-90" 
                                       max="90">
                            </div>
                            
                            <div class="address-field-group">
                                <label class="address-field-label" for="longitude">
                                    Longitude
                                </label>
                                <input type="number" 
                                       id="longitude" 
                                       name="longitude" 
                                       class="address-field-input"
                                       value="${addressData.longitude || ''}"
                                       step="any"
                                       placeholder="-79.3832"
                                       min="-180" 
                                       max="180">
                            </div>
                            
                            <button type="button" 
                                    class="geocode-button" 
                                    id="geocode-address-btn">
                                Geocode
                            </button>
                        </div>
                        
                        <small style="color: #999; margin-top: 1rem; display: block;">
                            Coordinates will be automatically filled when using address search. 
                            You can also enter them manually or click "Geocode" to get coordinates from the address.
                        </small>
                    </div>
                    
                    <div class="address-form-section">
                        <div class="address-section-title">Additional Information</div>
                        
                        <div class="address-field-group">
                            <label class="address-field-label" for="notes">
                                Notes
                            </label>
                            <textarea id="notes" 
                                      name="notes" 
                                      class="address-field-input"
                                      rows="4"
                                      placeholder="Additional notes about this address">${addressData.notes || ''}</textarea>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="secondary-button" data-action="cancel-address-form">
                            Cancel
                        </button>
                        <button type="submit" class="primary-button" id="save-address-btn">
                            ${addressData.id ? 'Update Address' : 'Save Address'}
                        </button>
                    </div>
                </form>
            </div>
        `;
    }
};