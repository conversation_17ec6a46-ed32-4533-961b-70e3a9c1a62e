// Secure Settings Manager for S.T.E.V.I Retro
import { app } from 'electron';
import { join } from 'path';
import { readFileSync, writeFileSync, existsSync, mkdirSync, readdirSync, statSync, unlinkSync } from 'fs';
import { createHash, randomBytes, createCipheriv, createDecipheriv } from 'crypto';
import { getSupabaseUrl, getSupabaseAnonKey } from './supabase-config.js';

export class SecureSettingsManager {
    constructor() {
        this.userDataPath = app.getPath('userData');
        this.settingsPath = join(this.userDataPath, 'settings.enc');
        this.keyPath = join(this.userDataPath, '.key');
        this.backupPath = join(this.userDataPath, 'backups');
        
        this.defaultSettings = {
            version: app.getVersion(),
            firstRun: true,
            installDate: new Date().toISOString(),
            security: {
                encryptionEnabled: true,
                autoLock: false,
                sessionTimeout: 3600000, // 1 hour
                maxLoginAttempts: 5
            },
            supabase: {
                url: process.env.SUPABASE_URL || getSupabaseUrl(),
                anonKey: process.env.SUPABASE_ANON_KEY || getSupabaseAnonKey()
            },
            paths: {
                data: join(this.userDataPath, 'data'),
                cache: join(this.userDataPath, 'cache'),
                reports: join(this.userDataPath, 'reports'),
                templates: join(this.userDataPath, 'templates'),
                media: join(this.userDataPath, 'media'),
                logs: join(this.userDataPath, 'logs'),
                backups: this.backupPath
            },
            ui: {
                theme: 'retro-green',
                fontSize: 'medium',
                animations: true,
                sounds: true
            },
            data: {
                autoSync: true,
                syncInterval: 300000, // 5 minutes
                offlineMode: false,
                cacheSize: 1000,
                autoBackup: true,
                maxBackups: 10
            },
            updates: {
                autoCheck: true,
                checkInterval: 86400000, // 24 hours
                autoDownload: false,
                betaChannel: false
            },
            privacy: {
                telemetry: false,
                crashReports: true,
                analytics: false
            }
        };

        this.settings = null;
        this.encryptionKey = null;
        
        this.initialize();
    }

    /**
     * Initialize the settings manager
     */
    initialize() {
        try {
            // Ensure directories exist
            this.ensureDirectories();
            
            // Load or create encryption key
            this.loadOrCreateKey();
            
            // Load settings
            this.loadSettings();
            
            console.log('Secure settings manager initialized');
        } catch (error) {
            console.error('Error initializing settings manager:', error);
            this.settings = { ...this.defaultSettings };
        }
    }

    /**
     * Ensure all required directories exist
     */
    ensureDirectories() {
        const directories = [
            this.userDataPath,
            this.backupPath,
            ...Object.values(this.defaultSettings.paths)
        ];

        directories.forEach(dir => {
            if (!existsSync(dir)) {
                mkdirSync(dir, { recursive: true });
                console.log(`Created directory: ${dir}`);
            }
        });
    }

    /**
     * Load or create encryption key
     */
    loadOrCreateKey() {
        if (existsSync(this.keyPath)) {
            // Load existing key
            const keyData = readFileSync(this.keyPath, 'utf8');
            this.encryptionKey = keyData.trim();
        } else {
            // Create new key
            this.encryptionKey = randomBytes(32).toString('hex');
            writeFileSync(this.keyPath, this.encryptionKey, { mode: 0o600 });
            console.log('Created new encryption key');
        }
    }

    /**
     * Encrypt data
     */
    encrypt(data) {
        try {
            const algorithm = 'aes-256-cbc';
            const key = createHash('sha256').update(this.encryptionKey).digest();
            const iv = randomBytes(16);

            const cipher = createCipheriv(algorithm, key, iv);
            let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
            encrypted += cipher.final('hex');

            // Prepend IV to encrypted data
            return iv.toString('hex') + ':' + encrypted;
        } catch (error) {
            console.error('Encryption error:', error);
            throw new Error('Failed to encrypt data');
        }
    }

    /**
     * Decrypt data
     */
    decrypt(encryptedData) {
        try {
            const algorithm = 'aes-256-cbc';
            const key = createHash('sha256').update(this.encryptionKey).digest();

            // Split IV and encrypted data
            const parts = encryptedData.split(':');
            if (parts.length !== 2) {
                throw new Error('Invalid encrypted data format');
            }

            const iv = Buffer.from(parts[0], 'hex');
            const encrypted = parts[1];

            const decipher = createDecipheriv(algorithm, key, iv);
            let decrypted = decipher.update(encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');

            return JSON.parse(decrypted);
        } catch (error) {
            console.error('Decryption error:', error);
            throw new Error('Failed to decrypt data');
        }
    }

    /**
     * Load settings from encrypted file
     */
    loadSettings() {
        try {
            if (existsSync(this.settingsPath)) {
                const encryptedData = readFileSync(this.settingsPath, 'utf8');
                this.settings = this.decrypt(encryptedData);
                
                // Merge with defaults for new settings
                this.settings = this.mergeWithDefaults(this.settings);
            } else {
                // First run - use defaults
                this.settings = { ...this.defaultSettings };
                this.saveSettings();
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            this.settings = { ...this.defaultSettings };
        }
    }

    /**
     * Save settings to encrypted file
     */
    saveSettings() {
        try {
            // Create backup before saving
            this.createBackup();
            
            // Encrypt and save
            const encryptedData = this.encrypt(this.settings);
            writeFileSync(this.settingsPath, encryptedData, { mode: 0o600 });
            
            console.log('Settings saved successfully');
        } catch (error) {
            console.error('Error saving settings:', error);
            throw new Error('Failed to save settings');
        }
    }

    /**
     * Create backup of current settings
     */
    createBackup() {
        try {
            if (existsSync(this.settingsPath)) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const backupFile = join(this.backupPath, `settings-${timestamp}.enc`);
                
                const currentData = readFileSync(this.settingsPath);
                writeFileSync(backupFile, currentData);
                
                // Clean old backups
                this.cleanOldBackups();
            }
        } catch (error) {
            console.warn('Error creating settings backup:', error);
        }
    }

    /**
     * Clean old backup files
     */
    cleanOldBackups() {
        try {
            const files = readdirSync(this.backupPath)
                .filter(file => file.startsWith('settings-') && file.endsWith('.enc'))
                .map(file => ({
                    name: file,
                    path: join(this.backupPath, file),
                    time: statSync(join(this.backupPath, file)).mtime
                }))
                .sort((a, b) => b.time - a.time);

            // Keep only the latest 10 backups
            const maxBackups = this.settings?.data?.maxBackups || 10;
            if (files.length > maxBackups) {
                files.slice(maxBackups).forEach(file => {
                    unlinkSync(file.path);
                });
            }
        } catch (error) {
            console.warn('Error cleaning old backups:', error);
        }
    }

    /**
     * Merge settings with defaults
     */
    mergeWithDefaults(settings) {
        const merged = { ...this.defaultSettings };
        
        // Deep merge
        Object.keys(settings).forEach(key => {
            if (typeof settings[key] === 'object' && !Array.isArray(settings[key])) {
                merged[key] = { ...merged[key], ...settings[key] };
            } else {
                merged[key] = settings[key];
            }
        });

        return merged;
    }

    /**
     * Get a setting value
     */
    get(key, defaultValue = null) {
        const keys = key.split('.');
        let value = this.settings;
        
        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    }

    /**
     * Set a setting value
     */
    set(key, value) {
        const keys = key.split('.');
        let current = this.settings;
        
        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in current) || typeof current[k] !== 'object') {
                current[k] = {};
            }
            current = current[k];
        }
        
        current[keys[keys.length - 1]] = value;
        this.saveSettings();
    }

    /**
     * Get all settings
     */
    getAll() {
        return { ...this.settings };
    }

    /**
     * Reset settings to defaults
     */
    reset() {
        this.settings = { ...this.defaultSettings };
        this.saveSettings();
    }

    /**
     * Export settings (unencrypted for backup)
     */
    export() {
        return JSON.stringify(this.settings, null, 2);
    }

    /**
     * Import settings from JSON
     */
    import(jsonData) {
        try {
            const importedSettings = JSON.parse(jsonData);
            this.settings = this.mergeWithDefaults(importedSettings);
            this.saveSettings();
            return true;
        } catch (error) {
            console.error('Error importing settings:', error);
            return false;
        }
    }

    /**
     * Get settings file paths
     */
    getPaths() {
        return {
            userData: this.userDataPath,
            settings: this.settingsPath,
            backups: this.backupPath,
            ...this.settings.paths
        };
    }

    /**
     * Set Azure configuration securely
     */
    async setAzureConfig(config) {
        try {
            if (!this.settings.azure) {
                this.settings.azure = {};
            }

            this.settings.azure = {
                tenantId: config.tenantId,
                clientId: config.clientId,
                clientSecret: config.clientSecret,
                storageAccountName: config.storageAccountName,
                containerName: config.containerName,
                blobEndpoint: config.blobEndpoint,
                configuredAt: new Date().toISOString()
            };

            this.saveSettings();
            console.log('Azure configuration saved securely');
            return true;
        } catch (error) {
            console.error('Error saving Azure configuration:', error);
            throw new Error('Failed to save Azure configuration');
        }
    }

    /**
     * Get Azure configuration
     */
    async getAzureConfig() {
        try {
            if (!this.settings.azure) {
                return null;
            }

            return {
                tenantId: this.settings.azure.tenantId,
                clientId: this.settings.azure.clientId,
                clientSecret: this.settings.azure.clientSecret,
                storageAccountName: this.settings.azure.storageAccountName,
                containerName: this.settings.azure.containerName,
                blobEndpoint: this.settings.azure.blobEndpoint
            };
        } catch (error) {
            console.error('Error getting Azure configuration:', error);
            return null;
        }
    }

    /**
     * Check if Azure is configured
     */
    isAzureConfigured() {
        return !!(this.settings.azure &&
                 this.settings.azure.tenantId &&
                 this.settings.azure.clientId &&
                 this.settings.azure.clientSecret);
    }

    /**
     * Remove Azure configuration
     */
    removeAzureConfig() {
        if (this.settings.azure) {
            delete this.settings.azure;
            this.saveSettings();
            console.log('Azure configuration removed');
        }
    }
}
