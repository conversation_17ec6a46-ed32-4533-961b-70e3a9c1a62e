/* Retro DOS Terminal Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    background: #000000;
    color: #ff0000;
    overflow: hidden;
    cursor: default;
}

/* Prevent text selection on UI elements */
button, .btn, .tab, .nav, .menu, .header, label, .control-label {
    user-select: none;
}

#terminal-container {
    width: 100vw;
    height: 100vh;
    background: #000000;
    position: relative;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    background: #000000;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

/* Boot Screen */
#boot-screen {
    justify-content: center;
    align-items: center;
}

.boot-content {
    text-align: center;
    max-width: 800px;
}

.iharc-branding {
    text-align: center;
    margin-bottom: 20px;
}

.iharc-logo {
    font-family: 'Courier New', monospace;
    font-size: 24px;
    line-height: 1;
    color: #ff4444;
    font-weight: bold;
    letter-spacing: 3px;
    margin-bottom: 10px;
}

.iharc-name {
    color: #ff0000;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 20px;
}

.ascii-art {
    font-size: 12px;
    line-height: 1;
    color: #ff0000;
    margin-bottom: 20px;
    white-space: pre;
    font-weight: bold;
}

.version-info {
    margin-bottom: 40px;
}

.version-info div {
    margin: 5px 0;
    color: #ff4444;
}

.boot-progress {
    margin-top: 40px;
}

/* Journey Animation */
.journey-animation {
    width: 400px;
    height: 140px;
    margin: 20px auto;
    position: relative;
    overflow: visible;
}

.journey-scene {
    width: 100%;
    height: 80px;
    position: relative;
    border: 2px solid #ff0000;
    background: linear-gradient(to bottom, #001122 0%, #000000 100%);
    overflow: hidden;
}

.scene-background {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: #333333;
    border-top: 1px solid #ff4444;
}

.person {
    position: absolute;
    bottom: 20px;
    left: 10px;
    transition: all 2s ease-in-out;
    z-index: 10;
}

/* Animated Stick Figure */
.stick-figure {
    position: relative;
    width: 30px;
    height: 40px;
}

.head {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    border: 2px solid #ff0000;
    border-radius: 50%;
    background: transparent;
}

.body {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 20px;
    background: #ff0000;
}

.arm {
    position: absolute;
    top: 15px;
    width: 2px;
    height: 12px;
    background: #ff0000;
    transform-origin: top center;
}

.arm-left {
    left: 10px;
    animation: arm-swing-left 0.8s infinite ease-in-out;
}

.arm-right {
    right: 10px;
    animation: arm-swing-right 0.8s infinite ease-in-out;
}

.leg {
    position: absolute;
    top: 28px;
    width: 2px;
    height: 15px;
    background: #ff0000;
    transform-origin: top center;
}

.leg-left {
    left: 12px;
    animation: leg-walk-left 0.8s infinite ease-in-out;
}

.leg-right {
    right: 12px;
    animation: leg-walk-right 0.8s infinite ease-in-out;
}

/* Walking Animations */
@keyframes arm-swing-left {
    0%, 100% { transform: rotate(-15deg); }
    50% { transform: rotate(15deg); }
}

@keyframes arm-swing-right {
    0%, 100% { transform: rotate(15deg); }
    50% { transform: rotate(-15deg); }
}

@keyframes leg-walk-left {
    0%, 100% { transform: rotate(-20deg); }
    50% { transform: rotate(20deg); }
}

@keyframes leg-walk-right {
    0%, 100% { transform: rotate(20deg); }
    50% { transform: rotate(-20deg); }
}

.shelter {
    position: absolute;
    bottom: 20px;
    font-size: 20px;
    transition: all 1s ease-in-out;
    opacity: 0;
}

.progress-path {
    position: absolute;
    bottom: 18px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, #ff0000 0%, transparent 0%);
    transition: background 0.5s ease;
}

.journey-text {
    text-align: center;
    color: #ff4444;
    font-size: 14px;
    margin-top: 15px;
    margin-bottom: 15px;
    min-height: 40px;
    line-height: 1.3;
    padding: 0 10px;
}

/* Animation States */
.journey-scene.stage-1 .person {
    left: 10px;
}

.journey-scene.stage-1 .shelter {
    left: 50px;
    opacity: 0;
}

.journey-scene.stage-2 .person {
    left: 25%;
}

.journey-scene.stage-2 .shelter {
    left: 30%;
    opacity: 1;
}

.journey-scene.stage-2 .shelter::before {
    content: "⛺";
}

.journey-scene.stage-3 .person {
    left: 50%;
}

.journey-scene.stage-3 .shelter {
    left: 55%;
    opacity: 1;
}

.journey-scene.stage-3 .shelter::before {
    content: "🏠";
}

.journey-scene.stage-4 .person {
    left: 75%;
}

.journey-scene.stage-4 .shelter {
    left: 80%;
    opacity: 1;
}

.journey-scene.stage-4 .shelter::before {
    content: "🏡";
}

.journey-scene.stage-5 .person {
    left: calc(100% - 50px);
}

.journey-scene.stage-5 .shelter {
    left: calc(100% - 45px);
    opacity: 1;
}

.journey-scene.stage-5 .shelter::before {
    content: "🏠";
}

/* Progress path animation */
.journey-scene.stage-1 .progress-path {
    background: linear-gradient(to right, #ff0000 20%, transparent 20%);
}

.journey-scene.stage-2 .progress-path {
    background: linear-gradient(to right, #ff0000 40%, transparent 40%);
}

.journey-scene.stage-3 .progress-path {
    background: linear-gradient(to right, #ff0000 60%, transparent 60%);
}

.journey-scene.stage-4 .progress-path {
    background: linear-gradient(to right, #ff0000 80%, transparent 80%);
}

.journey-scene.stage-5 .progress-path {
    background: linear-gradient(to right, #ff0000 100%, transparent 100%);
}

.progress-bar {
    width: 100%;
    height: 20px;
    border: 2px solid #ff0000;
    margin: 10px auto 15px;
    background: #000000;
}

.progress-fill {
    height: 100%;
    background: #ff0000;
    width: 0%;
    transition: width 0.3s ease;
}

.boot-messages {
    min-height: 60px;
    text-align: left;
    font-size: 14px;
}

.boot-messages div {
    margin: 2px 0;
    color: #ff4444;
}

/* Login Screen */
#login-screen {
    justify-content: center;
    align-items: center;
}

.login-content {
    text-align: center;
    max-width: 500px;
    border: 2px solid #ff0000;
    padding: 40px;
    background: #000000;
}

.login-header {
    margin-bottom: 30px;
    text-align: center;
}

.iharc-brand-login {
    font-size: 20px;
    font-weight: bold;
    color: #ff4444;
    margin-bottom: 5px;
    letter-spacing: 2px;
}

.system-name {
    font-size: 24px;
    font-weight: bold;
    color: #ff0000;
    margin-bottom: 10px;
}

.login-prompt {
    color: #ff4444;
    font-size: 14px;
}

.login-form {
    text-align: left;
}

.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 5px;
    color: #ff0000;
    font-weight: bold;
}

.form-field input,
.form-field select {
    width: 100%;
    max-width: 100%;
    padding: 8px;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: inherit;
    font-size: 14px;
    box-sizing: border-box;
}

.form-field input:focus,
.form-field select:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 5px #ff0000;
}

.form-field select option {
    background: #000000;
    color: #ff0000;
}

.form-field select option.option-header {
    font-weight: bold;
    background: #333333;
    color: #ffffff;
    font-style: italic;
}

/* Checkbox Styling */
.form-field input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #ff0000;
    background: #000000;
    margin-right: 8px;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}

.form-field input[type="checkbox"]:checked {
    background: #ff0000;
}

.form-field input[type="checkbox"]:checked::before {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 1px;
    color: #000000;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

.form-field input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 5px #ff0000;
    border-color: #ff4444;
}

.form-field input[type="checkbox"]:hover {
    border-color: #ff4444;
}

/* Custom Date Input */
.custom-date-input {
    display: flex;
    align-items: center;
    gap: 2px;
    max-width: 120px;
}

.custom-date-input input[type="text"] {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 2px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    text-align: center;
    width: auto !important;
    min-width: 0;
    max-width: none;
    box-sizing: border-box;
}

.custom-date-input .date-year {
    width: 38px;
}

.custom-date-input .date-month,
.custom-date-input .date-day {
    width: 24px;
}

.custom-date-input .date-separator {
    color: #ff0000;
    font-weight: bold;
    font-size: 12px;
    line-height: 1;
}

.custom-date-input input[type="text"]:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 2px #ff0000;
}

.custom-date-input input[type="text"].invalid {
    border-color: #ff6666;
    background: #330000;
}

.custom-date-input.invalid {
    animation: shake 0.2s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-1px); }
    75% { transform: translateX(1px); }
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.primary-button {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 10px 30px;
    font-family: inherit;
    font-weight: bold;
    cursor: pointer;
    font-size: 14px;
}

.primary-button:hover {
    background: #ff4444;
}

.primary-button:active {
    background: #cc0000;
}

.login-status {
    margin-top: 20px;
    min-height: 20px;
    color: #ff4444;
}

/* Main Interface */
#main-screen {
    flex-direction: column;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 2px solid #ff0000;
    background: #000000;
}

.header-left .system-name {
    font-size: 18px;
    font-weight: bold;
    margin-right: 20px;
}

.user-info {
    color: #ff4444;
    font-size: 14px;
}

.datetime {
    color: #ff4444;
    font-size: 14px;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Tab Bar */
.tab-bar {
    display: flex;
    background: #000000;
    border-bottom: 1px solid #ff0000;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    scrollbar-width: thin;
    scrollbar-color: #ff0000 #000000;
}

.tab-bar::-webkit-scrollbar {
    height: 4px;
}

.tab-bar::-webkit-scrollbar-track {
    background: #000000;
}

.tab-bar::-webkit-scrollbar-thumb {
    background: #ff0000;
    border-radius: 2px;
}

.tab-bar::-webkit-scrollbar-thumb:hover {
    background: #ff4444;
}

.tab {
    padding: 12px 24px;
    border-right: 1px solid #ff0000;
    cursor: pointer;
    color: #ff4444;
    font-weight: bold;
    transition: all 0.2s ease;
    user-select: none;
}

.tab:hover {
    background: #330000;
    color: #ff0000;
}

.tab.active {
    background: #ff0000;
    color: #000000;
}

.admin-tab {
    background: #330000;
    color: #ffaa00;
    font-weight: bold;
}

.admin-tab:hover {
    background: #440000;
    color: #ffcc00;
}

.admin-tab.active {
    background: #ffaa00;
    color: #000000;
}

.logout-tab {
    margin-left: auto;
    border-left: 1px solid #ff0000;
    border-right: none;
}

/* Content Area - reduced padding for maximum space usage */
.content-area {
    flex: 1;
    padding: 8px;
    background: #000000;
    overflow-y: auto;
    height: calc(100vh - 120px);
    max-height: calc(100vh - 120px);
    position: relative;
    box-sizing: border-box;
}

/* ===== Dashboard Redesigned Grid ===== */
.dashboard-container.redesigned {
    display: flex;
    flex-direction: column;
    gap: 0;
}

/* Dashboard header styles removed - using main app header instead */

/* Main grid: two columns with responsive stacking */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
}

.grid-left, .grid-right, .dashboard-main-cards { width: 100%; }

@media (min-width: 900px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
        align-items: start;
    }
    .grid-left { order: 1; }
    .grid-right { order: 2; }
}

/* ===== 1024x768 No-Scroll Optimization =====
   Ensure the dashboard fits within 1024x768 without vertical scrolling.
   Strategy:
   - Constrain overall content area height.
   - Use grid rows with fixed compact heights.
   - Eliminate vertical scroll at this breakpoint; compact typography/spacing. */
@media (min-width: 900px) and (max-width: 1280px) {
    /* Lock the main viewport to prevent body/content scroll */
    body {
        overflow: hidden;
    }
    .content-area {
        height: calc(768px - 120px); /* preserve existing header/footer offsets used by app */
        max-height: calc(768px - 120px);
        overflow: hidden; /* no outer scroll */
        padding: 12px;    /* slightly tighter padding */
    }

    /* Dashboard grid spacing tightened */
    .dashboard-container.redesigned .dashboard-grid {
        padding: 8px;
        gap: 8px;
    }

    /* Two-column fixed rows layout */
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
        align-items: start;
    }
    .grid-left,
    .grid-right {
        display: grid;
        gap: 8px;
    }

    /* Allocate approximate fixed heights to avoid scroll
       Left: Incidents ~380, Alerts ~200
       Right: Weather ~360, Stats ~220 (more space for weather forecast) */
    .grid-left { grid-template-rows: 380px 200px; }
    .grid-right { grid-template-rows: 360px 220px; }

    /* Card layout compaction */
    .dashboard-section,
    .weather-widget {
        padding: 6px;
    }
    .section-header, .widget-header {
        margin-bottom: 4px;
        min-height: 24px;
    }
    .section-header h3,
    .widget-header h3 {
        font-size: 15px;
    }
    .section-footer, .weather-footer {
        padding-top: 4px;
        min-height: 18px;
    }

    /* Cards fill allotted row; inner content does not scroll at this breakpoint */
    .grid-left .dashboard-section,
    .grid-right .dashboard-section,
    .grid-right .weather-widget {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .dashboard-section .section-content,
    .weather-widget .widget-content {
        flex: 1 1 auto;
        overflow: hidden;
        padding: 4px;
    }

    /* Incidents: fixed-height rows to guarantee fit */
    .incidents-dashboard-list {
        display: grid;
        grid-auto-rows: 56px; /* consistent row height */
        gap: 6px;
    }
    .incidents-dashboard-list .incident-row {
        margin: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    /* Weather compact styles (dashboard inline also defines, these ensure global fallback) */
    .weather-compact { display:grid; grid-template-columns:auto 1fr auto; align-items:center; gap:8px; border:1px solid #ff0000; padding:6px; }
    .wc-left { display:flex; align-items:center; gap:8px; min-width:0; }
    .wc-icon { font-size:18px; }
    .wc-temp { font-size:20px; font-weight:bold; }
    .wc-desc { color:#ff4444; font-size:12px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
    .wc-meta { display:grid; grid-auto-flow:column; grid-auto-columns:minmax(0,auto); gap:8px; }
    .wc-chip { border:1px dashed #ff0000; padding:2px 4px; font-size:11px; color:#ff4444; white-space:nowrap; }
    
    /* Forecast grid styles moved to dashboard template for better scoping */

    /* Alerts: compact rows */
    .alerts-list {
        display: grid;
        grid-auto-rows: 48px;
        gap: 6px;
    }
    .alerts-list .alert-row {
        grid-template-columns: 18px 1fr auto;
        padding: 4px;
        margin: 0;
    }
    .alerts-list .dismiss-btn {
        padding: 2px 6px;
    }

    /* Weather compaction */
    .weather-main .weather-temp { font-size: 18px; }
    .weather-main .weather-icon { font-size: 18px; }
    .weather-details {
        grid-template-columns: repeat(4, 1fr);
        gap: 4px;
    }
    .weather-detail { padding: 3px; }
    .weather-forecast.compact-row {
        grid-template-columns: 28px 1fr 28px;
        margin-top: 6px;
    }
    .forecast-scroll {
        grid-auto-columns: 100px; /* smaller tiles to fit */
        gap: 4px;
        padding: 3px;
    }
    .forecast-hour { padding: 3px; }
    .fh-time, .fh-extra { font-size: 10px; }
    .weather-footer small { font-size: 11px; }

    /* Stats: compact grid if items render */
    .stats-section .section-content .stat-item {
        display: grid;
        grid-template-columns: 1fr auto;
        padding: 4px 0;
    }

    /* Quick actions compact */
    .quick-actions {
        grid-template-columns: 1fr;
        gap: 6px;
    }
    .quick-actions .action-btn {
        padding: 4px 2px;
        min-height: 45px;
    }
}

/* Sections / Cards */
.dashboard-section {
    border: 1px solid #ff0000;
    background: #0a0a0a;
    padding: 8px 8px 6px;
}
.section-header,
.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}
.section-header h3,
.widget-header h3 {
    color: #ff0000;
    font-size: 16px;
}
.section-actions .action-btn,
.widget-controls .view-alerts-btn,
.view-all-alerts {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    text-decoration: none;
    font-weight: bold;
}
.section-actions .action-btn:hover,
.widget-controls .view-alerts-btn:hover,
.view-all-alerts:hover {
    background: #330000;
}

/* Scrollable content areas */
.section-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 6px;
    border-top: 1px dashed #ff0000;
    border-bottom: 1px dashed #ff0000;
}
.section-footer {
    padding-top: 6px;
    color: #ff4444;
    font-size: 12px;
}

/* Incidents list rows */
.incidents-dashboard-list .incident-row {
    border: 1px solid #ff0000;
    padding: 6px;
    margin-bottom: 6px;
    cursor: pointer;
}
.incidents-dashboard-list .incident-row:hover {
    background: #220000;
}
.incidents-dashboard-list .row-main {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-bottom: 2px;
}
.badge { border: 1px solid #ff0000; padding: 1px 4px; font-size: 11px; }
.status-open { color: #00ff00; }
.priority-high { color: #ffaa00; }

/* Alerts Panel */
.alerts-section .severity-dot {
    display: inline-block;
    width: 8px; height: 8px;
    background: #ffaa00; border-radius: 50%;
    margin-right: 6px;
}
.alerts-list [role="listitem"], .alerts-list .alert-row {
    display: grid;
    grid-template-columns: 20px 1fr auto;
    gap: 8px;
    align-items: center;
    border: 1px solid #ff0000;
    padding: 6px;
    margin-bottom: 6px;
}
.alerts-list .alert-icon { text-align: center; }
.alerts-list .alert-meta { color: #ff4444; font-size: 12px; }
.alerts-list .dismiss-btn {
    background: transparent; border: 1px solid #ff0000; color: #ff0000; padding: 2px 6px; cursor: pointer;
}
.alerts-list .placeholder { color: #666; text-align: center; padding: 10px; }

/* Weather widget */
.weather-widget .widget-content { padding: 6px; }
.weather-display { border: 1px solid #ff0000; padding: 6px; }
.weather-main { display: flex; align-items: center; gap: 10px; margin-bottom: 4px; }
.weather-icon { font-size: 20px; }
.weather-temp { font-size: 20px; font-weight: bold; }
.weather-details { display: grid; grid-template-columns: repeat(4, minmax(0,1fr)); gap: 6px; margin-top: 6px; }
.weather-detail { border: 1px dashed #ff0000; padding: 4px; }
.weather-forecast.compact-row {
    display: grid;
    grid-template-columns: 32px 1fr 32px;
    gap: 6px;
    align-items: center;
    margin-top: 8px;
}
.forecast-scroll {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 120px;
    overflow-x: auto;
    gap: 6px;
    padding: 4px;
    border: 1px dashed #ff0000;
}
.forecast-hour {
    border: 1px solid #ff0000;
    padding: 4px;
}
.fh-time { color: #ff4444; font-size: 12px; }
.fh-extra { color: #ff4444; font-size: 11px; display: grid; gap: 2px; margin-top: 2px; }
.forecast-nav {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px;
    cursor: pointer;
}

/* Sidebar Quick actions - large, thumb friendly */
.dashboard-sidebar .quick-actions,
.sidebar-content .quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
}
.quick-actions .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
    padding: 4px 2px;
    border: 1px solid #ff0000;
    background: #111;
    cursor: pointer;
    width: 100%;
    min-height: 40px;
    text-align: center;
}
.quick-actions .action-btn:hover { background: #220000; }
.quick-actions .action-icon { font-size: 12px; }
.quick-actions .action-text { font-weight: bold; font-size: 7px; line-height: 1.0; }

/* Utility */
.visually-hidden {
    position: absolute !important;
    height: 1px; width: 1px;
    overflow: hidden;
    clip: rect(1px, 1px, 1px, 1px);
    white-space: nowrap;
}
 
/* End of file */

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    border-top: 2px solid #ff0000;
    background: #000000;
    font-size: 12px;
}

.status-left {
    color: #ff0000;
    flex: 1;
}

.iharc-brand {
    color: #ff0000;
    font-weight: bold;
    font-size: 13px;
    letter-spacing: 1px;
}

.status-separator {
    margin: 0 8px;
    color: #ff0000;
}

.status-center {
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
}

.status-right {
    color: #ff4444;
    flex: 1;
    text-align: right;
}

/* Network Status */
.network-status {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #ff0000;
    font-weight: bold;
    font-size: 11px;
}

.network-indicator {
    font-size: 14px;
    line-height: 1;
}

.network-status.online .network-indicator {
    color: #ff0000;
    text-shadow: 0 0 3px #ff0000;
}

.network-status.offline .network-indicator {
    color: #666666;
}

.network-status.offline #network-text {
    color: #666666;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #000000;
    border: 1px solid #ff0000;
}

::-webkit-scrollbar-thumb {
    background: #ff0000;
}

::-webkit-scrollbar-thumb:hover {
    background: #ff4444;
}

/* Selection */
::selection {
    background: #ff0000;
    color: #000000;
}

/* Animations */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.cursor {
    animation: blink 1s infinite;
}

/* Modal Dialogs */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-dialog {
    background: #000000;
    border: 2px solid #ff0000;
    min-width: 400px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.form-modal {
    min-width: 500px;
}

.search-modal {
    min-width: 700px;
    max-width: 900px;
    max-height: 80vh;
}

/* Search Interface */
.search-form {
    margin-bottom: 20px;
}

.search-results {
    border-top: 1px solid #ff0000;
    padding-top: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.search-results h4 {
    color: #ff0000;
    margin-bottom: 15px;
    font-size: 16px;
}

.results-section {
    margin-bottom: 20px;
}

.results-section h5 {
    color: #ff4444;
    margin-bottom: 10px;
    font-size: 14px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.record-item {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.record-item:hover {
    background: #220000;
}

.record-header {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 5px;
}

.record-details {
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 5px;
}

.record-details span {
    display: inline-block;
    margin-right: 15px;
}

.record-meta {
    color: #666666;
    font-size: 11px;
    font-style: italic;
}

.no-results {
    text-align: center;
    color: #ff4444;
    padding: 40px;
    font-style: italic;
}

/* Quick Add Section for Person Creation */
.quick-add-section {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #ff4444;
    background: rgba(255, 68, 68, 0.1);
    border-radius: 4px;
}

.quick-add-section p {
    color: #ffff00;
    margin-bottom: 15px;
    font-weight: bold;
    font-style: normal;
}

.create-new-person-btn {
    display: inline-flex !important;
    align-items: center;
    gap: 8px;
    padding: 12px 20px !important;
    font-size: 14px !important;
    font-weight: bold;
    border: 2px solid #ffff00 !important;
    background: rgba(255, 255, 0, 0.1) !important;
    color: #ffff00 !important;
    cursor: pointer;
    transition: all 0.2s ease;
}

.create-new-person-btn:hover {
    background: rgba(255, 255, 0, 0.2) !important;
    border-color: #ffffff !important;
    color: #ffffff !important;
}

.create-new-person-btn .action-icon {
    font-size: 16px;
}

/* Quick Person Form Styles */
.quick-person-form .form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.quick-person-form .form-row .form-field {
    flex: 1;
}

.form-info {
    color: #ffff00;
    margin-bottom: 20px;
    font-style: italic;
}

/* Record Detail View */
.record-detail-modal {
    min-width: 900px;
    max-width: 1200px;
    max-height: 90vh;
}

.record-view-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr;
    gap: 20px;
    min-height: 500px;
}

/* Profile Section */
.profile-section {
    grid-row: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    background: #111111;
    border: 2px solid #ff0000;
}

.profile-picture {
    text-align: center;
    margin-bottom: 20px;
}

.ascii-avatar {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: #ff0000;
    line-height: 1.1;
    white-space: pre;
    background: #000000;
    padding: 8px;
    border: 2px solid #ff0000;
    margin-bottom: 10px;
    text-align: center;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
}

.profile-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    word-wrap: break-word;
}

.record-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.record-actions button {
    width: 100%;
    padding: 10px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

/* Details and Activities Sections */
.details-section,
.activities-section {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 20px;
}

.details-section h4,
.activities-section h4 {
    color: #ff0000;
    margin: 0 0 15px 0;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

/* Record Details */
.record-details-view {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.detail-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
    min-height: 30px;
}

.detail-label {
    color: #ff4444;
    font-weight: bold;
    min-width: 140px;
    margin-right: 10px;
}

.detail-value {
    color: #ff0000;
    flex: 1;
}

.detail-value em {
    color: #666666;
    font-style: italic;
}

/* Inline Edit Controls */
.edit-controls {
    display: none;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.edit-input {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    font-family: inherit;
    font-size: 13px;
    flex: 1;
    max-width: 200px;
}

.edit-input:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 3px #ff0000;
}

.edit-input[type="checkbox"] {
    width: 16px;
    height: 16px;
    flex: none;
}

.edit-input textarea {
    resize: vertical;
    min-height: 40px;
}

.save-field-btn,
.cancel-field-btn {
    background: none;
    border: 1px solid #ff0000;
    color: #ff0000;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
}

.save-field-btn:hover {
    background: #00ff00;
    color: #000000;
    border-color: #00ff00;
}

.cancel-field-btn:hover {
    background: #ff0000;
    color: #000000;
}

/* Activities */
.activities-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.activities-header h4 {
    color: #ff0000;
    margin: 0;
}

.activities-list {
    max-height: 500px;
    overflow-y: auto;
}

.activity-item {
    background: #111111;
    border: 1px solid #ff0000;
    margin-bottom: 15px;
    padding: 15px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.activity-title {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
}

.activity-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    font-size: 12px;
}

.activity-type {
    background: #ff0000;
    color: #000000;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
}

.priority {
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
}

.priority-low { background: #666666; color: #ffffff; }
.priority-medium { background: #ffaa00; color: #000000; }
.priority-high { background: #ff6600; color: #ffffff; }
.priority-urgent { background: #ff0000; color: #ffffff; }

.activity-date {
    color: #ff4444;
}

.activity-content {
    color: #ff4444;
    margin-bottom: 10px;
    line-height: 1.4;
}

.activity-content p {
    margin: 0 0 10px 0;
}

.activity-location,
.activity-outcome,
.activity-findings,
.activity-action {
    margin: 5px 0;
    font-size: 13px;
}

.activity-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666666;
    border-top: 1px solid #333333;
    padding-top: 8px;
}

.follow-up-required {
    color: #ffaa00;
    font-weight: bold;
}

.no-activities {
    text-align: center;
    color: #ff4444;
    padding: 40px;
    font-style: italic;
}

.close-button {
    background: none;
    border: none;
    color: #ff0000;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-button:hover {
    color: #ff4444;
}

/* Dashboard Styles - New Responsive Layout */
.dashboard-container {
    padding: 15px;
    max-width: 100%;
    margin: 0 auto;
    height: calc(100vh - 180px);
    overflow-y: auto;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 60px 1fr;
    gap: 8px;
    height: 100%;
}

/* Sidebar styling */
.dashboard-sidebar {
    background: #111111;
    border: 2px solid #ff0000;
    display: flex;
    flex-direction: column;
    width: 60px;
    min-width: 60px;
    height: 100%;
}

.sidebar-header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px;
    padding: 4px 2px;
    border-bottom: 1px solid #ff4444;
}

.sidebar-header h3 {
    color: #ff0000;
    font-size: 10px;
    margin: 0;
    font-weight: bold;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 2px 2px 2px;
}

/* Main content area */
.main-content-area {
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 100%;
}

/* Bottom sections container for weather and stats */
.bottom-sections {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.dashboard-section {
    background: #111111;
    border: 2px solid #ff0000;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1px;
    padding-bottom: 2px;
    border-bottom: 1px solid #ff4444;
}

.section-header h3 {
    color: #ff0000;
    font-size: clamp(14px, 2vw, 18px);
    margin: 0;
    font-weight: bold;
}

.section-content {
    flex: 1;
    overflow-y: auto;
}

.section-footer {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ff4444;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    /* Tab bar adjustments for small screens */
    .tab {
        padding: 8px 10px;
        font-size: 11px;
        flex-shrink: 0;
        min-width: fit-content;
    }

    .tab-bar {
        padding-right: 10px;
    }

    .dashboard-container {
        padding: 10px;
        height: calc(100vh - 160px);
    }

    .dashboard-content {
        grid-template-columns: 50px 1fr;
        gap: 6px;
    }

    .main-content-area {
        gap: 10px;
    }

    .bottom-sections {
        gap: 10px;
    }

    .dashboard-section {
        padding: 10px;
    }

    .section-header h3 {
        font-size: clamp(12px, 1.8vw, 16px);
    }

    /* Sidebar adjustments */
    .dashboard-sidebar {
        width: 50px;
        min-width: 50px;
    }

    .sidebar-header {
        padding: 8px;
    }

    .sidebar-header h3 {
        font-size: 12px;
    }

    .action-btn {
        min-height: 40px;
        padding: 4px 2px;
        font-size: 7px;
    }

    .action-icon {
        font-size: 12px;
    }

    .action-text {
        font-size: 6px;
    }
}

@media (max-width: 768px) {
    .dashboard-content {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    /* Convert to single column layout on small screens */
    .main-content-area {
        order: 2;
        gap: 10px;
    }

    .dashboard-sidebar {
        order: 1;
        width: 100%;
        min-width: 100%;
        height: auto;
        flex-direction: column;
    }

    .bottom-sections {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    /* Make quick actions horizontal on small screens */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
        gap: 6px;
    }

    .action-btn {
        min-height: 30px;
        padding: 4px 2px;
    }

    .weather-section,
    .stats-section {
        min-height: 150px;
    }
}

/* Weather Section Specific */
.weather-section {
    overflow: hidden;
    min-height: 180px;
}

/* Weather and Stats sections */
.weather-section,
.stats-section {
    min-height: 180px;
}

/* Stats Section Specific */
.stats-section {
    overflow: hidden;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ff4444;
    flex-shrink: 0;
}

.widget-header h3 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.task-count {
    background: #ff0000;
    color: #000000;
    padding: 3px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 12px;
}

.widget-content {
    color: #ff4444;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Special handling for weather widget to prevent scrolling */
.weather-widget .widget-content {
    overflow: hidden;
}

/* Forecast slider: compact single-row horizontal scroller */
.weather-forecast.compact-row {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 6px;
    padding-top: 6px;
    border-top: 1px solid #331111;
    flex-shrink: 0;
}

.forecast-scroll {
    display: flex;
    gap: 6px;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    padding: 4px;
    border: 1px solid #331111;
    flex: 1;
    outline: none;
}

.forecast-scroll:focus {
    border-color: #ff4444;
    box-shadow: inset 0 0 0 1px #ff0000;
}

.forecast-hour {
    min-width: 90px;
    max-width: 110px;
    flex: 0 0 auto;
    background: #0b0b0b;
    border: 1px solid #331111;
    padding: 6px 6px;
    text-align: center;
    scroll-snap-align: start;
}

.forecast-hour .fh-time {
    color: #ff6666;
    font-size: 10px;
    margin-bottom: 2px;
}

.forecast-hour .fh-icon {
    font-size: 14px;
    margin-bottom: 2px;
}

.forecast-hour .fh-temp {
    color: #ff0000;
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 2px;
}

.forecast-hour .fh-extra {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2px;
    font-size: 9px;
    color: #ff4444;
}

.forecast-nav {
    background: #111111;
    color: #ff0000;
    border: 1px solid #ff0000;
    padding: 4px 6px;
    cursor: pointer;
    user-select: none;
    font-weight: bold;
    font-size: 12px;
    height: 28px;
    min-width: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.forecast-nav:hover {
    background: #220000;
    border-color: #ff4444;
}

.forecast-nav:focus {
    outline: none;
    box-shadow: 0 0 0 2px #ff0000;
}

/* Legacy vertical forecast styling fallback */
.weather-forecast .forecast-header {
    color: #ff0000;
    font-weight: bold;
    margin: 4px 0;
    font-size: 12px;
}

.weather-forecast .forecast-list {
    display: grid;
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 6px;
    margin-top: 6px;
}

@media (max-width: 1024px) {
    .forecast-hour {
        min-width: 80px;
    }
}

@media (max-width: 768px) {
    .forecast-hour {
        min-width: 72px;
        padding: 4px;
    }
    .forecast-hour .fh-extra {
        grid-template-columns: 1fr 1fr;
    }
}

.loading {
    text-align: center;
    color: #ff4444;
    font-style: italic;
    padding: 20px;
}

.error {
    text-align: center;
    color: #ff6666;
    font-style: italic;
    padding: 20px;
}

/* Weather Widget - Responsive Design */
.weather-display {
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow: hidden;
    width: 100%;
}

.weather-main {
    margin-bottom: 0.4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
}

.weather-icon {
    font-size: clamp(16px, 2.5vw, 24px);
    margin-bottom: 0.1rem;
}

.weather-temp {
    font-size: clamp(20px, 3.5vw, 32px);
    font-weight: bold;
    color: #ff0000;
    display: block;
    line-height: 1;
}

.weather-condition {
    font-size: clamp(10px, 1.5vw, 16px);
    color: #ff4444;
    line-height: 1;
}

.weather-location {
    font-size: clamp(8px, 1.2vw, 14px);
    color: #ff6666;
    margin-bottom: 0.4rem;
}

.weather-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.2rem;
    margin-bottom: 0.4rem;
    flex-shrink: 0;
    overflow: hidden;
}

.weather-detail {
    text-align: center;
}

.detail-label {
    display: block;
    font-size: clamp(7px, 1vw, 12px);
    color: #ff4444;
    line-height: 1;
}

.detail-value {
    display: block;
    font-size: clamp(9px, 1.3vw, 14px);
    font-weight: bold;
    color: #ff0000;
    line-height: 1;
}

.weather-alert {
    background: #330000;
    border: 1px solid #ff6666;
    padding: 0.3rem 0.4rem;
    font-size: clamp(7px, 1vw, 12px);
    color: #ff6666;
    text-align: left;
    line-height: 1.1;
    margin-top: 0.3rem;
    flex-shrink: 0;
}

.weather-alert.alert-severe {
    background: #440000;
    border-color: #ff3333;
    color: #ff3333;
}

.weather-alert.alert-moderate {
    background: #332200;
    border-color: #ffaa00;
    color: #ffaa00;
}

.weather-alert.alert-low {
    background: #003322;
    border-color: #00aa66;
    color: #00aa66;
}

/* Weather Forecast Styles */
.weather-forecast {
    margin-top: 0.5rem;
    flex-shrink: 0;
}

.forecast-header {
    margin-bottom: 0.3rem;
    padding-bottom: 0.2rem;
    border-bottom: 1px solid #444444;
}

.forecast-title {
    font-size: clamp(8px, 1.2vw, 14px);
    color: #ff6666;
    font-weight: bold;
}

.forecast-timeline {
    display: flex;
    gap: 0.2rem;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.forecast-timeline::-webkit-scrollbar {
    display: none;
}

.forecast-hour {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.2rem;
    background: #111111;
    border: 1px solid #444444;
    border-radius: 3px;
    min-width: 45px;
}

.forecast-time {
    font-size: clamp(6px, 0.8vw, 10px);
    color: #ff6666;
    margin-bottom: 0.1rem;
    white-space: nowrap;
}

.forecast-icon {
    font-size: clamp(8px, 1.2vw, 14px);
    margin-bottom: 0.1rem;
}

.forecast-temp {
    font-size: clamp(7px, 1vw, 12px);
    color: #ff4444;
    font-weight: bold;
    margin-bottom: 0.1rem;
}

.forecast-precip {
    font-size: clamp(6px, 0.8vw, 10px);
    color: #66aaff;
}

/* Weather Alerts Summary */
.weather-alerts-summary {
    margin-top: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.2rem;
    background: #222222;
    border: 1px solid #666666;
    border-radius: 3px;
}

.alerts-count {
    font-size: clamp(7px, 1vw, 12px);
    color: #ffaa00;
}

.view-alerts-btn {
    background: #330000;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 0.1rem 0.3rem;
    font-size: clamp(6px, 0.8vw, 10px);
    cursor: pointer;
    border-radius: 2px;
    transition: all 0.2s;
}

.view-alerts-btn:hover {
    background: #440000;
    border-color: #ff6666;
    color: #ff6666;
}

/* Weather Alerts Modal */
.weather-alerts-modal {
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.current-conditions {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #111111;
    border: 1px solid #444444;
    border-radius: 5px;
}

.current-conditions h3 {
    color: #ff6666;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.conditions-summary {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.conditions-summary .location {
    color: #ff8888;
    font-weight: bold;
}

.conditions-summary .temperature {
    color: #ff4444;
    font-size: 1.2rem;
    font-weight: bold;
}

.conditions-summary .condition {
    color: #ff6666;
}

.alerts-section h3 {
    color: #ff6666;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.weather-alert-detail {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 5px;
    border: 1px solid;
}

.weather-alert-detail.alert-severe {
    background: #440000;
    border-color: #ff3333;
}

.weather-alert-detail.alert-moderate {
    background: #332200;
    border-color: #ffaa00;
}

.weather-alert-detail.alert-low {
    background: #003322;
    border-color: #00aa66;
}

.alert-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.alert-icon {
    font-size: 1.2rem;
}

.alert-title {
    font-weight: bold;
    flex: 1;
}

.alert-severity {
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: bold;
}

.severity-severe {
    background: #ff3333;
    color: #ffffff;
}

.severity-moderate {
    background: #ffaa00;
    color: #000000;
}

.severity-low {
    background: #00aa66;
    color: #ffffff;
}

.alert-message {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.alert-time {
    font-size: 0.9rem;
    color: #888888;
    margin-top: 0.3rem;
}

.no-alerts {
    text-align: center;
    padding: 2rem;
    color: #00aa66;
}

.no-alerts-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.no-alerts-message {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.no-alerts-sub {
    color: #888888;
    font-size: 0.9rem;
}

.weather-footer {
    margin-top: 0.3rem;
    padding-top: 0.2rem;
    border-top: 1px solid #444444;
    flex-shrink: 0;
}

.weather-footer small {
    color: #888888;
    font-size: clamp(6px, 0.8vw, 10px);
    line-height: 1;
}

/* Weather Widget Media Queries for Better Responsiveness */
@media (max-width: 768px) {
    .weather-temp {
        font-size: clamp(18px, 4vw, 28px);
    }

    .weather-condition {
        font-size: clamp(9px, 2vw, 14px);
    }

    .detail-label {
        font-size: clamp(6px, 1.2vw, 10px);
    }

    .detail-value {
        font-size: clamp(8px, 1.5vw, 12px);
    }
}

@media (min-width: 1200px) {
    .weather-temp {
        font-size: clamp(24px, 2.5vw, 36px);
    }

    .weather-condition {
        font-size: clamp(12px, 1.2vw, 18px);
    }

    .weather-icon {
        font-size: clamp(20px, 2vw, 28px);
    }

    .detail-label {
        font-size: clamp(8px, 0.8vw, 14px);
    }

    .detail-value {
        font-size: clamp(10px, 1vw, 16px);
    }

    .weather-alert {
        font-size: clamp(8px, 0.8vw, 14px);
    }
}

/* Task Queue Widget */
.task-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 8px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-item:hover {
    border-color: #ff0000;
    background: #220000;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
}

.task-number {
    font-weight: bold;
    color: #ff0000;
    font-size: 12px;
}

.task-priority {
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 9px;
    font-weight: bold;
}

.priority-low { background: #666666; color: #ffffff; }
.priority-medium { background: #ffaa00; color: #000000; }
.priority-high { background: #ff6600; color: #ffffff; }
.priority-urgent { background: #ff0000; color: #ffffff; }

.task-description {
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 3px;
    line-height: 1.2;
}

.task-location,
.task-time {
    font-size: 10px;
    color: #ff6666;
}

.no-tasks,
.no-activities {
    text-align: center;
    color: #ff4444;
    font-style: italic;
    padding: 20px;
}


/* Activities Widget */
.activity-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 8px;
    margin-bottom: 8px;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3px;
}

.activity-type {
    background: #ff0000;
    color: #000000;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
}

.activity-date {
    font-size: 11px;
    color: #ff6666;
}

.activity-title {
    color: #ff4444;
    font-size: 13px;
    margin-bottom: 3px;
}

.activity-staff {
    font-size: 11px;
    color: #ff6666;
}

/* Stats Widget */
.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    color: #ff4444;
    font-size: 14px;
}

.stat-value {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
}

/* Quick Actions Widget - Vertical Sidebar Layout */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 0;
}

.action-btn {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
    font-family: inherit;
    font-size: 9px;
    min-height: 30px;
    justify-content: center;
    width: 100%;
    margin-bottom: 4px;
}

.action-btn:hover {
    background: #ff0000;
    color: #000000;
}

.action-icon {
    font-size: 14px;
}

.action-text {
    font-size: 8px;
    font-weight: bold;
    text-align: center;
    line-height: 1.1;
}

/* User Profile Modal */
.user-profile-modal {
    min-width: 600px;
    max-width: 800px;
    max-height: 90vh;
}

.profile-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.profile-info-section,
.security-section,
.session-section {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 20px;
}

.profile-info-section h4,
.security-section h4,
.session-section h4 {
    color: #ff0000;
    margin: 0 0 15px 0;
    font-size: 18px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 8px;
}

.profile-details,
.session-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.profile-field,
.session-field {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
}

.profile-field:last-child,
.session-field:last-child {
    border-bottom: none;
}

.profile-field label,
.session-field label {
    color: #ff4444;
    font-weight: bold;
    min-width: 140px;
    margin-right: 15px;
}

.field-value {
    color: #ff0000;
    flex: 1;
    margin-right: 10px;
}

.status-active {
    color: #00ff00;
    font-weight: bold;
}

.edit-field-btn {
    background: none;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.edit-field-btn:hover {
    background: #ff0000;
    color: #000000;
}

.field-note {
    color: #888888;
    font-size: 12px;
    font-style: italic;
    margin-left: 10px;
}

.security-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.action-button {
    background: #000000;
    border: 2px solid #ff0000;
    color: #ff0000;
    padding: 5px 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-size: 14px;
}

.action-button:hover {
    background: #ff0000;
    color: #000000;
}

.action-icon {
    font-size: 16px;
}

.action-text {
    font-weight: bold;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ff0000;
}

.modal-header h3 {
    color: #ff0000;
    font-size: 18px;
    margin: 0;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #ff0000;
    text-align: right;
}

.secondary-button {
    background: #000000;
    color: #ff0000;
    border: 1px solid #ff0000;
    padding: 5px 5px;
    font-family: inherit;
    font-weight: bold;
    cursor: pointer;
    margin-right: 10px;
}

.secondary-button:hover {
    background: #330000;
}

/* Form Styling */
.form-field textarea {
    width: 100%;
    min-height: 80px;
    padding: 8px;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
}

.form-field textarea:focus {
    outline: none;
    border-color: #ff4444;
    box-shadow: 0 0 5px #ff0000;
}

/* Status Classes */
.status-error {
    color: #ff4444 !important;
}

.status-warning {
    color: #ff6666 !important;
}

.status-success {
    color: #ff0000 !important;
}

.login-status.error {
    color: #ff4444;
}

/* Retro Effects */
.scanlines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background: linear-gradient(transparent 50%, rgba(255, 0, 0, 0.03) 50%);
    background-size: 100% 4px;
    z-index: 999;
}

.crt-effect {
    filter: contrast(1.1) brightness(1.1);
}

.crt-effect::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, transparent 70%, rgba(0, 0, 0, 0.3) 100%);
    pointer-events: none;
    z-index: 998;
}

/* Admin Interface Styles */
.admin-container {
    padding: 20px;
    color: #ff0000;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ff0000;
}

.admin-header h2 {
    color: #ffaa00;
    margin: 0;
    font-size: 24px;
}

.admin-user-info {
    color: #ff4444;
    font-size: 14px;
}

.admin-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.admin-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid #ff0000;
    padding-bottom: 10px;
}

.admin-nav-btn {
    padding: 10px 20px;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    cursor: pointer;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    transition: all 0.2s ease;
}

.admin-nav-btn:hover {
    background: #330000;
    color: #ffaa00;
}

.admin-nav-btn.active {
    background: #ff0000;
    color: #000000;
}

.admin-section {
    border: 1px solid #ff0000;
    padding: 20px;
    background: #111111;
}

.admin-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.admin-section .section-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: 18px;
}

.item-controls {
    margin-bottom: 20px;
}

.search-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input, .filter-select {
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.filter-select {
    min-width: 120px;
}

.search-input:focus, .filter-select:focus {
    outline: none;
    border-color: #ffaa00;
    background: #111111;
}

.items-list {
    border: 1px solid #ff4444;
    background: #000000;
    min-height: 400px;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
}

.items-table th,
.items-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ff4444;
    color: #ff0000;
    font-family: 'Courier New', monospace;
}

.items-table th {
    background: #330000;
    color: #ffaa00;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.items-table tr:hover {
    background: #111111;
}

.item-actions {
    display: flex;
    gap: 10px;
}

.item-actions button {
    padding: 4px 5px;
    font-size: 12px;
    border: 1px solid #ff0000;
    background: #000000;
    color: #ff0000;
    cursor: pointer;
    font-family: 'Courier New', monospace;
}

.item-actions button:hover {
    background: #ff0000;
    color: #000000;
}

.item-actions .edit-btn {
    border-color: #ffaa00;
    color: #ffaa00;
}

.item-actions .edit-btn:hover {
    background: #ffaa00;
    color: #000000;
}

.item-actions .delete-btn {
    border-color: #ff4444;
    color: #ff4444;
}

.item-actions .delete-btn:hover {
    background: #ff4444;
    color: #000000;
}

.stock-low {
    color: #ff4444 !important;
    font-weight: bold;
}

.stock-ok {
    color: #00ff00;
}

.status-active {
    color: #00ff00;
}

.status-inactive {
    color: #ff4444;
}

/* User Management Styles */
.user-controls {
    margin-bottom: 20px;
}

.users-list {
    border: 1px solid #ff4444;
    background: #000000;
    min-height: 400px;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
}

.users-table th,
.users-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ff4444;
    color: #ff0000;
    font-family: 'Courier New', monospace;
}

.users-table th {
    background: #330000;
    color: #ffaa00;
    font-weight: bold;
    position: sticky;
    top: 0;
}

.users-table tr:hover {
    background: #111111;
}

.user-actions {
    display: flex;
    gap: 10px;
}

.user-actions button {
    padding: 4px 5px;
    font-size: 12px;
    border: 1px solid #ff0000;
    background: #000000;
    color: #ff0000;
    cursor: pointer;
    font-family: 'Courier New', monospace;
}

.user-actions button:hover {
    background: #ff0000;
    color: #000000;
}

.role-staff {
    color: #00ff00;
}

.role-admin {
    color: #ffaa00;
    font-weight: bold;
}

.user-actions .promote-btn {
    border-color: #00ff00;
    color: #00ff00;
}

.user-actions .promote-btn:hover {
    background: #00ff00;
    color: #000000;
}

.user-actions .demote-btn {
    border-color: #ffaa00;
    color: #ffaa00;
}

.user-actions .demote-btn:hover {
    background: #ffaa00;
    color: #000000;
}

.user-actions .reset-btn {
    border-color: #0088ff;
    color: #0088ff;
}

.user-actions .reset-btn:hover {
    background: #0088ff;
    color: #000000;
}

/* Responsive */
@media (max-width: 768px) {
    .ascii-art {
        font-size: 8px;
    }

    .tab {
        padding: 8px 12px;
        font-size: 12px;
    }

    .main-header {
        padding: 8px 12px;
    }

    .content-area {
        padding: 12px;
    }

    .modal-dialog {
        min-width: 300px;
        margin: 20px;
    }

    .form-modal {
        min-width: 350px;
    }

    .status-bar {
        padding: 6px 12px;
        font-size: 10px;
    }

    .network-status {
        font-size: 10px;
    }

    .network-indicator {
        font-size: 12px;
    }


    .form-modal {
        min-width: 95vw;
        max-width: 95vw;
        margin: 10px;
    }

    .modal-dialog.form-modal {
        min-width: 95vw;
        max-width: 95vw;
    }
}

/* Update Dialog Styles */
.update-notification .modal-dialog {
    max-width: 600px;
}

.update-details {
    background: #111111;
    border: 1px solid #ff0000;
    padding: 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.update-details p {
    margin: 5px 0;
    font-size: 14px;
}

.release-notes {
    margin: 15px 0;
}

.release-notes h4 {
    color: #ff0000;
    margin-bottom: 10px;
    font-size: 16px;
}

.release-notes-content {
    background: #111111;
    border: 1px solid #333333;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 3px;
}

.release-notes-content code {
    background: #222222;
    padding: 2px 4px;
    border-radius: 2px;
    font-family: 'JetBrains Mono', monospace;
}

.progress-container {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #111111;
    border: 1px solid #ff0000;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff0000, #cc0000);
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: bold;
    color: #ff0000;
}

.download-details {
    text-align: center;
    margin-top: 10px;
    font-size: 12px;
    color: #cccccc;
}

.update-error .modal-dialog {
    border-color: #ff4444;
}

.update-error .modal-header {
    background: #330000;
    border-bottom-color: #ff4444;
}

.no-update .modal-dialog {
    border-color: #00ff00;
}

.no-update .modal-header {
    background: #003300;
    border-bottom-color: #00ff00;
}

.install-confirmation .modal-dialog {
    border-color: #ffff00;
}

.install-confirmation .modal-header {
    background: #333300;
    border-bottom-color: #ffff00;
}

/* Development Mode Dialog */
.development-mode .modal-dialog {
    border-color: #ff8800;
}

.development-mode .modal-header {
    background: #332200;
    border-bottom-color: #ff8800;
}

.dev-info {
    background: #111111;
    border: 1px solid #ff8800;
    padding: 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.dev-info ul {
    margin: 10px 0 0 20px;
    color: #cccccc;
}

.dev-info li {
    margin: 5px 0;
}

/* Force Enable Dialog */
.force-enable .modal-dialog {
    border-color: #ff4444;
}

.force-enable .modal-header {
    background: #330000;
    border-bottom-color: #ff4444;
}

.warning-box {
    background: #331100;
    border: 2px solid #ff8800;
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}

.warning-box p {
    margin: 5px 0;
    color: #ffaa00;
}

.danger-button {
    background: #ff4444;
    color: #ffffff;
    border: 1px solid #ff6666;
    padding: 10px 20px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.danger-button:hover {
    background: #ff6666;
    border-color: #ff8888;
}

/* Release Notes Dialog */
.release-notes-dialog .modal-dialog {
    border-color: #00aaff;
}

.release-notes-dialog .modal-header {
    background: #002244;
    border-bottom-color: #00aaff;
}

.release-info {
    background: #111111;
    border: 1px solid #00aaff;
    padding: 15px;
    margin: 15px 0;
    border-radius: 3px;
}

.release-info p {
    margin: 5px 0;
    font-size: 14px;
}

/* Success Dialog */
.success-dialog .modal-dialog {
    border-color: #00ff00;
}

.success-dialog .modal-header {
    background: #003300;
    border-bottom-color: #00ff00;
}

/* Search Incidents Styles */
.large-modal .modal-dialog {
    max-width: 900px;
    width: 90%;
}

.search-form {
    background: rgba(0, 255, 0, 0.05);
    border: 1px solid #00ff00;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    color: #00ff00;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: clamp(10px, 1.2vw, 14px);
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid #00ff00;
    border-radius: 4px;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    font-size: clamp(10px, 1.1vw, 13px);
}

.form-control:focus {
    outline: none;
    border-color: #00ff88;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
    margin-top: 20px;
}

.search-results {
    margin-top: 20px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #00ff00;
}

.results-header h4 {
    color: #00ff00;
    margin: 0;
    font-size: clamp(14px, 1.5vw, 18px);
}

.results-count {
    color: #00ff88;
    font-size: clamp(10px, 1.1vw, 13px);
    font-weight: bold;
}

.results-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #00ff00;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
}


/* Dispatch Screen Styles */
.dispatch-container {
    padding: 15px;
    height: calc(100vh - 180px);
    overflow: hidden;
}

.dispatch-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 15px;
    height: 100%;
}


.dispatch-header {
    padding: 10px 15px;
    border-bottom: 1px solid #ff0000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #000000;
}

.dispatch-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: 16px;
    font-weight: bold;
}

.dispatch-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}


.refresh-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 14px;
}

.refresh-btn:hover {
    background: #ff0000;
    color: #000000;
}

.shortcut-hint {
    color: #ff4444;
    font-size: 11px;
    font-family: monospace;
}


.no-selection {
    text-align: center;
    color: #ff4444;
    padding: 50px 20px;
    font-style: italic;
}




.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}


.map-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff4444;
    font-style: italic;
}

.map-error {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff0000;
    font-weight: bold;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
}

.map-controls {
    margin-top: 10px;
    text-align: center;
}

.directions-btn {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
    font-family: monospace;
    font-weight: bold;
}

.directions-btn:hover {
    background: #ff4444;
}

/* Quick Actions */
.dispatch-quick-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ff0000;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-action-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
    font-weight: bold;
    flex: 1;
    min-width: 80px;
}

.quick-action-btn:hover {
    background: #ff0000;
    color: #000000;
}

.quick-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quick-action-btn:disabled:hover {
    background: transparent;
    color: #ff0000;
}

.shortcut-key {
    background: rgba(255, 0, 0, 0.2);
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: bold;
    margin-right: 5px;
    font-size: 10px;
}

/* Toast Notifications */
.dispatch-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #ff0000;
    color: #000000;
    padding: 10px 15px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.dispatch-toast.success {
    background: #00ff88;
}

.dispatch-toast.warning {
    background: #ffaa00;
}

.dispatch-toast.error {
    background: #ff0000;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design for Dispatch - Optimized for 1024x768 */
@media (max-width: 1024px) {
    /* Keep side-by-side layout for 1024x768 - email inbox style */
    .dispatch-grid {
        grid-template-columns: 320px 1fr; /* Adjusted for better fit */
        gap: 10px;
    }

    .dispatch-container {
        padding: 10px;
        height: calc(100vh - 160px); /* Adjust for smaller header */
    }


    .dispatch-header {
        padding: 6px 10px;
    }

    .dispatch-header h3 {
        font-size: 14px;
    }

    .shortcut-hint {
        font-size: 9px;
    }

}

@media (max-width: 768px) {
    .dispatch-container {
        padding: 10px;
    }

    /* On small screens, we still want to maintain the email inbox layout */
    .dispatch-grid {
        grid-template-columns: 280px 1fr;
        gap: 8px;
    }

    .dispatch-header {
        padding: 6px 8px;
    }

    .dispatch-header h3 {
        font-size: 12px;
    }

    .incident-row {
        grid-template-columns: 90px 25px 55px 35px;
        gap: 5px;
        font-size: 9px;
    }


    .quick-action-btn {
        font-size: 10px;
        padding: 4px 8px;
    }


    .map-placeholder {
        height: 100%;
        min-height: 200px;
    }
}

/* Map container styles for all screen sizes */

.map-placeholder {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff4444;
    font-family: monospace;
    font-size: 12px;
    background: #111111;
}

.map-error {
    color: #ff6666;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

.map-controls {
    margin-top: 10px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.directions-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
    transition: all 0.2s ease;
}

.directions-btn:hover {
    background: #ff4444;
    color: #000000;
}

/* Ensure Google Maps displays correctly */

/* Specific optimizations for 1024x768 (Panasonic Toughbook) */
@media (width: 1024px) and (height: 768px) {
    /* Main layout adjustments */
    body {
        font-size: 12px;
    }

    .main-header {
        padding: 6px 10px;
        height: 40px;
    }

    .tab {
        padding: 6px 8px;
        font-size: 10px;
        flex-shrink: 0;
        min-width: fit-content;
    }

    .tab-bar {
        padding-right: 10px; /* Add some padding for scroll area */
    }

    .content-area {
        padding: 2px;
        height: calc(100vh - 120px);
    }


    .status-bar {
        padding: 4px 10px;
        font-size: 10px;
        height: 24px;
    }

    /* Dashboard specific - optimized for 1024x768 */
    .dashboard-container {
        padding: 2px;
        height: calc(100vh - 120px);
    }

    .dashboard-content {
        grid-template-columns: 45px 1fr;
        gap: 3px;
    }

    .main-content-area {
        gap: 3px;
    }

    .grid-top, .grid-bottom {
        gap: 3px;
    }

    .bottom-sections {
        gap: 2px;
    }

    .dashboard-section, .weather-widget {
        padding: 3px;
        font-size: 11px;
    }

    .section-header h3, .widget-header h3 {
        font-size: 13px;
        margin-bottom: 2px;
    }

    /* Weather widget specific optimizations for 1024x768 */
    .weather-section, .weather-widget {
        flex: 1;
        width: 100%;
        max-width: none;
    }

    .weather-compact {
        min-height: 50px;
        padding: 6px;
        gap: 4px;
    }

    .wc-main {
        gap: 8px;
    }

    .wc-icon {
        font-size: 16px;
        width: 18px;
    }

    .wc-temp {
        font-size: 18px;
        min-width: 40px;
    }

    .wc-desc {
        font-size: 11px;
    }

    .wc-side {
        gap: 6px;
    }

    .wc-meta {
        gap: 4px;
    }

    .wc-chip {
        padding: 1px 3px;
        font-size: 8px;
    }

    /* Dispatch/CAD specific - email inbox layout */
    .dispatch-container {
        padding: 4px;
        height: calc(100vh - 120px);
    }

    .dispatch-grid {
        grid-template-columns: 300px 1fr; /* Adjusted for Toughbook */
        gap: 8px;
    }

    .dispatch-header {
        padding: 4px 4px;
    }

    .dispatch-header h3 {
        font-size: 12px;
    }

    .incident-row {
        grid-template-columns: 85px 25px 55px 35px;
        gap: 5px;
        font-size: 9px;
        padding: 2px 4px;
    }

    .dispatch-incident-item {
        padding: 2px 3px;
        font-size: 8px;
    }

    /* Incidents specific - ensure side-by-side layout */
    .incidents-main {
        grid-template-columns: 280px 1fr;
        gap: 2px;
        padding: 0;
    }

    .incidents-header {
        padding: 2px 2px;
    }

    .incidents-header h2 {
        font-size: 14px;
    }

    .incident-detail-container {
        padding: 0;
    }

    .incident-tabs {
        margin-bottom: 6px;
    }

    .incident-tab {
        padding: 4px 4px;
        font-size: 10px;
    }

    .incident-map-container {
        height: 220px;
    }

    /* Weather widget optimizations for 1024x768 - removed fixed width constraints */

    .weather-temp {
        font-size: 18px;
    }

    .weather-condition {
        font-size: 9px;
    }

    .weather-location {
        font-size: 7px;
    }

    .weather-details {
        grid-template-columns: 1fr 1fr;
        gap: 0.1rem;
    }

    .detail-label {
        font-size: 7px;
    }

    .detail-value {
        font-size: 8px;
    }

    /* Grid layout adjustments for 1024x768 */
    .dashboard-sidebar {
        width: 45px;
        min-width: 45px;
    }

    .sidebar-header {
        padding: 6px;
    }

    .sidebar-header h3 {
        font-size: 11px;
    }

    .sidebar-content {
        padding: 0 4px 4px 4px;
    }

    .weather-section,
    .stats-section {
        min-height: 160px;
    }

    .incidents-section {
        min-height: 280px;
    }

    /* Quick actions for 1024x768 */
    .quick-actions {
        gap: 4px;
    }

    .action-btn {
        padding: 3px 1px;
        font-size: 6px;
        min-height: 28px;
        margin-bottom: 2px;
        width: 100%;
    }

    .action-icon {
        font-size: 10px;
        margin-bottom: 1px;
    }

    .action-text {
        font-size: 5px;
        line-height: 1;
        text-align: center;
    }

    /* Incidents section optimizations */
    .incidents-section {
        min-height: 280px;
    }

    /* Quick actions */
    .quick-action-btn {
        padding: 4px 8px;
        font-size: 10px;
        margin: 1px;
    }

    /* Modal adjustments */
    .modal-dialog {
        max-width: 800px;
        margin: 20px;
    }

    .form-modal {
        max-width: 600px;
    }
}

/* Property Management Styles */
.property-container {
    color: #ffffff;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.property-header h2 {
    color: #ff0000;
    margin: 0;
}

.property-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.property-controls input,
.property-controls select {
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 5px 8px;
    font-family: monospace;
    font-size: 12px;
}

.property-stats {
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    padding: 15px;
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #ff0000;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #ffffff;
}

.property-content {
    flex: 1;
    overflow-y: auto;
}

.property-list {
    border: 1px solid #ff4444;
}

.property-list-header {
    background: rgba(255, 0, 0, 0.2);
    border-bottom: 1px solid #ff4444;
}

.property-row {
    display: grid;
    grid-template-columns: 120px 100px 1fr 120px 100px 80px;
    gap: 10px;
    align-items: center;
    padding: 8px 10px;
    font-size: 12px;
    font-family: monospace;
}

.property-row.header-row {
    font-weight: bold;
    color: #ff0000;
}

.property-item {
    border-bottom: 1px solid #ff2222;
}

.property-item:hover {
    background: rgba(255, 0, 0, 0.05);
}

.property-status {
    font-weight: bold;
}

.status-found { color: #ffaa00; }
.status-investigating { color: #00aaff; }
.status-owner_identified { color: #aa00ff; }
.status-returned { color: #00ff00; }
.status-handed_to_police { color: #ff6600; }
.status-disposed { color: #666666; }

.property-actions {
    display: flex;
    gap: 5px;
}

.property-actions .action-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff0000;
    padding: 2px 6px;
    cursor: pointer;
    font-size: 12px;
}

.property-actions .action-btn:hover {
    background: #ff0000;
    color: #000000;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #ff4444;
}

.no-data h3 {
    color: #ff0000;
    margin-bottom: 10px;
}

/* Property Actions Panel */
.property-actions-panel {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ff4444;
}

.property-actions-panel h4 {
    color: #ff0000;
    margin-bottom: 10px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons button {
    padding: 5px 5px;
    font-family: monospace;
    font-size: 12px;
    border: 1px solid;
    cursor: pointer;
    background: transparent;
}

.success-button {
    color: #00ff00;
    border-color: #00ff00;
}

.success-button:hover {
    background: #00ff00;
    color: #000000;
}

.warning-button {
    color: #ffaa00;
    border-color: #ffaa00;
}

.warning-button:hover {
    background: #ffaa00;
    color: #000000;
}

/* Property Details Styling */
.property-details {
    color: #ffffff;
}

.property-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.info-section {
    border: 1px solid #ff4444;
    padding: 15px;
}

.info-section h4 {
    color: #ff0000;
    margin-bottom: 10px;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

.info-row {
    margin-bottom: 8px;
    font-size: 12px;
}

.info-row strong {
    color: #ff8888;
    display: inline-block;
    width: 120px;
}

.notes-content {
    background: rgba(255, 0, 0, 0.1);
    padding: 10px;
    border-left: 3px solid #ff0000;
    font-size: 12px;
    white-space: pre-wrap;
}

.action-history {
    max-height: 200px;
    overflow-y: auto;
}

.action-item {
    border-bottom: 1px solid #ff2222;
    padding: 8px 0;
    font-size: 11px;
}

.action-item:last-child {
    border-bottom: none;
}

.action-date {
    color: #ff8888;
    font-weight: bold;
}

.action-description {
    color: #ffffff;
    margin: 2px 0;
}

.action-by {
    color: #aaaaaa;
    font-style: italic;
}

.action-notes {
    color: #cccccc;
    margin-top: 3px;
    padding-left: 10px;
    border-left: 2px solid #ff4444;
}

/* Property Report Styles */
.property-report {
    color: #ffffff;
    font-family: monospace;
}

.report-header {
    border-bottom: 2px solid #ff0000;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.report-header h3 {
    color: #ff0000;
    margin: 0 0 5px 0;
}

.report-header p {
    color: #aaaaaa;
    margin: 0;
    font-size: 12px;
}

.report-section {
    margin-bottom: 20px;
    border: 1px solid #ff4444;
    padding: 15px;
}

.report-section h4 {
    color: #ff0000;
    margin: 0 0 10px 0;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
}

.report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.report-stats .stat-item {
    background: rgba(255, 0, 0, 0.1);
    padding: 8px;
    border-left: 3px solid #ff0000;
    font-size: 12px;
}

.report-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
}

.breakdown-item {
    background: rgba(255, 0, 0, 0.05);
    padding: 6px 10px;
    border: 1px solid #ff2222;
    font-size: 12px;
}

.overdue-list, .trends-list, .location-list {
    max-height: 300px;
    overflow-y: auto;
}

.overdue-item, .trend-item, .location-item {
    border-bottom: 1px solid #ff2222;
    padding: 10px 0;
    font-size: 12px;
}

.overdue-item:last-child, .trend-item:last-child, .location-item:last-child {
    border-bottom: none;
}

.property-number {
    color: #ff8888;
    font-weight: bold;
    margin-bottom: 3px;
}

.property-details {
    color: #ffffff;
    margin-bottom: 3px;
}

.property-date {
    color: #aaaaaa;
    font-size: 11px;
}

.trend-date {
    color: #ff8888;
    font-weight: bold;
    margin-bottom: 3px;
}

.trend-stats {
    color: #ffffff;
    font-size: 11px;
}

.location-name {
    color: #ff8888;
    margin-bottom: 3px;
}

.location-stats {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 2px;
}

.location-breakdown {
    color: #aaaaaa;
    font-size: 10px;
}

/* Property Type Selection Styles */
.property-type-selection {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.property-type-selection h3 {
    color: #00ff00;
    margin-bottom: 10px;
    font-family: 'Courier New', monospace;
}

.property-type-selection p {
    color: #cccccc;
    margin-bottom: 30px;
    font-family: 'Courier New', monospace;
}

.property-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.property-type-card {
    background: #1a1a1a;
    border: 2px solid #333333;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.property-type-card:hover {
    border-color: #00ff00;
    background: #2a2a2a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 255, 0, 0.2);
}

.property-type-card:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 255, 0, 0.3);
}

.property-type-card .type-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.property-type-card .type-title {
    color: #00ff00;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.property-type-card .type-description {
    color: #cccccc;
    font-size: 14px;
    line-height: 1.4;
}

/* Property Form Styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    height: calc(100vh - 200px);
    overflow-y: auto;
}

.property-form {
    background: #1a1a1a;
    border: 1px solid #333333;
    border-radius: 8px;
    padding: 30px;
    font-family: 'Courier New', monospace;
}

.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #333333;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.form-section h3 {
    color: #00ff00;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #cccccc;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: #000000;
    border: 1px solid #333333;
    color: #ffffff;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333333;
}

/* Content Section Styles for Property Pages */
.content-section {
    height: calc(100vh - 60px); /* Account for header height */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-section .section-header {
    flex-shrink: 0;
    padding: 5px 5px;
    border-bottom: 1px solid #ff4444;
    background: #000000;
    position: sticky;
    top: 0;
    z-index: 10;
}

.content-section .form-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 0;
}

.content-section .form-container,
.content-section .property-list,
.content-section .missing-property-sections {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    min-height: 0; /* Fix for flexbox scrolling in some browsers */
}

/* Encampments List Layout - matching incidents structure */
.encampments-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #000000;
}

.encampments-header {
    padding: 15px;
    border-bottom: 1px solid #ff4444;
    background: #000000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.encampments-header h2 {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
}

.encampments-status {
    color: #ff0000;
    font-size: 12px;
}

.encampments-main {
    display: flex;
    flex: 1;
    min-height: 0;
}

.encampments-list-section {
    width: 50%;
    border-right: 1px solid #ff4444;
    display: flex;
    flex-direction: column;
}

.encampments-list-header {
    padding: 10px;
    border-bottom: 1px solid #ff4444;
}

.encampments-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.encampment-details-section {
    width: 50%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;
}

.encampments-actions {
    padding: 15px;
    border-top: 1px solid #ff4444;
    background: #000000;
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Encampment List Items */
.encampment-item {
    /* Inherits from .incident-item styles */
}

.encampment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.encampment-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
}

.encampment-status {
    color: #ff0000;
    font-size: 11px;
    font-weight: bold;
}

.encampment-summary {
    font-size: 11px;
    color: #ff0000;
}

.encampment-summary div {
    margin-bottom: 2px;
}

.location-summary, .population-summary, .last-visited {
    color: #ff0000;
    opacity: 0.8;
}

/* Ensure form takes full height and scrolls */
.encampment-form {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; /* Fix for flexbox scrolling */
}

.encampment-form .form-grid {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px; /* Add some padding for scrollbar */
    margin-bottom: 20px;
}

/* Full-screen form styles */
#form-screen {
    display: none; /* Hidden by default */
    flex-direction: column;
    height: 100vh;
    background: #000000;
    color: #00ff00;
}

#form-screen.active {
    display: flex; /* Show when active */
}

#form-screen .main-header {
    flex-shrink: 0;
}

.form-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 20px;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #00ff00;
}

.form-header h2 {
    margin: 0;
    color: #00ff00;
    font-size: 24px;
    font-weight: bold;
}

.form-header .form-actions {
    display: flex;
    gap: 10px;
}

.form-body {
    flex: 1;
    overflow-y: auto;
    padding-right: 10px;
}

.fullscreen-form {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px); /* Account for top navigation */
    margin-top: 60px; /* Ensure top nav remains visible */
    position: relative;
    z-index: 10;
}

#form-fields-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

/* Responsive design for form fields */
@media (max-width: 768px) {
    .form-fields-container {
        padding: 15px;
        max-width: 100%;
    }

    .fullscreen-form {
        height: calc(100vh - 50px);
        margin-top: 50px;
    }
}

@media (max-width: 480px) {
    .form-fields-container {
        padding: 10px;
    }
}

/* Form field styling for full-screen forms */
.fullscreen-form .form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    width: 100%;
}

.fullscreen-form .form-group label {
    color: #00ff00;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.fullscreen-form .form-group input,
.fullscreen-form .form-group select,
.fullscreen-form .form-group textarea {
    background: #111111;
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 8px 12px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 14px;
    border-radius: 3px;
}

.fullscreen-form .form-group input:focus,
.fullscreen-form .form-group select:focus,
.fullscreen-form .form-group textarea:focus {
    outline: none;
    border-color: #00aaff;
    box-shadow: 0 0 5px rgba(0, 170, 255, 0.3);
}

.fullscreen-form .form-group textarea {
    min-height: 80px;
    resize: vertical;
}

/* Checkbox styling for full-screen forms */
.fullscreen-form .checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 5px;
}

.fullscreen-form .checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.fullscreen-form .checkbox-group label {
    margin: 0;
    font-weight: normal;
}

/* Multi-select styling for full-screen forms */
.fullscreen-form .multi-select-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 5px;
}

.fullscreen-form .multi-select-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fullscreen-form .multi-select-option input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.fullscreen-form .multi-select-option label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

/* Required field indicator */
.fullscreen-form .form-group label.required::after {
    content: " *";
    color: #ff0000;
}

/* Form validation styling */
.fullscreen-form .form-group.error input,
.fullscreen-form .form-group.error select,
.fullscreen-form .form-group.error textarea {
    border-color: #ff0000;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

.fullscreen-form .error-message {
    color: #ff0000;
    font-size: 12px;
    margin-top: 5px;
}

/* Person creation menu styles */
.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.menu-item {
    background: #111111;
    border: 2px solid #333333;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    text-align: center;
    transition: all 0.3s ease;
    color: #00ff00;
}

.menu-item:hover {
    border-color: #00ff00;
    background: #1a1a1a;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.2);
}

.menu-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
    display: block;
}

.menu-title {
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 16px;
    color: #00ff00;
}

.menu-desc {
    font-size: 13px;
    color: #cccccc;
    line-height: 1.4;
}

/* Responsive menu grid */
@media (max-width: 768px) {
    .menu-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .menu-item {
        padding: 15px;
    }

    .menu-icon {
        font-size: 2em;
    }

    .menu-title {
        font-size: 15px;
    }

    .menu-desc {
        font-size: 12px;
    }
}

/* Person deletion warning styles */
.warning-section {
    background: #1a1a1a;
    border: 1px solid #ff4444;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.warning-section h4 {
    color: #ff4444;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.warning-section ul {
    margin: 8px 0;
    padding-left: 20px;
    color: #cccccc;
}

.warning-section li {
    margin: 4px 0;
    font-size: 13px;
}

.danger-text {
    color: #ff4444;
    font-weight: bold;
    text-align: center;
    margin: 15px 0 5px 0;
}

.danger-button {
    background: #ff4444;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s;
}

.danger-button:hover {
    background: #ff0000;
}

/* Linked Records Styles */
.linked-records-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.linked-record-card {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.2s ease;
}

.linked-record-card:hover {
    border-color: #ff0000;
    background: #1a0000;
}

.record-info {
    flex: 1;
    min-width: 0;
}

.record-title {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
}

.record-description {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.record-detail {
    color: #cccccc;
    font-size: 13px;
    margin-bottom: 4px;
}

.record-status, .record-type, .record-severity, .record-priority {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin: 4px 4px 4px 0;
}

.record-status {
    background: #333333;
    color: #ffffff;
}

.status-active, .status-scheduled {
    background: #28a745;
    color: #ffffff;
}

.status-completed, .status-resolved {
    background: #007cba;
    color: #ffffff;
}

.status-cancelled, .status-breached, .status-missed {
    background: #dc3545;
    color: #ffffff;
}

.status-in-progress, .status-rescheduled {
    background: #ffc107;
    color: #000000;
}

.record-type {
    background: #6f42c1;
    color: #ffffff;
}

.record-severity, .record-priority {
    background: #fd7e14;
    color: #ffffff;
}

.severity-high, .priority-high, .priority-urgent {
    background: #dc3545;
    color: #ffffff;
}

.severity-medium, .priority-medium {
    background: #ffc107;
    color: #000000;
}

.severity-low, .priority-low {
    background: #28a745;
    color: #ffffff;
}

.record-flag {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    margin: 2px 4px 2px 0;
}

.emergency-contact {
    background: #dc3545;
    color: #ffffff;
}

.primary-contact {
    background: #007cba;
    color: #ffffff;
}

/* Specific styling for criminal justice record cards */
.linked-record-card .record-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-end;
    min-width: 80px;
}

.linked-record-card .action-button {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 60px;
    justify-content: center;
    white-space: nowrap;
}

/* Fix for pet cards that use different class name */
.pet-card .pet-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-end;
    min-width: 80px;
}

.pet-card .action-button {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 60px;
    justify-content: center;
    white-space: nowrap;
}

.no-records {
    text-align: center;
    color: #ff4444;
    padding: 30px;
    font-style: italic;
}

/* Criminal Justice Specific Styles */
.criminal-justice-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.criminal-justice-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.criminal-justice-actions .primary-button {
    font-size: 12px;
    padding: 6px 12px;
}

.criminal-justice-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.criminal-justice-subsection {
    border: 1px solid #333333;
    padding: 15px;
    background: #111111;
}

.criminal-justice-subsection h5 {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #333333;
}

/* Responsive adjustments for linked records */
@media (max-width: 768px) {
    .linked-record-card {
        flex-direction: column;
        gap: 10px;
    }

    .linked-record-card .record-actions,
    .pet-card .pet-actions {
        align-self: stretch;
        justify-content: flex-end;
        flex-direction: row;
        gap: 8px;
    }

    .criminal-justice-actions {
        flex-direction: column;
    }

    .criminal-justice-actions .primary-button {
        width: 100%;
    }
}

.encampment-form .form-actions {
    flex-shrink: 0;
    padding: 15px 0;
    background: #000000;
    border-top: 1px solid #ff4444;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    position: sticky;
    bottom: 0;
    z-index: 5;
}

/* Make sure form actions stay at the bottom */
.form-section {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Style scrollbar for better visibility */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #ff4444;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ff6666;
}

.content-section .property-type-selection {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Search Section Styles */
.search-section {
    flex-shrink: 0;
    padding: 15px 20px;
    border-bottom: 1px solid #333333;
    background: #111111;
}

.search-bar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    flex: 1;
    background: #000000;
    border: 1px solid #333333;
    color: #ffffff;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border-radius: 4px;
}

.search-input:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
}

.search-button {
    background: #333333;
    border: 1px solid #555555;
    color: #ffffff;
    padding: 5px 5px;
    cursor: pointer;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.search-button:hover {
    background: #555555;
    border-color: #777777;
}

/* Links Tab Styles */
.incident-links {
    color: #ffffff;
}

.incident-links h4, .incident-links h5 {
    color: #ff0000;
    margin: 0 0 10px 0;
}

.links-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.link-category {
    border: 1px solid #ff4444;
    padding: 10px;
}

.attach-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
    margin-bottom: 10px;
}

.attach-btn:hover {
    background: #ff0000;
    color: #000000;
}

.linked-items {
    min-height: 30px;
}

.linked-item {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    padding: 5px 8px;
    margin-bottom: 5px;
    font-size: 12px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.link-type {
    color: #ff8888;
    font-size: 10px;
    font-style: italic;
}

.link-notes {
    color: #cccccc;
    font-size: 10px;
    margin-top: 3px;
    padding-left: 10px;
    border-left: 2px solid #ff4444;
}

.vehicle-details, .address-details {
    color: #aaaaaa;
    font-size: 10px;
}

.remove-link-btn {
    position: absolute;
    top: 2px;
    right: 5px;
    background: transparent;
    border: none;
    color: #ff4444;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-link-btn:hover {
    background: #ff4444;
    color: #000000;
    border-radius: 50%;
}

.no-links {
    color: #ff4444;
    font-style: italic;
    font-size: 12px;
}

/* Log Tab Styles */
.incident-log {
    color: #ffffff;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.log-header h4 {
    color: #ff0000;
    margin: 0;
}

.add-log-btn {
    background: transparent;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 11px;
}

.add-log-btn:hover {
    background: #ff0000;
    color: #000000;
}

.log-entries {
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    border-left: 2px solid #ff4444;
    padding-left: 10px;
    margin-bottom: 15px;
}

.log-timestamp {
    color: #ff4444;
    font-size: 11px;
    margin-bottom: 5px;
}

.log-content {
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
}

.log-type {
    display: inline-block;
    font-weight: bold;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.log-type.incident-created {
    background: #004400;
    color: #00ff00;
}

.log-type-note {
    background: #333333;
    color: #ffffff;
}

.log-type-status_update {
    background: #444400;
    color: #ffff00;
}

.log-type-action_taken {
    background: #004400;
    color: #00ff00;
}

.log-type-follow_up {
    background: #440000;
    color: #ff4444;
}

.log-type-contact_made {
    background: #004444;
    color: #00ffff;
}

.log-type-other {
    background: #440044;
    color: #ff44ff;
}

.log-user {
    color: #ff4444;
    font-size: 10px;
    font-style: italic;
    margin-bottom: 3px;
}

.log-text {
    color: #ffffff;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
}

/* Status Tab Styles */
.incident-status {
    color: #ffffff;
}

.incident-status h4 {
    color: #ff0000;
    margin: 0 0 15px 0;
}

.status-timeline {
    position: relative;
}

.status-timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ff4444;
}

.status-entry {
    position: relative;
    padding-left: 30px;
    margin-bottom: 20px;
}

.status-entry::before {
    content: '';
    position: absolute;
    left: 5px;
    top: 5px;
    width: 10px;
    height: 10px;
    background: #ff0000;
    border-radius: 50%;
}

.status-timestamp {
    color: #ff4444;
    font-size: 11px;
    margin-bottom: 3px;
}

.status-change {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 3px;
}

.status-user {
    color: #ff4444;
    font-size: 11px;
    font-style: italic;
}

/* Updated Dashboard Widget Styles */
.dashboard-widget .widget-footer {
    padding: 10px 0 0 0;
    border-top: 1px solid #ff4444;
    margin-top: 10px;
    text-align: center;
}

/* People Management Styles - Clean Implementation */
.content-area.people-management-content {
    padding: 0px 15px 15px 15px !important;
}

.content-section {
    margin: 5px 0 0 0 !important;
    padding: 0 !important;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 8px 0 !important;
    padding: 0 0 6px 0 !important;
    border-bottom: 2px solid #ff4444;
}

.section-header h2 {
    color: #ff0000;
    margin: 0;
    padding: 0;
    font-size: clamp(16px, 2vw, 20px);
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-actions .primary-button,
.header-actions .secondary-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 5px;
    font-size: 12px;
    min-height: 30px;
}

.button-icon {
    font-size: 14px;
}

.search-section {
    margin-bottom: 20px;
}

.search-bar {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 10px;
    background: #111111;
    border: 2px solid #ff4444;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #ff0000;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

.search-button {
    padding: 10px 15px;
    background: #ff0000;
    border: none;
    color: #ffffff;
    cursor: pointer;
    font-size: 16px;
    min-width: 45px;
}

.search-button:hover {
    background: #cc0000;
}

.people-list-container {
    background: #111111;
    border: 2px solid #ff4444;
    padding: 15px;
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    /* Smooth scrolling */
    scroll-behavior: smooth;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #ff4444 #111111;
}

/* Webkit scrollbar styling for better UX */
.people-list-container::-webkit-scrollbar {
    width: 8px;
}

.people-list-container::-webkit-scrollbar-track {
    background: #111111;
}

.people-list-container::-webkit-scrollbar-thumb {
    background: #ff4444;
    border-radius: 4px;
}

.people-list-container::-webkit-scrollbar-thumb:hover {
    background: #ff0000;
}

.people-count {
    color: #ff4444;
    font-size: 14px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
    flex-shrink: 0;
}

.people-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
    overflow-y: auto;
    /* Ensure smooth scrolling within the list */
    scroll-behavior: smooth;
    /* Custom scrollbar for the inner list */
    scrollbar-width: thin;
    scrollbar-color: #ff4444 #000000;
}

/* Webkit scrollbar styling for people list */
.people-list::-webkit-scrollbar {
    width: 6px;
}

.people-list::-webkit-scrollbar-track {
    background: #000000;
}

.people-list::-webkit-scrollbar-thumb {
    background: #ff4444;
    border-radius: 3px;
}

.people-list::-webkit-scrollbar-thumb:hover {
    background: #ff0000;
}

/* No records message styling */
.people-list .no-records {
    text-align: center;
    padding: 3rem;
    color: #ff6666;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.person-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #000000;
    border: 1px solid #ff4444;
    cursor: pointer;
    transition: all 0.2s ease;
}

.person-card:hover {
    border-color: #ff0000;
    background: #1a0000;
}

.person-avatar {
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 50%;
}

.person-info {
    flex: 1;
    min-width: 0;
}

.person-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

.person-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 5px;
}

.detail-item {
    color: #ffffff;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 3px;
}

.person-meta {
    color: #ff4444;
    font-size: 11px;
}

.person-actions {
    flex-shrink: 0;
}

/* Duplicate .action-button removed - using primary definition at line 1912 */

.view-button {
    background: #ff4444;
}

.view-button:hover {
    background: #ff0000;
}

.no-records {
    text-align: center;
    color: #ff4444;
    padding: 40px;
    font-style: italic;
}

/* Person Detail Styles */
.person-detail-container {
    background: #111111;
    border: 2px solid #ff4444;
    padding: 10px 15px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.person-detail-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 2px solid #ff4444;
}

.person-avatar-large {
    flex-shrink: 0;
}

.avatar-placeholder-large {
    width: 55px;
    height: 55px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 50%;
}

.person-title-info {
    flex: 1;
}

.person-full-name {
    color: #ff0000;
    font-size: clamp(18px, 2.5vw, 22px);
    margin: 0 0 8px 0;
}

.person-id,
.person-created {
    color: #ff4444;
    font-size: 13px;
    margin-bottom: 4px;
}

.person-detail-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.detail-sections {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.detail-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 10px 12px;
}

.detail-section h4 {
    color: #ff0000;
    font-size: 15px;
    margin: 0 0 8px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.detail-grid .detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-grid .detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-grid .detail-item label {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
}

.detail-grid .detail-item span {
    color: #ffffff;
    font-size: 14px;
    word-wrap: break-word;
}

.activity-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 20px;
    height: fit-content;
}

.activity-section h4 {
    color: #ff0000;
    font-size: 16px;
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.no-activities {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Person Detail Tabs */
.person-detail-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 10px;
    border-bottom: 2px solid #ff4444;
}

.person-detail-tab {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    border-bottom: none;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.person-detail-tab:hover {
    background: #ff4444;
    color: #000000;
}

.person-detail-tab.active {
    background: #ff0000;
    color: #ffffff;
    font-weight: bold;
}

.person-detail-content .tab-content {
    display: none;
}

.person-detail-content .tab-content.active {
    display: block;
}

/* Pets Section */
.pets-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.pets-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.pets-section h4 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.pets-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.pet-card {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.pet-info {
    flex: 1;
}

.pet-name {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.pet-details {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 5px;
}

.pet-species {
    color: #ff4444;
    font-weight: bold;
}

.pet-breed,
.pet-age {
    color: #ffffff;
}

.pet-color,
.pet-description,
.pet-microchip,
.pet-vaccination {
    color: #ffffff;
    font-size: 13px;
    margin-bottom: 3px;
}

.pet-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.pet-actions .action-button {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.no-pets {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Medical Issues Section */
.medical-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.medical-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.medical-section h4 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.medical-issues-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.medical-issue-card {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
}

.medical-issue-info {
    flex: 1;
}

.medical-issue-name {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.medical-issue-details {
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 5px;
}

.medical-severity {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.medical-severity.severity-mild {
    background: #28a745;
    color: #ffffff;
}

.medical-severity.severity-moderate {
    background: #ffc107;
    color: #000000;
}

.medical-severity.severity-severe {
    background: #fd7e14;
    color: #ffffff;
}

.medical-severity.severity-critical {
    background: #dc3545;
    color: #ffffff;
}

.medical-status {
    color: #ff4444;
    font-weight: bold;
}

.medical-date {
    color: #ffffff;
}

.medical-treatment,
.medical-medication,
.medical-provider,
.medical-followup,
.medical-notes {
    color: #ffffff;
    font-size: 13px;
    margin-bottom: 3px;
}

.medical-treatment {
    color: #90EE90;
}

.medical-medication {
    color: #87CEEB;
}

.medical-provider {
    color: #DDA0DD;
}

.medical-followup {
    color: #FFB6C1;
    font-weight: bold;
}

.medical-issue-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.medical-issue-actions .action-button {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.no-medical-issues {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Organizations Management Styles */
.organizations-management-content {
    background: #000000;
    color: #ffffff;
    min-height: calc(100vh - 100px);
}

.organizations-list-container {
    background: #111111;
    border: 2px solid #ff4444;
    padding: 15px;
    margin-top: 15px;
}

.organizations-count {
    color: #ff4444;
    font-size: 14px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff4444;
}

.organizations-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.organization-card {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    transition: border-color 0.2s ease;
}

.organization-card:hover {
    border-color: #ff0000;
}

.organization-icon {
    flex-shrink: 0;
}

.org-icon-placeholder {
    width: 50px;
    height: 50px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    border-radius: 8px;
}

.organization-info {
    flex: 1;
}

.organization-name {
    color: #ff0000;
    font-size: clamp(16px, 2.5vw, 20px);
    font-weight: bold;
    margin-bottom: 8px;
}

.organization-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 8px;
}

.organization-details .detail-item {
    color: #ffffff;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.organization-services {
    color: #90EE90;
    font-size: 13px;
    margin-bottom: 8px;
    font-style: italic;
}

.organization-meta {
    color: #ff4444;
    font-size: 12px;
}

.organization-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex-shrink: 0;
}

.organization-actions .action-button {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 80px;
}

.no-records {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 40px;
    font-size: 16px;
}

/* Multi-select checkbox styles */
.multi-select-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    padding: 10px;
    background: #111111;
}

.checkbox-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px;
}

.checkbox-option input[type="checkbox"] {
    accent-color: #ff0000;
}

.checkbox-option label {
    color: #ffffff;
    font-size: 13px;
    cursor: pointer;
}

.checkbox-option:hover {
    background: #222222;
}

/* Services tags display */
.services-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 5px;
}

.service-tag {
    background: #ff0000;
    color: #ffffff;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
}

/* Responsive adjustments for person detail */
@media (max-width: 1024px) {
    .person-detail-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .person-detail-header {
        flex-direction: column;
        text-align: center;
        gap: 12px;
        margin-bottom: 15px;
        padding-bottom: 10px;
    }

    .detail-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .content-area.people-management-content {
        padding: 5px 10px 10px 10px;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
        margin-bottom: 10px;
        padding-bottom: 8px;
    }

    .header-actions {
        justify-content: center;
    }

    .person-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .person-details {
        justify-content: center;
    }
}

/* Person Summary Modal Styles */
.person-summary-modal {
    max-width: 600px;
    width: 90%;
}

.person-summary-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.person-summary-header {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ff4444;
}

.person-avatar-summary {
    flex-shrink: 0;
}

.avatar-placeholder-summary {
    width: 60px;
    height: 60px;
    background: #ff0000;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    border-radius: 50%;
}

.person-summary-info {
    flex: 1;
}

.person-summary-name {
    color: #ff0000;
    font-size: 20px;
    margin: 0 0 5px 0;
}

.person-summary-id {
    color: #ff4444;
    font-size: 14px;
}

.person-summary-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.summary-section {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 15px;
}

.summary-section h5 {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.summary-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.summary-label {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
}

.summary-value {
    color: #ffffff;
    font-size: 13px;
    word-wrap: break-word;
}

/* Responsive adjustments for summary modal */
@media (max-width: 768px) {
    .person-summary-modal {
        width: 95%;
        max-width: none;
    }

    .person-summary-header {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .summary-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
}

.go-to-dispatch-btn {
    background: #ff0000;
    color: #000000;
    border: none;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-weight: bold;
    font-size: 11px;
    width: 100%;
}

.go-to-dispatch-btn:hover {
    background: #ff4444;
}

/* Duplicate incident styles removed - using organized styles from incidents.css */

/* Final Polish - Ensure consistent monospace theme */
.dispatch-container * {
    font-family: 'Courier New', monospace;
}

.dispatch-incident-item:focus {
    outline: 2px solid #ff0000;
    outline-offset: -2px;
}

.incident-detail-tab:focus {
    outline: 2px solid #ff0000;
    outline-offset: -2px;
}

.quick-action-btn:focus {
    outline: 2px solid #ff0000;
    outline-offset: -2px;
}

/* Loading states */
.loading {
    color: #ff4444;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

.error {
    color: #ff0000;
    text-align: center;
    padding: 20px;
    font-weight: bold;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
}

/* Accessibility improvements */
.dispatch-incident-item[aria-selected="true"] {
    background: rgba(255, 0, 0, 0.2);
    border-color: #ff0000;
}

/* Animation for new incidents */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.dispatch-incident-item.new-incident {
    animation: slideInLeft 0.5s ease-out;
}

/* Duplicate incident scrollbar styles removed - using organized styles from incidents.css */

/* Supply Provision Styles */
.large-modal .modal-dialog {
    max-width: 800px;
    width: 90%;
}

.activity-summary {
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
    padding: 10px;
    margin-bottom: 15px;
    font-family: monospace;
}

.activity-summary p {
    margin: 5px 0;
    color: #ffffff;
}

.activity-summary strong {
    color: #ff0000;
}

.supply-items-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    padding: 10px;
    background: #111111;
}

.supply-item {
    border: 1px solid #ff4444;
    margin-bottom: 10px;
    padding: 10px;
    background: #000000;
    font-family: monospace;
}

.supply-item:last-child {
    margin-bottom: 0;
}

.supply-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.supply-item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
}

.supply-item-stock {
    color: #00ff88;
    font-size: 12px;
}

.supply-item-description {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 5px;
    font-style: italic;
}

.supply-item-category {
    color: #ff4444;
    font-size: 11px;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.supply-item-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.supply-item-controls label {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
}

.quantity-input {
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 4px 8px;
    width: 80px;
    font-family: monospace;
    font-size: 12px;
}

.quantity-input:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.unit-label {
    color: #ff4444;
    font-size: 11px;
}

.supply-notes {
    margin-top: 15px;
}

.supply-notes label {
    display: block;
    color: #ff0000;
    font-weight: bold;
    margin-bottom: 5px;
    font-family: monospace;
}

.supply-notes textarea {
    width: 100%;
    height: 80px;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    resize: vertical;
}

.supply-notes textarea:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.05);
}

/* Supply provision activity display */
.activity-supplies {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid #00ff88;
    padding: 8px;
    margin-top: 8px;
    font-family: monospace;
}

.activity-supplies h5 {
    color: #00ff88;
    margin: 0 0 5px 0;
    font-size: 12px;
}

.supply-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.supply-list li {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 2px;
}

.supply-quantity {
    color: #00ff88;
    font-weight: bold;
}

/* Items Management Styles */
.items-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ff4444;
}

.items-table {
    font-family: monospace;
    width: 100%;
}

.items-header {
    display: grid;
    grid-template-columns: 2fr 1fr 80px 80px 80px 120px;
    gap: 10px;
    padding: 10px;
    background: #ff0000;
    color: #000000;
    font-weight: bold;
    font-size: 12px;
    border: 1px solid #ff0000;
}

.items-body {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    border-top: none;
}

.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 80px 80px 80px 120px;
    gap: 10px;
    padding: 8px 10px;
    border-bottom: 1px solid #ff4444;
    color: #ffffff;
    font-size: 12px;
    align-items: center;
}

.item-row:hover {
    background: rgba(255, 0, 0, 0.1);
}

.item-row.low-stock {
    background: rgba(255, 170, 0, 0.1);
    border-color: #ffaa00;
}

.item-name {
    font-weight: bold;
    color: #ff0000;
}

.item-category {
    text-transform: capitalize;
    color: #ff4444;
}

.item-stock.low-stock {
    color: #ffaa00;
    font-weight: bold;
}

.item-status.active {
    color: #00ff88;
}

.item-status.inactive {
    color: #ff4444;
}

.item-actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 2px 2px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 2px;
}

.action-btn:hover {
    background: #ff4444;
    color: #000000;
}

.no-items {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
    font-style: italic;
}

/* Low Stock Alert Styles */
.low-stock-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ffaa00;
    padding: 10px;
    background: rgba(255, 170, 0, 0.1);
}

.low-stock-item {
    background: #000000;
    border: 1px solid #ffaa00;
    padding: 10px;
    margin-bottom: 10px;
    color: #ffffff;
    font-family: monospace;
    font-size: 12px;
}

.low-stock-item:last-child {
    margin-bottom: 0;
}

.low-stock-item strong {
    color: #ffaa00;
}

/* Outreach Transaction Screen Styles */
.outreach-transaction-screen {
    padding: 15px;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.outreach-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ff0000;
}

.outreach-header h2 {
    color: #ff0000;
    margin: 0;
    font-size: clamp(18px, 2.5vw, 24px);
}

.close-screen-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
    transition: all 0.2s ease;
}

.close-screen-btn:hover {
    background: #ff4444;
    color: #000000;
}

.outreach-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

.outreach-section {
    background: #111111;
    border: 2px solid #ff0000;
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
}

.person-section {
    min-height: 200px;
}

.items-section {
    min-height: 250px;
}

.details-section {
    min-height: 150px;
}

.outreach-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #ff4444;
}

.outreach-section .section-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: clamp(14px, 1.8vw, 18px);
}

/* Person Search Styles */
.person-search-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.search-input-group {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
}

.person-search-input {
    flex: 1;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 8px 12px;
    font-family: monospace;
    font-size: 12px;
}

.person-search-input:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.search-btn, .add-person-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 8px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
    white-space: nowrap;
}

.search-btn:hover, .add-person-btn:hover {
    background: #ff4444;
    color: #000000;
}

.add-person-btn {
    border-color: #00ff88;
    color: #00ff88;
}

.add-person-btn:hover {
    background: #00ff88;
    color: #000000;
}

.person-results {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #ff4444;
    background: #000000;
}

.person-result {
    padding: 10px;
    border-bottom: 1px solid #ff4444;
    cursor: pointer;
    transition: background 0.2s ease;
}

.person-result:hover {
    background: rgba(255, 0, 0, 0.1);
}

.person-result:last-child {
    border-bottom: none;
}

.person-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 4px;
}

.person-details {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 4px;
}

.person-id {
    color: #ff4444;
    font-size: 10px;
}

.selected-person {
    border: 2px solid #00ff88;
    padding: 15px;
    background: rgba(0, 255, 136, 0.1);
}

.selected-person-info h4 {
    color: #00ff88;
    margin: 0 0 8px 0;
    font-size: 16px;
}

.person-contact {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 8px;
}

.change-person-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 4px 8px;
    cursor: pointer;
    font-family: monospace;
    font-size: 10px;
    margin-top: 10px;
}

.change-person-btn:hover {
    background: #ff4444;
    color: #000000;
}

.no-selection, .no-results, .no-items {
    text-align: center;
    color: #ff4444;
    padding: 20px;
    font-style: italic;
}

.loading {
    text-align: center;
    color: #ffaa00;
    padding: 20px;
}

.error {
    text-align: center;
    color: #ff6666;
    padding: 20px;
}

/* Items Section Styles */
.add-item-btn {
    background: transparent;
    border: 1px solid #00ff88;
    color: #00ff88;
    padding: 6px 12px;
    cursor: pointer;
    font-family: monospace;
    font-size: 12px;
}

.add-item-btn:hover:not(:disabled) {
    background: #00ff88;
    color: #000000;
}

.add-item-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.items-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #ff4444;
    background: #000000;
}

.transaction-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ff4444;
}

.transaction-item:last-child {
    border-bottom: none;
}

.item-details {
    flex: 1;
}

.item-details .item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 4px;
}

.item-details .item-category {
    color: #ff4444;
    font-size: 10px;
    text-transform: uppercase;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0 15px;
}

.item-quantity .quantity {
    color: #00ff88;
    font-weight: bold;
    font-size: 16px;
}

.item-quantity .unit {
    color: #ffffff;
    font-size: 12px;
}

.item-actions {
    display: flex;
    gap: 5px;
}

.edit-quantity-btn, .remove-item-btn {
    background: transparent;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 4px 6px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 2px;
}

.edit-quantity-btn:hover {
    background: #ffaa00;
    border-color: #ffaa00;
    color: #000000;
}

.remove-item-btn:hover {
    background: #ff4444;
    color: #000000;
}

.items-total {
    margin-top: 10px;
    padding: 10px;
    border-top: 1px solid #ff4444;
    text-align: center;
}

.total-items {
    color: #00ff88;
    font-weight: bold;
    font-size: 14px;
}

/* Details Section Styles */
.details-form {
    flex: 1;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    color: #ff0000;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 12px;
}

.form-group input, .form-group textarea {
    width: 100%;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    padding: 8px;
    font-family: monospace;
    font-size: 12px;
    box-sizing: border-box;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
}

.form-group textarea {
    height: 80px;
    resize: vertical;
}

.location-btn {
    background: transparent;
    border: 1px solid #ffaa00;
    color: #ffaa00;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 8px;
    border-radius: 2px;
}

.location-btn:hover {
    background: #ffaa00;
    color: #000000;
}

/* Outreach Actions */
.outreach-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 2px solid #ff0000;
    flex-shrink: 0;
}

.outreach-actions button {
    padding: 10px 20px;
    font-family: monospace;
    font-size: 12px;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.2s ease;
}

.outreach-actions .secondary-button {
    border-color: #ff4444;
    color: #ff4444;
}

.outreach-actions .secondary-button:hover {
    background: #ff4444;
    color: #000000;
}

.outreach-actions .primary-button {
    border-color: #00ff88;
    color: #00ff88;
}

.outreach-actions .primary-button:hover:not(:disabled) {
    background: #00ff88;
    color: #000000;
}

.outreach-actions .primary-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Item Catalog Modal Styles */
.item-catalog {
    max-height: 500px;
    overflow-y: auto;
}

.catalog-category {
    margin-bottom: 20px;
}

.category-title {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.category-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

.catalog-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.catalog-item:hover:not(.out-of-stock) {
    background: rgba(255, 0, 0, 0.1);
    border-color: #ff0000;
}

.catalog-item.out-of-stock {
    opacity: 0.5;
    cursor: not-allowed;
}

.catalog-item .item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 13px;
    margin-bottom: 5px;
}

.catalog-item .item-description {
    color: #ffffff;
    font-size: 11px;
    margin-bottom: 5px;
}

.catalog-item .item-stock {
    color: #00ff88;
    font-size: 10px;
}

.catalog-item .stock-warning {
    color: #ff6666;
    font-size: 10px;
    font-weight: bold;
    margin-top: 5px;
}

/* Quantity Input Modal */
.quantity-input-form {
    text-align: center;
}

.quantity-input-form .item-info {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff4444;
}

.quantity-input-form .item-name {
    color: #ff0000;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

.quantity-input-form .item-description {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 5px;
}

.quantity-input-form .item-stock {
    color: #00ff88;
    font-size: 11px;
}

.quantity-input {
    width: 80px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

.unit-label {
    color: #ff4444;
    font-size: 12px;
    margin-left: 8px;
}

/* Responsive adjustments for outreach transactions */
@media (max-width: 1024px) {
    .outreach-content {
        gap: 10px;
    }

    .outreach-section {
        min-height: unset;
    }

    .person-section {
        min-height: 180px;
    }

    .items-section {
        min-height: 200px;
    }

    .details-section {
        min-height: 120px;
    }

    .search-input-group {
        flex-wrap: wrap;
    }

    .category-items {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .outreach-transaction-screen {
        padding: 10px;
    }

    .outreach-actions {
        flex-direction: column;
    }

    .outreach-actions button {
        width: 100%;
    }
}

/* Notification animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    border: 1px solid #ff0000;
    box-shadow: 2px 2px 0 rgba(255, 0, 0, 0.3);
}

.notification-info {
    background: #ff0000;
    color: #000000;
}

.notification-success {
    background: #00ff00;
    color: #000000;
}

.notification-error {
    background: #ff4444;
    color: #ffffff;
}





.no-selection {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
}

.no-selection h3 {
    color: #ff0000;
    margin-bottom: 10px;
}


.detail-row {
    display: flex;
    margin-bottom: 5px;
}

.detail-label {
    font-weight: bold;
    color: #ff0000;
    min-width: 120px;
}

.detail-value {
    color: #ff4444;
}


.incident-actions .action-button {
    flex: 1;
    min-height: 32px;
    font-size: 11px;
    padding: 5px 5px;
}

.close-button {
    background: #004400;
    color: #00ff00;
    border-color: #00ff00;
}

.close-button:hover {
    background: #006600;
    color: #00ff00;
    border-color: #00ff00;
}

.delete-button {
    background: #660000;
    color: #ff4444;
    border-color: #ff4444;
}

.delete-button:hover {
    background: #990000;
    color: #ff0000;
    border-color: #ff0000;
}


.tab-content {
    display: none;
    height: 100%;
    overflow-y: auto;
    padding-bottom: 5px;
}

.tab-content.active {
    display: block;
}


.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.add-log-btn,
.attach-btn {
    background: #000000;
    color: #00ff00;
    border: 1px solid #00ff00;
    padding: 4px 8px;
    font-family: inherit;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-log-btn:hover,
.attach-btn:hover {
    background: #003300;
    color: #00ff00;
}

.log-entries {
    max-height: 300px;
    overflow-y: auto;
}

.log-entry {
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid #ff4444;
    background: #000000;
}

.log-timestamp {
    font-size: 11px;
    color: #888888;
    margin-bottom: 3px;
}

.log-content {
    color: #ff4444;
}

.links-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.link-category h5 {
    color: #ff0000;
    margin-bottom: 8px;
}

.linked-items {
    margin-top: 8px;
}

.linked-item {
    padding: 6px;
    margin-bottom: 5px;
    border: 1px solid #ff4444;
    background: #000000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.remove-link-btn {
    background: #660000;
    color: #ff4444;
    border: 1px solid #ff4444;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-link-btn:hover {
    background: #990000;
    color: #ff0000;
}

.status-timeline {
    max-height: 300px;
    overflow-y: auto;
}

.status-entry {
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid #ff4444;
    background: #000000;
}

.status-timestamp {
    font-size: 11px;
    color: #888888;
    margin-bottom: 3px;
}

.status-change {
    color: #ff4444;
    margin-bottom: 3px;
}

.status-user {
    font-size: 11px;
    color: #00ff00;
}

.no-links {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 20px;
}


.search-filters {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
}

.filter-group input,
.filter-group select {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 8px;
    font-family: inherit;
    font-size: 12px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: #ff0000;
    background: #110000;
}

.search-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.search-actions .action-button.primary {
    background: #004400;
    color: #00ff00;
    border-color: #00ff00;
}

.search-actions .action-button.primary:hover {
    background: #006600;
}


.no-results {
    text-align: center;
    color: #888888;
    padding: 40px;
    font-style: italic;
}

.error {
    text-align: center;
    color: #ff0000;
    padding: 40px;
}

.loading {
    text-align: center;
    color: #ff4444;
    padding: 40px;
}

/* Responsive fixes for smaller screens */
@media (max-width: 1200px) {
    .incidents-main {
        grid-template-columns: 300px 1fr;
        gap: 8px;
        padding: 0;
    }

    .list-filters {
        gap: 5px;
    }

    .list-filters select,
    .list-filters input {
        font-size: 10px;
        padding: 3px 5px;
    }

    .list-filters input[type="text"] {
        min-width: 80px;
        max-width: 150px;
    }

    .list-filters select {
        min-width: 60px;
        max-width: 100px;
    }

    .incident-actions .action-button {
        font-size: 10px;
        padding: 4px 6px;
        min-height: 28px;
    }

    /* Incident detail tabs responsive */
    .incident-detail-tabs {
        flex-wrap: wrap;
        gap: 2px;
    }

    .incident-detail-tab {
        padding: 6px 12px;
        font-size: 11px;
        min-width: auto;
        flex: 1 1 auto;
    }
}

@media (max-width: 900px) {
    .incidents-main {
        grid-template-columns: 250px 1fr;
    }

    .list-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .list-filters input[type="text"],
    .list-filters select {
        max-width: none;
    }

    /* Incident detail tabs for medium screens */
    .incident-detail-tabs {
        flex-wrap: wrap;
        gap: 1px;
    }

    .incident-detail-tab {
        padding: 5px 8px;
        font-size: 10px;
        flex: 1 1 calc(25% - 1px);
        text-align: center;
        min-width: 80px;
    }
}

/* Record Linking Styles for Incident Creation */
.record-links-container {
    border: 1px solid #ff4444;
    background: #111111;
    padding: 15px;
    margin-top: 10px;
}

.link-section {
    margin-bottom: 20px;
}

.link-section:last-child {
    margin-bottom: 0;
}

.link-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.link-header span {
    color: #ff0000;
    font-weight: bold;
    font-size: 14px;
}

.link-button {
    background: #000000;
    color: #00ff00;
    border: 1px solid #00ff00;
    padding: 4px 8px;
    font-family: inherit;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.link-button:hover {
    background: #003300;
    color: #00ff00;
}

.linked-records {
    min-height: 40px;
    max-height: 150px;
    overflow-y: auto;
}

.linked-record-item {
    background: #000000;
    border: 1px solid #ff4444;
    padding: 8px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.linked-record-info {
    flex: 1;
    color: #ff4444;
    font-size: 12px;
}

.linked-record-type {
    color: #888888;
    font-size: 10px;
    margin-bottom: 2px;
}

.linked-record-details {
    color: #ff4444;
}

.remove-link {
    background: #660000;
    color: #ff4444;
    border: 1px solid #ff4444;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-link:hover {
    background: #990000;
    color: #ff0000;
}

.no-links {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 15px;
    font-size: 12px;
}

.incidents-actions {
    display: flex;
    gap: 5px;
    padding: 5px 5px;
    border-top: 1px solid #ff0000;
    background: #000000;
    flex-shrink: 0;
}

/* Duplicate .action-button removed - using primary definition at line 1912 */

.action-button.primary {
    background: #ff0000;
    color: #000000;
    border-color: #ff0000;
}

.action-button.primary:hover {
    background: #ff4444;
}

.loading {
    text-align: center;
    color: #ffff00;
    padding: 20px;
    font-style: italic;
}

/* Responsive Design - Stack only on very small screens */
@media (max-width: 768px) {
    .incidents-main {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
    }
}

@media (max-width: 768px) {
    .incidents-header {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .list-filters {
        flex-direction: column;
        gap: 5px;
    }

    .incidents-actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .action-button {
        flex: 1;
        min-width: 120px;
    }
}

/* Geocoding Status Styling */
.geocode-status {
    font-size: 12px;
    margin-left: 10px;
    padding: 4px 8px;
    border-radius: 3px;
    font-family: inherit;
}

.geocode-status.loading {
    color: #ffff00;
    background: rgba(255, 255, 0, 0.1);
    border: 1px solid #ffff00;
}

.geocode-status.success {
    color: #00ff00;
    background: rgba(0, 255, 0, 0.1);
    border: 1px solid #00ff00;
}

.geocode-status.error {
    color: #ff0000;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid #ff0000;
}

/* Address Form Styling */
.form-section h3 {
    color: #ff0000;
    border-bottom: 1px solid #ff4444;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.form-group label {
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 5px;
    display: block;
}

.form-group input[readonly] {
    background: #111111;
    color: #888888;
    border-color: #444444;
}

.form-group select {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    font-family: inherit;
    font-size: 12px;
    padding: 6px 8px;
}

.form-group select:focus {
    outline: none;
    border-color: #ff0000;
    color: #ff0000;
}

.secondary-button {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 5px 5px;
    font-family: inherit;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-button:hover {
    background: #330000;
    color: #ff0000;
    border-color: #ff0000;
}

/* Duplicate incident creation form styles removed - using organized styles from incidents.css */

/* Google Maps Address Search Styles */
.address-search-wrapper {
    position: relative;
    width: 100%;
}

.address-search-input {
    width: 100%;
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    padding: 8px 12px;
    font-family: inherit;
    font-size: 12px;
    box-sizing: border-box;
}

.address-search-input:focus {
    outline: none;
    border-color: #ff0000;
    color: #ff0000;
}

.address-suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #000000;
    border: 1px solid #ff4444;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.address-suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #333333;
    transition: background-color 0.2s ease;
}

.address-suggestion-item:hover,
.address-suggestion-item.active {
    background: #330000;
}

.address-suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-main {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
}

.suggestion-secondary {
    color: #888888;
    font-size: 11px;
    margin-top: 2px;
}

/* Address field validation styles */
.field-help {
    display: block;
    font-size: 10px;
    color: #888888;
    margin-top: 4px;
}

.field-help.error {
    color: #ff4444;
}

.field-help.success {
    color: #00ff00;
}

/* Postal code validation feedback */
input[name="postal_code"] {
    transition: border-color 0.3s ease;
}

input[name="postal_code"]:valid {
    border-color: #00ff00;
}

input[name="postal_code"]:invalid {
    border-color: #ff4444;
}

/* File Upload Styles - Retro theme compatible */
.file-upload-area {
    margin-top: 15px;
    border: 1px solid #333333;
    padding: 15px;
    background: rgba(0, 0, 0, 0.3);
}

.upload-zone {
    border: 1px dashed #333333;
    padding: 30px 20px;
    text-align: center;
    background: rgba(0, 255, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.upload-zone:hover,
.upload-zone.drag-over {
    border-color: #00ff00;
    background: rgba(0, 255, 0, 0.1);
}

.upload-icon {
    font-size: 36px;
    margin-bottom: 10px;
    opacity: 0.7;
}

.uploaded-files {
    margin-top: 15px;
    display: none;
}

.uploaded-file {
    display: flex;
    align-items: center;
    background: rgba(0, 255, 0, 0.05);
    border: 1px solid #333333;
    padding: 10px;
    margin-bottom: 8px;
    gap: 10px;
}

.file-preview {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.file-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.file-info {
    flex: 1;
}

.file-name {
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 2px;
    word-break: break-all;
}

.file-size {
    color: #888888;
    font-size: 10px;
    margin-bottom: 2px;
}

.file-status {
    color: #00ff00;
    font-size: 10px;
}

.remove-file-btn {
    background: #ff0000;
    border: none;
    color: #ffffff;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
}

.remove-file-btn:hover {
    background: #ff4444;
}

.link-button {
    background: none;
    border: none;
    color: #00ff00;
    text-decoration: underline;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
}

.link-button:hover {
    color: #44ff44;
}

/* Attachment Display Styles */
.no-attachments {
    color: #888888;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.attachment-item {
    display: flex;
    align-items: center;
    background: rgba(0, 255, 0, 0.05);
    border: 1px solid #333333;
    padding: 10px;
    margin-bottom: 8px;
    gap: 10px;
}

.attachment-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.attachment-info {
    flex: 1;
}

.attachment-name {
    color: #ffffff;
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 2px;
}

.attachment-details {
    color: #888888;
    font-size: 10px;
    margin-bottom: 2px;
}

.attachment-description {
    color: #cccccc;
    font-size: 10px;
    font-style: italic;
}

.attachment-actions {
    display: flex;
    gap: 5px;
}

/* Duplicate .action-button removed - using primary definition at line 1912 */


/* Enhanced Incident Form - Tabbed Interface */
.incident-form-tabs {
    display: flex;
    background: #111111;
    border-bottom: 1px solid #ff4444;
    margin-bottom: 5px;
}

.incident-tab {
    background: #000000;
    color: #ff4444;
    border: 1px solid #ff4444;
    border-bottom: none;
    padding: 5px 5px;
    font-family: inherit;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 2px;
    min-width: 120px;
    text-align: center;
}

.incident-tab:hover {
    background: #330000;
    color: #ff0000;
}

.incident-tab.active {
    background: #ff4444;
    color: #000000;
    border-color: #ff4444;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

/* Checkbox grid for services and resources */
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.checkbox-grid label {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #ff4444;
    cursor: pointer;
}

/* Tabbed Incident Form Styles */
.form-group {
    margin-bottom: 5px;
}

.form-group label {
    display: block;
    color: #ff4444;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group input[type="time"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    background: #000000;
    border: 1px solid #ff4444;
    color: #ffffff;
    font-family: inherit;
    font-size: 11px;
    border-radius: 2px;
}

.form-group input[type="text"]:focus,
.form-group input[type="date"]:focus,
.form-group input[type="time"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff0000;
    background: #110000;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #ff0000;
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.checkbox-grid label {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #ff4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    transition: background-color 0.2s;
}

.checkbox-grid label:hover {
    background: rgba(255, 0, 0, 0.1);
}

.checkbox-grid input[type="checkbox"] {
    margin-right: 6px;
}

.checkbox-grid input[type="checkbox"] {
    margin-right: 8px;
    margin-bottom: 0;
}

.checkbox-grid label:hover {
    color: #ff6666;
}


/* Enhanced incident overview styling */
.overview-section {
    margin-bottom: 5px;
    border-bottom: 1px solid #333333;
    padding-bottom: 5px;
}

.overview-section:last-child {
    border-bottom: none;
}

.overview-section h4 {
    color: #ff6666;
    margin-bottom: 10px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.detail-value.safety-safe { color: #00ff00; }
.detail-value.safety-caution { color: #ffff00; }
.detail-value.safety-unsafe { color: #ff6600; }
.detail-value.safety-hazardous { color: #ff0000; }

/* People management for incidents */
.people-management-section {
    background: #0a0a0a;
    border: 1px solid #333333;
    padding: 20px;
    margin-bottom: 20px;
}

.people-management-section .form-section-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff0000;
}

.people-management-section .form-section-header h4 {
    color: #ff0000;
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
}

.people-search-container {
    margin-bottom: 20px;
}

.search-input-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.search-input-group .text-input {
    flex: 1;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 8px 12px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    min-height: 18px;
}

.search-input-group .text-input:focus {
    border-color: #ff3333;
    outline: none;
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
}

/* People search specific styling - larger input, smaller button */
.people-search-container .search-input-group {
    display: flex;
    gap: 8px;
    align-items: stretch;
}

.people-search-container .search-input-group .text-input {
    flex: 3; /* Give more space to the input field */
    min-width: 300px;
}

.people-search-container .search-input-group .action-btn {
    flex: 0 0 auto; /* Don't let button grow */
    width: auto;
    min-width: 80px; /* Set reasonable minimum button width */
    padding: 8px 12px;
    font-size: 11px;
    white-space: nowrap;
}

/* Results styling */
.people-search-results {
    background: #000000;
    border: 1px solid #ff0000;
    margin-top: 15px;
    max-height: 200px;
    overflow-y: auto;
    width: 100%;
    box-sizing: border-box;
}

.results-header {
    padding: 8px 15px;
    background: #ff0000;
    border-bottom: 1px solid #ff0000;
}

.results-header h5 {
    color: #000000;
    margin: 0;
    font-size: 11px;
    font-weight: bold;
    font-family: 'Courier New', monospace;
    text-transform: uppercase;
}

.search-results-list {
    padding: 8px;
    width: 100%;
    box-sizing: border-box;
}

.search-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid #333333;
    background: #0a0a0a;
    cursor: pointer;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;
    flex-wrap: wrap;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background: #1a0a0a;
    border-color: #ff0000;
}

.search-result-item .person-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
    flex: 1;
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.search-result-item .person-info strong {
    color: #ff6666;
    font-size: 12px;
    font-family: 'Courier New', monospace;
}

.person-age, .person-status {
    color: #888888;
    font-size: 10px;
    font-family: 'Courier New', monospace;
}

.no-results {
    padding: 20px;
    text-align: center;
    color: #888888;
    font-family: 'Courier New', monospace;
    font-style: italic;
}

.involved-people-display {
    margin-top: 20px;
}

.people-list-container {
    background: #0a0a0a;
    border: 1px solid #333333;
    min-height: 80px;
    padding: 15px;
}

.no-people-message {
    color: #888888;
    text-align: center;
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
}

.no-people-message.terminal-text {
    line-height: 1.6;
}

.involved-person-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px;
    background: #1a0a0a;
    border: 1px solid #333333;
    margin-bottom: 8px;
}

.involved-person-item:last-child {
    margin-bottom: 0;
}

.person-details {
    flex: 1;
}

.person-name {
    color: #ff6666;
    font-weight: bold;
    margin-bottom: 4px;
}

.person-role {
    margin-bottom: 6px;
}

.involvement-badge {
    background: #ff0000;
    color: #000000;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 9px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.involvement-witness { background: #0066ff; color: #ffffff; }
.involvement-victim { background: #ff6600; color: #ffffff; }
.involvement-suspect { background: #ff0000; color: #ffffff; }
.involvement-complainant { background: #00ff00; color: #000000; }
.involvement-subject { background: #ffff00; color: #000000; }
.involvement-family_member { background: #ff00ff; color: #ffffff; }
.involvement-service_recipient { background: #00ffff; color: #000000; }
.involvement-other { background: #888888; color: #ffffff; }

.person-description {
    color: #cccccc;
    font-size: 10px;
    margin-bottom: 4px;
    font-style: italic;
}

.person-info {
    color: #888888;
    font-size: 10px;
    margin-bottom: 4px;
}

.person-notes {
    color: #aaaaaa;
    font-size: 9px;
    margin-top: 6px;
    padding-top: 4px;
    border-top: 1px solid #333333;
}

.person-notes strong {
    color: #ff6666;
}

.action-btn.small {
    font-size: 9px;
    padding: 4px 8px;
    margin-left: 10px;
}

.action-btn.danger {
    background: #ff0000;
    color: #ffffff;
    border: 1px solid #ff0000;
}

.action-btn.danger:hover {
    background: #cc0000;
    border-color: #cc0000;
}

/* Dialog styling for people management */
.dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.dialog-content {
    background: #000000;
    border: 2px solid #ff0000;
    padding: 0;
    min-width: 400px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
}

.dialog-header {
    background: #ff0000;
    color: #000000;
    padding: 10px 15px;
    font-weight: bold;
    font-size: 12px;
    text-transform: uppercase;
}

.dialog-body {
    padding: 20px;
}

.dialog-body .form-field {
    margin-bottom: 15px;
}

.dialog-body .form-field label {
    display: block;
    color: #ff6666;
    font-size: 11px;
    margin-bottom: 5px;
    text-transform: uppercase;
}

.dialog-body .form-field input,
.dialog-body .form-field select,
.dialog-body .form-field textarea {
    width: 100%;
    background: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 8px 10px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
}

.dialog-body .form-field textarea {
    resize: vertical;
}

.dialog-actions {
    padding: 15px;
    border-top: 1px solid #333333;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* LARGE DUPLICATE INCIDENT BLOCK REMOVED (638 lines) - All incident form, creation, detail, and medical dialog styles moved to organized incidents.css file for better maintainability and modularity. This includes:
- Incident creation container and forms
- Incident detail tabs and content
- Medical dialog styling 
- Address search and suggestions
- Form actions and responsive design
The organized versions in renderer/templates/incidents/incidents.css are more comprehensive and include proper responsive design patterns. */

/* ===================================================
   OUTREACH TRANSACTION INTERFACE STYLES
   =================================================== */

.outreach-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    background: #000000;
    color: #ff0000;
}

.outreach-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #ff0000;
    background: #000000;
}

.outreach-header h2 {
    color: #ff0000;
    font-size: 18px;
    margin: 0;
}

.outreach-status {
    font-size: 12px;
    color: #ff4444;
}

.outreach-main {
    flex: 1;
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 15px;
    padding: 15px;
    overflow: hidden;
    min-height: 0;
}

.outreach-list-section {
    display: flex;
    flex-direction: column;
    border: 1px solid #ff4444;
    background: #000000;
    height: 100%;
    overflow: hidden;
}

.outreach-list-header {
    padding: 10px;
    border-bottom: 1px solid #ff4444;
    background: #111111;
}

.outreach-list-header .list-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.outreach-list-header select,
.outreach-list-header input {
    background: #000000;
    color: #ff0000;
    border: 1px solid #ff4444;
    padding: 5px 8px;
    font-family: inherit;
    font-size: 11px;
    flex: 1;
    min-width: 80px;
}

.outreach-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.outreach-item {
    border: 1px solid #ff4444;
    background: #111111;
    margin-bottom: 10px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.outreach-item:hover {
    background: #1a1a1a;
    border-color: #ff6666;
}

.outreach-item.selected {
    background: #330000;
    border-color: #ff0000;
    box-shadow: 0 0 5px #ff0000;
}

.outreach-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.transaction-time {
    color: #ff6666;
    font-size: 12px;
    font-weight: bold;
}

.transaction-id {
    color: #ff4444;
    font-size: 10px;
    opacity: 0.8;
}

.outreach-item-content {
    margin-bottom: 8px;
}

.person-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.person-name {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
}

.person-id {
    color: #ff4444;
    font-size: 10px;
    opacity: 0.7;
}

.transaction-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.items-count,
.location {
    color: #ff4444;
    font-size: 11px;
}

.staff-info {
    margin-bottom: 5px;
}

.staff-member {
    color: #ff6666;
    font-size: 11px;
}

.outreach-item-footer {
    text-align: right;
}

.transaction-status {
    color: #00ff00;
    font-size: 10px;
    text-transform: uppercase;
}

.outreach-details-section {
    border: 1px solid #ff4444;
    background: #000000;
    padding: 15px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.outreach-details-section .no-selection {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
}

.outreach-details-section .no-selection h3 {
    color: #ff0000;
    margin-bottom: 10px;
}

.outreach-quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.stat-card {
    border: 1px solid #ff4444;
    background: #111111;
    padding: 15px;
    text-align: center;
}

.stat-number {
    color: #ff0000;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #ff4444;
    font-size: 11px;
    text-transform: uppercase;
}

.outreach-actions {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid #ff0000;
    background: #000000;
}

/* Outreach action buttons - using proper CSS specificity instead of !important */
.outreach-actions .action-button {
    /* Inherits from main .action-button definition - no overrides needed */
}

/* Outreach Detail View Styles */
.outreach-detail-view {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #ff4444;
    margin-bottom: 15px;
}

.detail-header h3 {
    color: #ff0000;
    margin: 0;
    font-size: 16px;
}

.detail-id {
    color: #ff4444;
    font-size: 12px;
    opacity: 0.8;
}

.detail-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    border-bottom: 1px solid #ff4444;
}

.detail-tab {
    padding: 8px 16px;
    background: #111111;
    border: 1px solid #ff4444;
    border-bottom: none;
    color: #ff4444;
    cursor: pointer;
    font-size: 11px;
    text-transform: uppercase;
    transition: all 0.2s ease;
}

.detail-tab:hover {
    background: #1a1a1a;
    color: #ff6666;
}

.detail-tab.active {
    background: #330000;
    color: #ff0000;
    border-color: #ff0000;
}

.detail-content {
    flex: 1;
    overflow-y: auto;
}

.detail-tab-content {
    display: block;
    padding: 20px;
    color: #cccccc;
    font-family: 'Courier New', monospace;
}

.detail-section {
    margin-bottom: 20px;
}

.section-title {
    color: #ff0000;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.detail-grid {
    display: grid;
    gap: 8px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #333333;
}

.detail-label {
    color: #ff4444;
    font-size: 12px;
    font-weight: bold;
}

.detail-value {
    color: #ff0000;
    font-size: 12px;
    text-align: right;
    flex: 1;
    margin-left: 20px;
}

.status-completed {
    color: #00ff00 !important;
    text-transform: uppercase;
}

.notes-content {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 12px;
    color: #ff4444;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
}

.items-list {
    display: grid;
    gap: 10px;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #111111;
    border: 1px solid #ff4444;
    padding: 10px;
}

.item-info {
    flex: 1;
}

.item-name {
    color: #ff0000;
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 3px;
}

.item-category {
    color: #ff4444;
    font-size: 10px;
    text-transform: capitalize;
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quantity {
    color: #ff0000;
    font-size: 14px;
    font-weight: bold;
}

.unit {
    color: #ff4444;
    font-size: 10px;
}

.person-info-detail {
    text-align: center;
    padding: 20px;
}

.person-name-large {
    color: #ff0000;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
}

.person-actions {
    margin-top: 15px;
}

.action-link {
    background: none;
    border: 1px solid #ff4444;
    color: #ff4444;
    padding: 8px 16px;
    cursor: pointer;
    font-family: inherit;
    font-size: 11px;
    text-transform: uppercase;
    transition: all 0.2s ease;
}

.action-link:hover {
    background: #ff4444;
    color: #000000;
}

.location-info {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 15px;
}

.location-address {
    color: #ff0000;
    font-size: 14px;
    margin-bottom: 10px;
}

.location-coordinates {
    color: #ff4444;
    font-size: 11px;
    opacity: 0.8;
}

.detail-actions {
    display: flex;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #ff4444;
    margin-top: auto;
}

.no-results,
.no-items {
    text-align: center;
    color: #ff4444;
    padding: 40px 20px;
}

.no-results h3 {
    color: #ff0000;
    margin-bottom: 10px;
}

.no-results-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.loading {
    text-align: center;
    color: #ff4444;
    padding: 20px;
}

.loading-spinner {
    border: 2px solid #333333;
    border-top: 2px solid #ff0000;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px auto;
}

.error {
    text-align: center;
    color: #ff0000;
    padding: 40px 20px;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design for Outreach */
@media (max-width: 1200px) {
    .outreach-main {
        grid-template-columns: 300px 1fr;
    }
}

@media (max-width: 900px) {
    .outreach-main {
        grid-template-columns: 250px 1fr;
    }
    
    .outreach-quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .outreach-main {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
    }
    
    .outreach-actions {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .outreach-quick-stats {
        grid-template-columns: 1fr;
    }
}

/* Reports Styles */
.reports-main {
    padding: 20px;
}

.reports-menu {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.terminal-section {
    border: 2px solid #ff0000;
    padding: 15px;
    background: #000000;
}

.section-header {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    border-bottom: 1px solid #ff0000;
    padding-bottom: 5px;
}

.report-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.report-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid #ff0000;
    cursor: pointer;
    transition: all 0.2s ease;
}

.report-item:hover {
    background: #330000;
    border-color: #ff4444;
}

.report-icon {
    font-size: 24px;
    filter: sepia(100%) hue-rotate(0deg) brightness(0.8) saturate(2);
}

.report-details {
    flex-grow: 1;
}

.report-title {
    color: #ff0000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 3px;
}

.report-description {
    color: #ff4444;
    font-size: 12px;
}

.report-action {
    margin-left: auto;
}

.terminal-button {
    background: #000000;
    color: #ff0000;
    border: 2px solid #ff0000;
    padding: 8px 16px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    cursor: pointer;
    transition: all 0.2s ease;
}

.terminal-button:hover {
    background: #ff0000;
    color: #000000;
}

.terminal-button.primary {
    background: #ff0000;
    color: #000000;
}

.terminal-button.primary:hover {
    background: #ff4444;
}

/* Report Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #000000;
    border: 3px solid #ff0000;
    width: 90vw;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.report-modal {
    padding: 0;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 2px solid #ff0000;
    background: #000000;
}

.modal-header h3 {
    color: #ff0000;
    font-size: 16px;
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    color: #ff0000;
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.close-modal:hover {
    color: #ff4444;
}

.modal-body {
    padding: 20px;
}

.report-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-section {
    border: 1px solid #ff0000;
    padding: 15px;
}

.person-search-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.search-bar {
    display: flex;
    gap: 10px;
}

.search-input {
    flex: 1;
    background: #000000;
    color: #ff0000;
    border: 2px solid #ff0000;
    padding: 8px 12px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #ff4444;
}

.search-button {
    background: #000000;
    color: #ff0000;
    border: 2px solid #ff0000;
    padding: 8px 12px;
    cursor: pointer;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.search-button:hover {
    background: #ff0000;
    color: #000000;
}

.search-results {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ff0000;
    background: #000000;
}

.person-result {
    padding: 10px;
    border-bottom: 1px solid #ff0000;
    cursor: pointer;
    transition: background-color 0.2s;
}

.person-result:hover {
    background: #330000;
}

.person-result:last-child {
    border-bottom: none;
}

.person-name {
    color: #ff0000;
    font-weight: bold;
}

.person-details {
    color: #ff4444;
    font-size: 12px;
    margin-top: 2px;
}

.selected-person {
    border: 2px solid #ff0000;
    padding: 10px;
    background: #330000;
}

.person-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ff0000;
}

.change-person-btn {
    background: none;
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 4px 8px;
    cursor: pointer;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
}

.change-person-btn:hover {
    background: #ff0000;
    color: #000000;
}

.no-results {
    padding: 10px;
    text-align: center;
    color: #ff4444;
    font-style: italic;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #ff0000;
}

.checkbox-item label {
    color: #ff0000;
    font-size: 14px;
    cursor: pointer;
}

.location-options {
    display: flex;
    flex-direction: column;
}

.terminal-select {
    background: #000000;
    color: #ff0000;
    border: 2px solid #ff0000;
    padding: 8px 12px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 14px;
    width: 200px;
}

.terminal-select:focus {
    outline: none;
    border-color: #ff4444;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid #ff0000;
}

.history-placeholder {
    color: #ff4444;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Narrative Editor Styles */
.narrative-container {
    margin-top: 10px;
}

.narrative-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ff0000;
}

.add-narrative-btn {
    background: #000000;
    border: 1px solid #00ff00;
    color: #00ff00;
    padding: 8px 15px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.add-narrative-btn:hover {
    background: #003300;
    border-color: #00ff44;
    color: #00ff44;
}

.narrative-entries {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff0000;
    background: #111111;
}

.narrative-entry {
    border-bottom: 1px solid #333333;
    padding: 15px;
    margin: 0;
}

.narrative-entry:last-child {
    border-bottom: none;
}

.narrative-entry .narrative-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    border-bottom: none;
    padding-bottom: 5px;
}

.narrative-meta {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.narrative-type {
    font-size: 11px;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}

.narrative-type-initial_observation {
    background: #003366;
    color: #66ccff;
}

.narrative-type-action_taken {
    background: #003300;
    color: #66ff66;
}

.narrative-type-status_update {
    background: #333300;
    color: #ffff66;
}

.narrative-type-follow_up {
    background: #330033;
    color: #ff66ff;
}

.narrative-type-contact_made {
    background: #663300;
    color: #ffcc66;
}

.narrative-type-note {
    background: #333333;
    color: #cccccc;
}

.narrative-type-other {
    background: #330000;
    color: #ff6666;
}

.narrative-timestamp {
    color: #888888;
    font-size: 10px;
}

.narrative-user {
    color: #aaaaaa;
    font-size: 10px;
}

.narrative-actions {
    display: flex;
    gap: 5px;
}

.edit-entry-btn, .delete-entry-btn {
    background: none;
    border: 1px solid #666666;
    color: #cccccc;
    padding: 3px 8px;
    font-size: 10px;
    cursor: pointer;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    transition: all 0.2s;
}

.edit-entry-btn:hover {
    border-color: #ffff00;
    color: #ffff00;
}

.delete-entry-btn:hover {
    border-color: #ff4444;
    color: #ff4444;
}

.narrative-content {
    color: #ffffff;
    line-height: 1.4;
    font-size: 12px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.no-entries-message {
    text-align: center;
    color: #666666;
    font-style: italic;
    padding: 30px;
    line-height: 1.5;
}

/* Narrative Modal Styles */
.narrative-modal {
    min-width: 600px;
    max-width: 800px;
    max-height: 80vh;
}

.editing-info {
    color: #ffff66;
    font-size: 11px;
    margin: 5px 0 0 0;
    font-style: italic;
}

.narrative-modal .modal-body {
    padding: 20px;
}

.narrative-modal textarea {
    min-height: 200px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    resize: vertical;
}

/* Text Formatting Toolbar Styles */
.text-formatting-toolbar {
    display: flex;
    align-items: center;
    gap: 3px;
    padding: 6px 8px;
    background: #1a1a1a;
    border: 1px solid #444444;
    border-radius: 3px 3px 0 0;
    margin-bottom: 0;
}

.format-btn {
    background: #333333;
    border: 1px solid #555555;
    color: #ff3333;
    padding: 4px 8px;
    border-radius: 2px;
    cursor: pointer;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
    transition: all 0.2s ease;
    min-width: 28px;
    text-align: center;
}

.format-btn:hover {
    background: #555555;
    border-color: #777777;
    color: #ff5555;
}

.format-btn:active {
    background: #ff3333;
    color: #000000;
}

.toolbar-divider {
    color: #666666;
    margin: 0 4px;
    font-size: 12px;
}

/* Adjust textarea when toolbar is present */
.text-formatting-toolbar + textarea {
    border-radius: 0 0 3px 3px;
    border-top: none;
}

/* List styling in narrative content */
.bullet-item {
    display: block;
    margin-left: 15px;
    margin-bottom: 3px;
    color: #cccccc;
    line-height: 1.4;
}

.number-item {
    display: block;
    margin-left: 15px;
    margin-bottom: 3px;
    color: #cccccc;
    line-height: 1.4;
}

/* Format content styling */
.narrative-content strong {
    color: #ffffff;
    font-weight: bold;
}

.narrative-content em {
    color: #cccccc;
    font-style: italic;
}

.narrative-content u {
    color: #ffffff;
    text-decoration: underline;
}

/* WYSIWYG Editor Styles */
.wysiwyg-editor {
    min-height: 200px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
    background: #000000;
    color: #ff0000;
    border: 1px solid #ff0000;
    padding: 10px;
    outline: none;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.wysiwyg-editor:focus {
    border-color: #ff4444;
    box-shadow: 0 0 3px #ff0000;
}

.wysiwyg-editor.empty .placeholder-text {
    color: #666666;
    font-style: italic;
    pointer-events: none;
}

.wysiwyg-editor strong {
    color: #ffffff;
    font-weight: bold;
}

.wysiwyg-editor em {
    color: #cccccc;
    font-style: italic;
}

.wysiwyg-editor u {
    color: #ff0000;
    text-decoration: underline;
}

.wysiwyg-editor ul, 
.wysiwyg-editor ol {
    margin-left: 20px;
    margin-top: 5px;
    margin-bottom: 5px;
}

.wysiwyg-editor li {
    color: #ff0000;
    margin-bottom: 2px;
}

.field-help {
    color: #888888;
    font-size: 10px;
    margin-top: 5px;
    display: block;
}

/* Modal Close Button */
.modal-close {
    background: none;
    border: none;
    color: #ff0000;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
}

.modal-close:hover {
    color: #ff4444;
    background: #330000;
}

/* Responsive adjustments for narrative editor */
@media (max-width: 768px) {
    .narrative-modal {
        min-width: 95vw;
        max-width: 95vw;
        margin: 10px;
    }

    .narrative-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .narrative-actions {
        justify-content: center;
    }

    .narrative-entries {
        max-height: 300px;
    }
}

/* AI Text Enhancement Styles */
.ai-enhance-btn {
    background: #1a1a1a !important;
    color: #00ff00 !important;
    border: 1px solid #00ff00 !important;
    font-weight: bold;
}

.ai-enhance-btn:hover {
    background: #00ff00 !important;
    color: #000000 !important;
}

.ai-enhance-modal {
    width: 90%;
    max-width: 900px;
    max-height: 80vh;
}

.ai-enhance-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.original-text-section,
.enhanced-text-section {
    flex: 1;
}

.original-text-section h4,
.enhanced-text-section h4 {
    color: #00ff00;
    font-size: 14px;
    margin-bottom: 8px;
    border-bottom: 1px solid #333;
    padding-bottom: 4px;
}

.text-preview {
    background: #1a1a1a;
    border: 1px solid #333;
    padding: 15px;
    min-height: 120px;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #ffffff;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.text-preview.original-text {
    border-color: #666;
}

.text-preview.enhanced-text {
    border-color: #00ff00;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    color: #00ff00;
    font-size: 14px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.error-message {
    color: #ff6b6b;
    text-align: center;
    padding: 20px;
}

.error-message p {
    margin-bottom: 8px;
}

.ai-enhancement-status {
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
    text-align: center;
}

.ai-enhancement-status.error {
    background: #2a0a0a;
    border: 1px solid #ff6b6b;
    color: #ff6b6b;
}

.ai-enhancement-status.success {
    background: #0a2a0a;
    border: 1px solid #00ff00;
    color: #00ff00;
}

/* AI Settings Admin Styles */
.ai-settings-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
    max-width: 800px;
}

.ai-settings-form .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ai-settings-form label {
    color: #00ff00;
    font-weight: bold;
    font-size: 14px;
}

.ai-prompt-textarea {
    min-height: 150px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    resize: vertical;
}

.form-help {
    color: #888;
    font-size: 11px;
    font-style: italic;
    margin-top: 4px;
}

.ai-settings-status {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.status-label {
    color: #ccc;
    font-weight: bold;
}

.status-value {
    color: #ffffff;
}

.status-value.status-success {
    color: #00ff00;
}

.status-value.status-error {
    color: #ff6b6b;
}

.status-value.status-warning {
    color: #ffa500;
}

.status-value.status-neutral {
    color: #888;
}

.ai-test-section {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    padding: 20px;
}

.ai-test-section h4 {
    color: #00ff00;
    font-size: 14px;
    margin-bottom: 15px;
    border-bottom: 1px solid #333;
    padding-bottom: 8px;
}

.ai-test-result {
    margin-top: 15px;
    padding: 15px;
    background: #0a0a0a;
    border: 1px solid #00ff00;
    border-radius: 4px;
}

.ai-test-result h5 {
    color: #00ff00;
    font-size: 12px;
    margin-bottom: 10px;
}

.enhanced-text-preview {
    background: #1a1a1a;
    border: 1px solid #333;
    padding: 10px;
    min-height: 100px;
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    color: #ffffff;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Responsive adjustments for AI enhancement */
@media (max-width: 768px) {
    .ai-enhance-modal {
        width: 95%;
        height: 90vh;
    }
    
    .text-preview {
        min-height: 80px;
        max-height: 150px;
        font-size: 11px;
    }
    
    .ai-settings-form {
        gap: 20px;
    }
    
    .ai-prompt-textarea {
        min-height: 120px;
    }
}

/* ===== ENCAMPMENT VISIT TRACKING STYLES ===== */

/* Visit Form Modal */
.visit-form-modal {
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
}

.visit-form {
    font-family: 'Courier New', monospace;
}

.visit-form .form-section {
    background: #000000;
    border: 1px solid #ff4444;
    margin-bottom: 15px;
    padding: 12px;
}

.visit-form .form-section h3 {
    color: #ff0000;
    font-size: 14px;
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ff4444;
}

.visit-form .form-section h4 {
    color: #ff6666;
    font-size: 12px;
    margin: 10px 0 8px 0;
}

.visit-form .checkbox-section {
    margin-top: 12px;
}

.visit-form .checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.visit-form .checkbox-grid label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    font-size: 11px;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    transition: background-color 0.2s;
}

.visit-form .checkbox-grid label:hover {
    background: #330000;
}

.visit-form .checkbox-grid input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #ff0000;
}

/* Visit History Styles */
.visit-history-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff4444;
    background: #000000;
    padding: 10px;
}

.visit-history-item {
    background: #111111;
    border: 1px solid #ff4444;
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 3px;
}

.visit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.visit-date-time {
    color: #ff0000;
    font-weight: bold;
    font-size: 12px;
}

.visit-visitor {
    color: #ff6666;
    font-size: 11px;
}

.visit-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 8px;
}

.visit-stat {
    background: #ff4444;
    color: #000000;
    padding: 2px 6px;
    font-size: 10px;
    border-radius: 2px;
    font-weight: bold;
}

.visit-stat.condition-excellent { background: #00ff00; }
.visit-stat.condition-good { background: #88ff88; }
.visit-stat.condition-fair { background: #ffff00; }
.visit-stat.condition-poor { background: #ff8800; }
.visit-stat.condition-critical { background: #ff0000; }

.visit-stat.safety-low { background: #00ff00; }
.visit-stat.safety-moderate { background: #ffff00; }
.visit-stat.safety-high { background: #ff8800; }
.visit-stat.safety-critical { background: #ff0000; }

.visit-notes {
    color: #cccccc;
    font-size: 11px;
    line-height: 1.4;
    margin-bottom: 8px;
    padding: 6px;
    background: #222222;
    border-left: 3px solid #ff4444;
}

.visit-actions {
    display: flex;
    gap: 10px;
}

.link-button {
    background: none;
    border: none;
    color: #ff6666;
    font-family: inherit;
    font-size: 11px;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
}

.link-button:hover {
    color: #ff0000;
}

/* Visit Details Modal */
.visit-details-modal {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.visit-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.visit-detail-grid .detail-section.full-width {
    grid-column: 1 / -1;
}

/* History Actions */
.history-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.history-actions .primary-button,
.history-actions .secondary-button {
    font-size: 11px;
    padding: 6px 12px;
}

/* Loading and Error States */
.loading-message {
    text-align: center;
    color: #ff6666;
    font-style: italic;
    padding: 20px;
}

.error-message {
    text-align: center;
    color: #ff0000;
    background: #330000;
    border: 1px solid #ff0000;
    padding: 15px;
    border-radius: 3px;
}

/* Mobile Responsiveness for Visit Features */
@media (max-width: 768px) {
    .visit-form-modal {
        width: 95vw;
        max-width: none;
        margin: 10px;
    }
    
    .visit-form .checkbox-grid {
        grid-template-columns: 1fr;
    }
    
    .visit-detail-grid {
        grid-template-columns: 1fr;
    }
    
    .visit-summary {
        flex-direction: column;
        gap: 5px;
    }
    
    .history-actions {
        flex-direction: column;
        gap: 5px;
    }
}

/* Enhanced Property Return Styles */
.return-type-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    margin-left: 8px;
    white-space: nowrap;
}

.return-type-badge.business {
    background: #004400;
    color: #00ff00;
    border: 1px solid #00ff00;
}

.return-type-badge.individual {
    background: #000044;
    color: #0088ff;
    border: 1px solid #0088ff;
}

.property-meta span.return-type-badge {
    margin-left: 0;
    margin-right: 8px;
}

/* Enhanced return modal overlay */
.enhanced-return-overlay {
    z-index: 10000;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Justice Module Styles */
@import url('./css/justice.css');
