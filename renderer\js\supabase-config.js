/**
 * Centralized Supabase Configuration Module
 * 
 * This module provides a single source of truth for all Supabase configuration
 * including URLs, API keys, and project references. This ensures that changing
 * the Supabase project URL only requires updating one location.
 * 
 * To change the Supabase project URL:
 * 1. Update the PROJECT_URL constant below
 * 2. Update the PROJECT_REF constant below
 * 3. Update the ANON_KEY constant below
 * 
 * All other files will automatically use the new configuration.
 */

// ============================================================================
// SUPABASE PROJECT CONFIGURATION
// ============================================================================
// 🔧 CHANGE THESE VALUES TO UPDATE THE SUPABASE PROJECT
const PROJECT_URL = 'https://vfavknkfiiclzgpjpntj.supabase.co';
const PROJECT_REF = 'vfavknkfiiclzgpjpntj';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZmYXZrbmtmaWljbHpncGpwbnRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMjQzMzEsImV4cCI6MjA2NjgwMDMzMX0.ywwytraoAsGCEWcxj3U8MHs_E1xpbeKP9LDSYsEziJU';
// ============================================================================

/**
 * Supabase Configuration Manager
 * Provides centralized access to Supabase configuration values
 */
export class SupabaseConfig {
    constructor() {
        this.projectUrl = PROJECT_URL;
        this.projectRef = PROJECT_REF;
        this.anonKey = ANON_KEY;
        
        // Validate configuration on initialization
        this.validateConfig();
    }

    /**
     * Get the Supabase project URL
     * @returns {string} The Supabase project URL
     */
    getUrl() {
        return this.projectUrl;
    }

    /**
     * Get the Supabase project reference (used for MCP and other integrations)
     * @returns {string} The Supabase project reference
     */
    getProjectRef() {
        return this.projectRef;
    }

    /**
     * Get the Supabase anonymous key
     * @returns {string} The Supabase anonymous key
     */
    getAnonKey() {
        return this.anonKey;
    }

    /**
     * Get the REST API base URL for direct fetch calls
     * @returns {string} The REST API base URL
     */
    getRestApiUrl() {
        return `${this.projectUrl}/rest/v1`;
    }

    /**
     * Get the complete configuration object
     * @returns {object} Complete Supabase configuration
     */
    getConfig() {
        return {
            url: this.projectUrl,
            anonKey: this.anonKey,
            projectRef: this.projectRef,
            restApiUrl: this.getRestApiUrl()
        };
    }

    /**
     * Validate the configuration to ensure all required values are present
     * @throws {Error} If configuration is invalid
     */
    validateConfig() {
        if (!this.projectUrl) {
            throw new Error('Supabase project URL is required');
        }
        
        if (!this.projectRef) {
            throw new Error('Supabase project reference is required');
        }
        
        if (!this.anonKey) {
            throw new Error('Supabase anonymous key is required');
        }

        // Validate URL format
        try {
            new URL(this.projectUrl);
        } catch (error) {
            throw new Error(`Invalid Supabase URL format: ${this.projectUrl}`);
        }

        // Validate that URL contains supabase.co
        if (!this.projectUrl.includes('supabase.co')) {
            throw new Error(`URL does not appear to be a valid Supabase URL: ${this.projectUrl}`);
        }

        // Validate that project ref matches URL
        if (!this.projectUrl.includes(this.projectRef)) {
            console.warn(`⚠️ Project reference "${this.projectRef}" does not match URL "${this.projectUrl}"`);
        }
    }

    /**
     * Create headers for direct REST API calls
     * @param {object} options - Additional options
     * @param {string} options.contentType - Content type (default: application/json)
     * @param {string} options.acceptProfile - Accept profile header
     * @param {string} options.contentProfile - Content profile header
     * @param {string} options.prefer - Prefer header
     * @returns {object} Headers object for fetch calls
     */
    createRestApiHeaders(options = {}) {
        const {
            contentType = 'application/json',
            acceptProfile,
            contentProfile,
            prefer
        } = options;

        const headers = {
            'Content-Type': contentType,
            'apikey': this.anonKey,
            'Authorization': `Bearer ${this.anonKey}`
        };

        if (acceptProfile) {
            headers['Accept-Profile'] = acceptProfile;
        }

        if (contentProfile) {
            headers['Content-Profile'] = contentProfile;
        }

        if (prefer) {
            headers['Prefer'] = prefer;
        }

        return headers;
    }

    /**
     * Build a complete REST API URL for a specific table and operation
     * @param {string} table - Table name
     * @param {string} filter - Optional filter (e.g., "id=eq.123")
     * @returns {string} Complete REST API URL
     */
    buildRestApiUrl(table, filter = '') {
        let url = `${this.getRestApiUrl()}/${table}`;
        if (filter) {
            url += `?${filter}`;
        }
        return url;
    }
}

// Create and export a singleton instance
export const supabaseConfig = new SupabaseConfig();

// Export individual functions for backward compatibility
export const getSupabaseUrl = () => supabaseConfig.getUrl();
export const getSupabaseAnonKey = () => supabaseConfig.getAnonKey();
export const getSupabaseProjectRef = () => supabaseConfig.getProjectRef();
export const getSupabaseConfig = () => supabaseConfig.getConfig();

// Default export
export default supabaseConfig;
