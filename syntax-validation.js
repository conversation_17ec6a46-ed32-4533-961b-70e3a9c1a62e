// Syntax Validation Script for Justice Module
// Quick validation to ensure no syntax errors remain

console.log('🔍 Justice Module Syntax Validation');
console.log('===================================');

async function validateSyntax() {
    const results = {
        passed: 0,
        failed: 0,
        errors: []
    };
    
    function test(name, condition, errorMsg = '') {
        if (condition) {
            console.log(`✅ ${name}`);
            results.passed++;
        } else {
            console.log(`❌ ${name}${errorMsg ? ': ' + errorMsg : ''}`);
            results.failed++;
            results.errors.push(name + (errorMsg ? ': ' + errorMsg : ''));
        }
    }
    
    // Test 1: Core module files
    const coreFiles = [
        './modules/justice/js/index.js',
        './modules/justice/js/justice-api.js',
        './modules/justice/js/justice-cache.js',
        './modules/justice/js/justice-state.js',
        './modules/justice/js/justice-commands.js'
    ];
    
    console.log('\n📁 Testing Core Module Files:');
    for (const file of coreFiles) {
        try {
            await import(file);
            test(`${file.split('/').pop()}`, true);
        } catch (error) {
            test(`${file.split('/').pop()}`, false, error.message);
        }
    }
    
    // Test 2: Template files
    const templateFiles = [
        './modules/justice/templates/index.js',
        './modules/justice/templates/status-ribbon-templates.js',
        './modules/justice/templates/start-episode-templates.js',
        './modules/justice/templates/timeline-templates.js',
        './modules/justice/templates/add-event-templates.js',
        './modules/justice/templates/charges-templates.js',
        './modules/justice/templates/conditions-templates.js',
        './modules/justice/templates/contacts-templates.js'
    ];
    
    console.log('\n📄 Testing Template Files:');
    for (const file of templateFiles) {
        try {
            const module = await import(file);
            const hasExports = Object.keys(module).length > 0;
            test(`${file.split('/').pop()}`, hasExports, hasExports ? '' : 'No exports found');
        } catch (error) {
            test(`${file.split('/').pop()}`, false, error.message);
        }
    }
    
    // Test 3: App integration
    console.log('\n🔗 Testing App Integration:');
    test('App instance available', typeof window.app === 'object');
    test('Commands system available', window.app?.commands?.commands instanceof Map);
    
    // Test 4: Justice commands registered
    console.log('\n⚡ Testing Command Registration:');
    const justiceCommands = ['je:new', 'je:view', 'je:bail', 'je:xfer', 'je:court', 'je:release', 'je:sentence', 'je:warrant'];
    justiceCommands.forEach(cmd => {
        test(`Command '${cmd}'`, window.app?.commands?.commands?.has(cmd));
    });
    
    // Test 5: App methods
    console.log('\n🔧 Testing App Methods:');
    const appMethods = [
        'mountJusticeTab',
        'showStartEpisodeWizard', 
        'openAddEventModal',
        'showEpisodeDetail',
        'addBailHearingEvent',
        'addTransferEvent',
        'addCourtAppearanceEvent'
    ];
    appMethods.forEach(method => {
        test(`App method '${method}'`, typeof window.app?.[method] === 'function');
    });
    
    // Test 6: No old commands
    console.log('\n🗑️ Testing Old Commands Removed:');
    const oldCommands = ['add-arrest-history', 'add-incarceration', 'add-bail-conditions', 'add-court-date', 'add-legal-contact'];
    oldCommands.forEach(cmd => {
        test(`Old command '${cmd}' removed`, !window.app?.commands?.commands?.has(cmd));
    });
    
    // Summary
    console.log('\n📊 Validation Results');
    console.log('=====================');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
    
    if (results.failed === 0) {
        console.log('\n🎉 ALL SYNTAX VALIDATION PASSED!');
        console.log('✅ Justice module is syntax-error-free');
        console.log('✅ All imports working correctly');
        console.log('✅ All integrations validated');
        console.log('✅ Ready for testing');
    } else {
        console.log('\n⚠️ Issues found:');
        results.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    return results;
}

// Test specific functionality
async function testBasicFunctionality() {
    console.log('\n🧪 Testing Basic Functionality');
    console.log('==============================');
    
    try {
        // Test template rendering
        const { statusRibbonTemplates } = await import('./modules/justice/templates/status-ribbon-templates.js');
        const mockStatus = {
            current_state: 'ACTIVE',
            current_facility: null,
            next_court_date: null,
            active_conditions_count: 0,
            active_warrants: false
        };
        
        const html = statusRibbonTemplates.statusRibbon(mockStatus);
        console.log('✅ Template rendering works');
        console.log(`   Generated ${html.length} characters of HTML`);
        
        // Test command structure
        if (window.app?.commands) {
            const justiceCommandCount = Array.from(window.app.commands.commands.keys())
                .filter(cmd => cmd.startsWith('je:')).length;
            console.log(`✅ ${justiceCommandCount} justice commands registered`);
        }
        
        console.log('✅ Basic functionality test passed');
        
    } catch (error) {
        console.error('❌ Basic functionality test failed:', error);
    }
}

// Export functions
window.validateSyntax = validateSyntax;
window.testBasicFunctionality = testBasicFunctionality;

// Auto-run validation
if (typeof window !== 'undefined') {
    console.log('🔍 Syntax validation loaded');
    console.log('Run validateSyntax() for complete validation');
    console.log('Run testBasicFunctionality() for functionality test');
    
    // Auto-run after delay
    setTimeout(async () => {
        await validateSyntax();
        await testBasicFunctionality();
    }, 500);
}
