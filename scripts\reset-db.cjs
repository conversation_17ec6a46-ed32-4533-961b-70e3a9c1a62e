// Database Reset Script for S.T.E.V.I Retro
// This script removes the corrupted SQLite database to force a clean rebuild

const fs = require('fs');
const path = require('path');

// Get the data path (same as in app)
const dataPath = path.join(require('os').homedir(), 'AppData', 'Roaming', 'steviretro', 'data');
const dbPath = path.join(dataPath, 'cache.db');
const dbShmPath = path.join(dataPath, 'cache.db-shm');
const dbWalPath = path.join(dataPath, 'cache.db-wal');

console.log('🔄 Resetting SQLite database...');
console.log('Database path:', dbPath);

try {
    // Remove main database file
    if (fs.existsSync(dbPath)) {
        fs.unlinkSync(dbPath);
        console.log('✅ Removed cache.db');
    }
    
    // Remove WAL file if exists
    if (fs.existsSync(dbWalPath)) {
        fs.unlinkSync(dbWalPath);
        console.log('✅ Removed cache.db-wal');
    }
    
    // Remove SHM file if exists
    if (fs.existsSync(dbShmPath)) {
        fs.unlinkSync(dbShmPath);
        console.log('✅ Removed cache.db-shm');
    }
    
    console.log('🎉 Database reset complete! The app will create a clean database on next startup.');
    
} catch (error) {
    console.error('❌ Error resetting database:', error.message);
    process.exit(1);
}