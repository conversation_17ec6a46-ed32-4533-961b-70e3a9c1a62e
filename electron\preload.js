// Preload script for S.T.E.V.I Retro
// Exposes secure APIs to the renderer process with context isolation
console.log('🔧 Preload: Starting preload script...');
const { contextBridge, ipc<PERSON>enderer } = require('electron');
console.log('🔧 Preload: contextBridge and ipc<PERSON><PERSON>er loaded');

// GPS API - Expose GPS functionality to renderer
const gpsAPI = {
    // Subscribe to GPS position updates
    onUpdate: (callback) => {
        const wrappedCallback = (event, data) => callback(data);
        ipcRenderer.on('gps-update', wrappedCallback);
        
        // Return unsubscribe function
        return () => {
            ipcRenderer.removeListener('gps-update', wrappedCallback);
        };
    },

    // Get the latest GPS position (one-time request)
    getLatest: () => {
        return new Promise((resolve, reject) => {
            // Set up timeout
            const timeout = setTimeout(() => {
                reject(new Error('GPS position request timed out'));
            }, 10000); // 10 second timeout

            // Listen for response
            const responseHandler = (event, data) => {
                clearTimeout(timeout);
                ipcRenderer.removeListener('gps-response', responseHandler);
                
                if (data.success) {
                    resolve(data.position);
                } else {
                    reject(new Error(data.error || 'Failed to get GPS position'));
                }
            };

            ipcRenderer.once('gps-response', responseHandler);
            
            // Request current position
            ipcRenderer.send('gps-request');
        });
    },

    // Check if GPS is connected
    isConnected: () => {
        return ipcRenderer.invoke('gps-is-connected');
    },

    // Get GPS connection status
    getStatus: () => {
        return ipcRenderer.invoke('gps-get-status');
    },

    // Listen for GPS becoming available
    onAvailable: (callback) => {
        const wrappedCallback = () => callback();
        ipcRenderer.on('gps-available', wrappedCallback);
        
        // Return unsubscribe function
        return () => {
            ipcRenderer.removeListener('gps-available', wrappedCallback);
        };
    }
};

// Location API - Enhanced location services
const locationAPI = {
    // Get current position using GPS or fallback methods
    getCurrentPosition: (options = {}) => {
        return new Promise(async (resolve, reject) => {
            try {
                // Try GPS first
                const gpsPosition = await gpsAPI.getLatest();
                if (gpsPosition) {
                    resolve({
                        coords: {
                            latitude: gpsPosition.latitude,
                            longitude: gpsPosition.longitude,
                            accuracy: gpsPosition.accuracy || 10,
                            altitude: gpsPosition.altitude || null,
                            altitudeAccuracy: null,
                            heading: gpsPosition.course || null,
                            speed: gpsPosition.speed || null
                        },
                        timestamp: gpsPosition.timestamp || Date.now()
                    });
                    return;
                }
            } catch (gpsError) {
                console.warn('GPS position failed, trying fallback methods:', gpsError.message);
            }

            // Fallback to native location services
            try {
                const nativeResult = await ipcRenderer.invoke('get-native-location');
                if (nativeResult.success) {
                    resolve({
                        coords: {
                            latitude: nativeResult.location.latitude,
                            longitude: nativeResult.location.longitude,
                            accuracy: nativeResult.location.accuracy || 50,
                            altitude: null,
                            altitudeAccuracy: null,
                            heading: null,
                            speed: null
                        },
                        timestamp: Date.now()
                    });
                    return;
                }
            } catch (nativeError) {
                console.warn('Native location failed:', nativeError.message);
            }

            // If all methods fail, reject
            reject(new Error('No location sources available'));
        });
    },

    // Watch position changes
    watchPosition: (successCallback, errorCallback, options = {}) => {
        let watchId = Math.random().toString(36).substr(2, 9);
        
        // Subscribe to GPS updates
        const unsubscribe = gpsAPI.onUpdate((position) => {
            successCallback({
                coords: {
                    latitude: position.latitude,
                    longitude: position.longitude,
                    accuracy: position.accuracy || 10,
                    altitude: position.altitude || null,
                    altitudeAccuracy: null,
                    heading: position.course || null,
                    speed: position.speed || null
                },
                timestamp: position.timestamp || Date.now()
            });
        });

        // Store unsubscribe function for clearWatch
        locationAPI._watchers = locationAPI._watchers || {};
        locationAPI._watchers[watchId] = unsubscribe;

        return watchId;
    },

    // Clear position watch
    clearWatch: (watchId) => {
        if (locationAPI._watchers && locationAPI._watchers[watchId]) {
            locationAPI._watchers[watchId]();
            delete locationAPI._watchers[watchId];
        }
    }
};

// SQLite API - Database functionality
const sqliteAPI = {
    // Initialize SQLite database
    initialize: (dbPath) => ipcRenderer.invoke('sqlite-initialize', dbPath),
    
    // Execute SQL query
    run: (sql, params) => ipcRenderer.invoke('sqlite-run', sql, params),
    
    // Get single result
    get: (sql, params) => ipcRenderer.invoke('sqlite-get', sql, params),
    
    // Get all results
    all: (sql, params) => ipcRenderer.invoke('sqlite-all', sql, params),
    
    // Prepare statement
    prepare: (sql) => ipcRenderer.invoke('sqlite-prepare', sql),
    
    // Close database
    close: () => ipcRenderer.invoke('sqlite-close'),
    
    // Check if database exists
    exists: (dbPath) => ipcRenderer.invoke('sqlite-exists', dbPath)
};

// App API - General application functionality
const appAPI = {
    // Get app version
    getVersion: () => ipcRenderer.invoke('app-version'),

    // Get user data paths
    getUserDataPaths: () => ipcRenderer.invoke('get-user-data-paths'),

    // Show message box
    showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),

    // Open external URL
    openExternal: (url) => ipcRenderer.invoke('open-external', url),

    // Generic invoke method for any IPC call
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args)
};

// Update API - Update functionality
const updateAPI = {
    // Listen for update progress
    onUpdateProgress: (callback) => {
        const wrappedCallback = (event, data) => callback(data);
        ipcRenderer.on('update-progress', wrappedCallback);
        
        // Return unsubscribe function
        return () => {
            ipcRenderer.removeListener('update-progress', wrappedCallback);
        };
    },
    
    // Listen for update events
    onUpdateAvailable: (callback) => {
        const wrappedCallback = (event, data) => callback(data);
        ipcRenderer.on('update-available', wrappedCallback);
        
        return () => {
            ipcRenderer.removeListener('update-available', wrappedCallback);
        };
    },
    
    // Listen for download progress
    onDownloadProgress: (callback) => {
        const wrappedCallback = (event, data) => callback(data);
        ipcRenderer.on('download-progress', wrappedCallback);
        
        return () => {
            ipcRenderer.removeListener('download-progress', wrappedCallback);
        };
    }
};

// Expose APIs to renderer
console.log('🔧 Preload: Exposing APIs to renderer...');

// Check if contextIsolation is enabled
const isContextIsolationEnabled = typeof window !== 'undefined';

if (!isContextIsolationEnabled) {
    // When contextIsolation is false, use contextBridge
    try {
        contextBridge.exposeInMainWorld('electronAPI', {
            gps: gpsAPI,
            location: locationAPI,
            app: appAPI,
            update: updateAPI,
            sqlite: sqliteAPI,

            // Configuration methods for secure-config.js
            getConfig: () => ipcRenderer.invoke('get-config'),
            getAppPaths: () => ipcRenderer.invoke('get-app-paths'),

            // Backward compatibility - expose update functions directly
            onUpdateProgress: updateAPI.onUpdateProgress,
            onUpdateAvailable: updateAPI.onUpdateAvailable,
            onDownloadProgress: updateAPI.onDownloadProgress,

            // Generic invoke for any IPC calls
            invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args)
        });
        console.log('✅ Preload: APIs exposed via contextBridge');
    } catch (error) {
        console.error('❌ Preload: contextBridge failed:', error.message);
        // Fallback to direct window assignment
        setupDirectWindowAPI();
    }
} else {
    // When contextIsolation is false, attach directly to window
    setupDirectWindowAPI();
}

function setupDirectWindowAPI() {
    // Wait for window to be available
    const attachAPI = () => {
        if (typeof window !== 'undefined') {
            window.electronAPI = {
                gps: gpsAPI,
                location: locationAPI,
                app: appAPI,
                update: updateAPI,
                sqlite: sqliteAPI,

                // Configuration methods for secure-config.js
                getConfig: () => ipcRenderer.invoke('get-config'),
                getAppPaths: () => ipcRenderer.invoke('get-app-paths'),

                // Backward compatibility - expose update functions directly
                onUpdateProgress: updateAPI.onUpdateProgress,
                onUpdateAvailable: updateAPI.onUpdateAvailable,
                onDownloadProgress: updateAPI.onDownloadProgress,

                // Generic invoke for any IPC calls
                invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args)
            };
            console.log('✅ Preload: APIs exposed via direct window attachment');
        } else {
            // Retry after a short delay
            setTimeout(attachAPI, 10);
        }
    };
    
    attachAPI();
}

console.log('✅ Preload: API exposure configured');

// Optional: Override navigator.geolocation to use our enhanced location services
// This provides a seamless upgrade for existing web-based geolocation code
if (typeof window !== 'undefined') {
    // Wait for window to be available
    window.addEventListener('DOMContentLoaded', () => {
        // Store original geolocation API as fallback
        const originalGeolocation = navigator.geolocation;
        
        // Override with our enhanced version
        Object.defineProperty(navigator, 'geolocation', {
            value: {
                getCurrentPosition: (successCallback, errorCallback, options) => {
                    locationAPI.getCurrentPosition(options)
                        .then(position => successCallback(position))
                        .catch(error => {
                            if (errorCallback) {
                                errorCallback({
                                    code: 2, // POSITION_UNAVAILABLE
                                    message: error.message
                                });
                            }
                        });
                },
                
                watchPosition: (successCallback, errorCallback, options) => {
                    return locationAPI.watchPosition(successCallback, errorCallback, options);
                },
                
                clearWatch: (watchId) => {
                    locationAPI.clearWatch(watchId);
                }
            },
            writable: false,
            configurable: false
        });

        console.log('🛰️ Enhanced geolocation API loaded with GPS support');
    });
}

// Debug logging
console.log('🔧 Preload script loaded successfully');
console.log('🛰️ GPS API exposed as window.electronAPI.gps');
console.log('📍 Location API exposed as window.electronAPI.location');
console.log('🔄 Navigator.geolocation enhanced with GPS support');
