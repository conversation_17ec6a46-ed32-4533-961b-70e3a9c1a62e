# Justice Module

The Justice module provides comprehensive justice episode management for the S.T.E.V.I. Retro application, including episode creation, event tracking, condition management, and offline synchronization.

## Architecture

### Online-First Design
- **Primary Storage**: Supabase PostgreSQL with `justice` schema
- **Local Cache**: SQLite tables mirroring Supabase schemas exactly
- **Sync Strategy**: Real-time sync with last-write-wins conflict resolution
- **Offline Support**: Full CRUD operations with background sync on reconnection

### Module Structure
```
renderer/js/modules/justice/
├── index.js                    # Module entry point and public API
├── justice-api.js              # Supabase operations (schema-qualified)
├── justice-cache.js            # SQLite cache with delta sync
├── justice-state.js            # In-memory state management
├── justice-commands.js         # Lightweight command delegation
└── views/                      # View components
    ├── status-ribbon.js        # Read-only status header
    ├── start-episode-wizard.js # 60-second episode creation
    ├── timeline-view.js        # Events timeline with add actions
    ├── charges-grid.js         # Charge management with dispositions
    ├── conditions-panel.js     # Active conditions management
    ├── contacts-panel.js       # Justice contacts (lawyer, probation, etc.)
    └── add-event/              # Event creation modals
        ├── bail-hearing.js
        ├── transfer-facility.js
        ├── release-conditions.js
        ├── court-appearance.js
        ├── sentence.js
        └── warrant.js
```

## Public API

### Module Mounting
```javascript
import { mountJusticeTab } from './modules/justice/index.js';

// Mount justice tab in person detail view
await mountJusticeTab(person, container, app);
```

### Core Operations

#### Episode Management
```javascript
// Create new episode
const episode = await justiceAPI.createEpisode({
    person_id: 'uuid',
    origin: 'ARREST',
    origin_dt: '2024-01-15T14:30:00Z',
    origin_agency: 'Cobourg Police',
    jurisdiction: 'Cobourg',
    charges: [
        { code: 'CC 266', label: 'Assault', severity: 'HYBRID' }
    ]
});

// Get episode with status
const status = await justiceAPI.getEpisode(episodeId);

// List person's episodes
const episodes = await justiceAPI.listEpisodesByPerson(personId);
```

#### Event Management
```javascript
// Add event to episode
const event = await justiceAPI.addEvent(episodeId, eventType, eventDt, payload);

// Event type examples:
// - BAIL_HEARING: { outcome: 'GRANTED', notes: '...' }
// - TRANSFER_TO_FACILITY: { facility_id: 'uuid', reason: '...' }
// - RELEASE_ORDER: { pack_id: 'uuid', replace: true, conditions: [...] }
// - COURT_APPEARANCE: { court_name: '...', appearance_type: '...', outcome: '...' }
// - SENTENCE: { mode: 'CUSTODIAL', length_days: 90, credit_days: 10 }
// - WARRANT_ISSUED: { type: 'BENCH', notes: '...' }
```

#### Condition Management
```javascript
// List condition packs
const packs = await justiceAPI.listConditionPacks();

// Get pack items
const items = await justiceAPI.listPackItems(packId);

// Get active conditions
const conditions = await justiceAPI.getActiveConditions(episodeId);
```

## Command System

### Justice Commands
The module registers the following commands with the app's command system:

#### Episode Commands
- `je:new {personId}` - Start new episode wizard
- `je:view {episodeId}` - View episode details

#### Event Commands
- `je:bail {episodeId} granted|denied [notes]` - Add bail hearing
- `je:xfer {episodeId} {facilityNameOrUUID}` - Transfer to facility
- `je:court {episodeId} {YYYY-MM-DD hh:mm} {CourtName} {AppearanceType}` - Court appearance
- `je:release {episodeId} pack:"Standard Release" replace:true` - Release with conditions
- `je:sentence {episodeId} CUSTODIAL lengthDays:90` - Add sentence
- `je:warrant {episodeId} issue BENCH` - Issue/execute warrant

#### Modal Commands
- `je:add-bail-hearing {episodeId}` - Show bail hearing modal
- `je:add-transfer {episodeId}` - Show transfer modal
- `je:add-release-conditions {episodeId}` - Show release conditions modal
- `je:add-court-appearance {episodeId}` - Show court appearance modal
- `je:add-sentence {episodeId}` - Show sentence modal
- `je:add-warrant {episodeId} [action]` - Show warrant modal

## Database Schema

### Core Tables
- `justice_episode` - Main episode records
- `je_event` - Append-only event log
- `je_charge` - Charges with status tracking
- `je_condition` - Active/historical conditions
- `je_contact` - Justice-related contacts
- `je_custody_episode` - Custody periods
- `je_warrant` - Warrant records

### Reference Tables
- `facility` - Correctional facilities
- `condition_pack` - Predefined condition sets
- `condition_pack_item` - Individual conditions in packs

### Views
- `v_je_status` - Current episode status with computed fields
- `v_je_current_custody` - Current custody information
- `v_je_next_court` - Next court appearance
- `v_je_active_conditions` - Active conditions summary

## Offline Synchronization

### Cache Strategy
1. **Initial Sync**: Load all reference data and recent episodes
2. **Real-time Updates**: Subscribe to Supabase changes
3. **Offline Operations**: Store in SQLite with client-generated UUIDs
4. **Reconnection Sync**: Upload pending changes, download updates
5. **Conflict Resolution**: Server wins, local changes merged where possible

### Sync Tables
All justice tables are cached locally with identical schemas:
- `justice_episode` → SQLite `justice_episode`
- `je_event` → SQLite `je_event`
- `je_charge` → SQLite `je_charge`
- `je_condition` → SQLite `je_condition`
- `je_contact` → SQLite `je_contact`
- `facility` → SQLite `facility`
- `condition_pack` → SQLite `condition_pack`
- `condition_pack_item` → SQLite `condition_pack_item`

## Event Payload Contracts

### BAIL_HEARING
```javascript
{
    outcome: 'GRANTED' | 'DENIED' | 'RESERVED',
    notes?: string,
    next_date?: string // ISO datetime
}
```

### TRANSFER_TO_FACILITY
```javascript
{
    facility_id: string, // UUID
    reason?: string
}
```

### RELEASE_ORDER / CONDITIONS_SET / PROBATION_ORDER
```javascript
{
    pack_id?: string,     // UUID of condition pack
    replace?: boolean,    // Replace existing conditions
    start_dt?: string,    // ISO datetime
    end_dt?: string,      // ISO datetime
    overrides?: object,   // Per-item overrides
    conditions?: array    // Expanded conditions
}
```

### COURT_APPEARANCE
```javascript
{
    court_name: string,
    address?: string,
    appearance_type: string,
    outcome?: string,
    next_date?: string // ISO datetime
}
```

### SENTENCE
```javascript
{
    mode: 'CUSTODIAL' | 'COMMUNITY',
    length_days?: number,
    credit_days?: number,
    conditions?: string,
    notes?: string
}
```

### WARRANT_ISSUED / WARRANT_EXECUTED
```javascript
{
    type: string,        // BENCH, ARREST, etc.
    notes?: string,
    // For WARRANT_ISSUED:
    issuing_court?: string,
    reason?: string,
    // For WARRANT_EXECUTED:
    executing_officer?: string,
    location?: string
}
```

## Security Model

### Row Level Security (RLS)
- **Read Access**: All authenticated users can read justice data
- **Write Access**: Staff, supervisors, and admins can create/update
- **Event Log**: Append-only (no updates/deletes)
- **Episode Updates**: Admin/supervisor only for status changes

### User Roles
- `iharc_volunteer` - Read-only access
- `iharc_staff` - Read + create episodes/events
- `iharc_supervisor` - Read + create + update episodes
- `iharc_admin` - Full access including deletions

## Integration Points

### App.js Integration
```javascript
// In app.js viewPersonDetail method
import { mountJusticeTab } from './modules/justice/index.js';

// Replace legacy CJ tab mounting with:
const justiceTabContent = document.getElementById('justice-tab-content');
await mountJusticeTab(person, justiceTabContent, this);
```

### Modal Management
Uses the existing `modal-management` module for all dialogs:
```javascript
const modal = this.justice.ui.createModal({
    id: 'modal-id',
    title: 'Modal Title',
    size: 'medium'
});
```

### Event Delegation
All buttons use `data-action` attributes for consistent event handling:
```html
<button data-action="je:add-bail-hearing" data-episode-id="uuid">
    Add Bail Hearing
</button>
```

## Testing Scenarios

### Episode Creation
1. Open person record → Justice tab
2. Click "New Episode" → 60-second wizard
3. Select origin type, set datetime, add charges
4. Submit → Episode created, timeline visible

### Event Addition
1. In episode timeline → Click "Add Event" buttons
2. Fill event-specific forms
3. Submit → Event added, timeline refreshed, status updated

### Condition Management
1. Add release order with condition pack
2. Verify conditions appear in Conditions panel
3. End a condition → Verify it's removed from active list

### Offline Functionality
1. Disconnect network
2. Create episode/events → Stored locally
3. Reconnect → Data syncs to server
4. Verify server state matches expectations

## Performance Considerations

- **Lazy Loading**: Episodes loaded on-demand per person
- **Event Pagination**: Large timelines paginated (future enhancement)
- **Cache Expiry**: Reference data cached for 1 hour
- **Batch Operations**: Multiple events can be added in single transaction
- **Index Optimization**: SQLite indexes on person_id, episode_id, event_dt

## Implementation Status

### ✅ Completed Features
- [x] **Database Schema**: Complete `justice` schema with RLS policies
- [x] **Module Structure**: 8 core files + 7 view components + 8 template files
- [x] **API Layer**: Schema-qualified Supabase operations
- [x] **SQLite Cache**: Exact schema mirroring with delta sync
- [x] **State Management**: In-memory state with reactive updates
- [x] **Command System**: 8 core commands with lightweight delegation
- [x] **App Integration**: Modal management and global event delegation
- [x] **Episode Creation**: 60-second wizard workflow
- [x] **Event Timeline**: All 8 event types with add/view functionality
- [x] **Status Ribbon**: Real-time status display with refresh capability
- [x] **Modal Forms**: Complete forms for all event types
- [x] **Offline Support**: Full CRUD with background sync
- [x] **Template System**: Pure template functions in `/templates/justice/`
- [x] **Data-Action Integration**: Global event delegation for UI interactions

### 🧪 Testing
- [x] **Test Suite**: `test-justice-module.js` with comprehensive validation
- [x] **Integration Tests**: Command registration, app methods, template imports
- [x] **Manual Testing**: Episode creation, event addition, modal workflows

### 📋 Command Reference
```bash
# Core Commands
je:new {personId}                    # Start new episode wizard
je:view {episodeId}                  # View episode details
je:bail {episodeId} [outcome] [notes] # Add bail hearing
je:xfer {episodeId} [facilityId]     # Transfer to facility
je:court {episodeId} [datetime] [court] [type] # Court appearance
je:release {episodeId}               # Release with conditions
je:sentence {episodeId} [mode]       # Add sentence
je:warrant {episodeId} [action] [type] # Issue/execute warrant
```

### 🎯 Ready for Production
The Justice module is fully implemented and integrated with the S.T.E.V.I. Retro application following all established patterns:

- ✅ Templates in `/templates/justice/` (no imports in commands)
- ✅ Complex DOM/modals handled in `app.js` methods
- ✅ Lightweight commands that delegate to app methods
- ✅ Global `data-action` event delegation
- ✅ SQLite cache schemas match Supabase exactly
- ✅ Modal management integration
- ✅ Offline-first architecture with sync

**Next Steps**: Run acceptance tests and deploy to production.
