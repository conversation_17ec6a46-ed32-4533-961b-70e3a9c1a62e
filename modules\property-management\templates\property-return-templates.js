// Property Return Templates for S.T.E.V.I Retro - Enhanced Return Tracking

export const propertyReturnTemplates = {
    
    // Enhanced property return modal with business/individual options
    enhancedReturnModal: (property) => {
        const now = new Date();
        const currentDate = now.toISOString().split('T')[0];
        const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);
        
        return `
            <div class="property-return-modal">
                <div class="return-header">
                    <h3>Return Property: ${property.property_number}</h3>
                    <div class="property-summary">
                        <strong>${property.description}</strong>
                        ${property.brand ? ` (${property.brand}${property.model ? ` ${property.model}` : ''})` : ''}
                    </div>
                </div>

                <form id="enhanced-return-form" class="enhanced-return-form">
                    <!-- Return Type Selection -->
                    <div class="form-section">
                        <h4>Return Type</h4>
                        <div class="return-type-options">
                            <label class="radio-option">
                                <input type="radio" name="return_type" value="business" checked>
                                <span class="radio-custom"></span>
                                Business Return
                                <div class="option-description">Return to a business or organization</div>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="return_type" value="individual">
                                <span class="radio-custom"></span>
                                Individual Return
                                <div class="option-description">Return to a private individual</div>
                            </label>
                        </div>
                    </div>

                    <!-- Business Return Fields -->
                    <div id="business-return-fields" class="conditional-fields">
                        <div class="form-section">
                            <h4>Business Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="return_business_name">Business Name *</label>
                                    <input type="text" id="return_business_name" name="return_business_name" 
                                           placeholder="Enter business name" required>
                                </div>
                                <div class="form-group">
                                    <label for="return_contact_person">Contact Person *</label>
                                    <input type="text" id="return_contact_person" name="return_contact_person" 
                                           placeholder="Name of contact person" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="return_recipient_phone">Phone Number</label>
                                    <input type="tel" id="return_recipient_phone" name="return_recipient_phone" 
                                           placeholder="Business phone number">
                                </div>
                                <div class="form-group">
                                    <label for="return_recipient_email">Email</label>
                                    <input type="email" id="return_recipient_email" name="return_recipient_email" 
                                           placeholder="Business email address">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="return_business_address">Business Address</label>
                                <textarea id="return_business_address" name="return_business_address" 
                                         placeholder="Full business address" rows="2"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Individual Return Fields -->
                    <div id="individual-return-fields" class="conditional-fields" style="display: none;">
                        <div class="form-section">
                            <h4>Individual Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="return_recipient_name_individual">Full Name *</label>
                                    <input type="text" id="return_recipient_name_individual" name="return_recipient_name" 
                                           placeholder="Full name of recipient">
                                </div>
                                <div class="form-group">
                                    <label for="return_recipient_phone_individual">Phone Number</label>
                                    <input type="tel" id="return_recipient_phone_individual" name="return_recipient_phone" 
                                           placeholder="Contact phone number">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="return_individual_id_type">ID Type</label>
                                    <select id="return_individual_id_type" name="return_individual_id_type">
                                        <option value="">Select ID type</option>
                                        <option value="drivers_license">Driver's License</option>
                                        <option value="passport">Passport</option>
                                        <option value="provincial_id">Provincial ID</option>
                                        <option value="health_card">Health Card</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="return_individual_id_number">ID Number (Optional)</label>
                                    <input type="text" id="return_individual_id_number" name="return_individual_id_number" 
                                           placeholder="ID number for verification">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="return_relationship">Relationship to Property</label>
                                <select id="return_relationship" name="return_relationship">
                                    <option value="">Select relationship</option>
                                    <option value="owner">Owner</option>
                                    <option value="family_member">Family Member</option>
                                    <option value="authorized_representative">Authorized Representative</option>
                                    <option value="employee">Employee</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Common Return Details -->
                    <div class="form-section">
                        <h4>Return Details</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="return_accepted_by">Accepted By *</label>
                                <input type="text" id="return_accepted_by" name="return_accepted_by" 
                                       placeholder="Name of person who physically accepted the property" required>
                            </div>
                            <div class="form-group">
                                <label for="return_method">Return Method *</label>
                                <select id="return_method" name="return_method" required>
                                    <option value="">Select method</option>
                                    <option value="delivered">Delivered to location</option>
                                    <option value="picked_up">Picked up by recipient</option>
                                    <option value="mailed">Mailed to recipient</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="return_location">Return Location *</label>
                                <input type="text" id="return_location" name="return_location" 
                                       placeholder="Address or location where property was returned" required>
                            </div>
                            <div class="form-group">
                                <label for="return_receipt_number">Receipt/Confirmation Number</label>
                                <input type="text" id="return_receipt_number" name="return_receipt_number" 
                                       placeholder="Receipt or confirmation number">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="returned_date">Return Date *</label>
                                <input type="date" id="returned_date" name="returned_date" value="${currentDate}" required>
                            </div>
                            <div class="form-group">
                                <label for="returned_time">Return Time *</label>
                                <input type="time" id="returned_time" name="returned_time" value="${currentTime}" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="return_verification_method">Verification Method</label>
                            <select id="return_verification_method" name="return_verification_method">
                                <option value="">Select verification method</option>
                                <option value="photo_id">Photo ID shown</option>
                                <option value="business_card">Business card provided</option>
                                <option value="verbal_confirmation">Verbal confirmation</option>
                                <option value="signature">Signature obtained</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="return_notes">Return Notes</label>
                            <textarea id="return_notes" name="return_notes" 
                                     placeholder="Additional notes about the return process, any special circumstances, etc." 
                                     rows="3"></textarea>
                        </div>
                    </div>

                    <!-- Photo Upload Section -->
                    <div class="form-section">
                        <h4>Documentation (Optional)</h4>
                        <div class="form-group">
                            <label for="return_photos">Return Photos</label>
                            <input type="file" id="return_photos" name="return_photos" 
                                   accept="image/*" multiple>
                            <div class="field-description">Upload photos documenting the return (receipt, handover, etc.)</div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="secondary-button" onclick="app.ui.closeDialog()">Cancel</button>
                        <button type="submit" class="primary-button">Complete Return</button>
                    </div>
                </form>
            </div>
        `;
    },

    // Enhanced return details view for property details modal
    enhancedReturnDetailsSection: (property) => {
        if (property.status !== 'returned') return '';
        
        const isBusinessReturn = property.return_type === 'business';
        const isIndividualReturn = property.return_type === 'individual';
        
        return `
            <div class="info-section enhanced-return-details">
                <h4>Return Information</h4>
                <div class="return-type-indicator">
                    <span class="return-type-badge ${property.return_type || 'basic'}">${property.return_type ? (property.return_type === 'business' ? 'Business Return' : 'Individual Return') : 'Basic Return'}</span>
                </div>
                
                ${isBusinessReturn ? `
                    <div class="return-details-grid">
                        <div class="info-row"><strong>Business Name:</strong> ${property.return_business_name || 'N/A'}</div>
                        <div class="info-row"><strong>Contact Person:</strong> ${property.return_contact_person || 'N/A'}</div>
                        <div class="info-row"><strong>Phone:</strong> ${property.return_recipient_phone || 'N/A'}</div>
                        <div class="info-row"><strong>Email:</strong> ${property.return_recipient_email || 'N/A'}</div>
                        ${property.return_business_address ? `<div class="info-row"><strong>Address:</strong> ${property.return_business_address}</div>` : ''}
                    </div>
                ` : ''}
                
                ${isIndividualReturn ? `
                    <div class="return-details-grid">
                        <div class="info-row"><strong>Recipient:</strong> ${property.return_recipient_name || property.returned_to || 'N/A'}</div>
                        <div class="info-row"><strong>Phone:</strong> ${property.return_recipient_phone || 'N/A'}</div>
                        <div class="info-row"><strong>ID Type:</strong> ${property.return_individual_id_type ? property.return_individual_id_type.replace('_', ' ').toUpperCase() : 'N/A'}</div>
                        <div class="info-row"><strong>Relationship:</strong> ${property.return_relationship ? property.return_relationship.replace('_', ' ').toUpperCase() : 'N/A'}</div>
                    </div>
                ` : ''}
                
                ${!isBusinessReturn && !isIndividualReturn ? `
                    <div class="return-details-grid">
                        <div class="info-row"><strong>Returned To:</strong> ${property.returned_to || 'N/A'}</div>
                    </div>
                ` : ''}
                
                <div class="return-details-grid">
                    <div class="info-row"><strong>Return Location:</strong> ${property.return_location || 'N/A'}</div>
                    <div class="info-row"><strong>Return Date:</strong> ${property.returned_date ? app.formatDate(property.returned_date) : 'N/A'}</div>
                    <div class="info-row"><strong>Return Time:</strong> ${property.returned_time || 'N/A'}</div>
                    ${property.return_method ? `<div class="info-row"><strong>Return Method:</strong> ${property.return_method.replace('_', ' ').toUpperCase()}</div>` : ''}
                    ${property.return_accepted_by ? `<div class="info-row"><strong>Accepted By:</strong> ${property.return_accepted_by}</div>` : ''}
                    ${property.return_receipt_number ? `<div class="info-row"><strong>Receipt Number:</strong> ${property.return_receipt_number}</div>` : ''}
                    ${property.return_verification_method ? `<div class="info-row"><strong>Verification:</strong> ${property.return_verification_method.replace('_', ' ').toUpperCase()}</div>` : ''}
                </div>
                
                ${property.return_notes ? `
                    <div class="return-notes">
                        <strong>Return Notes:</strong>
                        <div class="notes-content">${property.return_notes}</div>
                    </div>
                ` : ''}
            </div>
        `;
    },

    // CSS styles for enhanced return functionality
    returnModalStyles: () => `
        <style>
        .property-return-modal {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .return-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ff0000;
            padding-bottom: 15px;
        }
        
        .return-header h3 {
            color: #ff0000;
            margin-bottom: 10px;
        }
        
        .property-summary {
            color: #cccccc;
            font-size: 14px;
        }
        
        .enhanced-return-form {
            background: #000000;
            color: #ff0000;
        }
        
        .form-section {
            margin-bottom: 30px;
            border: 1px solid #ff0000;
            padding: 20px;
            background: #0a0a0a;
        }
        
        .form-section h4 {
            color: #ff0000;
            margin-bottom: 15px;
            border-bottom: 1px solid #ff0000;
            padding-bottom: 5px;
        }
        
        .return-type-options {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .radio-option {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            border: 1px solid #333333;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .radio-option:hover {
            border-color: #ff0000;
            background: #1a0000;
        }
        
        .radio-option input[type="radio"] {
            display: none;
        }
        
        .radio-option input[type="radio"]:checked + .radio-custom {
            background: #ff0000;
            border-color: #ff0000;
        }
        
        .radio-option input[type="radio"]:checked + .radio-custom::after {
            display: block;
        }
        
        .radio-custom {
            width: 20px;
            height: 20px;
            border: 2px solid #666666;
            border-radius: 50%;
            margin-bottom: 10px;
            position: relative;
        }
        
        .radio-custom::after {
            content: '';
            width: 8px;
            height: 8px;
            background: #000000;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }
        
        .option-description {
            font-size: 12px;
            color: #888888;
            text-align: center;
        }
        
        .conditional-fields {
            transition: all 0.3s ease;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .form-group {
            flex: 1;
        }
        
        .form-group label {
            display: block;
            color: #ff0000;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            background: #000000;
            border: 1px solid #333333;
            color: #ff0000;
            font-family: 'Courier New', monospace;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: #ff0000;
            outline: none;
        }
        
        .field-description {
            font-size: 12px;
            color: #888888;
            margin-top: 5px;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ff0000;
        }
        
        .primary-button, .secondary-button {
            padding: 10px 20px;
            border: 1px solid #ff0000;
            background: #000000;
            color: #ff0000;
            cursor: pointer;
            font-family: 'Courier New', monospace;
        }
        
        .primary-button:hover {
            background: #ff0000;
            color: #000000;
        }
        
        .secondary-button:hover {
            background: #333333;
        }
        
        .enhanced-return-details {
            border: 1px solid #ff0000;
            padding: 15px;
            background: #0a0000;
        }
        
        .return-type-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .return-type-badge.business {
            background: #004400;
            color: #00ff00;
            border: 1px solid #00ff00;
        }
        
        .return-type-badge.individual {
            background: #000044;
            color: #0088ff;
            border: 1px solid #0088ff;
        }
        
        .return-type-badge.basic {
            background: #333333;
            color: #cccccc;
            border: 1px solid #666666;
        }
        
        .return-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .return-notes {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #333333;
        }
        
        .notes-content {
            background: #0a0a0a;
            padding: 10px;
            border: 1px solid #333333;
            margin-top: 5px;
            font-style: italic;
        }
        </style>
    `
};