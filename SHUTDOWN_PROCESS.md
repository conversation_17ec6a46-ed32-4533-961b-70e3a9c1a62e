# S.T.E.V.I Retro - Clean Shutdown Process

This document describes the comprehensive shutdown process implemented to ensure all background processes are properly terminated when the application is closed.

## Overview

The shutdown process is designed to gracefully terminate all background services and clean up resources in a specific order to prevent data loss and ensure no zombie processes remain.

## Shutdown Sequence

### 1. Main Process Shutdown Handlers

The main process (`electron/main.js`) implements two shutdown event handlers:

- **`before-quit`**: Primary shutdown handler that prevents immediate quit and coordinates cleanup
- **`will-quit`**: Safety net handler that ensures cleanup happens even if before-quit fails

### 2. Cleanup Order

The cleanup process follows this specific order:

1. **Renderer Process Cleanup**
   - Notify renderer processes to cleanup their resources
   - Wait up to 3 seconds for renderer cleanup completion
   - DataManager stops periodic sync and closes SQLite connections
   - App-level cleanup of intervals, event handlers, and modules

2. **GPS Service Cleanup**
   - Disconnect from serial port (COM3)
   - Clear watchdog timer
   - Remove all event listeners
   - Prevent reconnection attempts

3. **SQLite Database Cleanup**
   - Close database connections
   - Ensure WAL mode files are properly closed

4. **UpdateManager Cleanup**
   - Stop periodic cleanup timer (24-hour interval)
   - Perform final cleanup of temporary update files

5. **Native Location Service Cleanup**
   - Clean up any Windows location API resources

6. **Settings Manager Cleanup**
   - Clean up secure settings resources

## Background Processes Managed

### GPS Service (`electron/gps-service.js`)
- **Serial Port Connection**: COM3 at 4800 baud
- **Watchdog Timer**: 10-second interval checking for data timeout
- **Reconnection Logic**: Disabled during shutdown

### SQLite Database (`better-sqlite3`)
- **WAL Mode**: Write-Ahead Logging for better concurrency
- **Connection Pooling**: Single connection per process
- **Cache Tables**: All cache_* tables properly closed

### UpdateManager (`electron/updater.js`)
- **Periodic Cleanup**: 24-hour interval for temp file cleanup
- **Temp Files**: Located in system temp directory under 'stevi-updates'

### DataManager (`renderer/js/data.js`)
- **Periodic Sync**: 5-minute interval for Supabase synchronization
- **Real-time Subscriptions**: WebSocket connections to Supabase
- **Memory Cache**: In-memory data cache

## IPC Communication

The shutdown process uses IPC (Inter-Process Communication) to coordinate between main and renderer processes:

- **`app-shutdown-cleanup`**: Main → Renderer (start cleanup)
- **`renderer-cleanup-complete`**: Renderer → Main (cleanup finished)

## Error Handling

The shutdown process includes comprehensive error handling:

- Each cleanup step is wrapped in try-catch blocks
- Errors are logged but don't prevent other cleanup steps
- Timeout mechanisms prevent hanging during shutdown
- Force quit as fallback if graceful shutdown fails

## Testing

Use the provided test script to verify shutdown functionality:

```bash
node test-shutdown.js
```

This script:
1. Starts the Electron app
2. Waits for startup completion
3. Triggers graceful shutdown after 5 seconds
4. Monitors cleanup messages
5. Reports test results

## Shutdown Triggers

The clean shutdown process is triggered by:

- **Window Close**: When the main window is closed
- **App Quit**: When app.quit() is called
- **System Shutdown**: When the operating system shuts down
- **Process Signals**: SIGTERM, SIGINT (Ctrl+C)

## Platform Considerations

### Windows
- GPS service uses COM3 serial port
- Windows Location Services integration
- Proper handling of Windows shutdown signals

### Cross-Platform
- macOS behavior: App stays running when windows are closed (standard macOS behavior)
- Linux: Standard quit behavior

## Monitoring

During shutdown, the following log messages indicate successful cleanup:

- `🧹 Starting comprehensive app cleanup...`
- `✅ GPS Service cleanup completed`
- `✅ SQLite database closed`
- `✅ UpdateManager cleanup completed`
- `✅ Renderer cleanup completed`
- `✅ Comprehensive app cleanup completed successfully`

## Troubleshooting

If the app doesn't shut down cleanly:

1. Check console logs for error messages
2. Verify no background processes remain using Task Manager (Windows) or Activity Monitor (macOS)
3. Look for zombie processes with names containing 'electron' or 'stevi'
4. Run the test script to identify specific cleanup failures

## Future Enhancements

Potential improvements to the shutdown process:

- Add metrics collection for shutdown timing
- Implement graceful degradation for network-dependent cleanup
- Add user notification for long-running shutdown operations
- Implement backup cleanup mechanisms for critical resources
