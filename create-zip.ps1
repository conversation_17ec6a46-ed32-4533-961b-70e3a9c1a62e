# Create ZIP of the built application
param(
    [string]$SourcePath = "dist\win-unpacked",
    [string]$ZipPath = "S.T.E.V.I-Retro-Portable.zip"
)

Write-Host "🗜️  Creating ZIP package..." -ForegroundColor Green

try {
    # Remove existing ZIP if it exists
    if (Test-Path $ZipPath) {
        Remove-Item $ZipPath -Force
        Write-Host "Removed existing ZIP file" -ForegroundColor Yellow
    }
    
    # Check if source exists
    if (!(Test-Path $SourcePath)) {
        Write-Host "❌ Source path not found: $SourcePath" -ForegroundColor Red
        exit 1
    }
    
    # Create the ZIP using .NET compression
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    
    Write-Host "Compressing $SourcePath to $ZipPath..." -ForegroundColor Yellow
    [System.IO.Compression.ZipFile]::CreateFromDirectory(
        (Resolve-Path $SourcePath).Path, 
        (Join-Path (Get-Location) $ZipPath),
        [System.IO.Compression.CompressionLevel]::Optimal,
        $false
    )
    
    # Get file size
    $zipSize = (Get-Item $ZipPath).Length
    $zipSizeMB = [math]::Round($zipSize / 1MB, 2)
    
    Write-Host "✅ ZIP created successfully!" -ForegroundColor Green
    Write-Host "📍 Location: $ZipPath" -ForegroundColor Cyan
    Write-Host "📊 Size: $zipSizeMB MB" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ Failed to create ZIP: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}