# Supabase Configuration Update Guide

This guide explains how to update the Supabase project URL and configuration for the S.T.E.V.I DOS application.

## Quick Update Process

To change the Supabase project URL, you only need to update **3 constants** in **2 files**:

### 1. Update Renderer Configuration
**File:** `renderer/js/supabase-config.js`

```javascript
// ============================================================================
// SUPABASE PROJECT CONFIGURATION
// ============================================================================
// 🔧 CHANGE THESE VALUES TO UPDATE THE SUPABASE PROJECT
const PROJECT_URL = 'https://YOUR_NEW_PROJECT.supabase.co';
const PROJECT_REF = 'YOUR_NEW_PROJECT_REF';
const ANON_KEY = 'YOUR_NEW_ANON_KEY';
// ============================================================================
```

### 2. Update Electron Main Process Configuration
**File:** `electron/supabase-config.js`

```javascript
// ============================================================================
// SUPABASE PROJECT CONFIGURATION
// ============================================================================
// 🔧 CHANGE THESE VALUES TO UPDATE THE SUPABASE PROJECT
const PROJECT_URL = 'https://YOUR_NEW_PROJECT.supabase.co';
const PROJECT_REF = 'YOUR_NEW_PROJECT_REF';
const ANON_KEY = 'YOUR_NEW_ANON_KEY';
// ============================================================================
```

### 3. Update MCP Configuration (Optional)
**File:** `mcp.json`

```json
{
  "_comment": "To change Supabase project: Update project-ref below to match PROJECT_REF in renderer/js/supabase-config.js",
  "mcpServers": {
    "supabase": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@modelcontextprotocol/server-supabase@latest",
        "--project-ref=YOUR_NEW_PROJECT_REF"
      ],
```

## What Gets Updated Automatically

Once you update the configuration files above, the following files will automatically use the new configuration:

- ✅ `renderer/js/config.js` - Main configuration manager
- ✅ `renderer/js/secure-config.js` - Secure configuration manager
- ✅ `renderer/js/auth.js` - Authentication manager (via config)
- ✅ `renderer/js/data.js` - Data manager (via auth)
- ✅ `renderer/js/app.js` - Direct fetch calls
- ✅ `electron/settings.js` - Electron main process settings

## Environment Variables (Optional)

You can also override the configuration using environment variables:

```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
```

Environment variables take precedence over the hardcoded values in the configuration files.

## Validation

The configuration includes automatic validation that will:

1. ✅ Check that all required values are present
2. ✅ Validate URL format
3. ✅ Ensure URL contains 'supabase.co'
4. ⚠️ Warn if project reference doesn't match URL

## Testing the Configuration

After updating the configuration:

1. Restart the application
2. Check the console for any configuration validation errors
3. Verify that authentication works with the new project
4. Test that data operations (create, read, update, delete) work correctly

## Troubleshooting

### Common Issues

**Error: "Missing required Supabase configuration"**
- Ensure all three constants (PROJECT_URL, PROJECT_REF, ANON_KEY) are set

**Error: "Invalid Supabase URL format"**
- Check that the URL is properly formatted: `https://project-ref.supabase.co`

**Warning: "Project reference doesn't match URL"**
- Ensure the PROJECT_REF matches the subdomain in PROJECT_URL

**Authentication fails after update**
- Verify the ANON_KEY is correct for the new project
- Check that the new project has the same database schema
- Ensure RLS policies are properly configured

### Rollback Process

If you need to rollback to the previous configuration:

1. Revert the changes in `renderer/js/supabase-config.js`
2. Revert the changes in `electron/supabase-config.js`
3. Optionally revert `mcp.json`
4. Restart the application

## Security Notes

- Never commit sensitive keys to version control
- Use environment variables for production deployments
- Rotate keys regularly
- Ensure proper RLS policies are in place on the new project

## Files Modified by This Consolidation

The following files were modified to use the centralized configuration:

- ✅ `renderer/js/supabase-config.js` (NEW - Central config)
- ✅ `electron/supabase-config.js` (NEW - Electron config)
- ✅ `renderer/js/config.js` (MODIFIED - Uses central config)
- ✅ `renderer/js/secure-config.js` (MODIFIED - Uses central config)
- ✅ `renderer/js/app.js` (MODIFIED - Uses central config for fetch)
- ✅ `electron/settings.js` (MODIFIED - Uses central config)
- ✅ `mcp.json` (MODIFIED - Added comment for manual update)
