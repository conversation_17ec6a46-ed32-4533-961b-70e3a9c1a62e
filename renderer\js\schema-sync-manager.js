// Schema Synchronization Manager for S.T.E.V.I Retro
// Automatically ensures SQLite cache schemas match Supabase database schemas

export class SchemaSyncManager {
    constructor(sqliteManager, supabaseManager) {
        this.sqliteManager = sqliteManager;
        this.supabaseManager = supabaseManager;
        this.tables = [
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts',
            'incidents', 'incident_people', 'incident_person_medical',
            'addresses', 'bikes', 'encampments', 'items', 'organizations',
            'property_records', 'property_actions', 'license_plates',
            'people_activities', 'supply_provisions', 'activity_logs',
            'outreach_transactions', 'incident_links'
        ];
        this.autoSyncEnabled = true;
        this.lastSyncCheck = null;
    }

    /**
     * Main schema synchronization method
     * Compares all table schemas and fixes differences
     */
    async syncAllSchemas() {
        if (!this.sqliteManager.isInitialized) {
            throw new Error('SQLite manager not initialized');
        }

        console.log('🔄 Starting comprehensive schema synchronization...');
        const results = {
            checked: 0,
            matched: 0,
            fixed: 0,
            errors: 0,
            details: []
        };

        for (const tableName of this.tables) {
            try {
                console.log(`🔍 Checking schema for table: ${tableName}`);
                const result = await this.syncTableSchema(tableName);
                
                results.checked++;
                if (result.matched) results.matched++;
                if (result.fixed) results.fixed++;
                
                results.details.push({
                    table: tableName,
                    ...result
                });

            } catch (error) {
                console.error(`❌ Schema sync failed for ${tableName}:`, error);
                results.errors++;
                results.details.push({
                    table: tableName,
                    error: error.message,
                    matched: false,
                    fixed: false
                });
            }
        }

        this.lastSyncCheck = new Date();
        console.log('✅ Schema synchronization completed:', results);
        return results;
    }

    /**
     * Synchronize a single table's schema
     */
    async syncTableSchema(tableName) {
        // Get current schemas
        const supabaseSchema = await this.getSupabaseTableSchema(tableName);
        const sqliteSchema = this.getSQLiteTableSchema(tableName);

        if (!supabaseSchema || supabaseSchema.length === 0) {
            // Only warn if this is an expected table that should exist
            const expectedTables = [
                'people', 'pets', 'medical_issues', 'organizations', 'items', 'bikes', 
                'encampments', 'addresses', 'incidents', 'property_records', 'license_plates', 'activity_logs'
            ];
            if (expectedTables.includes(tableName)) {
                console.warn(`⚠️ No Supabase schema found for ${tableName}`);
            }
            return { matched: false, fixed: false, reason: 'No Supabase schema' };
        }

        if (!sqliteSchema || sqliteSchema.length === 0) {
            console.warn(`⚠️ No SQLite schema found for cache_${tableName}`);
            return { matched: false, fixed: false, reason: 'No SQLite cache table' };
        }

        // Compare schemas
        const comparison = this.compareSchemas(supabaseSchema, sqliteSchema);
        
        if (comparison.isMatch) {
            console.log(`✅ Schema match confirmed for ${tableName}`);
            return { matched: true, fixed: false, comparison };
        }

        // Fix schema differences
        console.log(`🔧 Fixing schema differences for ${tableName}:`, comparison);
        const fixed = await this.fixSchemaDifferences(tableName, comparison);

        return {
            matched: false,
            fixed,
            comparison,
            reason: `${comparison.missingInSQLite.length} missing columns, ${comparison.typeMismatches.length} type mismatches`
        };
    }

    /**
     * Get Supabase table schema using proper REST API
     */
    async getSupabaseTableSchema(tableName) {
        try {
            // Determine schema based on table name - updated mapping
            let schema = 'public';
            
            // Audit schema tables
            if (['activity_logs', 'found_bike_reports', 'recovery_logs'].includes(tableName)) {
                schema = 'audit';
            } 
            // Case management schema tables
            else if (['incidents', 'incident_links', 'incident_people', 'incident_person_medical', 
                     'property_records', 'property_actions', 'license_plates'].includes(tableName)) {
                schema = 'case_mgmt';
            } 
            // Core schema tables (most tables)
            else if ([
                'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
                'service_barriers', 'support_contacts', 'people_activities',
                'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
                'addresses', 'address_activities', 'organizations', 'media', 'vehicle_activities',
                'bikes', 'bike_activities', 'encampments', 'outreach_transactions'
            ].includes(tableName)) {
                schema = 'core';
            }

            console.log(`🔄 Fetching schema for ${schema}.${tableName}`);

            // Use the data manager's new getTableSchema method
            const result = await this.supabaseManager.getTableSchema(schema, tableName);

            // If query returned empty result (authentication failed, table not found, etc.)
            if (!result || result.length === 0) {
                // Only log for expected core tables, suppress for optional tables
                const expectedTables = [
                    'people', 'pets', 'medical_issues', 'organizations', 'items', 'bikes', 
                    'encampments', 'addresses', 'incidents', 'property_records', 'license_plates', 'activity_logs'
                ];
                if (expectedTables.includes(tableName)) {
                    console.warn(`No schema info available for ${schema}.${tableName} - skipping sync`);
                }
                return null;
            }
            
            return result.map(col => ({
                name: col.column_name,
                type: this.mapPostgresToSQLiteType(col.data_type),
                nullable: col.is_nullable === 'YES',
                defaultValue: col.column_default,
                postgresType: col.data_type
            }));

        } catch (error) {
            console.error(`Error getting Supabase schema for ${tableName}:`, error);
            return null;
        }
    }

    /**
     * Get SQLite table schema
     */
    getSQLiteTableSchema(tableName) {
        try {
            return this.sqliteManager.getTableSchema(tableName);
        } catch (error) {
            console.error(`Error getting SQLite schema for ${tableName}:`, error);
            return null;
        }
    }

    /**
     * Compare two table schemas and identify differences
     */
    compareSchemas(supabaseSchema, sqliteSchema) {
        const supabaseColumns = new Map(supabaseSchema.map(col => [col.name, col]));
        const sqliteColumns = new Map(sqliteSchema.map(col => [col.name, col]));

        // Find missing columns in SQLite
        const missingInSQLite = [];
        for (const [name, col] of supabaseColumns) {
            // Skip cache metadata columns that only exist in SQLite
            if (name.startsWith('cached_at') || name.startsWith('last_synced') || 
                name.startsWith('sync_status') || name.startsWith('cache_version')) {
                continue;
            }
            
            if (!sqliteColumns.has(name)) {
                missingInSQLite.push(col);
            }
        }

        // Find extra columns in SQLite (informational only)
        const extraInSQLite = [];
        for (const [name, col] of sqliteColumns) {
            // Skip cache metadata columns
            if (name.startsWith('cached_at') || name.startsWith('last_synced') || 
                name.startsWith('sync_status') || name.startsWith('cache_version')) {
                continue;
            }
            
            if (!supabaseColumns.has(name)) {
                extraInSQLite.push(col);
            }
        }

        // Find type mismatches
        const typeMismatches = [];
        for (const [name, supabaseCol] of supabaseColumns) {
            const sqliteCol = sqliteColumns.get(name);
            if (sqliteCol && !this.typesAreCompatible(supabaseCol.type, sqliteCol.type)) {
                typeMismatches.push({
                    column: name,
                    supabaseType: supabaseCol.type,
                    sqliteType: sqliteCol.type
                });
            }
        }

        return {
            isMatch: missingInSQLite.length === 0 && typeMismatches.length === 0,
            missingInSQLite,
            extraInSQLite,
            typeMismatches,
            supabaseColumnCount: supabaseSchema.length,
            sqliteColumnCount: sqliteSchema.length
        };
    }

    /**
     * Fix schema differences by adding missing columns
     */
    async fixSchemaDifferences(tableName, comparison) {
        let fixedCount = 0;

        try {
            // Add missing columns to SQLite
            for (const missingCol of comparison.missingInSQLite) {
                const columnDef = this.buildColumnDefinition(missingCol);
                const success = this.sqliteManager.addColumnIfNotExists(
                    tableName, 
                    missingCol.name, 
                    columnDef
                );
                
                if (success) {
                    fixedCount++;
                    console.log(`✅ Added missing column ${missingCol.name} to cache_${tableName}`);
                } else {
                    console.warn(`⚠️ Failed to add column ${missingCol.name} to cache_${tableName}`);
                }
            }

            // Log type mismatches (SQLite is flexible with types, so we mainly log these in debug mode)
            if (comparison.typeMismatches.length > 0) {
                // Only show type mismatches in debug mode to reduce noise
                // SQLite is flexible with types so these are usually not critical
                if (console.debug) {
                    console.debug(`Type mismatches detected in ${tableName}:`, comparison.typeMismatches);
                }
            }

            return fixedCount > 0;

        } catch (error) {
            console.error(`❌ Error fixing schema differences for ${tableName}:`, error);
            return false;
        }
    }

    /**
     * Build SQLite column definition from Supabase column info
     */
    buildColumnDefinition(column) {
        let definition = column.type;

        // Add default value if exists
        if (column.defaultValue && column.defaultValue !== null) {
            // Handle boolean defaults
            if (column.type.toUpperCase().includes('BOOLEAN')) {
                if (column.defaultValue.includes('true') || column.defaultValue.includes('1')) {
                    definition += ' DEFAULT 1';
                } else if (column.defaultValue.includes('false') || column.defaultValue.includes('0')) {
                    definition += ' DEFAULT 0';
                }
            } else if (column.defaultValue !== "NULL" && !column.defaultValue.includes('now()')) {
                // For other types, add the default (but skip timestamps and nulls)
                definition += ` DEFAULT ${column.defaultValue}`;
            }
        }

        return definition;
    }

    /**
     * Map PostgreSQL types to SQLite types
     */
    mapPostgresToSQLiteType(postgresType) {
        const typeMapping = {
            // Numeric types
            'bigint': 'INTEGER',
            'integer': 'INTEGER',
            'smallint': 'INTEGER',
            'decimal': 'REAL',
            'numeric': 'REAL',
            'real': 'REAL',
            'double precision': 'REAL',
            'money': 'REAL',

            // Text types
            'character varying': 'TEXT',
            'varchar': 'TEXT',
            'char': 'TEXT',
            'text': 'TEXT',
            'uuid': 'TEXT',

            // Date/time types
            'timestamp with time zone': 'DATETIME',
            'timestamp without time zone': 'DATETIME',
            'timestamptz': 'DATETIME',
            'timestamp': 'DATETIME',
            'date': 'DATE',
            'time': 'TIME',
            'time without time zone': 'TIME',

            // Boolean
            'boolean': 'BOOLEAN',

            // JSON types (stored as text in SQLite)
            'json': 'TEXT',
            'jsonb': 'TEXT',

            // Arrays (stored as JSON text in SQLite)
            'ARRAY': 'TEXT',

            // Other types default to TEXT
            'USER-DEFINED': 'TEXT'
        };

        // Handle array types
        if (postgresType.includes('[]') || postgresType.startsWith('_')) {
            return 'TEXT'; // Arrays stored as JSON strings in SQLite
        }

        return typeMapping[postgresType.toLowerCase()] || 'TEXT';
    }

    /**
     * Check if two types are compatible
     */
    typesAreCompatible(supabaseType, sqliteType) {
        // SQLite is very flexible with types, so we're quite permissive
        const supabaseNormalized = supabaseType.toUpperCase();
        const sqliteNormalized = sqliteType.toUpperCase();

        // Exact match
        if (supabaseNormalized === sqliteNormalized) {
            return true;
        }

        // Common compatible mappings
        const compatibleMappings = [
            ['INTEGER', 'BIGINT'],
            ['INTEGER', 'INT'],
            ['INTEGER', 'SMALLINT'],
            ['TEXT', 'VARCHAR'],
            ['TEXT', 'CHAR'],
            ['TEXT', 'CHARACTER VARYING'],
            ['TEXT', 'UUID'],
            ['REAL', 'DECIMAL'],
            ['REAL', 'NUMERIC'],
            ['REAL', 'DOUBLE PRECISION'],
            ['DATETIME', 'TIMESTAMP'],
            ['DATETIME', 'TIMESTAMPTZ'],
            ['DATETIME', 'TIMESTAMP WITH TIME ZONE'],
            ['DATETIME', 'TIMESTAMP WITHOUT TIME ZONE'],
            ['DATE', 'DATE'],
            ['TIME', 'TIME'],
            ['TIME', 'TIME WITHOUT TIME ZONE'],
            ['BOOLEAN', 'BOOL'],
            ['TEXT', 'JSON'],
            ['TEXT', 'JSONB'],
            ['TEXT', 'ARRAY']
        ];

        return compatibleMappings.some(([type1, type2]) => 
            (supabaseNormalized.includes(type1) && sqliteNormalized.includes(type2)) ||
            (supabaseNormalized.includes(type2) && sqliteNormalized.includes(type1))
        );
    }

    /**
     * Automatically check and sync schemas on app startup
     */
    async autoSync() {
        if (!this.autoSyncEnabled) {
            console.log('ℹ️ Auto schema sync is disabled');
            return;
        }

        try {
            console.log('🚀 Auto schema sync starting...');
            const results = await this.syncAllSchemas();
            
            if (results.errors > 0) {
                console.warn(`⚠️ Schema sync completed with ${results.errors} errors`);
            } else if (results.fixed > 0) {
                console.log(`✅ Schema sync completed - fixed ${results.fixed} tables`);
            } else {
                console.log('✅ All schemas are in sync');
            }

            return results;

        } catch (error) {
            console.error('❌ Auto schema sync failed:', error);
            throw error;
        }
    }

    /**
     * Generate schema validation report
     */
    async generateValidationReport() {
        const report = {
            timestamp: new Date(),
            tables: [],
            summary: {
                total: 0,
                matched: 0,
                needsSync: 0,
                errors: 0
            }
        };

        for (const tableName of this.tables) {
            try {
                const result = await this.syncTableSchema(tableName);
                report.tables.push({
                    name: tableName,
                    ...result
                });

                report.summary.total++;
                if (result.matched) report.summary.matched++;
                if (result.comparison && !result.comparison.isMatch) report.summary.needsSync++;

            } catch (error) {
                report.tables.push({
                    name: tableName,
                    error: error.message
                });
                report.summary.errors++;
            }
        }

        return report;
    }

    /**
     * Enable/disable auto-sync
     */
    setAutoSync(enabled) {
        this.autoSyncEnabled = enabled;
        console.log(`Schema auto-sync ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get sync status
     */
    getSyncStatus() {
        return {
            autoSyncEnabled: this.autoSyncEnabled,
            lastSyncCheck: this.lastSyncCheck,
            tablesMonitored: this.tables.length
        };
    }
}

// Export default instance
export default SchemaSyncManager;