/**
 * Modal Management Module
 * 
 * Centralized modal and dialog management system providing consistent
 * modal creation, lifecycle management, and cleanup.
 * 
 * Extracted from app.js as part of Phase 3 modularization.
 */

import { ModalManager } from './modal-manager.js';
import { DialogManager } from './dialog-manager.js';
import { ModalTemplateLoader } from './modal-template-loader.js';

export class ModalManagement {
    constructor(dataManager = null, uiManager = null) {
        this.modalManager = new ModalManager();
        this.dialogManager = new DialogManager(uiManager);
        this.templateLoader = new ModalTemplateLoader();
        this.dataManager = dataManager;
        this.uiManager = uiManager;
    }

    // Core modal management
    createModal(config) {
        return this.modalManager.createModal(config);
    }

    showModal(modalElement) {
        return this.modalManager.showModal(modalElement);
    }

    closeModal(modalId) {
        return this.modalManager.closeModal(modalId);
    }

    closeAllModals() {
        return this.modalManager.closeAllModals();
    }

    // Dialog shortcuts
    showDialog(title, content, type = 'info') {
        return this.dialogManager.showDialog(title, content, type);
    }

    showConfirmDialog(message, title = 'Confirm') {
        return this.dialogManager.showConfirmDialog(message, title);
    }

    // Template-based modals
    async showTemplateModal(templateName, data = {}, options = {}) {
        const template = await this.templateLoader.loadTemplate(templateName);
        const modalHTML = template(data);
        
        const config = {
            id: options.id || `${templateName}-modal`,
            className: options.className || 'modal-overlay',
            content: modalHTML,
            ...options
        };

        return this.createModal(config);
    }

    // Common modal patterns
    async showUpdateStatusModal(incidentId) {
        try {
            if (!this.dataManager) {
                throw new Error('DataManager not available');
            }

            const incidents = await this.dataManager.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.uiManager?.showDialog?.('Error', 'Incident not found.', 'error');
                return null;
            }

            return await this.showTemplateModal('updateStatusModal', incident, {
                id: 'update-status-modal',
                onShow: (modal) => this.setupStatusFormHandler(modal, incidentId)
            });

        } catch (error) {
            console.error('Error showing update status modal:', error);
            this.uiManager?.showDialog?.('Error', 'Failed to open status update modal.', 'error');
            return null;
        }
    }

    closeUpdateStatusModal() {
        return this.closeModal('update-status-modal');
    }

    // Migration dialog
    async showMigrationDialog() {
        return new Promise((resolve) => {
            const dialogHTML = `
                <div class="migration-dialog">
                    <h3>🔄 Database Update Required</h3>
                    <p>This version of the application includes new features that require database updates.</p>
                    <p><strong>What will happen:</strong></p>
                    <ul>
                        <li>New tables and features will be added to your database</li>
                        <li>Existing data will not be affected</li>
                        <li>The process is automatic and safe</li>
                    </ul>
                    <p>Would you like to proceed with the database update?</p>
                    <div class="dialog-actions">
                        <button id="migration-proceed" class="primary-button">Update Database</button>
                        <button id="migration-skip" class="secondary-button">Skip for Now</button>
                    </div>
                </div>
            `;

            const modal = this.createModal({
                id: 'migration-dialog',
                className: 'modal-overlay',
                content: `<div class="modal-content">${dialogHTML}</div>`,
                onShow: (modalElement) => {
                    document.getElementById('migration-proceed').addEventListener('click', () => {
                        this.closeModal('migration-dialog');
                        resolve(true);
                    });

                    document.getElementById('migration-skip').addEventListener('click', () => {
                        this.closeModal('migration-dialog');
                        resolve(false);
                    });
                }
            });

            this.showModal(modal);
        });
    }

    // Helper methods
    setupStatusFormHandler(modal, incidentId) {
        const form = modal.querySelector('#update-status-form');
        if (form && this.onUpdateIncidentStatus) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.onUpdateIncidentStatus(incidentId);
            });
        }
    }

    // Set callback for incident status updates
    setIncidentStatusUpdateHandler(handler) {
        this.onUpdateIncidentStatus = handler;
    }

    // Set managers for enhanced functionality
    setDataManager(dataManager) {
        this.dataManager = dataManager;
    }

    setUIManager(uiManager) {
        this.uiManager = uiManager;
        this.dialogManager.setUIManager(uiManager);
    }
}

// Export individual components for specific use cases
export { ModalManager, DialogManager, ModalTemplateLoader };