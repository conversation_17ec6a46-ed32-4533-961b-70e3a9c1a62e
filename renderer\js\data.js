// Data Manager for S.T.E.V.I DOS Electron App - Online-First with SQLite Caching
import { ConfigManager } from './config.js';
import { SchemaManager } from './schema.js';
import { SQLiteManager } from './sqlite-manager.js';
import { DataValidator } from './data-validator.js';
import { SchemaSyncManager } from './schema-sync-manager.js';

// Use Node.js path module if available (Electron renderer)
const path = window.require ? window.require('path') : null;

export class DataManager {
    constructor(authManager = null) {
        this.config = new ConfigManager();
        this.auth = authManager;
        this.isOnline = navigator.onLine;
        this.isTestMode = this.config.isTestMode();
        this.schema = new SchemaManager(this);
        this.validator = new DataValidator(); // CRITICAL: Add data validation for production
        this.sqlite = null;
        this.schemaSync = null; // Schema synchronization manager
        this.syncInProgress = false;
        this.syncInterval = null;
        this.realtimeSubscriptions = new Map();
        this.lastRefreshTimes = new Map(); // Track last refresh time per table

        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            // Online - Starting sync and real-time subscriptions
            this.syncPendingData();
            this.startPeriodicSync();
            this.setupRealtimeSubscriptions();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            // Offline - Stopping sync and real-time subscriptions
            this.stopPeriodicSync();
            this.cleanupRealtimeSubscriptions();
        });
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        if (!this.auth) {
            return false;
        }

        // Check if auth manager has a current user and session
        return !!(this.auth.currentUser && this.auth.session);
    }

    async initialize() {
        // Initializing DataManager

        // Clear all caches on startup to ensure fresh data
        await this.purgeAllCaches();

        // Initialize fallback schemas first (don't load from database yet)
        this.schema.loadFallbackSchemas();
        // Loaded fallback schemas

        // Always initialize memory cache
        this.cache = new Map();

        // Try to initialize SQLite database with robust error handling
        await this.initializeSQLiteWithFallback();

        // Initialize schema synchronization with proper API calls
        if (this.sqlite && this.sqlite.isInitialized) {
            this.schemaSync = new SchemaSyncManager(this.sqlite, this);
            // Schema synchronization initialized
        } else {
            // Schema synchronization deferred - SQLite not yet initialized
        }

        // Note: Online services will be started after authentication in initializeAfterAuth()
        // DataManager initialized successfully
    }

    /**
     * Initialize data after authentication is established
     * This should be called after successful login
     */
    async initializeAfterAuth() {
        // Initializing data after authentication

        if (!this.isAuthenticated()) {
            console.warn('⚠️ Cannot initialize after auth - user not authenticated');
            return;
        }

        try {
            // Now that we're authenticated, rebuild cache if needed
            if (this.sqlite && this.isOnline) {
                // Check if we need to rebuild cache
                const hasData = await this.sqlite.hasAnyData();
                if (!hasData) {
                    // No cached data found, rebuilding from Supabase
                    await this.rebuildCacheFromSupabase();
                }
            }

            // Initialize schema sync if not already done (fallback)
            if (!this.schemaSync && this.sqlite && this.sqlite.isInitialized) {
                this.schemaSync = new SchemaSyncManager(this.sqlite, this);
                // Schema synchronization initialized in post-auth (fallback)
            }

            // Run schema sync now that we're authenticated (with error handling)
            if (this.schemaSync && this.sqlite && this.sqlite.isInitialized && this.isOnline) {
                try {
                    // Running post-auth schema synchronization
                    await this.schemaSync.autoSync();
                    // Schema synchronization completed successfully
                } catch (schemaError) {
                    console.warn('⚠️ Schema synchronization failed, but continuing with app initialization:', schemaError.message);
                    // Don't throw - let the app continue without schema sync
                }
            } else {
                if (!this.sqlite || !this.sqlite.isInitialized) {
                    console.warn('⚠️ Skipping schema sync - SQLite not initialized');
                } else if (!this.schemaSync) {
                    console.warn('⚠️ Skipping schema sync - SchemaSyncManager not initialized');
                } else if (!this.isOnline) {
                    // Skipping schema sync - offline mode
                }
            }

            // Start online services
            if (this.isOnline) {
                this.startPeriodicSync();
                this.setupRealtimeSubscriptions();
            }

            // Post-authentication data initialization completed
        } catch (error) {
            console.error('❌ Error during post-auth data initialization:', error);

            // For critical errors that prevent the app from functioning, still throw
            // But for non-critical errors (like schema sync), we've already handled them above
            if (error.message.includes('SQLite manager not initialized') ||
                error.message.includes('schema sync') ||
                error.message.includes('schema synchronization')) {
                console.warn('⚠️ Non-critical error handled, continuing with app initialization');
                return; // Don't throw for schema-related errors
            }

            // For other critical errors, still throw
            throw error;
        }
    }

    /**
     * Initialize SQLite with robust error handling and recovery
     */
    async initializeSQLiteWithFallback() {
        // Initializing SQLite with fallback handling

        try {
            // Check prerequisites first
            if (!window.require) {
                throw new Error('Node.js integration not available - Electron nodeIntegration may be disabled');
            }

            const dataPath = this.config.getDataPath();
            // Data path specified

            // Ensure the data directory exists
            const fs = window.require('fs');
            if (!fs.existsSync(dataPath)) {
                try {
                    fs.mkdirSync(dataPath, { recursive: true });
                    // Created data directory
                } catch (error) {
                    throw new Error(`Failed to create data directory at ${dataPath}: ${error.message}. Check file permissions.`);
                }
            }

            // Test write permissions in the data directory
            try {
                const testFile = path ? path.join(dataPath, '.write-test') : `${dataPath}/.write-test`;
                fs.writeFileSync(testFile, 'test');
                fs.unlinkSync(testFile);
                // Write permissions verified for data directory
            } catch (error) {
                throw new Error(`No write permissions for data directory ${dataPath}: ${error.message}`);
            }

            const dbPath = path ? path.join(dataPath, 'cache.db') : `${dataPath}/cache.db`;
            // Database path specified

            // Check if database file exists and is valid
            let needsRebuild = false;
            if (!fs.existsSync(dbPath)) {
                // SQLite database file does not exist, will create new one
                needsRebuild = true;
            } else {
                // Existing SQLite database file found

                // Test if we can read/write to the existing database file
                try {
                    fs.accessSync(dbPath, fs.constants.R_OK | fs.constants.W_OK);
                    // Database file permissions verified
                } catch (error) {
                    throw new Error(`No read/write permissions for database file ${dbPath}: ${error.message}`);
                }
            }

            // Creating SQLiteManager instance
            this.sqlite = new SQLiteManager(dbPath);

            // Initializing SQLite database
            try {
                await this.sqlite.init();
            } catch (initError) {
                console.error('❌ SQLite initialization failed, attempting recovery:', initError.message);

                // Try to delete corrupted database and recreate
                if (fs.existsSync(dbPath)) {
                    // Removing corrupted database file
                    fs.unlinkSync(dbPath);
                }

                // Retry initialization with fresh database
                // Retrying SQLite initialization with fresh database
                this.sqlite = new SQLiteManager(dbPath);
                await this.sqlite.init();
            }

            // Initialize schema synchronization now that SQLite is ready
            if (this.sqlite.isInitialized && !this.schemaSync) {
                this.schemaSync = new SchemaSyncManager(this.sqlite, this);
                // Schema synchronization initialized after SQLite init
            }

            // Handle data population based on database state
            if (needsRebuild) {
                // Fresh database detected
                if (this.isOnline && this.isAuthenticated()) {
                    // Online and authenticated: Rebuilding cache from Supabase
                    await this.rebuildCacheFromSupabase();
                } else {
                    // Offline or not authenticated: Will rebuild cache when authenticated and online
                    // Migrate any existing localStorage data as fallback
                    await this.migrateLocalStorageToSQLite();
                }
            } else {
                // Existing database - just migrate any localStorage data
                await this.migrateLocalStorageToSQLite();
            }

            // SQLite caching enabled successfully
        } catch (error) {
            console.error('❌ SQLite initialization failed, falling back to memory-only cache');
            console.error('Error message:', error.message);
            console.error('Error details:', error);

            // Provide specific guidance based on the error
            if (error.message.includes('better-sqlite3')) {
                console.error('💡 Suggestion: Ensure better-sqlite3 is properly installed for your platform');
                console.error('   Try running: npm rebuild better-sqlite3');
            } else if (error.message.includes('nodeIntegration')) {
                console.error('💡 Suggestion: Check Electron webPreferences - nodeIntegration should be enabled');
            } else if (error.message.includes('permission') || error.message.includes('EACCES')) {
                console.error('💡 Suggestion: Check file permissions for the data directory');
            }

            // Clean up and set to null
            this.sqlite = null;

            console.warn('⚠️ Application will continue with memory-only caching (data will not persist between sessions)');
        }
    }

    /**
     * Manually trigger schema synchronization
     */
    async syncSchemas() {
        if (!this.schemaSync) {
            console.warn('⚠️ Schema sync manager not initialized');
            return null;
        }
        
        try {
            // Manual schema synchronization triggered
            return await this.schemaSync.syncAllSchemas();
        } catch (error) {
            console.error('❌ Manual schema sync failed:', error);
            throw error;
        }
    }

    /**
     * Get schema synchronization status and generate report
     */
    async getSchemaSyncReport() {
        if (!this.schemaSync) {
            return { error: 'Schema sync manager not initialized' };
        }
        
        try {
            return await this.schemaSync.generateValidationReport();
        } catch (error) {
            console.error('❌ Failed to generate schema sync report:', error);
            return { error: error.message };
        }
    }

    /**
     * Rebuild entire cache from Supabase (useful after schema changes or corruption)
     */
    async rebuildCacheFromSupabase() {
        if (!this.sqlite || !this.isOnline) {
            console.warn('Cannot rebuild cache: SQLite not available or offline');
            return;
        }

        try {
            // Rebuilding cache from Supabase

            // Clear existing cache data
            await this.sqlite.clearAllCaches();

            // List of tables to rebuild
            const tablesToRebuild = ['people', 'incidents', 'property_records', 'incident_people', 'incident_links'];

            for (const tableName of tablesToRebuild) {
                try {
                    // Rebuilding table cache
                    const data = await this.getAll(tableName);
                    // Rebuilt table cache with records
                } catch (tableError) {
                    console.warn(`⚠️ Failed to rebuild ${tableName}:`, tableError.message);
                }
            }

            // Cache rebuild completed
        } catch (error) {
            console.error('❌ Cache rebuild failed:', error);
        }
    }

    /**
     * Purge all caches on startup to ensure fresh data from Supabase
     */
    async purgeAllCaches() {
        // Purging all caches for fresh startup

        try {
            // Clear memory cache
            if (this.cache) {
                this.cache.clear();
                // Memory cache cleared
            }

            // Clear SQLite cache database
            if (this.sqlite && this.sqlite.isInitialized) {
                await this.sqlite.clearAllCaches();
                // SQLite cache cleared
            }

            // Clear localStorage (except saved username)
            if (typeof localStorage !== 'undefined') {
                const savedUsername = localStorage.getItem('stevidos_saved_username');
                localStorage.clear();
                if (savedUsername) {
                    localStorage.setItem('stevidos_saved_username', savedUsername);
                }
                // localStorage cleared (preserved username)
            }

            // Clear any stored JWT tokens/sessions (only if user is not currently authenticated)
            if (window.app && window.app.auth && window.app.auth.sessionManager) {
                // Check if user is currently authenticated before clearing session
                if (this.auth && this.auth.isAuthenticated()) {
                    // Skipping session clear - user is currently authenticated
                } else {
                    window.app.auth.sessionManager.clearSession();
                    // JWT session cleared
                }
            }

            // Clear session storage (only if user is not currently authenticated)
            if (typeof sessionStorage !== 'undefined') {
                // Check if user is currently authenticated before clearing session storage
                if (this.auth && this.auth.isAuthenticated()) {
                    // Skipping sessionStorage clear - user is currently authenticated
                } else {
                    sessionStorage.clear();
                    // sessionStorage cleared
                }
            }

            // All caches purged successfully
        } catch (error) {
            console.warn('⚠️ Error purging caches:', error);
        }
    }

    /**
     * Initialize real schemas from database after authentication
     * This should be called after user login
     */
    async initializeRealSchemas() {
        // Loading real schemas from database

        // Wait a moment for authentication to fully propagate
        await new Promise(resolve => setTimeout(resolve, 500));

        try {
            // Check authentication before attempting to load schemas
            const isAuthenticated = await this.checkAuthenticationStatus();
            if (!isAuthenticated) {
                console.warn('⚠️ User not authenticated - using fallback schemas');
                this.schema.loadFallbackSchemas();
                return;
            }

            await this.schema.reinitialize();
            // Real schemas loaded successfully

            // After schemas are loaded, do initial data sync from Supabase
            // Performing initial data sync from Supabase
            await this.performInitialDataSync();
            // Initial data sync completed
        } catch (error) {
            console.warn('⚠️ Failed to load real schemas, using fallback:', error);
            this.schema.loadFallbackSchemas();
        }
    }

    /**
     * Perform initial data sync from Supabase after authentication
     * This ensures the cache is populated with existing data
     */
    async performInitialDataSync() {
        if (!this.isOnline || this.isTestMode) {
            // Skipping initial sync - offline or test mode
            return;
        }

        const supabase = await this.getSupabaseClient();
        if (!supabase) {
            console.warn('No Supabase client available for initial sync');
            return;
        }

        // Sync core tables that are commonly used
        const coreTables = ['people', 'pets', 'incidents', 'addresses', 'organizations', 'items'];

        for (const table of coreTables) {
            try {
                // Syncing table from Supabase
                await this.refreshTableCache(table, supabase);
            } catch (error) {
                console.warn(`Failed to sync ${table}:`, error);
            }
        }
    }

    // Real-time subscription management
    async setupRealtimeSubscriptions() {
        if (!this.isOnline || this.isTestMode) return;

        // Check if real-time is disabled in configuration
        if (this.config?.config?.app?.disableRealtime) {
            // Real-time subscriptions disabled in configuration
            return;
        }

        const supabase = await this.getSupabaseClient();
        if (!supabase) return;

        // Setting up real-time subscriptions

        // Add error handling for WebSocket connection issues
        try {

        // Subscribe to ALL tables used in the app for complete real-time sync
        const tables = [
            // Core schema tables
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'arrest_history', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
            'addresses', 'address_activities', 'organizations', 'media',
            'vehicle_activities', 'bikes', 'bike_activities', 'encampments', 'outreach_transactions',

            // Case management schema tables
            'incidents', 'incident_links', 'property_records', 'property_actions', 'license_plates',

            // Audit schema tables
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

            tables.forEach(table => {
                // Capture table in closure to prevent variable reference issues
                const tableName = table;
                const schemaName = this.getSchemaName(tableName);

                const subscription = supabase
                    .channel(`${tableName}_changes`)
                    .on('postgres_changes',
                        {
                            event: '*',
                            schema: schemaName,
                            table: tableName  // Use just the table name, not the full name with schema
                        },
                        (payload) => {
                            // Real-time event received
                            this.handleRealtimeChange(tableName, payload);
                        }
                    )
                    .subscribe((status) => {
                        // Subscription status updated
                        if (status === 'SUBSCRIBED') {
                            // Successfully subscribed to table
                        } else if (status === 'CHANNEL_ERROR') {
                            console.warn(`⚠️ WebSocket connection error for ${tableName}, continuing without real-time updates`);
                        }
                    });

                this.realtimeSubscriptions.set(tableName, subscription);
            });
        } catch (error) {
            console.warn('⚠️ Failed to setup real-time subscriptions (likely network issue):', error.message);
            // Continuing without real-time updates - data will sync on manual refresh
        }
    }

    cleanupRealtimeSubscriptions() {
        // Cleaning up real-time subscriptions

        this.realtimeSubscriptions.forEach((subscription, table) => {
            subscription.unsubscribe();
            // Unsubscribed from table changes
        });

        this.realtimeSubscriptions.clear();
    }

    handleRealtimeChange(table, payload) {
        // Add debugging to catch undefined table issues
        if (!table) {
            console.error('❌ handleRealtimeChange called with undefined table!', {
                table,
                payload: payload?.eventType,
                recordId: payload?.new?.id || payload?.old?.id,
                stack: new Error().stack
            });
            return;
        }

        // Real-time change in table

        try {
            switch (payload.eventType) {
                case 'INSERT':
                    this.handleRealtimeInsert(table, payload.new);
                    break;
                case 'UPDATE':
                    this.handleRealtimeUpdate(table, payload.new);
                    break;
                case 'DELETE':
                    this.handleRealtimeDelete(table, payload.old);
                    break;
            }
        } catch (error) {
            console.error(`Error handling real-time change for ${table}:`, error);
        }
    }

    handleRealtimeInsert(table, record) {
        if (!table) {
            console.error('❌ handleRealtimeInsert called with undefined table!', { table, record: record?.id });
            return;
        }

        // Real-time INSERT

        // Add to SQLite cache (use upsert to handle duplicates)
        this.sqlite?.upsert(table, {
            ...record,
            last_synced: new Date().toISOString(),
            sync_status: 'synced'
        });

        // Emit event for UI updates
        this.emitDataChange(table, 'insert', record);
    }

    handleRealtimeUpdate(table, record) {
        if (!table) {
            console.error('❌ handleRealtimeUpdate called with undefined table!', { table, record: record?.id });
            return;
        }

        // Real-time UPDATE

        // Update SQLite cache with conflict resolution
        const existing = this.sqlite?.get(table, record.id);

        if (existing) {
            // Last write wins - compare timestamps
            const remoteTime = new Date(record.updated_at || record.created_at);
            const localTime = new Date(existing.updated_at || existing.created_at);

            if (remoteTime >= localTime) {
                this.sqlite?.update(table, record.id, {
                    ...record,
                    last_synced: new Date().toISOString(),
                    sync_status: 'synced'
                });

                this.emitDataChange(table, 'update', record);
            } else {
                // Local version is newer, keeping local changes
            }
        } else {
            // Record doesn't exist locally, add it
            this.handleRealtimeInsert(table, record);
        }
    }

    handleRealtimeDelete(table, record) {
        if (!table) {
            console.error('❌ handleRealtimeDelete called with undefined table!', { table, record: record?.id });
            return;
        }

        // Real-time DELETE

        // Remove from SQLite cache
        this.sqlite?.delete(table, record.id);

        // Emit event for UI updates
        this.emitDataChange(table, 'delete', record);
    }

    emitDataChange(table, operation, record) {
        // Robust validation of table parameter
        if (!table || typeof table !== 'string' || table.trim() === '') {
            console.error('❌ emitDataChange called with invalid table parameter!', {
                table: table,
                type: typeof table,
                operation,
                record: record?.id || 'no-id',
                stack: new Error().stack
            });
            return; // Don't emit event with invalid table
        }

        // Ensure clean table name
        const cleanTable = table.toString().trim();
        // Emitting dataChange event

        // Emit custom event for UI components to listen to
        const event = new CustomEvent('dataChange', {
            detail: { table: cleanTable, operation, record }
        });
        window.dispatchEvent(event);
    }

    getSchemaName(table) {
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'arrest_history', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments', 'encampment_visits', 'outreach_transactions'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'incident_people', 'incident_person_medical',
            'property_records', 'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(table)) {
            return 'core';
        } else if (auditSchema.includes(table)) {
            return 'audit';
        } else if (caseMgmtSchema.includes(table)) {
            return 'case_mgmt';
        } else {
            return 'public';
        }
    }

    async getSupabaseClient() {
        const client = this.auth?.supabase || null;

        if (!client) {
            console.warn('⚠️ No Supabase client available from auth manager');
            return null;
        }

        // Verify the client has required methods
        if (!client.rpc) {
            console.error('❌ Supabase client missing rpc method. Available methods:', Object.keys(client));
            return null;
        }
        
        if (!client.schema) {
            console.error('❌ Supabase client missing schema method. Available methods:', Object.keys(client));
            return null;
        }

        if (client && this.auth && this.auth.session) {
            // Ensure the client has the current session before returning it
            try {
                const { data: { session } } = await client.auth.getSession();
                if (!session || session.access_token !== this.auth.session.access_token) {
                    // Syncing session with Supabase client
                    await client.auth.setSession({
                        access_token: this.auth.session.access_token,
                        refresh_token: this.auth.session.refresh_token
                    });
                    // Session synced successfully
                }
            } catch (error) {
                console.warn('🔐 Failed to sync session:', error);
            }
        }

        // Client ready with verified RPC functionality
        return client;
    }

    /**
     * Check if the current user is authenticated and has a valid session
     * @returns {Promise<boolean>} True if authenticated with valid session
     */
    async checkAuthenticationStatus() {
        // Simple check - just verify that auth manager has a valid session
        if (this.auth && this.auth.isAuthenticated() && this.auth.session) {
            // Authentication valid
            return true;
        }

        console.warn('🔐 User not authenticated');
        return false;
    }

    /**
     * Get table schema information from Supabase using information_schema
     * @param {string} schemaName - Schema name (e.g., 'core', 'case_mgmt', 'audit')
     * @param {string} tableName - Table name
     * @returns {Promise<Array>} Schema information
     */
    async getTableSchema(schemaName, tableName) {
        const supabase = await this.getSupabaseClient();
        if (!supabase) {
            console.warn('No Supabase client available for schema query');
            return [];
        }

        try {
            // Check if user is authenticated
            const isAuthenticated = await this.checkAuthenticationStatus();
            if (!isAuthenticated) {
                console.warn('User not authenticated - skipping schema query');
                return [];
            }

            // Calling get_table_schema RPC

            // Use RPC function to get table schema (safer than direct information_schema access)
            const { data, error } = await supabase.rpc('get_table_schema', {
                schema_name: schemaName,
                table_name: tableName
            });

            if (error) {
                console.error(`Error getting schema for ${schemaName}.${tableName}:`, error);
                return [];
            }

            if (!data || data.length === 0) {
                // Only warn for expected core tables
                const expectedTables = [
                    'people', 'pets', 'medical_issues', 'organizations', 'items', 'bikes', 
                    'encampments', 'addresses', 'incidents', 'property_records', 'license_plates', 'activity_logs'
                ];
                if (expectedTables.includes(tableName)) {
                    console.warn(`No schema found for ${schemaName}.${tableName}`);
                }
                return [];
            }

            // Loaded schema with columns
            return data;
        } catch (error) {
            console.error(`Error getting table schema for ${schemaName}.${tableName}:`, error);
            return [];
        }
    }

    /**
     * Legacy query method - now redirects to getTableSchema for information_schema queries
     * @param {string} sql - SQL query to execute
     * @param {Array} params - Query parameters (optional)
     * @returns {Promise<Array>} Query results
     */
    async query(sql, params = []) {
        // Check if this is an information_schema query for schema sync
        if (sql.includes('information_schema.columns')) {
            // Redirecting information_schema query to proper schema fetching method

            // Extract schema and table name from the SQL
            const schemaParam = params[0];
            const tableParam = params[1];

            if (schemaParam && tableParam) {
                const result = await this.getTableSchema(schemaParam, tableParam);
                // Convert to the format expected by the schema sync system
                return result.map(col => ({
                    column_name: col.column_name,
                    data_type: col.data_type,
                    is_nullable: col.is_nullable,
                    column_default: col.column_default,
                    character_maximum_length: col.character_maximum_length,
                    numeric_precision: col.numeric_precision,
                    numeric_scale: col.numeric_scale
                }));
            }
        }

        console.warn('Raw SQL query not supported - use specific data methods instead');
        return [];
    }

    getFullTableName(tableName) {
        // Map table names to their appropriate schemas
        const coreSchema = [
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items',
            'supply_provisions', 'addresses', 'address_activities',
            'organizations', 'media', 'vehicle_activities', 'bikes', 'bike_activities',
            'encampments', 'encampment_visits', 'outreach_transactions'
        ];

        const auditSchema = [
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        const caseMgmtSchema = [
            'incidents', 'incident_links', 'incident_people', 'incident_person_medical',
            'property_records', 'property_actions', 'license_plates'
        ];

        if (coreSchema.includes(tableName)) {
            return `core.${tableName}`;
        } else if (auditSchema.includes(tableName)) {
            return `audit.${tableName}`;
        } else if (caseMgmtSchema.includes(tableName)) {
            return `case_mgmt.${tableName}`;
        } else {
            // Default to public for bikes and AI tables
            return tableName;
        }
    }

    // Online-first data operations
    async get(table, id) {
        try {
            // Always try SQLite cache first for immediate response
            const cachedData = this.sqlite?.get(table, id);

            // If online, fetch fresh data in background and update cache if changed
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    // Check authentication before making the request
                    const isAuthenticated = await this.checkAuthenticationStatus();
                    if (!isAuthenticated) {
                        console.warn(`⚠️ User not authenticated - returning cached data for ${table}:${id}`);
                        return cachedData;
                    }

                    const fullTableName = this.getFullTableName(table);

                    // Use schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        query = supabase.from(fullTableName);
                    }

                    const { data, error } = await query
                        .select('*')
                        .eq('id', id)
                        .single();

                    if (!error && data) {
                        // Check if data has changed using updated_at timestamp
                        const hasChanged = !cachedData ||
                            new Date(data.updated_at || data.created_at) >
                            new Date(cachedData.updated_at || cachedData.created_at);

                        if (hasChanged) {
                            // Update SQLite cache with fresh data
                            if (cachedData) {
                                this.sqlite?.update(table, id, {
                                    ...data,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });
                            } else {
                                this.sqlite?.upsert(table, {
                                    ...data,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });
                            }
                            return data;
                        }
                    }
                }
            }

            // Return cached data if available
            if (cachedData) {
                // Remove cache metadata before returning
                const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = cachedData;
                return cleanData;
            }

            return null;
        } catch (error) {
            console.error('Error fetching data:', error);
            // Fallback to cached data
            const cachedData = this.sqlite?.get(table, id);
            if (cachedData) {
                const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = cachedData;
                return cleanData;
            }
            return null;
        }
    }

    async insert(table, data) {
        try {
            // CRITICAL: Validate data before insertion for production safety
            const validationResult = await this.validator.validateAndSanitize(table, data, 'insert');
            const sanitizedData = validationResult.data;

            // Check referential integrity
            const integrityCheck = await this.validator.checkReferentialIntegrity(table, sanitizedData, this);
            if (!integrityCheck.isValid) {
                throw new Error(`Referential integrity violation: ${integrityCheck.errors.join(', ')}`);
            }

            // Tables that don't have updated_at column
            const tablesWithoutUpdatedAt = [
                'activity_logs', 'address_activities', 'admin_actions', 'bike_activities',
                'incident_links', 'n8n_chat_histories', 'property_actions', 'ranger_activity_log',
                'ranger_activity_people', 'recovery_logs', 'role_permissions', 'schema_versions',
                'search_logs', 'user_roles', 'vector_store', 'vehicle_activities'
            ];

            const record = {
                ...sanitizedData,
                created_at: new Date().toISOString()
            };

            // Only add updated_at for tables that have this column
            if (!tablesWithoutUpdatedAt.includes(table)) {
                record.updated_at = new Date().toISOString();
            }

            // For auto-increment tables, remove any ID field that might have been included
            const autoIncrementTables = ['license_plates', 'incidents', 'people', 'addresses'];
            if (autoIncrementTables.includes(table) && record.id !== undefined) {
                // Removing ID field for auto-increment table
                delete record.id;
            }

            // ONLINE-FIRST: Always try Supabase first when online
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    // Inserting record to Supabase
                    const fullTableName = this.getFullTableName(table);
                    // Table specified
                    console.log(`📋 Data:`, record);
                    console.log(`📋 Supabase URL:`, supabase.supabaseUrl);
                    console.log(`📋 Supabase Key:`, supabase.supabaseKey?.substring(0, 20) + '...');

                    // Try using schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        // Using schema method
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        // Using direct table access
                        query = supabase.from(fullTableName);
                    }

                    const { data: result, error } = await query
                        .insert(record)
                        .select();

                    if (!error && result && result.length > 0) {
                        const savedRecord = result[0];
                        console.log(`✅ ${table} record saved to Supabase with ID: ${savedRecord.id}`);

                        // Cache in SQLite immediately (use upsert to handle duplicates)
                        if (this.sqlite) {
                            try {
                                this.sqlite.upsert(table, {
                                    ...savedRecord,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });
                            } catch (cacheError) {
                                console.warn(`⚠️ Failed to cache ${table} record:`, cacheError);
                                // Don't throw - the Supabase insert succeeded, caching is secondary
                            }
                        }

                        // Emit event for UI updates
                        this.emitDataChange(table, 'insert', savedRecord);

                        return savedRecord;
                    } else {
                        console.warn(`⚠️ Supabase insert failed for ${table}:`, error);
                        console.warn(`⚠️ Error details:`, {
                            message: error?.message,
                            details: error?.details,
                            hint: error?.hint,
                            code: error?.code
                        });

                        // Provide more helpful error messages for common issues
                        let errorMessage = error?.message || 'Supabase insert failed';
                        if (error?.message?.includes('Could not find') && error?.message?.includes('column')) {
                            errorMessage = `Database schema mismatch: ${error.message}. This may indicate the database schema needs to be updated.`;
                        }

                        throw new Error(errorMessage);
                    }
                }
            }

            // OFFLINE FALLBACK: Store locally and queue for sync
            console.log(`📴 Offline mode - storing ${table} record locally`);

            // Only add ID for tables that use TEXT PRIMARY KEY (like items, bikes)
            // Tables with INTEGER PRIMARY KEY (people, addresses, incidents) should auto-increment
            const tablesWithTextId = ['items', 'bikes'];
            let recordForStorage = { ...record };
            let queueId = null;

            if (tablesWithTextId.includes(table)) {
                const tempId = this.generateId();
                recordForStorage = { ...record, id: tempId };
                queueId = tempId;
            } else {
                // For INTEGER PRIMARY KEY tables, let SQLite auto-generate the ID
                // Use a temporary ID for the sync queue only
                queueId = this.generateId();
            }

            // Store in SQLite with pending sync status (use upsert to handle duplicates)
            const insertedRecord = this.sqlite?.upsert(table, {
                ...recordForStorage,
                sync_status: 'pending'
            });

            // Add to sync queue with the queue ID
            this.sqlite?.addToSyncQueue(table, 'insert', queueId, recordForStorage);

            // Emit event for UI updates
            this.emitDataChange(table, 'insert', insertedRecord || recordForStorage);

            return insertedRecord || recordForStorage;
        } catch (error) {
            console.error(`❌ Error inserting ${table} record:`, error);

            // Final fallback - store locally and queue for sync
            const tablesWithTextId = ['items', 'bikes'];
            
            // Tables that don't have updated_at column (same list as above)
            const tablesWithoutUpdatedAt = [
                'activity_logs', 'address_activities', 'admin_actions', 'bike_activities',
                'incident_links', 'n8n_chat_histories', 'property_actions', 'ranger_activity_log',
                'ranger_activity_people', 'recovery_logs', 'role_permissions', 'schema_versions',
                'search_logs', 'user_roles', 'vector_store', 'vehicle_activities'
            ];
            
            let record = {
                ...data,
                created_at: new Date().toISOString()
            };
            
            // Only add updated_at for tables that have this column
            if (!tablesWithoutUpdatedAt.includes(table)) {
                record.updated_at = new Date().toISOString();
            }
            let queueId = null;

            // For auto-increment tables, remove any ID field that might have been included
            const autoIncrementTables = ['license_plates', 'incidents', 'people', 'addresses'];
            if (autoIncrementTables.includes(table) && record.id !== undefined) {
                // Fallback: Removing ID field for auto-increment table
                delete record.id;
            }

            if (tablesWithTextId.includes(table)) {
                const id = data.id || this.generateId();
                record = { ...record, id: id };
                queueId = id;
            } else {
                // For INTEGER PRIMARY KEY tables, let SQLite auto-generate the ID
                // Use a temporary ID for the sync queue only
                queueId = this.generateId();
            }

            const insertedRecord = this.sqlite?.upsert(table, {
                ...record,
                sync_status: 'pending'
            });

            this.sqlite?.addToSyncQueue(table, 'insert', queueId, record);

            // Emit event for UI updates
            this.emitDataChange(table, 'insert', insertedRecord || record);

            return insertedRecord || record;
        }
    }

    async update(table, id, data) {
        try {
            const existing = await this.get(table, id);
            if (!existing) {
                throw new Error('Record not found');
            }

            const updateData = {
                ...data,
                updated_at: new Date().toISOString()
            };

            // ONLINE-FIRST: Always try Supabase first when online
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    // Check authentication before making the request
                    const isAuthenticated = await this.checkAuthenticationStatus();
                    if (!isAuthenticated) {
                        console.warn(`⚠️ User not authenticated - skipping Supabase update for ${table}:${id}`);
                        throw new Error('User not authenticated. Please log in again.');
                    }

                    console.log(`📤 Updating ${table} record ${id} in Supabase...`);
                    const fullTableName = this.getFullTableName(table);

                    // Use schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        query = supabase.from(fullTableName);
                    }

                    const { data: result, error } = await query
                        .update(updateData)
                        .eq('id', id)
                        .select();

                    if (!error && result && result.length > 0) {
                        const updatedRecord = result[0];
                        console.log(`✅ ${table} record ${id} updated in Supabase`);

                        // Update SQLite cache
                        this.sqlite?.update(table, id, {
                            ...updatedRecord,
                            last_synced: new Date().toISOString(),
                            sync_status: 'synced'
                        });

                        // Emit event for UI updates
                        this.emitDataChange(table, 'update', updatedRecord);

                        return updatedRecord;
                    } else {
                        console.warn(`⚠️ Supabase update failed for ${table}:${id}:`, error);
                        console.warn(`Full table name used: ${fullTableName}`);

                        // Check if it's an authentication error
                        if (error?.code === 'PGRST301' || error?.message?.includes('JWT')) {
                            throw new Error('Authentication error. Please log in again.');
                        }

                        throw new Error(error?.message || `Supabase update failed for ${table}`);
                    }
                }
            }

            // OFFLINE FALLBACK: Store locally and queue for sync
            console.log(`📴 Offline mode - updating ${table} record ${id} locally`);
            const updated = { ...existing, ...updateData };

            // Update in SQLite with pending sync status
            this.sqlite?.update(table, id, {
                ...updated,
                sync_status: 'pending'
            });

            // Add to sync queue
            this.sqlite?.addToSyncQueue(table, 'update', id, updated);

            return updated;

            // Queue for sync if offline
            if (!this.isOnline) {
                this.queueForSync(table, 'update', updated);
            }

            return updated;
        } catch (error) {
            console.error('Error updating data:', error);
            throw error;
        }
    }

    async delete(table, id) {
        try {
            // Try Supabase first if online and available
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    const fullTableName = this.getFullTableName(table);

                    // Use schema method for custom schemas
                    let query;
                    if (fullTableName.includes('.')) {
                        const [schema, tableName] = fullTableName.split('.');
                        query = supabase.schema(schema).from(tableName);
                    } else {
                        query = supabase.from(fullTableName);
                    }

                    const { data, error } = await query
                        .delete()
                        .eq('id', id)
                        .select();

                    if (!error) {
                        console.log(`✅ Successfully deleted ${table} record ${id} from Supabase`);
                    } else {
                        console.warn(`⚠️ Supabase delete failed for ${table}:${id}:`, error.message);
                    }

                    if (!error) {
                        // Remove from SQLite cache
                        if (this.sqlite) {
                            this.sqlite.delete(table, id);
                        }

                        // Remove from memory cache
                        const cacheKey = `${table}_${id}`;
                        this.cache.delete(cacheKey);

                        // Emit event for UI updates
                        this.emitDataChange(table, 'delete', { id });

                        return true;
                    }
                    // If Supabase failed, fall through to local cache
                }
            }

            // Fallback to local cache
            if (this.sqlite) {
                this.sqlite.delete(table, id);
            }

            // Remove from memory cache
            const cacheKey = `${table}_${id}`;
            this.cache.delete(cacheKey);

            // Queue for sync if offline
            if (!this.isOnline) {
                this.queueForSync(table, 'delete', { id });
            }

            return true;
        } catch (error) {
            console.error('Error deleting data:', error);
            throw error;
        }
    }

    async getAll(table) {
        try {
            let cachedData = [];

            // Try SQLite cache first, fallback to memory cache if SQLite failed
            if (this.sqlite) {
                cachedData = this.sqlite.getAll(table) || [];
                // SQLite cache returned records
            } else {
                // Fallback to memory cache when SQLite is not available
                cachedData = Array.from(this.cache.values())
                    .filter(item => item.table === table)
                    .map(item => item.data);
                console.log(`🧠 Memory cache returned ${cachedData.length} records for ${table}`);
            }

            // Clean cache metadata from results
            const cleanCachedData = cachedData.map(record => {
                const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = record;
                return cleanData;
            });

            // If online, refresh cache in background (with throttling to prevent loops)
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    // Throttle background refreshes - only refresh if it's been more than 30 seconds
                    const now = Date.now();
                    const lastRefresh = this.lastRefreshTimes.get(table) || 0;
                    const refreshThreshold = 30000; // 30 seconds

                    if (now - lastRefresh > refreshThreshold) {
                        console.log(`🔄 Background refresh allowed for ${table} (last refresh: ${Math.round((now - lastRefresh) / 1000)}s ago)`);
                        this.lastRefreshTimes.set(table, now);

                        // Background refresh - don't await to keep UI responsive
                        // Ensure table parameter is preserved in async context
                        const tableToRefresh = table; // Capture table in closure
                        this.refreshTableCache(tableToRefresh, supabase).catch(error => {
                            console.warn(`Background refresh failed for ${tableToRefresh}:`, error);
                        });
                    } else {
                        console.log(`🔄 Background refresh throttled for ${table} (last refresh: ${Math.round((now - lastRefresh) / 1000)}s ago)`);
                    }
                }
            }

            return cleanCachedData;
        } catch (error) {
            console.error(`Error fetching all ${table} data:`, error);
            // Return empty array as fallback
            return [];
        }
    }

    async updateCacheInBackground(table, data) {
        try {
            // Updating cache in background
            
            // Update SQLite cache if available
            if (this.sqlite && data && data.length > 0) {
                try {
                    this.sqlite.replaceAll(table, data);
                    // Cache updated
                } catch (cacheError) {
                    console.warn(`Cache update failed for ${table}:`, cacheError);
                }
            }
        } catch (error) {
            console.warn(`Background cache update failed for ${table}:`, error);
        }
    }

    async refreshTableCache(table, supabase) {
        try {
            // Add debugging to catch undefined table issues
            if (!table) {
                console.error('❌ refreshTableCache called with undefined table!', {
                    table,
                    supabase: !!supabase,
                    stack: new Error().stack
                });
                return;
            }

            // Ensure table parameter is a clean string
            const cleanTable = table.toString().trim();
            console.log(`🔄 Starting refreshTableCache for: "${cleanTable}"`);

            // Check authentication before making API calls
            const isAuthenticated = await this.checkAuthenticationStatus();
            if (!isAuthenticated) {
                console.warn(`⚠️ User not authenticated - skipping refresh for ${cleanTable}`);
                return;
            }

            const fullTableName = this.getFullTableName(cleanTable);

            // Handle schema.table format properly
            let query;
            if (fullTableName.includes('.')) {
                const [schema, table] = fullTableName.split('.');
                query = supabase.schema(schema).from(table);
            } else {
                query = supabase.from(fullTableName);
            }

            const { data, error } = await query
                .select('*')
                .order('created_at', { ascending: false });

            if (!error && data) {
                console.log(`🔄 Refreshing ${cleanTable} cache with ${data.length} records`);

                // Update cache with fresh data (SQLite or memory fallback)
                data.forEach(record => {
                    const recordWithMeta = {
                        ...record,
                        last_synced: new Date().toISOString(),
                        sync_status: 'synced'
                    };

                    if (this.sqlite) {
                        // Use SQLite cache if available
                        const existing = this.sqlite.get(cleanTable, record.id);
                        if (existing) {
                            this.sqlite.update(cleanTable, record.id, recordWithMeta);
                        } else {
                            this.sqlite.upsert(cleanTable, recordWithMeta);
                        }
                    } else {
                        // Fallback to memory cache when SQLite is not available
                        const cacheKey = `${cleanTable}_${record.id}`;
                        this.cache.set(cacheKey, {
                            table: cleanTable,
                            data: record // Store clean data without metadata in memory cache
                        });
                        console.log(`🧠 Stored ${cleanTable} record ${record.id} in memory cache`);
                    }
                });

                // Emit event for UI updates if data changed
                console.log(`📡 About to emit refresh event for table: "${cleanTable}" with ${data.length} records`);
                this.emitDataChange(cleanTable, 'refresh', data);
            }
        } catch (error) {
            const cleanTable = table?.toString().trim() || 'unknown_table';
            console.error(`Error refreshing ${cleanTable} cache:`, error);
        }
    }

    async search(table, query) {
        try {
            // search() called for table
            console.log(`🌐 Online status: ${this.isOnline}, Test mode: ${this.isTestMode}`);

            let allRecords;

            // When online, get fresh data directly from Supabase; when offline, use cached data
            if (this.isOnline && !this.isTestMode) {
                console.log(`📡 Getting fresh data directly from Supabase for ${table}...`);
                // Get fresh data directly from Supabase to avoid duplication
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    try {
                        // Check authentication before making API calls
                        const isAuthenticated = await this.checkAuthenticationStatus();
                        if (!isAuthenticated) {
                            console.warn(`⚠️ User not authenticated - using cached data for ${table}`);
                            allRecords = this.sqlite ? this.sqlite.getAll(table) || [] : [];
                        } else {
                            const fullTableName = this.getFullTableName(table);
                            
                            // Use schema method for custom schemas
                            let query;
                            if (fullTableName.includes('.')) {
                                const [schema, tableName] = fullTableName.split('.');
                                query = supabase.schema(schema).from(tableName);
                            } else {
                                query = supabase.from(fullTableName);
                            }
                            
                            const { data, error } = await query.select('*');
                            if (error) {
                                console.error(`Supabase query error for ${table}:`, error);
                                // Fallback to cache if Supabase fails
                                allRecords = this.sqlite ? this.sqlite.getAll(table) || [] : [];
                            } else {
                                allRecords = data || [];
                                // Update cache in background
                                this.updateCacheInBackground(table, allRecords);
                            }
                        }
                    } catch (error) {
                        console.error(`Error querying Supabase for ${table}:`, error);
                        // Fallback to cache if Supabase fails
                        allRecords = this.sqlite ? this.sqlite.getAll(table) || [] : [];
                    }
                } else {
                    // No Supabase client, use cache
                    allRecords = this.sqlite ? this.sqlite.getAll(table) || [] : [];
                }
                console.log(`📊 Got ${allRecords?.length || 0} records for ${table}`);
            } else {
                // Getting cached data
                // Get all records from SQLite cache or memory cache
                if (this.sqlite) {
                    allRecords = this.sqlite.getAll(table) || [];
                    console.log(`📊 SQLite cache returned ${allRecords.length} records for ${table}`);
                } else {
                    // Fallback to memory cache
                    allRecords = Array.from(this.cache.values())
                        .filter(item => item.table === table)
                        .map(item => item.data);
                    console.log(`📊 Memory cache returned ${allRecords.length} records for ${table}`);
                }
            }

            if (!allRecords) {
                console.log(`⚠️ No records found for ${table}, returning empty array`);
                return [];
            }

            console.log(`📋 Total records before filtering: ${allRecords.length}`);

            if (!query || Object.keys(query).length === 0) {
                console.log(`📋 No query filters, returning all ${allRecords.length} records`);
                return allRecords;
            }

            // Search implementation with support for searchFields and searchQuery
            // Applying query filters to records
            
            let filteredRecords;
            
            // Handle new searchFields + searchQuery format (used by person search)
            if (query.searchFields && query.searchQuery) {
                const searchFields = Array.isArray(query.searchFields) ? query.searchFields : [query.searchFields];
                const searchQuery = query.searchQuery.toString().toLowerCase();
                
                // Searching fields for query
                
                filteredRecords = allRecords.filter(record => {
                    // Search across multiple fields with OR logic
                    return searchFields.some(field => {
                        if (!record[field]) return false;
                        const fieldValue = record[field].toString().toLowerCase();
                        return fieldValue.includes(searchQuery);
                    });
                });
            } else {
                // Handle legacy key-value pair format
                filteredRecords = allRecords.filter(record => {
                    return Object.entries(query).every(([key, value]) => {
                        // Handle missing properties (but allow false values)
                        if (record[key] === undefined || record[key] === null) return false;

                        // For boolean values, do exact comparison
                        if (typeof value === 'boolean') {
                            return record[key] === value;
                        }

                        // For other values, use string includes (case-insensitive)
                        const recordValue = record[key].toString().toLowerCase();
                        const searchValue = value.toString().toLowerCase();

                        return recordValue.includes(searchValue);
                    });
                });
            }

            console.log(`📋 Filtered results: ${filteredRecords.length} records`);
            return filteredRecords;
        } catch (error) {
            console.error('❌ Error searching data:', error);
            return [];
        }
    }

    // Legacy localStorage helpers - kept only for migration
    getAllFromLocalStorage(table) {
        const key = `stevidos_${table}`;
        const data = localStorage.getItem(key);

        if (data) {
            return JSON.parse(data);
        }

        return [];
    }

    // Sync helpers - now using SQLite for queue storage
    queueForSync(table, operation, data) {
        if (this.sqlite) {
            // Store sync queue in SQLite
            this.sqlite.insert('sync_queue', {
                id: this.generateId(),
                table,
                operation,
                data: JSON.stringify(data),
                timestamp: new Date().toISOString(),
                status: 'pending'
            });
        } else {
            // Fallback to memory queue
            if (!this.syncQueue) this.syncQueue = [];
            this.syncQueue.push({
                table,
                operation,
                data,
                timestamp: new Date().toISOString()
            });
        }
    }

    async syncPendingData() {
        if (this.syncInProgress) {
            console.log('🔄 Sync already in progress, skipping...');
            return;
        }

        if (!this.sqlite) {
            console.warn('SQLite not initialized, cannot sync');
            return;
        }

        const syncQueue = this.sqlite.getSyncQueue();

        if (syncQueue.length === 0) {
            console.log('📭 No pending sync operations');
            return;
        }

        console.log(`🔄 Syncing ${syncQueue.length} pending operations...`);
        this.syncInProgress = true;

        const supabase = await this.getSupabaseClient();
        if (!supabase) {
            console.warn('No Supabase client available for sync');
            this.syncInProgress = false;
            return;
        }

        let successCount = 0;
        let failCount = 0;

        for (const syncItem of syncQueue) {
            this.sqlite.updateSyncQueueStatus(syncItem.id, 'syncing');

            try {
                const { id: queueId, table_name: table, operation, record_id, data } = syncItem;
                const recordData = JSON.parse(data);
                const fullTableName = this.getFullTableName(table);

                console.log(`🔄 Syncing ${operation} for ${table}:${record_id}`);

                switch (operation) {
                    case 'insert':
                        const { data: insertResult, error: insertError } = await supabase
                            .from(fullTableName)
                            .insert(recordData)
                            .select();

                        if (insertError) throw insertError;

                        if (insertResult && insertResult.length > 0) {
                            const serverRecord = insertResult[0];

                            // Update SQLite cache with server data
                            this.sqlite.update(table, record_id, {
                                ...serverRecord,
                                last_synced: new Date().toISOString(),
                                sync_status: 'synced'
                            });

                            console.log(`✅ ${table} insert synced: ${record_id} → ${serverRecord.id}`);
                        }
                        break;

                    case 'update':
                        // Implement last write wins conflict resolution
                        const localRecord = this.sqlite.get(table, record_id);
                        if (localRecord) {
                            const { data: updateResult, error: updateError } = await supabase
                                .from(fullTableName)
                                .update(recordData)
                                .eq('id', record_id)
                                .select();

                            if (updateError) throw updateError;

                            if (updateResult && updateResult.length > 0) {
                                const serverRecord = updateResult[0];

                                // Update SQLite cache
                                this.sqlite.update(table, record_id, {
                                    ...serverRecord,
                                    last_synced: new Date().toISOString(),
                                    sync_status: 'synced'
                                });

                                console.log(`✅ ${table} update synced: ${record_id}`);
                            }
                        }
                        break;

                    case 'delete':
                        const { error: deleteError } = await supabase
                            .from(fullTableName)
                            .delete()
                            .eq('id', record_id);

                        if (deleteError) throw deleteError;

                        // Remove from SQLite cache
                        this.sqlite.delete(table, record_id);
                        console.log(`✅ ${table} delete synced: ${record_id}`);
                        const cacheKey = `${table}_${data.id}`;
                        this.cache.delete(cacheKey);
                        break;

                    default:
                        console.warn(`Unknown sync operation: ${operation}`);
                }

                // Mark as completed in sync queue
                this.sqlite.updateSyncQueueStatus(queueId, 'completed');
                successCount++;

            } catch (error) {
                console.error(`❌ Failed to sync ${operation} for ${table}:${record_id}:`, error);

                // Mark as failed and increment retry count
                this.sqlite.updateSyncQueueStatus(queueId, 'failed', error.message);
                failCount++;
            }
        }

        // Clean up completed sync items
        this.sqlite.clearSyncQueue();

        console.log(`🔄 Sync completed: ${successCount} successful, ${failCount} failed`);
        this.syncInProgress = false;

        // If there are failed syncs, they remain in queue for retry
        if (failCount > 0) {
            console.warn(`⚠️ ${failCount} sync operations failed and will be retried later`);
        }
    }

    // Manual sync trigger
    async forceSyncPendingData() {
        console.log('Manually triggering sync...');
        await this.syncPendingData();
    }

    // Periodic sync management
    startPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        // Sync every 5 minutes when online
        this.syncInterval = setInterval(() => {
            if (this.isOnline && !this.isTestMode) {
                this.syncPendingData();
            }
        }, 5 * 60 * 1000); // 5 minutes

        console.log('Periodic sync started (every 5 minutes)');
    }

    stopPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            console.log('Periodic sync stopped');
        }
    }

    // Utility methods
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    clearCache() {
        this.cache.clear();
    }

    getStats() {
        const sqliteStats = this.sqlite?.getStats() || {};
        const syncQueue = this.sqlite?.getSyncQueue() || [];

        return {
            isOnline: this.isOnline,
            isTestMode: this.isTestMode,
            sqliteInitialized: !!this.sqlite?.isInitialized,
            realtimeSubscriptions: this.realtimeSubscriptions.size,
            syncInProgress: this.syncInProgress,
            pendingSync: syncQueue.length,
            cacheStats: sqliteStats,
            lastSync: this.lastSyncTime || null
        };
    }

    // Cleanup method for app shutdown
    async cleanup() {
        console.log('🧹 Cleaning up DataManager...');

        // Stop periodic sync
        this.stopPeriodicSync();

        // Cleanup real-time subscriptions
        this.cleanupRealtimeSubscriptions();

        // Close SQLite database
        if (this.sqlite) {
            this.sqlite.close();
        }

        console.log('✅ DataManager cleanup completed');
    }

    // Force refresh all cached data
    async refreshAllCaches() {
        if (!this.isOnline || this.isTestMode) {
            console.log('Cannot refresh caches - offline or test mode');
            return;
        }

        const supabase = await this.getSupabaseClient();
        if (!supabase) return;

        // Refresh cache for ALL tables used in the app
        const tables = [
            // Core schema tables
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
            'addresses', 'address_activities', 'organizations', 'media',
            'vehicle_activities', 'bikes', 'bike_activities', 'encampments',

            // Case management schema tables
            'incidents', 'incident_links', 'property_records', 'property_actions', 'license_plates',

            // Audit schema tables
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];

        console.log('🔄 Refreshing all caches...');

        for (const table of tables) {
            try {
                await this.refreshTableCache(table, supabase);
            } catch (error) {
                console.error(`Failed to refresh ${table} cache:`, error);
            }
        }

        console.log('✅ All caches refreshed');
    }

    /**
     * Force rebuild of SQLite cache - useful for development or after schema changes
     */
    async forceCacheRebuild() {
        console.log('🔄 Force rebuilding SQLite cache...');

        if (!this.sqlite) {
            console.warn('SQLite not initialized, attempting to initialize...');
            await this.initializeSQLiteWithFallback();
        }

        if (this.sqlite) {
            await this.rebuildCacheFromSupabase();
            console.log('✅ Force cache rebuild completed');
        } else {
            console.error('❌ Cannot rebuild cache: SQLite initialization failed');
        }
    }

    // Migrate existing localStorage data to SQLite
    async migrateLocalStorageToSQLite() {
        if (!this.sqlite) return;

        console.log('🔄 Migrating localStorage data to SQLite...');

        // First, let's see what's actually in localStorage
        // Checking localStorage contents
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('stevidos_')) {
                const value = localStorage.getItem(key);
                console.log(`📋 localStorage[${key}]: ${value ? value.substring(0, 100) + '...' : 'null'}`);
            }
        }

        // Migrate localStorage data for ALL tables used in the app
        const tables = [
            // Core schema tables
            'people', 'pets', 'medical_issues', 'disabilities', 'case_management',
            'service_barriers', 'support_contacts', 'people_activities',
            'ranger_activity_log', 'ranger_activity_people', 'items', 'supply_provisions',
            'addresses', 'address_activities', 'organizations', 'media',
            'vehicle_activities', 'bikes', 'bike_activities', 'encampments',

            // Case management schema tables
            'incidents', 'incident_links', 'property_records', 'property_actions', 'license_plates',

            // Audit schema tables
            'activity_logs', 'found_bike_reports', 'recovery_logs'
        ];
        let totalMigrated = 0;

        for (const table of tables) {
            try {
                // Check if SQLite table already has data
                const existingData = this.sqlite.getAll(table);
                if (existingData && existingData.length > 0) {
                    console.log(`⏭️ ${table} already has ${existingData.length} records in SQLite, skipping migration`);
                    continue;
                }

                // Try multiple localStorage key patterns
                const possibleKeys = [
                    `stevidos_${table}`,
                    `stevidos_data_${table}`,
                    `stevidos_cache_${table}`,
                    table
                ];

                let localStorageData = null;
                let usedKey = null;

                for (const key of possibleKeys) {
                    const data = localStorage.getItem(key);
                    if (data) {
                        try {
                            const parsed = JSON.parse(data);
                            if (Array.isArray(parsed) && parsed.length > 0) {
                                localStorageData = parsed;
                                usedKey = key;
                                break;
                            } else if (parsed && typeof parsed === 'object') {
                                // Single object, convert to array
                                localStorageData = [parsed];
                                usedKey = key;
                                break;
                            }
                        } catch (e) {
                            console.warn(`⚠️ Failed to parse localStorage[${key}]:`, e);
                        }
                    }
                }

                if (!localStorageData || localStorageData.length === 0) {
                    console.log(`📭 No ${table} data found in localStorage (checked keys: ${possibleKeys.join(', ')})`);
                    continue;
                }

                // Migrating records from localStorage

                // Migrate each record
                for (const record of localStorageData) {
                    try {
                        this.sqlite.upsert(table, {
                            ...record,
                            last_synced: new Date().toISOString(),
                            sync_status: 'synced'
                        });
                        totalMigrated++;
                    } catch (error) {
                        console.warn(`⚠️ Failed to migrate ${table} record ${record.id}:`, error);
                        console.warn('Record data:', record);
                    }
                }

                console.log(`✅ Migrated ${localStorageData.length} ${table} records`);

                // Clear the migrated data from localStorage
                localStorage.removeItem(usedKey);
                console.log(`🧹 Cleared localStorage[${usedKey}]`);

            } catch (error) {
                console.error(`❌ Error migrating ${table} data:`, error);
            }
        }

        // Clean up data localStorage entries (preserve app settings)
        const preservedKeys = [
            'stevidos_saved_username',  // Remember email functionality
            'stevidos_session',         // Authentication session
            'stevidos_config',          // User configuration
            'stevidos_data_path',       // Data path configuration
            'stevidos_cache'            // General cache (managed by auth)
        ];

        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('stevidos_') && !preservedKeys.includes(key)) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            console.log(`🧹 Cleaned up localStorage[${key}]`);
        });

        if (preservedKeys.some(key => localStorage.getItem(key))) {
            console.log(`🔒 Preserved app settings: ${preservedKeys.filter(key => localStorage.getItem(key)).join(', ')}`);
        }

        if (totalMigrated > 0) {
            console.log(`🎉 Migration completed: ${totalMigrated} total records migrated to SQLite`);
            console.log('🧹 localStorage cleanup completed');
        } else {
            console.log('📭 No data to migrate from localStorage');
        }
    }

    // Alias management methods for people records
    async getPersonAliases(personId) {
        try {
            console.log(`📋 Getting aliases for person ${personId}`);
            
            // Try Supabase first if online
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    const { data, error } = await supabase
                        .schema('core')
                        .from('people_aliases')
                        .select('*')
                        .eq('person_id', personId)
                        .order('created_at', { ascending: true });

                    if (!error && data) {
                        console.log(`✅ Found ${data.length} aliases for person ${personId}`);
                        
                        // Update cache
                        if (this.sqlite) {
                            data.forEach(alias => {
                                this.sqlite.upsert('people_aliases', {
                                    ...alias,
                                    sync_status: 'synced',
                                    last_synced: new Date().toISOString()
                                });
                            });
                        }
                        
                        return data;
                    }
                }
            }

            // Fallback to cache
            if (this.sqlite) {
                const aliases = this.sqlite.db.prepare(`
                    SELECT * FROM cache_people_aliases 
                    WHERE person_id = ? 
                    ORDER BY created_at ASC
                `).all(personId);
                
                return aliases.map(alias => {
                    const { cached_at, last_synced, sync_status, cache_version, ...cleanData } = alias;
                    return cleanData;
                });
            }

            return [];
        } catch (error) {
            console.error(`Error getting aliases for person ${personId}:`, error);
            return [];
        }
    }

    async addPersonAlias(personId, aliasName, createdBy) {
        try {
            console.log(`➕ Adding alias "${aliasName}" to person ${personId}`);
            
            const aliasData = {
                person_id: personId,
                alias_name: aliasName.trim(),
                created_at: new Date().toISOString(),
                created_by: createdBy,
                updated_at: new Date().toISOString(),
                updated_by: createdBy
            };

            // Try Supabase first if online
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    const { data, error } = await supabase
                        .schema('core')
                        .from('people_aliases')
                        .insert(aliasData)
                        .select()
                        .single();

                    if (!error && data) {
                        console.log(`✅ Added alias "${aliasName}" to person ${personId}`);
                        
                        // Update cache
                        if (this.sqlite) {
                            this.sqlite.upsert('people_aliases', {
                                ...data,
                                sync_status: 'synced',
                                last_synced: new Date().toISOString()
                            });
                        }
                        
                        return data;
                    } else {
                        console.warn(`⚠️ Supabase alias insert failed:`, error);
                        throw new Error(error?.message || 'Failed to add alias');
                    }
                }
            }

            // Offline fallback
            console.log(`📴 Offline mode - adding alias locally`);
            const localAlias = {
                ...aliasData,
                id: Date.now() // Temporary ID for offline
            };
            
            if (this.sqlite) {
                this.sqlite.upsert('people_aliases', {
                    ...localAlias,
                    sync_status: 'pending'
                });
                
                this.sqlite.addToSyncQueue('people_aliases', 'insert', localAlias.id, localAlias);
            }
            
            return localAlias;
        } catch (error) {
            console.error(`Error adding alias to person ${personId}:`, error);
            throw error;
        }
    }

    async updatePersonAlias(aliasId, aliasName, updatedBy) {
        try {
            console.log(`✏️ Updating alias ${aliasId} to "${aliasName}"`);
            
            const updateData = {
                alias_name: aliasName.trim(),
                updated_at: new Date().toISOString(),
                updated_by: updatedBy
            };

            // Try Supabase first if online
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    const { data, error } = await supabase
                        .schema('core')
                        .from('people_aliases')
                        .update(updateData)
                        .eq('id', aliasId)
                        .select()
                        .single();

                    if (!error && data) {
                        console.log(`✅ Updated alias ${aliasId}`);
                        
                        // Update cache
                        if (this.sqlite) {
                            this.sqlite.update('people_aliases', aliasId, {
                                ...data,
                                sync_status: 'synced',
                                last_synced: new Date().toISOString()
                            });
                        }
                        
                        return data;
                    } else {
                        console.warn(`⚠️ Supabase alias update failed:`, error);
                        throw new Error(error?.message || 'Failed to update alias');
                    }
                }
            }

            // Offline fallback
            console.log(`📴 Offline mode - updating alias locally`);
            if (this.sqlite) {
                const updated = this.sqlite.update('people_aliases', aliasId, {
                    ...updateData,
                    sync_status: 'pending'
                });
                
                this.sqlite.addToSyncQueue('people_aliases', 'update', aliasId, updateData);
                return updated;
            }
            
            throw new Error('Cannot update alias - offline and no local cache available');
        } catch (error) {
            console.error(`Error updating alias ${aliasId}:`, error);
            throw error;
        }
    }

    async deletePersonAlias(aliasId) {
        try {
            console.log(`🗑️ Deleting alias ${aliasId}`);
            
            // Try Supabase first if online
            if (this.isOnline && !this.isTestMode) {
                const supabase = await this.getSupabaseClient();
                if (supabase) {
                    const { error } = await supabase
                        .schema('core')
                        .from('people_aliases')
                        .delete()
                        .eq('id', aliasId);

                    if (!error) {
                        console.log(`✅ Deleted alias ${aliasId}`);
                        
                        // Remove from cache
                        if (this.sqlite) {
                            this.sqlite.delete('people_aliases', aliasId);
                        }
                        
                        return true;
                    } else {
                        console.warn(`⚠️ Supabase alias delete failed:`, error);
                        throw new Error(error?.message || 'Failed to delete alias');
                    }
                }
            }

            // Offline fallback
            console.log(`📴 Offline mode - marking alias for deletion`);
            if (this.sqlite) {
                this.sqlite.delete('people_aliases', aliasId);
                this.sqlite.addToSyncQueue('people_aliases', 'delete', aliasId, { id: aliasId });
                return true;
            }
            
            throw new Error('Cannot delete alias - offline and no local cache available');
        } catch (error) {
            console.error(`Error deleting alias ${aliasId}:`, error);
            throw error;
        }
    }
}
