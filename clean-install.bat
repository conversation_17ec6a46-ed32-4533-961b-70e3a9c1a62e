@echo off
title S.T.E.V.I Retro - Clean Installation
echo.
echo ===============================================
echo  S.T.E.V.I Retro - Clean Development Setup
echo ===============================================
echo.

REM Kill any running Electron processes that might lock files
echo Stopping any running Electron processes...
taskkill /f /im electron.exe >nul 2>&1
timeout /t 2 >nul

REM Kill any Node.js processes that might be holding locks
echo Stopping any Node.js processes...
taskkill /f /im node.exe >nul 2>&1
timeout /t 2 >nul

REM Remove package-lock.json to prevent version conflicts
if exist "package-lock.json" (
    echo Removing package-lock.json to prevent version conflicts...
    del /f "package-lock.json"
)

REM Remove node_modules with limited retry logic
echo Cleaning node_modules directory...
set /a retry_count=0
:RETRY_REMOVE
if exist "node_modules" (
    set /a retry_count+=1
    if %retry_count% GTR 5 (
        echo WARNING: Could not remove node_modules after 5 attempts.
        echo This may be due to file locks. Continuing with installation...
        goto SKIP_REMOVE
    )
    echo Attempt %retry_count%: Removing node_modules...
    rmdir /s /q "node_modules" >nul 2>&1
    if exist "node_modules" (
        echo Retrying in 3 seconds...
        timeout /t 3 >nul
        goto RETRY_REMOVE
    )
)
:SKIP_REMOVE

REM Clear npm cache
echo Clearing npm cache...
npm cache clean --force

REM Install dependencies with specific flags to prevent locking issues
echo Installing dependencies...
if exist "node_modules" (
    echo Using existing node_modules, installing missing packages only...
    npm install semver --no-package-lock --no-save
    npm install @supabase/supabase-js --no-package-lock --no-save
    npm install better-sqlite3 --no-package-lock --no-save
    npm install fs-extra --no-package-lock --no-save
    npm install node-fetch --no-package-lock --no-save
    npm install uuid --no-package-lock --no-save
) else (
    npm install --no-package-lock --no-save --ignore-scripts
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Install Electron separately to ensure it's available
echo Installing/Verifying Electron...
npx electron --version >nul 2>&1
if errorlevel 1 (
    npm install electron@^37.2.3 --no-package-lock --no-save
    if errorlevel 1 (
        echo ERROR: Failed to install Electron
        pause
        exit /b 1
    )
)

REM Verify installation
echo Verifying installation...
npx electron --version
if errorlevel 1 (
    echo ERROR: Electron installation verification failed
    pause
    exit /b 1
)

echo.
echo ✅ Clean installation completed successfully!
echo You can now run the application with: npm start
echo.
pause
