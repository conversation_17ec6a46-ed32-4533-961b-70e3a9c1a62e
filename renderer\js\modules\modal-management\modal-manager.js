/**
 * Modal Manager
 * 
 * Core modal creation, display, and lifecycle management.
 * Handles DOM manipulation and event handling for modals.
 */

export class ModalManager {
    constructor() {
        this.activeModals = new Map();
        this.setupGlobalHandlers();
    }

    /**
     * Create a modal element
     * @param {Object} config - Modal configuration
     * @param {string} config.id - Unique modal ID
     * @param {string} config.className - CSS class for modal container
     * @param {string} config.content - HTML content for modal
     * @param {Function} config.onShow - Callback when modal is shown
     * @param {Function} config.onClose - Callback when modal is closed
     * @param {boolean} config.closeOnEscape - Close on Escape key (default: true)
     * @param {boolean} config.closeOnClickOutside - Close on outside click (default: true)
     * @returns {HTMLElement} Modal element
     */
    createModal(config) {
        const {
            id,
            className = 'modal-overlay',
            content = '',
            onShow = null,
            onClose = null,
            closeOnEscape = true,
            closeOnClickOutside = true
        } = config;

        // Remove existing modal with same ID
        this.closeModal(id);

        // Create modal element
        const modal = document.createElement('div');
        modal.id = id;
        modal.className = className;
        modal.innerHTML = content;

        // Store modal info
        this.activeModals.set(id, {
            element: modal,
            onShow,
            onClose,
            closeOnEscape,
            closeOnClickOutside
        });

        // Setup close button handlers
        this.setupCloseHandlers(modal, id);

        return modal;
    }

    /**
     * Show a modal
     * @param {HTMLElement|string} modalOrId - Modal element or ID
     */
    showModal(modalOrId) {
        const modal = typeof modalOrId === 'string' 
            ? document.getElementById(modalOrId) 
            : modalOrId;

        if (!modal) return;

        // Add to DOM if not already there
        if (!modal.parentNode) {
            document.body.appendChild(modal);
        }

        // Show modal
        modal.style.display = 'flex';

        // Execute onShow callback
        const modalInfo = this.activeModals.get(modal.id);
        if (modalInfo?.onShow) {
            modalInfo.onShow(modal);
        }

        // Focus management
        this.manageFocus(modal);
    }

    /**
     * Close a modal by ID
     * @param {string} modalId - Modal ID to close
     */
    closeModal(modalId) {
        const modalInfo = this.activeModals.get(modalId);
        if (!modalInfo) return;

        const modal = modalInfo.element;

        // Execute onClose callback
        if (modalInfo.onClose) {
            modalInfo.onClose(modal);
        }

        // Remove from DOM
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }

        // Clean up
        this.activeModals.delete(modalId);
    }

    /**
     * Close all active modals
     */
    closeAllModals() {
        const modalIds = Array.from(this.activeModals.keys());
        modalIds.forEach(id => this.closeModal(id));
    }

    /**
     * Setup close button handlers for a modal
     * @param {HTMLElement} modal - Modal element
     * @param {string} modalId - Modal ID
     */
    setupCloseHandlers(modal, modalId) {
        // Close button handlers
        const closeButtons = modal.querySelectorAll('[data-close-modal], .modal-close, .close-button');
        closeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.closeModal(modalId);
            });
        });
    }

    /**
     * Setup global event handlers
     */
    setupGlobalHandlers() {
        // Escape key handler
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Find topmost modal that allows escape closing
                for (const [id, info] of this.activeModals) {
                    if (info.closeOnEscape) {
                        this.closeModal(id);
                        break; // Only close the topmost modal
                    }
                }
            }
        });

        // Click outside handler
        document.addEventListener('click', (e) => {
            // Check if click is on a modal overlay
            const modalOverlay = e.target.closest('.modal-overlay');
            if (modalOverlay && e.target === modalOverlay) {
                const modalId = modalOverlay.id;
                const modalInfo = this.activeModals.get(modalId);
                
                if (modalInfo?.closeOnClickOutside) {
                    this.closeModal(modalId);
                }
            }
        });
    }

    /**
     * Manage focus for accessibility
     * @param {HTMLElement} modal - Modal element
     */
    manageFocus(modal) {
        // Find first focusable element
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    }

    /**
     * Get active modal count
     * @returns {number} Number of active modals
     */
    getActiveModalCount() {
        return this.activeModals.size;
    }

    /**
     * Check if a modal is active
     * @param {string} modalId - Modal ID
     * @returns {boolean} True if modal is active
     */
    isModalActive(modalId) {
        return this.activeModals.has(modalId);
    }
}