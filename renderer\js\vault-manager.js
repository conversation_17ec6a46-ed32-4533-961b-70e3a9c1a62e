export class VaultManager {
    constructor(supabaseClient) {
        this.supabase = supabaseClient;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache for secrets
    }

    /**
     * Get a secret from Supabase Vault by name
     * @param {string} secretName - The name of the secret to retrieve
     * @returns {Promise<string|null>} The decrypted secret value or null if not found
     */
    async getSecret(secretName) {
        try {
            // Check if Supabase client is available
            if (!this.supabase) {
                console.warn(`VaultManager: Supabase client not available for secret: ${secretName}`);
                return null;
            }

            // Check cache first
            const cacheKey = `secret_${secretName}`;
            const cached = this.cache.get(cacheKey);

            if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
                console.log(`VaultManager: Returning cached secret: ${secretName}`);
                return cached.value;
            }

            console.log(`VaultManager: Fetching secret from Vault: ${secretName}`);

            // Check if RPC method is available
            if (!this.supabase.rpc) {
                console.error(`VaultManager: Supabase RPC method not available for secret: ${secretName}`);
                return null;
            }

            const { data, error } = await this.supabase.rpc('get_secret_by_name', { secret_name: secretName });

            if (error) {
                console.error(`VaultManager: RPC error for secret ${secretName}:`, error);

                // Check for authentication errors
                if (error.code === 'PGRST301' || error.message?.includes('JWT') || error.message?.includes('not authenticated')) {
                    console.warn(`VaultManager: Authentication error accessing vault for secret: ${secretName}`);
                    return null;
                }

                throw error;
            }

            const secretValue = data;

            if (secretValue) {
                // Cache the secret
                this.cache.set(cacheKey, {
                    value: secretValue,
                    timestamp: Date.now()
                });

                console.log(`VaultManager: Successfully retrieved secret: ${secretName}`);
                return secretValue;
            }

            console.warn(`VaultManager: Secret found but empty: ${secretName}`);
            return null;

        } catch (error) {
            console.error(`VaultManager: Error retrieving secret ${secretName}:`, error);

            // Log additional error details for debugging
            if (error.details) {
                console.error(`VaultManager: Error details:`, error.details);
            }
            if (error.hint) {
                console.error(`VaultManager: Error hint:`, error.hint);
            }

            return null;
        }
    }

    /**
     * Store a secret in Supabase Vault
     * @param {string} secretName - The name of the secret
     * @param {string} secretValue - The secret value to store
     * @param {string} description - Optional description of the secret
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async storeSecret(secretName, secretValue, description = null) {
        try {
            console.log(`Storing secret in Vault: ${secretName}`);

            // Use the vault.create_secret() function
            const { data, error } = await this.supabase.rpc('create_secret', {
                secret: secretValue,
                name: secretName,
                description: description
            });

            if (error) {
                throw error;
            }

            console.log(`Successfully stored secret: ${secretName}`);
            
            // Clear cache for this secret to force refresh
            this.cache.delete(`secret_${secretName}`);
            
            return true;

        } catch (error) {
            console.error(`Error storing secret ${secretName}:`, error);
            return false;
        }
    }

    /**
     * Update an existing secret in Supabase Vault
     * @param {string} secretName - The name of the secret to update
     * @param {string} newSecretValue - The new secret value
     * @param {string} newDescription - Optional new description
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async updateSecret(secretName, newSecretValue, newDescription = null) {
        try {
            console.log(`Updating secret in Vault: ${secretName}`);

            // First get the secret ID
            const { data: secretData, error: getError } = await this.supabase
                .from('vault.decrypted_secrets')
                .select('id')
                .eq('name', secretName)
                .single();

            if (getError) {
                throw getError;
            }

            if (!secretData) {
                console.warn(`Secret not found for update: ${secretName}`);
                return false;
            }

            // Use the vault.update_secret() function
            const { data, error } = await this.supabase.rpc('update_secret', {
                secret_id: secretData.id,
                secret: newSecretValue,
                name: secretName,
                description: newDescription
            });

            if (error) {
                throw error;
            }

            console.log(`Successfully updated secret: ${secretName}`);
            
            // Clear cache for this secret to force refresh
            this.cache.delete(`secret_${secretName}`);
            
            return true;

        } catch (error) {
            console.error(`Error updating secret ${secretName}:`, error);
            return false;
        }
    }

    /**
     * Delete a secret from Supabase Vault
     * @param {string} secretName - The name of the secret to delete
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async deleteSecret(secretName) {
        try {
            console.log(`Deleting secret from Vault: ${secretName}`);

            // First get the secret ID
            const { data: secretData, error: getError } = await this.supabase
                .from('vault.decrypted_secrets')
                .select('id')
                .eq('name', secretName)
                .single();

            if (getError) {
                throw getError;
            }

            if (!secretData) {
                console.warn(`Secret not found for deletion: ${secretName}`);
                return false;
            }

            // Delete the secret
            const { error } = await this.supabase
                .from('secrets')
                .delete()
                .eq('id', secretData.id);

            if (error) {
                throw error;
            }

            console.log(`Successfully deleted secret: ${secretName}`);
            
            // Clear cache for this secret
            this.cache.delete(`secret_${secretName}`);
            
            return true;

        } catch (error) {
            console.error(`Error deleting secret ${secretName}:`, error);
            return false;
        }
    }

    /**
     * List all available secrets (names only, not values)
     * @returns {Promise<Array>} Array of secret objects with id, name, and description
     */
    async listSecrets() {
        try {
            console.log('Listing all secrets from Vault');

            const { data, error } = await this.supabase
                .from('vault.decrypted_secrets')
                .select('id, name, description, created_at, updated_at');

            if (error) {
                throw error;
            }

            return data || [];

        } catch (error) {
            console.error('Error listing secrets:', error);
            return [];
        }
    }

    /**
     * Clear the secrets cache
     */
    clearCache() {
        console.log('Clearing Vault secrets cache');
        this.cache.clear();
    }

    /**
     * Get Google API key from Vault
     * @returns {Promise<string|null>} The Google API key
     */
    async getGoogleApiKey() {
        const secret = await this.getSecret('google_api_key');
        if (secret) {
            return secret;
        }

        console.warn('Google API key not found in Vault');
        return null;
    }

    /**
     * Get Gemini API key from Vault
     * @returns {Promise<string|null>} The Gemini API key
     */
    async getGeminiApiKey() {
        const secret = await this.getSecret('gemini_api_key');
        if (secret) {
            return secret;
        }

        console.warn('Gemini API key not found in Vault');
        return null;
    }

    /**
     * Test vault connectivity and permissions
     * @returns {Promise<Object>} Test results
     */
    async testVaultConnectivity() {
        const results = {
            supabaseAvailable: false,
            rpcAvailable: false,
            googleApiKey: false,
            geminiApiKey: false,
            errors: []
        };

        try {
            // Test Supabase client
            if (!this.supabase) {
                results.errors.push('Supabase client not available');
                return results;
            }
            results.supabaseAvailable = true;

            // Test RPC method
            if (!this.supabase.rpc) {
                results.errors.push('Supabase RPC method not available');
                return results;
            }
            results.rpcAvailable = true;

            // Test Google API key
            try {
                const googleKey = await this.getSecret('google_api_key');
                results.googleApiKey = !!googleKey;
                if (!googleKey) {
                    results.errors.push('Google API key not found in vault');
                }
            } catch (error) {
                results.errors.push(`Google API key test failed: ${error.message}`);
            }

            // Test Gemini API key
            try {
                const geminiKey = await this.getSecret('gemini_api_key');
                results.geminiApiKey = !!geminiKey;
                if (!geminiKey) {
                    results.errors.push('Gemini API key not found in vault');
                }
            } catch (error) {
                results.errors.push(`Gemini API key test failed: ${error.message}`);
            }

        } catch (error) {
            results.errors.push(`Vault connectivity test failed: ${error.message}`);
        }

        return results;
    }
}