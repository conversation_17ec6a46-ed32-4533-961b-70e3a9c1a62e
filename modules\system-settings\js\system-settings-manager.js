// System Settings Manager - Handles system administration and configuration
// Extracted from app.js and commands.js for better maintainability

export class SystemSettingsManager {
    constructor(dataManager, uiManager, authManager, configManager, templates = null) {
        this.data = dataManager;
        this.ui = uiManager;
        this.auth = authManager;
        this.config = configManager;
        this.templates = templates;
        
        this.init();
    }

    init() {
        // Set up event listeners for system settings
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Event listeners will be handled through app.js event delegation
        // This is a placeholder for any module-specific initialization
    }

    // Main system content template - extracted from app.js loadSystemContent()
    async loadSystemContent() {
        if (this.templates && this.templates.systemSettingsTemplates) {
            return this.templates.systemSettingsTemplates.createSystemContent();
        }
        
        // Fallback to inline template if templates not provided
        return `
            <div class="content-section">
                <h2>SYSTEM ADMINISTRATION</h2>
                <div class="menu-grid">
                    <div class="menu-item" data-action="sync-data">
                        <div class="menu-icon">🔄</div>
                        <div class="menu-title">Sync Data</div>
                        <div class="menu-desc">Synchronize with server</div>
                    </div>
                    <div class="menu-item" data-action="user-profile">
                        <div class="menu-icon">👤</div>
                        <div class="menu-title">User Profile</div>
                        <div class="menu-desc">Manage your account</div>
                    </div>
                    <div class="menu-item" data-action="settings">
                        <div class="menu-icon">⚙️</div>
                        <div class="menu-title">Settings</div>
                        <div class="menu-desc">Configure application</div>
                    </div>
                    <div class="menu-item" data-action="check-updates">
                        <div class="menu-icon">🔍</div>
                        <div class="menu-title">Check for Updates</div>
                        <div class="menu-desc">Check for application updates</div>
                    </div>
                    <div class="menu-item" data-action="release-notes">
                        <div class="menu-icon">📋</div>
                        <div class="menu-title">Release Notes</div>
                        <div class="menu-desc">View current version release notes</div>
                    </div>
                    <div class="menu-item" data-action="about">
                        <div class="menu-icon">ℹ️</div>
                        <div class="menu-title">About</div>
                        <div class="menu-desc">System information</div>
                    </div>
                </div>
            </div>
        `;
    }

    // Settings modal functionality - extracted from commands.js SettingsCommand
    showSettingsMenu() {
        return new Promise((resolve) => {
            const settingsModal = document.createElement('div');
            settingsModal.className = 'modal-overlay';
            
            let modalContent;
            if (this.templates && this.templates.systemSettingsModals) {
                modalContent = this.templates.systemSettingsModals.createSettingsModal();
            } else {
                // Fallback to inline template
                modalContent = `
                    <div class="modal-dialog">
                        <div class="modal-header">
                            <h3>System Settings</h3>
                        </div>
                        <div class="modal-body">
                            <div class="settings-menu">
                                <div class="menu-item" data-action="seed-data">
                                    <div class="menu-title">Add Sample Data</div>
                                    <div class="menu-desc">Add sample records for testing search functionality</div>
                                </div>
                                <div class="menu-item" data-action="clear-data">
                                    <div class="menu-title">Clear All Data</div>
                                    <div class="menu-desc">Remove all local records (use with caution)</div>
                                </div>
                                <div class="menu-item" data-action="sync-status">
                                    <div class="menu-title">Sync Status</div>
                                    <div class="menu-desc">Check data synchronization status</div>
                                </div>
                                <div class="menu-item" data-action="cleanup-duplicates">
                                    <div class="menu-title">Clean Up Duplicates</div>
                                    <div class="menu-desc">Remove duplicate records from local storage</div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                        </div>
                    </div>
                `;
            }
            
            settingsModal.innerHTML = modalContent;
            document.body.appendChild(settingsModal);

            // Handle menu clicks
            settingsModal.addEventListener('click', async (e) => {
                const menuItem = e.target.closest('.menu-item');
                if (menuItem) {
                    const action = menuItem.dataset.action;

                    switch (action) {
                        case 'seed-data':
                            await this.seedSampleData();
                            break;
                        case 'clear-data':
                            await this.clearAllData();
                            break;
                        case 'sync-status':
                            this.showSyncStatus();
                            break;
                        case 'cleanup-duplicates':
                            await this.cleanupDuplicates();
                            break;
                    }
                }
            });

            resolve(null);
        });
    }

    // Sample data seeding - extracted from commands.js
    async seedSampleData() {
        try {
            this.ui.setStatus('Adding sample data...');

            // Sample people
            const samplePeople = [
                {
                    first_name: 'John',
                    last_name: 'Doe',
                    email: '<EMAIL>',
                    phone: '555-0123',
                    housing_status: 'Housed',
                    notes: 'Sample person record'
                },
                {
                    first_name: 'Jane',
                    last_name: 'Smith',
                    email: '<EMAIL>',
                    phone: '555-0456',
                    housing_status: 'Temporarily Housed',
                    emergency_contact: 'Mary Smith',
                    emergency_contact_phone: '555-0789'
                }
            ];

            // Sample addresses
            const sampleAddresses = [
                {
                    street_address: '123 Main Street',
                    city: 'Calgary',
                    province: 'AB',
                    postal_code: 'T2P 1A1',
                    country: 'Canada'
                },
                {
                    street_address: '456 Oak Avenue',
                    city: 'Edmonton',
                    province: 'AB',
                    postal_code: 'T5K 2B2',
                    country: 'Canada'
                }
            ];

            // Sample license plates
            const samplePlates = [
                {
                    plate_number: 'ABC123',
                    province: 'AB',
                    vehicle_make: 'Toyota',
                    vehicle_model: 'Camry',
                    vehicle_year: 2020,
                    vehicle_color: 'Blue',
                    owner_name: 'John Doe'
                },
                {
                    plate_number: 'XYZ789',
                    province: 'BC',
                    vehicle_make: 'Honda',
                    vehicle_model: 'Civic',
                    vehicle_year: 2019,
                    vehicle_color: 'Red',
                    owner_name: 'Jane Smith'
                }
            ];

            // Insert sample data
            for (const person of samplePeople) {
                await this.data.insert('people', person);
            }

            for (const address of sampleAddresses) {
                await this.data.insert('addresses', address);
            }

            for (const plate of samplePlates) {
                await this.data.insert('license_plates', plate);
            }

            this.ui.showDialog('Success', 'Sample data added successfully! You can now test the search functionality.', 'success');
            this.ui.setStatus('Ready');

        } catch (error) {
            this.ui.showDialog('Error', `Failed to add sample data: ${error.message}`, 'error');
            this.ui.setStatus('Error');
        }
    }

    // Clear all data - extracted from commands.js
    async clearAllData() {
        const confirmed = confirm('Are you sure you want to clear all local data? This cannot be undone.');
        if (confirmed) {
            try {
                // Clear SQLite cache if available
                if (this.data.sqlite) {
                    const cacheTables = [
                        'cache_people', 'cache_pets', 'cache_incidents',
                        'cache_addresses', 'cache_bikes', 'cache_encampments',
                        'cache_media', 'cache_items', 'cache_organizations'
                    ];
                    for (const table of cacheTables) {
                        this.data.sqlite.clear(table);
                    }
                }

                // Clear memory cache
                this.data.clearMemoryCache();
                
                this.ui.showDialog('Success', 'All local data cleared successfully.', 'success');
            } catch (error) {
                this.ui.showDialog('Error', `Failed to clear data: ${error.message}`, 'error');
            }
        }
    }

    // Show sync status - extracted from commands.js
    showSyncStatus() {
        const networkStatus = navigator.onLine ? 'Online' : 'Offline';
        const cacheSize = this.data.getCacheSize ? this.data.getCacheSize() : 'Unknown';
        
        // Get local record counts
        const recordCounts = {
            people: this.getLocalRecordCount('cache_people'),
            incidents: this.getLocalRecordCount('cache_incidents'),
            bikes: this.getLocalRecordCount('cache_bikes'),
            addresses: this.getLocalRecordCount('cache_addresses')
        };

        const message = `
Network Status: ${networkStatus}
Cache Size: ${cacheSize}
Pending Sync Operations: 0

Local Records:
- People: ${recordCounts.people}
- Incidents: ${recordCounts.incidents}  
- Bikes: ${recordCounts.bikes}
- Addresses: ${recordCounts.addresses}
        `;

        this.ui.showDialog('Sync Status', message, 'info');
    }

    // Get local record count helper - extracted from commands.js
    getLocalRecordCount(table) {
        try {
            if (this.data.sqlite) {
                const result = this.data.sqlite.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
                return result ? result.count : 0;
            } else if (this.data.cache && this.data.cache[table]) {
                return this.data.cache[table].length;
            }
            return 0;
        } catch (error) {
            return 0;
        }
    }

    // Cleanup duplicates - extracted from commands.js
    async cleanupDuplicates() {
        try {
            this.ui.setStatus('Cleaning up duplicates...');
            
            const tables = ['cache_people', 'cache_incidents', 'cache_addresses', 'cache_bikes'];
            let totalRemoved = 0;

            for (const table of tables) {
                if (this.data.sqlite) {
                    // Use SQL to identify and remove duplicates based on multiple columns
                    const beforeCount = this.getLocalRecordCount(table);
                    
                    // This is a simplified approach - in practice you'd want more sophisticated duplicate detection
                    const result = this.data.sqlite.prepare(`
                        DELETE FROM ${table} 
                        WHERE id NOT IN (
                            SELECT MIN(id) 
                            FROM ${table} 
                            GROUP BY COALESCE(first_name,'') || COALESCE(last_name,'') || COALESCE(phone,'')
                        )
                    `).run();
                    
                    const afterCount = this.getLocalRecordCount(table);
                    const removed = beforeCount - afterCount;
                    totalRemoved += removed;
                }
            }

            this.ui.showDialog('Success', `Cleanup completed. Removed ${totalRemoved} duplicate records.`, 'success');
            this.ui.setStatus('Ready');

        } catch (error) {
            this.ui.showDialog('Error', `Failed to cleanup duplicates: ${error.message}`, 'error');
            this.ui.setStatus('Error');
        }
    }
}