-- Migration: Add Property Disposition Fields
-- This migration adds fields to track how recovered property was handled during incidents

-- Add new disposition tracking fields to property_records table
ALTER TABLE case_mgmt.property_records 
ADD COLUMN IF NOT EXISTS disposition_type TEXT CHECK (disposition_type IN (
    'turned_over_to_police', 
    'left_where_found', 
    'returned_to_owner', 
    'stored_at_facility',
    'disposed_of',
    'other'
));

ALTER TABLE case_mgmt.property_records 
ADD COLUMN IF NOT EXISTS disposition_date DATE;

ALTER TABLE case_mgmt.property_records 
ADD COLUMN IF NOT EXISTS disposition_notes TEXT;

ALTER TABLE case_mgmt.property_records 
ADD COLUMN IF NOT EXISTS recovery_method TEXT CHECK (recovery_method IN (
    'found_during_incident',
    'confiscated',
    'abandoned',
    'turned_in_by_citizen',
    'other'
));

-- Add comments to document the new fields
COMMENT ON COLUMN case_mgmt.property_records.disposition_type IS 'How the property was ultimately handled: turned_over_to_police, left_where_found, returned_to_owner, stored_at_facility, disposed_of, other';
COMMENT ON COLUMN case_mgmt.property_records.disposition_date IS 'Date when the disposition action was taken';
COMMENT ON COLUMN case_mgmt.property_records.disposition_notes IS 'Additional notes about how the property was handled';
COMMENT ON COLUMN case_mgmt.property_records.recovery_method IS 'How the property was initially recovered: found_during_incident, confiscated, abandoned, turned_in_by_citizen, other';

-- Create index for better query performance on disposition tracking
CREATE INDEX IF NOT EXISTS idx_property_records_disposition_type ON case_mgmt.property_records (disposition_type);
CREATE INDEX IF NOT EXISTS idx_property_records_recovery_method ON case_mgmt.property_records (recovery_method);

-- Update property_actions to include new action types for disposition tracking
-- Note: The property_actions table structure is already flexible enough to handle these new action types
-- We just need to document the new action types that will be used

COMMENT ON COLUMN case_mgmt.property_actions.action_type IS 'Type of action performed. Standard types include: found, status_changed, investigation_updated, returned, handed_to_police, disposition_set, recovery_method_set';
