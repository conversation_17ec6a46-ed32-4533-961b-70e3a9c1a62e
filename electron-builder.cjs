/**
 * @type {import('electron-builder').Configuration}
 * @see https://www.electron.build/configuration/configuration
 */
module.exports = {
  appId: 'ca.iharc.steviretro',
  productName: 'S.T.E.V.I Retro',
  copyright: 'Copyright © 2024 I.H.A.R.C. All rights reserved.',
  electronVersion: '37.2.3',
  directories: {
    output: 'dist',
    buildResources: 'build'
  },
  files: [
    'electron/**/*',
    'src/**/*',
    'renderer/**/*',
    'templates/**/*',
    'assets/**/*',
    'node_modules/better-sqlite3/**/*',
    'node_modules/@supabase/**/*',
    'node_modules/fs-extra/**/*',
    'node_modules/node-fetch/**/*',
    'node_modules/semver/**/*',
    'node_modules/uuid/**/*',
    // Include all required node_modules dependencies
    'node_modules/bindings/**/*',
    'node_modules/base64-js/**/*',
    'node_modules/buffer/**/*',
    'node_modules/decompress-response/**/*',
    'node_modules/deep-extend/**/*',
    'node_modules/detect-libc/**/*',
    'node_modules/end-of-stream/**/*',
    'node_modules/expand-template/**/*',
    'node_modules/fetch-blob/**/*',
    'node_modules/file-uri-to-path/**/*',
    'node_modules/formdata-polyfill/**/*',
    'node_modules/fs-constants/**/*',
    'node_modules/github-from-package/**/*',
    'node_modules/graceful-fs/**/*',
    'node_modules/ieee754/**/*',
    'node_modules/inherits/**/*',
    'node_modules/ini/**/*',
    'node_modules/isows/**/*',
    'node_modules/jsonfile/**/*',
    'node_modules/minimist/**/*',
    'node_modules/mkdirp-classic/**/*',
    'node_modules/napi-build-utils/**/*',
    'node_modules/node-abi/**/*',
    'node_modules/node-domexception/**/*',
    'node_modules/once/**/*',
    'node_modules/prebuild-install/**/*',
    'node_modules/pump/**/*',
    'node_modules/rc/**/*',
    'node_modules/readable-stream/**/*',
    'node_modules/safe-buffer/**/*',
    'node_modules/simple-concat/**/*',
    'node_modules/simple-get/**/*',
    'node_modules/string_decoder/**/*',
    'node_modules/strip-json-comments/**/*',
    'node_modules/tar-fs/**/*',
    'node_modules/tar-stream/**/*',
    'node_modules/tr46/**/*',
    'node_modules/tunnel-agent/**/*',
    'node_modules/universalify/**/*',
    'node_modules/util-deprecate/**/*',
    'node_modules/web-streams-polyfill/**/*',
    'node_modules/webidl-conversions/**/*',
    'node_modules/whatwg-url/**/*',
    'node_modules/wrappy/**/*',
    'node_modules/ws/**/*'
  ],
  extraResources: [
    {
      from: 'templates',
      to: 'templates',
      filter: ['**/*']
    }
  ],
  nodeGypRebuild: false,
  npmRebuild: false,
  buildDependenciesFromSource: false,
  onNodeModuleFile: (file, packager, arch) => {
    // Allow all files to be included
    return true;
  },
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64']
      }
    ],
    verifyUpdateCodeSignature: false,
    requestedExecutionLevel: 'asInvoker',
    legalTrademarks: 'I.H.A.R.C. - Supportive Technology to Enable Vulnerable Individuals'
  },
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    allowElevation: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'S.T.E.V.I Retro',
    runAfterFinish: true,
    menuCategory: 'I.H.A.R.C',
    artifactName: 'S.T.E.V.I-Retro-Setup-${version}.${ext}',
    deleteAppDataOnUninstall: false,
    perMachine: true,
    packElevateHelper: true,
    unicode: true,
    warningsAsErrors: false,
    include: 'build/installer.nsh',
    displayLanguageSelector: false,
    multiLanguageInstaller: false,
    removeDefaultUninstallWelcomePage: false,
    guid: 'ca.iharc.steviretro',
    differentialPackage: false
  },
  publish: null
};