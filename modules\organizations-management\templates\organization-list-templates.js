export const organizationListTemplates = {
    organizationListView: (organizations) => {
        return `
            <style>
                .organization-list-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .organization-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .organization-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .organization-controls {
                    display: flex;
                    gap: 1rem;
                }
                .organization-list {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    margin-bottom: 2rem;
                }
                .organization-list-header {
                    background-color: rgba(255, 0, 0, 0.1);
                    border-bottom: 1px solid #ff0000;
                    padding: 0.75rem;
                    display: grid;
                    grid-template-columns: 2fr 1fr 1.5fr 1fr 120px;
                    gap: 1rem;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.8rem;
                    color: #ff0000;
                    text-transform: uppercase;
                }
                .organization-list-item {
                    padding: 0.75rem;
                    border-bottom: 1px solid rgba(255, 0, 0, 0.2);
                    display: grid;
                    grid-template-columns: 2fr 1fr 1.5fr 1fr 120px;
                    gap: 1rem;
                    align-items: center;
                    transition: all 0.2s ease;
                }
                .organization-list-item:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .organization-list-item:last-child {
                    border-bottom: none;
                }
                .org-name {
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.9rem;
                    color: #ff0000;
                    text-transform: uppercase;
                }
                .org-type {
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    color: #ffaaaa;
                }
                .org-contact {
                    font-family: 'Courier New', monospace;
                    font-size: 0.75rem;
                    color: #cccccc;
                    line-height: 1.2;
                }
                .org-status {
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .org-status.active {
                    color: #00ff00;
                }
                .org-status.inactive {
                    color: #ff6666;
                }
                .org-actions {
                    display: flex;
                    gap: 0.3rem;
                }
                .org-actions .secondary-button,
                .org-actions .action-button {
                    font-size: 0.7rem;
                    padding: 0.25rem 0.5rem;
                    text-transform: uppercase;
                    min-width: 45px;
                }
                .no-organizations {
                    text-align: center;
                    padding: 3rem;
                    color: #ff6666;
                    font-family: 'Courier New', monospace;
                }
                .search-container {
                    margin-bottom: 1.5rem;
                }
                .search-input {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    margin-bottom: 0.5rem;
                }
                .search-input::placeholder {
                    color: #ff6666;
                }
            </style>
            
            <div class="organization-list-container">
                <div class="organization-header">
                    <div class="organization-title">ORGANIZATION DIRECTORY</div>
                    <div class="organization-controls">
                        <button class="secondary-button" data-action="back-to-records">← Back to Records</button>
                        <button class="action-button primary" data-action="add-organization-form">+ Add Organization</button>
                    </div>
                </div>
                
                <div class="search-container">
                    <input type="text" 
                           class="search-input" 
                           placeholder="Search organizations by name, type, or contact..."
                           data-action="search-organizations-input">
                </div>
                
                <div class="organization-list" id="organization-list">
                    ${organizations && organizations.length > 0 
                        ? `<div class="organization-list-header">
                            <div>Organization Name</div>
                            <div>Type</div>
                            <div>Contact Info</div>
                            <div>Status</div>
                            <div>Actions</div>
                           </div>
                           ${organizations.map(org => organizationListTemplates.organizationListItem(org)).join('')}`
                        : '<div class="no-organizations">NO ORGANIZATIONS FOUND<br><br>Click "Add Organization" to create the first entry.</div>'
                    }
                </div>
            </div>
        `;
    },

    organizationListItem: (org) => {
        const contactInfo = [
            org.contact_person ? `Contact: ${org.contact_person}` : '',
            org.phone ? `Phone: ${org.phone}` : '',
            org.email ? `Email: ${org.email}` : ''
        ].filter(Boolean).join('<br>');

        return `
            <div class="organization-list-item" data-organization-id="${org.id}">
                <div class="org-name">${org.name || 'UNNAMED ORGANIZATION'}</div>
                <div class="org-type">${org.organization_type || 'Unknown Type'}</div>
                <div class="org-contact">${contactInfo || 'No contact info'}</div>
                <div class="org-status ${org.is_active ? 'active' : 'inactive'}">${org.is_active ? 'Active' : 'Inactive'}</div>
                <div class="org-actions">
                    <button class="secondary-button" data-action="view-organization-detail" data-organization-id="${org.id}">View</button>
                    <button class="action-button" data-action="edit-organization" data-organization-id="${org.id}">Edit</button>
                </div>
            </div>
        `;
    },

    organizationDetailView: (org) => {
        return `
            <style>
                .organization-detail-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .detail-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .detail-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .detail-controls {
                    display: flex;
                    gap: 1rem;
                }
                .detail-content {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                }
                .detail-section {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    padding: 1rem;
                }
                .section-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.9rem;
                    margin-bottom: 0.75rem;
                    text-transform: uppercase;
                    border-bottom: 1px dashed #ff0000;
                    padding-bottom: 0.3rem;
                }
                .detail-field {
                    margin-bottom: 0.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                }
                .field-label {
                    color: #ff6666;
                    font-weight: bold;
                    display: inline-block;
                    min-width: 120px;
                    text-transform: uppercase;
                }
                .field-value {
                    color: #cccccc;
                }
                .description-section {
                    grid-column: 1 / -1;
                    margin-top: 1rem;
                }
                .description-text {
                    color: #cccccc;
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    line-height: 1.4;
                    padding: 0.5rem 0;
                }
            </style>
            
            <div class="organization-detail-container">
                <div class="detail-header">
                    <div class="detail-title">ORGANIZATION DETAILS</div>
                    <div class="detail-controls">
                        <button class="secondary-button" data-action="back-to-organization-list">← Back to List</button>
                        <button class="action-button primary" data-action="edit-organization" data-organization-id="${org.id}">Edit Organization</button>
                    </div>
                </div>
                
                <div class="detail-content">
                    <div class="detail-section">
                        <div class="section-title">Basic Information</div>
                        <div class="detail-field">
                            <span class="field-label">Name:</span>
                            <span class="field-value">${org.name || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Type:</span>
                            <span class="field-value">${org.organization_type || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Status:</span>
                            <span class="field-value">${org.is_active ? 'Active' : 'Inactive'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Created:</span>
                            <span class="field-value">${org.created_at ? new Date(org.created_at).toLocaleDateString() : 'Unknown'}</span>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <div class="section-title">Contact Information</div>
                        <div class="detail-field">
                            <span class="field-label">Contact Person:</span>
                            <span class="field-value">${org.contact_person || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Phone:</span>
                            <span class="field-value">${org.phone || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Email:</span>
                            <span class="field-value">${org.email || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Website:</span>
                            <span class="field-value">${org.website || 'Not specified'}</span>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <div class="section-title">Address</div>
                        <div class="detail-field">
                            <span class="field-label">Street:</span>
                            <span class="field-value">${org.street_address || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">City:</span>
                            <span class="field-value">${org.city || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Province:</span>
                            <span class="field-value">${org.province || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Postal Code:</span>
                            <span class="field-value">${org.postal_code || 'Not specified'}</span>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <div class="section-title">Services & Programs</div>
                        <div class="detail-field">
                            <span class="field-label">Services:</span>
                            <span class="field-value">${org.services_offered || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Programs:</span>
                            <span class="field-value">${org.programs || 'Not specified'}</span>
                        </div>
                        <div class="detail-field">
                            <span class="field-label">Hours:</span>
                            <span class="field-value">${org.operating_hours || 'Not specified'}</span>
                        </div>
                    </div>
                    
                    ${org.description ? `
                    <div class="detail-section description-section">
                        <div class="section-title">Description</div>
                        <div class="description-text">${org.description}</div>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    },

    organizationFormView: (org = null, isEdit = false) => {
        return `
            <style>
                .organization-form-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .form-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .form-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .form-controls {
                    display: flex;
                    gap: 1rem;
                }
                .organization-form {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                    margin-bottom: 2rem;
                }
                .form-section {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    padding: 1rem;
                }
                .form-group {
                    margin-bottom: 1rem;
                }
                .form-label {
                    display: block;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.8rem;
                    margin-bottom: 0.3rem;
                    text-transform: uppercase;
                }
                .form-input, .form-select, .form-textarea {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    box-sizing: border-box;
                }
                .form-input::placeholder, .form-textarea::placeholder {
                    color: #ff6666;
                }
                .form-textarea {
                    resize: vertical;
                    min-height: 80px;
                }
                .form-checkbox-group {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }
                .form-checkbox {
                    accent-color: #ff0000;
                }
                .description-section {
                    grid-column: 1 / -1;
                }
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    padding-top: 1rem;
                    border-top: 1px dashed #ff0000;
                }
            </style>
            
            <div class="organization-form-container">
                <div class="form-header">
                    <div class="form-title">${isEdit ? 'EDIT ORGANIZATION' : 'ADD NEW ORGANIZATION'}</div>
                    <div class="form-controls">
                        <button class="secondary-button" data-action="${isEdit ? 'back-to-organization-detail' : 'back-to-organization-list'}" ${isEdit ? `data-organization-id="${org?.id}"` : ''}>← Cancel</button>
                    </div>
                </div>
                
                <form class="organization-form" id="organization-form">
                    <div class="form-section">
                        <div class="section-title">Basic Information</div>
                        <div class="form-group">
                            <label class="form-label" for="org-name">Organization Name *</label>
                            <input type="text" 
                                   id="org-name" 
                                   name="name" 
                                   class="form-input" 
                                   value="${org?.name || ''}" 
                                   placeholder="Enter organization name" 
                                   required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-type">Organization Type</label>
                            <select id="org-type" name="organization_type" class="form-select">
                                <option value="">Select type...</option>
                                <option value="Addiction" ${org?.organization_type === 'Addiction' ? 'selected' : ''}>Addiction</option>
                                <option value="Crisis Support" ${org?.organization_type === 'Crisis Support' ? 'selected' : ''}>Crisis Support</option>
                                <option value="Food Services" ${org?.organization_type === 'Food Services' ? 'selected' : ''}>Food Services</option>
                                <option value="Housing" ${org?.organization_type === 'Housing' ? 'selected' : ''}>Housing</option>
                                <option value="Mental Health" ${org?.organization_type === 'Mental Health' ? 'selected' : ''}>Mental Health</option>
                                <option value="Multi-Service" ${org?.organization_type === 'Multi-Service' ? 'selected' : ''}>Multi-Service</option>
                                <option value="Healthcare" ${org?.organization_type === 'Healthcare' ? 'selected' : ''}>Healthcare</option>
                                <option value="Government" ${org?.organization_type === 'Government' ? 'selected' : ''}>Government</option>
                                <option value="Non-Profit" ${org?.organization_type === 'Non-Profit' ? 'selected' : ''}>Non-Profit</option>
                                <option value="Faith-Based" ${org?.organization_type === 'Faith-Based' ? 'selected' : ''}>Faith-Based</option>
                                <option value="Community Center" ${org?.organization_type === 'Community Center' ? 'selected' : ''}>Community Center</option>
                                <option value="Legal Services" ${org?.organization_type === 'Legal Services' ? 'selected' : ''}>Legal Services</option>
                                <option value="Other" ${org?.organization_type === 'Other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="form-checkbox-group">
                                <input type="checkbox" 
                                       id="org-active" 
                                       name="is_active" 
                                       class="form-checkbox" 
                                       ${org?.is_active !== false ? 'checked' : ''}>
                                <label class="form-label" for="org-active">Organization is Active</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <div class="section-title">Contact Information</div>
                        <div class="form-group">
                            <label class="form-label" for="org-contact">Contact Person</label>
                            <input type="text" 
                                   id="org-contact" 
                                   name="contact_person" 
                                   class="form-input" 
                                   value="${org?.contact_person || ''}" 
                                   placeholder="Contact person name">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-phone">Phone Number</label>
                            <input type="tel" 
                                   id="org-phone" 
                                   name="phone" 
                                   class="form-input" 
                                   value="${org?.phone || ''}" 
                                   placeholder="(*************">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-email">Email Address</label>
                            <input type="email" 
                                   id="org-email" 
                                   name="email" 
                                   class="form-input" 
                                   value="${org?.email || ''}" 
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-website">Website</label>
                            <input type="url" 
                                   id="org-website" 
                                   name="website" 
                                   class="form-input" 
                                   value="${org?.website || ''}" 
                                   placeholder="https://www.organization.com">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <div class="section-title">Address</div>
                        <div class="form-group">
                            <label class="form-label" for="org-street">Street Address</label>
                            <input type="text" 
                                   id="org-street" 
                                   name="street_address" 
                                   class="form-input" 
                                   value="${org?.street_address || ''}" 
                                   placeholder="123 Main Street">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-city">City</label>
                            <input type="text" 
                                   id="org-city" 
                                   name="city" 
                                   class="form-input" 
                                   value="${org?.city || ''}" 
                                   placeholder="Toronto">
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-province">Province</label>
                            <select id="org-province" name="province" class="form-select">
                                <option value="">Select province...</option>
                                <option value="ON" ${org?.province === 'ON' ? 'selected' : ''}>Ontario</option>
                                <option value="BC" ${org?.province === 'BC' ? 'selected' : ''}>British Columbia</option>
                                <option value="AB" ${org?.province === 'AB' ? 'selected' : ''}>Alberta</option>
                                <option value="SK" ${org?.province === 'SK' ? 'selected' : ''}>Saskatchewan</option>
                                <option value="MB" ${org?.province === 'MB' ? 'selected' : ''}>Manitoba</option>
                                <option value="QC" ${org?.province === 'QC' ? 'selected' : ''}>Quebec</option>
                                <option value="NB" ${org?.province === 'NB' ? 'selected' : ''}>New Brunswick</option>
                                <option value="NS" ${org?.province === 'NS' ? 'selected' : ''}>Nova Scotia</option>
                                <option value="PE" ${org?.province === 'PE' ? 'selected' : ''}>Prince Edward Island</option>
                                <option value="NL" ${org?.province === 'NL' ? 'selected' : ''}>Newfoundland and Labrador</option>
                                <option value="YT" ${org?.province === 'YT' ? 'selected' : ''}>Yukon</option>
                                <option value="NT" ${org?.province === 'NT' ? 'selected' : ''}>Northwest Territories</option>
                                <option value="NU" ${org?.province === 'NU' ? 'selected' : ''}>Nunavut</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-postal">Postal Code</label>
                            <input type="text" 
                                   id="org-postal" 
                                   name="postal_code" 
                                   class="form-input" 
                                   value="${org?.postal_code || ''}" 
                                   placeholder="M5V 3A4">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <div class="section-title">Services & Programs</div>
                        <div class="form-group">
                            <label class="form-label" for="org-services">Services Offered</label>
                            <textarea id="org-services" 
                                      name="services_offered" 
                                      class="form-textarea" 
                                      placeholder="List of services provided...">${org?.services_offered || ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-programs">Programs</label>
                            <textarea id="org-programs" 
                                      name="programs" 
                                      class="form-textarea" 
                                      placeholder="List of programs offered...">${org?.programs || ''}</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="org-hours">Operating Hours</label>
                            <input type="text" 
                                   id="org-hours" 
                                   name="operating_hours" 
                                   class="form-input" 
                                   value="${org?.operating_hours || ''}" 
                                   placeholder="Mon-Fri 9AM-5PM">
                        </div>
                    </div>
                    
                    <div class="form-section description-section">
                        <div class="section-title">Description</div>
                        <div class="form-group">
                            <label class="form-label" for="org-description">Organization Description</label>
                            <textarea id="org-description" 
                                      name="description" 
                                      class="form-textarea" 
                                      style="min-height: 120px;" 
                                      placeholder="Detailed description of the organization, its mission, and activities...">${org?.description || ''}</textarea>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="secondary-button" data-action="${isEdit ? 'back-to-organization-detail' : 'back-to-organization-list'}" ${isEdit ? `data-organization-id="${org?.id}"` : ''}>Cancel</button>
                        <button type="submit" class="action-button primary" data-action="save-organization" ${isEdit ? `data-organization-id="${org?.id}"` : ''}>${isEdit ? 'Update Organization' : 'Save Organization'}</button>
                    </div>
                </form>
            </div>
        `;
    }
};