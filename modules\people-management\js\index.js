/**
 * People Management Module - Main Entry Point
 * Modular people management system following the established patterns
 * Handles people, pets, and medical information
 */

import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { PeopleListManager } from './people-list-manager.js';
import { PeopleSearchManager } from './people-search-manager.js';
import { PeopleCrudManager } from './people-crud-manager.js';
import { PeopleDetailManager } from './people-detail-manager.js';
import { PetManager } from './pet-manager.js';
import { MedicalManager } from './medical-manager.js';
import { PersonActivitiesManager } from './person-activities-manager.js';
import { PeopleCommandsFactory } from './people-commands.js';

export class PeopleManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement, app = null) {
        super('PeopleManagement', '1.0.0', [], ['person', 'pet', 'medical_issue']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.app = app;

        // Initialize all sub-managers
        this.listManager = new PeopleListManager(dataManager, authManager, uiManager, uiUtilities);
        this.searchManager = new PeopleSearchManager(dataManager, authManager, uiManager, uiUtilities);
        this.crudManager = new PeopleCrudManager(dataManager, authManager, uiManager, uiUtilities, modalManagement);
        this.detailManager = new PeopleDetailManager(dataManager, authManager, uiManager, uiUtilities);
        this.petManager = new PetManager(dataManager, authManager, uiManager, uiUtilities, modalManagement);
        this.medicalManager = new MedicalManager(dataManager, authManager, uiManager, uiUtilities, modalManagement);
        this.activitiesManager = new PersonActivitiesManager(dataManager, authManager, uiManager, uiUtilities);

        // Set up cross-manager communication
        this.setupManagerCommunication();

        // Initialize commands factory
        this.commandsFactory = new PeopleCommandsFactory(null, this);
        
        // Store selected person for detail views
        this.selectedPerson = null;
    }

    setupManagerCommunication() {
        // Connect detail manager with pet, medical, and activities managers
        this.detailManager.petManager = this.petManager;
        this.detailManager.medicalManager = this.medicalManager;
        this.detailManager.activitiesManager = this.activitiesManager;
        
        // Connect pet manager with detail updates
        this.petManager.onPetChanged = () => {
            this.triggerDetailUpdate();
        };

        // Connect medical manager with detail updates
        this.medicalManager.onMedicalChanged = () => {
            this.triggerDetailUpdate();
        };

        // Connect activities manager with detail updates
        this.activitiesManager.onActivitiesChanged = () => {
            this.triggerDetailUpdate();
        };

        // Connect CRUD operations with list updates
        this.crudManager.onPersonChanged = () => {
            this.triggerListUpdate();
        };
    }

    // === LIST MANAGEMENT ===
    async loadPeopleManagementContent() {
        return await this.listManager.loadPeopleManagementContent();
    }

    async loadPeopleData() {
        return await this.listManager.loadPeopleData();
    }

    async refreshPeopleList() {
        return await this.listManager.refreshPeopleList();
    }

    async showPeopleListView(people) {
        return await this.listManager.showPeopleListView(people);
    }

    // === SEARCH MANAGEMENT ===
    async searchPeople(query) {
        return await this.searchManager.searchPeople(query);
    }

    async showPeopleSearchInterface() {
        return await this.searchManager.showPeopleSearchInterface();
    }

    // === CRUD OPERATIONS ===
    async addPerson(personData) {
        return await this.crudManager.addPerson(personData);
    }

    async updatePerson(personId, updateData) {
        return await this.crudManager.updatePerson(personId, updateData);
    }

    async deletePerson(personId) {
        return await this.crudManager.deletePerson(personId);
    }

    async editPerson(personId) {
        return await this.crudManager.editPerson(personId);
    }

    async showAddPersonForm() {
        return await this.crudManager.showAddPersonForm();
    }

    async showEditPersonForm(personId) {
        return await this.crudManager.showEditPersonForm(personId);
    }

    // === DETAIL MANAGEMENT ===
    async viewPersonDetail(personId) {
        this.selectedPerson = await this.detailManager.viewPersonDetail(personId);
        // Also set it on the app instance for consistency
        if (this.app) {
            this.app.selectedPerson = this.selectedPerson;
        }
        return this.selectedPerson;
    }

    async loadPersonDetail(personId) {
        return await this.detailManager.loadPersonDetail(personId);
    }

    async handlePersonEditAction(action, personRecord) {
        return await this.detailManager.handlePersonEditAction(action, personRecord);
    }

    // === PET MANAGEMENT ===
    async addPet(personId) {
        return await this.petManager.addPet(personId);
    }

    async editPet(petId) {
        return await this.petManager.editPet(petId);
    }

    async deletePet(petId) {
        return await this.petManager.deletePet(petId);
    }

    async loadPersonPets(personId) {
        return await this.petManager.loadPersonPets(personId);
    }

    // === MEDICAL MANAGEMENT ===
    async addMedicalIssue(personId) {
        return await this.medicalManager.addMedicalIssue(personId);
    }

    async showMedicalInfoDialog(personIndex) {
        return await this.medicalManager.showMedicalInfoDialog(personIndex);
    }

    async saveMedicalInfo(personIndex) {
        return await this.medicalManager.saveMedicalInfo(personIndex);
    }

    async loadPersonMedicalIssues(personId) {
        return await this.medicalManager.loadPersonMedicalIssues(personId);
    }

    // === ACTIVITY MANAGEMENT ===
    async addPersonActivity(personId) {
        return await this.activitiesManager.showAddActivityForm(personId);
    }

    async loadPersonActivities(personId) {
        return await this.activitiesManager.loadPersonActivities(personId);
    }

    async updatePersonActivity(activityId, updateData) {
        return await this.activitiesManager.updatePersonActivity(activityId, updateData);
    }

    async deletePersonActivity(activityId) {
        return await this.activitiesManager.deletePersonActivity(activityId);
    }

    // === INTEGRATED FUNCTIONALITY ===
    
    /**
     * Complete person creation workflow
     */
    async createPersonWorkflow() {
        try {
            return await this.showAddPersonForm();
        } catch (error) {
            console.error('Error in person creation workflow:', error);
            throw error;
        }
    }

    /**
     * Complete person search workflow
     */
    async searchPersonWorkflow() {
        try {
            return await this.showPeopleSearchInterface();
        } catch (error) {
            console.error('Error in person search workflow:', error);
            throw error;
        }
    }

    /**
     * Complete people management workflow (full page view)
     */
    async managePeopleWorkflow() {
        try {
            const people = await this.loadPeopleData();
            return await this.app?.loadPeopleManagementContent();
        } catch (error) {
            console.error('Error in people management workflow:', error);
            throw error;
        }
    }

    /**
     * Complete person detail workflow
     */
    async viewPersonWorkflow(personId) {
        try {
            return await this.viewPersonDetail(personId);
        } catch (error) {
            console.error('Error in person detail workflow:', error);
            throw error;
        }
    }

    // === UTILITY METHODS ===
    
    triggerDetailUpdate() {
        // Emit event for detail updates
        const event = new CustomEvent('personDetailUpdated', {
            detail: { timestamp: new Date() }
        });
        window.dispatchEvent(event);
    }

    triggerListUpdate() {
        // Emit event for list updates
        const event = new CustomEvent('peopleListUpdated', {
            detail: { timestamp: new Date() }
        });
        window.dispatchEvent(event);
    }

    // === COMMAND INTEGRATION ===
    
    /**
     * Get people commands for registration with command system
     */
    getCommands(commandManager) {
        // Update the factory with the actual command manager
        this.commandsFactory = new PeopleCommandsFactory(commandManager, this);
        return this.commandsFactory.createCommands();
    }

    // === MODULE INTERFACE IMPLEMENTATION ===
    
    /**
     * Initialize the module - Required by ModuleInterface
     */
    async initialize() {
        this.logger.info('Initializing People Management module');
        // Module initialization is handled in constructor and first use
        // No additional async initialization needed
    }

    /**
     * Cleanup module resources - Required by ModuleInterface
     */
    async cleanup() {
        this.logger.info('Cleaning up people management system...');
        
        // Cleanup all managers
        if (this.listManager?.cleanup) this.listManager.cleanup();
        if (this.searchManager?.cleanup) this.searchManager.cleanup();
        if (this.crudManager?.cleanup) this.crudManager.cleanup();
        if (this.detailManager?.cleanup) this.detailManager.cleanup();
        if (this.petManager?.cleanup) this.petManager.cleanup();
        if (this.medicalManager?.cleanup) this.medicalManager.cleanup();
        if (this.activitiesManager?.cleanup) this.activitiesManager.cleanup();
    }

    /**
     * Get workflows provided by this module
     */
    getWorkflows() {
        return {
            createPerson: () => this.createPersonWorkflow(),
            searchPerson: () => this.searchPersonWorkflow(),
            managePeople: () => this.managePeopleWorkflow(),
            viewPerson: (personId) => this.viewPersonWorkflow(personId)
        };
    }

    /**
     * Get statistics for this module
     */
    async getStatistics() {
        try {
            const people = await this.loadPeopleData();
            return {
                module: this.name,
                uptime: Date.now() - this.startTime,
                initialized: this.initialized,
                totalPeople: people?.length || 0,
                managersActive: Object.values(this.getSystemStatus().managers).filter(Boolean).length
            };
        } catch (error) {
            this.logger.error('Failed to generate statistics', error);
            return super.getStatistics();
        }
    }

    // === STATUS AND DEBUGGING ===
    
    getSystemStatus() {
        return {
            managers: {
                list: !!this.listManager,
                search: !!this.searchManager,
                crud: !!this.crudManager,
                detail: !!this.detailManager,
                pet: !!this.petManager,
                medical: !!this.medicalManager,
                activities: !!this.activitiesManager
            }
        };
    }
}

// Export individual managers for direct access if needed
export {
    PeopleListManager,
    PeopleSearchManager,
    PeopleCrudManager,
    PeopleDetailManager,
    PetManager,
    MedicalManager,
    PersonActivitiesManager,
    PeopleCommandsFactory
};