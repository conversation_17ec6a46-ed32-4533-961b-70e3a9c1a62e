// Charges Templates
// Templates for charge management and disposition tracking

export const chargesTemplates = {
    chargesGrid(data) {
        const { episode, charges } = data;
        
        return `
            <div class="charges-grid">
                <div class="charges-header">
                    <h4>Charges</h4>
                    <div class="charges-actions">
                        <button class="secondary-button" id="add-charge-btn">
                            <span class="button-icon">+</span>
                            Add Charge
                        </button>
                        <button class="secondary-button" data-action="refresh-charges">
                            <span class="button-icon">🔄</span>
                            Refresh
                        </button>
                    </div>
                </div>
                
                <div class="charges-content">
                    ${charges.length > 0 ? chargesTemplates.renderChargesList(charges) : chargesTemplates.renderNoCharges()}
                </div>
            </div>
        `;
    },
    
    renderChargesList(charges) {
        return `
            <div class="charges-table">
                <div class="charges-table-header">
                    <div class="charge-col-code">Code</div>
                    <div class="charge-col-description">Description</div>
                    <div class="charge-col-severity">Severity</div>
                    <div class="charge-col-status">Status</div>
                    <div class="charge-col-actions">Actions</div>
                </div>
                
                <div class="charges-table-body">
                    ${charges.map(charge => chargesTemplates.renderChargeRow(charge)).join('')}
                </div>
            </div>
        `;
    },
    
    renderChargeRow(charge) {
        return `
            <div class="charge-row" data-charge-id="${charge.id}">
                <div class="charge-col-code">
                    <span class="charge-code">${charge.code || 'N/A'}</span>
                </div>
                
                <div class="charge-col-description">
                    <span class="charge-label">${charge.label || 'Unlabeled charge'}</span>
                </div>
                
                <div class="charge-col-severity">
                    <span class="severity-badge severity-${charge.severity?.toLowerCase() || 'other'}">
                        ${chargesTemplates.formatSeverity(charge.severity)}
                    </span>
                </div>
                
                <div class="charge-col-status">
                    <select class="charge-status-select" data-charge-id="${charge.id}">
                        <option value="PENDING" ${charge.status === 'PENDING' ? 'selected' : ''}>Pending</option>
                        <option value="WITHDRAWN" ${charge.status === 'WITHDRAWN' ? 'selected' : ''}>Withdrawn</option>
                        <option value="DISMISSED" ${charge.status === 'DISMISSED' ? 'selected' : ''}>Dismissed</option>
                        <option value="CONVICTED" ${charge.status === 'CONVICTED' ? 'selected' : ''}>Convicted</option>
                        <option value="ACQUITTED" ${charge.status === 'ACQUITTED' ? 'selected' : ''}>Acquitted</option>
                        <option value="STAYED" ${charge.status === 'STAYED' ? 'selected' : ''}>Stayed</option>
                    </select>
                </div>
                
                <div class="charge-col-actions">
                    <button class="action-button edit-charge-btn" data-charge-id="${charge.id}" title="Edit Charge">
                        ✏️
                    </button>
                </div>
            </div>
        `;
    },
    
    renderNoCharges() {
        return `
            <div class="no-charges">
                <p>No charges have been added to this episode yet.</p>
                <p>Click "Add Charge" to add the first charge.</p>
            </div>
        `;
    },
    
    addChargeForm() {
        return `
            <form id="add-charge-form">
                <div class="form-row">
                    <label for="code">Charge Code:</label>
                    <input type="text" name="code" id="code" 
                           placeholder="e.g., CC 266, HTA 172">
                </div>
                
                <div class="form-row">
                    <label for="label">Description:</label>
                    <input type="text" name="label" id="label" required
                           placeholder="e.g., Assault, Dangerous Driving">
                </div>
                
                <div class="form-row">
                    <label for="severity">Severity:</label>
                    <select name="severity" id="severity">
                        <option value="SUMM">Summary</option>
                        <option value="IND">Indictable</option>
                        <option value="HYBRID">Hybrid</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-charge-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-charge-btn" class="primary-button">
                        Add Charge
                    </button>
                </div>
            </form>
        `;
    },
    
    editChargeForm(charge) {
        return `
            <form id="edit-charge-form">
                <div class="form-row">
                    <label for="code">Charge Code:</label>
                    <input type="text" name="code" id="code" 
                           value="${charge.code || ''}"
                           placeholder="e.g., CC 266, HTA 172">
                </div>
                
                <div class="form-row">
                    <label for="label">Description:</label>
                    <input type="text" name="label" id="label" required
                           value="${charge.label || ''}"
                           placeholder="e.g., Assault, Dangerous Driving">
                </div>
                
                <div class="form-row">
                    <label for="severity">Severity:</label>
                    <select name="severity" id="severity">
                        <option value="SUMM" ${charge.severity === 'SUMM' ? 'selected' : ''}>Summary</option>
                        <option value="IND" ${charge.severity === 'IND' ? 'selected' : ''}>Indictable</option>
                        <option value="HYBRID" ${charge.severity === 'HYBRID' ? 'selected' : ''}>Hybrid</option>
                        <option value="OTHER" ${charge.severity === 'OTHER' ? 'selected' : ''}>Other</option>
                    </select>
                </div>
                
                <div class="form-actions">
                    <button type="button" id="cancel-charge-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="save-charge-btn" class="primary-button">
                        Update Charge
                    </button>
                </div>
            </form>
        `;
    },
    
    errorView(message) {
        return `
            <div class="charges-error">
                <div class="error-message">
                    Failed to load charges: ${message}
                </div>
                <button class="secondary-button" data-action="refresh-charges">
                    Retry
                </button>
            </div>
        `;
    },
    
    formatSeverity(severity) {
        const severities = {
            'SUMM': 'Summary',
            'IND': 'Indictable',
            'HYBRID': 'Hybrid',
            'OTHER': 'Other'
        };
        
        return severities[severity] || 'Other';
    }
};
