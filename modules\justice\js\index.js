// Justice Module Entry Point
// Provides public API for mounting and managing justice functionality

import { FeatureModuleInterface } from '../../shared/feature-module-interface.js';
import { JusticeAPI } from './justice-api.js';
import { JusticeCache } from './justice-cache.js';
import { JusticeState } from './justice-state.js';
import { JusticeCommands } from './justice-commands.js';

// Import view components
import { StatusRibbon } from './views/status-ribbon.js';
import { StartEpisodeWizard } from './views/start-episode-wizard.js';
import { TimelineView } from './views/timeline-view.js';
import { ChargesGrid } from './views/charges-grid.js';
import { ConditionsPanel } from './views/conditions-panel.js';
import { ContactsPanel } from './views/contacts-panel.js';

export class JusticeModule extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager) {
        super();
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        
        // Initialize core components
        this.api = new JusticeAPI(dataManager);
        this.cache = new JusticeCache(dataManager);
        this.state = new JusticeState();
        this.commands = new JusticeCommands(this);
        
        // Initialize view components
        this.statusRibbon = new StatusRibbon(this);
        this.startEpisodeWizard = new StartEpisodeWizard(this);
        this.timelineView = new TimelineView(this);
        this.chargesGrid = new ChargesGrid(this);
        this.conditionsPanel = new ConditionsPanel(this);
        this.contactsPanel = new ContactsPanel(this);
        
        this.isInitialized = false;
    }
    
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Initialize cache (create tables if needed)
            await this.cache.initialize();
            
            // Register commands
            this.commands.register();
            
            this.isInitialized = true;
            console.log('✅ Justice module initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Justice module:', error);
            throw error;
        }
    }
    
    /**
     * Mount the Justice tab for a person record
     * @param {Object} person - Person record
     * @param {HTMLElement} container - Container element to mount in
     */
    async mountJusticeTab(person, container) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }
            
            // Clear container
            container.innerHTML = '';
            
            // Load person's justice episodes
            const episodes = await this.api.listEpisodesByPerson(person.id);
            
            // Set current person in state
            this.state.setCurrentPerson(person);
            this.state.setEpisodes(episodes);
            
            // Create main justice container
            const justiceContainer = document.createElement('div');
            justiceContainer.className = 'justice-container';
            justiceContainer.innerHTML = `
                <div class="justice-header">
                    <h3>Justice Episodes</h3>
                    <button class="primary-button" data-action="je:new" data-person-id="${person.id}">
                        <span class="button-icon">⚖️</span>
                        New Episode
                    </button>
                </div>
                <div class="justice-content">
                    <div id="justice-status-ribbon"></div>
                    <div id="justice-episodes-list"></div>
                </div>
            `;
            
            container.appendChild(justiceContainer);
            
            // Mount status ribbon if there are active episodes
            const activeEpisode = episodes.find(ep => ep.current_state !== 'COMPLETED');
            if (activeEpisode) {
                await this.statusRibbon.mount(
                    document.getElementById('justice-status-ribbon'),
                    activeEpisode
                );
            }
            
            // Mount episodes list
            await this.mountEpisodesList(
                document.getElementById('justice-episodes-list'),
                episodes
            );
            
        } catch (error) {
            console.error('Failed to mount Justice tab:', error);
            this.ui.showDialog('Error', `Failed to load Justice information: ${error.message}`, 'error');
        }
    }
    
    async mountEpisodesList(container, episodes) {
        if (episodes.length === 0) {
            container.innerHTML = `
                <div class="no-episodes">
                    <p>No justice episodes found for this person.</p>
                    <p>Click "New Episode" to create the first episode.</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = `
            <div class="episodes-list">
                ${episodes.map(episode => `
                    <div class="episode-card" data-episode-id="${episode.id}">
                        <div class="episode-header">
                            <div class="episode-title">
                                <span class="episode-origin">${episode.origin}</span>
                                <span class="episode-date">${this.formatDate(episode.origin_dt)}</span>
                            </div>
                            <div class="episode-status status-${episode.current_state.toLowerCase()}">
                                ${episode.current_state}
                            </div>
                        </div>
                        <div class="episode-details">
                            ${episode.origin_agency ? `<div class="episode-agency">Agency: ${episode.origin_agency}</div>` : ''}
                            ${episode.jurisdiction ? `<div class="episode-jurisdiction">Jurisdiction: ${episode.jurisdiction}</div>` : ''}
                        </div>
                        <div class="episode-actions">
                            <button class="action-button" data-action="je:view" data-episode-id="${episode.id}">
                                View Details
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

// Export the mount function for app.js integration
// Note: This function is deprecated - app.js now handles mounting directly
export async function mountJusticeTab(person, container, app) {
    console.warn('mountJusticeTab from justice/index.js is deprecated. Use app.mountJusticeTab() instead.');

    // Delegate to app method
    if (app && typeof app.mountJusticeTab === 'function') {
        return app.mountJusticeTab(person, container);
    }

    // Fallback for backward compatibility
    if (!window.justiceModule) {
        window.justiceModule = new JusticeModule(app.data, app.auth, app.ui);
    }

    await window.justiceModule.mountJusticeTab(person, container);
}
