/**
 * People CRUD Manager
 * Handles Create, Read, Update, Delete operations for people
 * Extends BaseCrudManager for standardized CRUD operations
 */

import { BaseCrudManager } from '../../shared/base-crud-manager.js';

export class PeopleCrudManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super(dataManager, authManager, 'people', 'person', uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.onPersonChanged = null; // Callback for when people change
    }

    /**
     * Show add person form
     */
    async showAddPersonForm() {
        try {
            const fields = this.data.schema.generateFormFields('people');

            this.ui.showForm('Add New Person', fields, async (formData) => {
                try {
                    const dbData = this.data.schema.convertFormToDatabase('people', formData);
                    const personData = {
                        ...dbData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    const newPerson = await this.data.insert('people', personData);

                    this.ui.showDialog(
                        'Person Added',
                        `New person record for ${formData.first_name} ${formData.last_name} has been created.`,
                        'success'
                    );

                    // Show post-creation options
                    this.showPersonCreationOptions(newPerson, formData.first_name, formData.last_name);

                    // Trigger change callback
                    if (this.onPersonChanged) {
                        this.onPersonChanged();
                    }

                    return newPerson;
                } catch (error) {
                    console.error('Error creating person:', error);
                    this.ui.showDialog('Error', `Failed to create person: ${error.message}`, 'error');
                    throw error;
                }
            });
        } catch (error) {
            console.error('Error showing add person form:', error);
            this.ui.showDialog('Error', 'Failed to show add person form', 'error');
            throw error;
        }
    }

    /**
     * Show edit person form
     */
    async showEditPersonForm(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (!person) {
                this.ui.showDialog('Error', 'Person not found', 'error');
                return;
            }

            return await this.editPerson(personId);
        } catch (error) {
            console.error('Error showing edit person form:', error);
            this.ui.showDialog('Error', 'Failed to show edit person form', 'error');
            throw error;
        }
    }

    /**
     * Edit person
     */
    async editPerson(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (!person) {
                this.ui.showDialog('Error', 'Person not found', 'error');
                return;
            }

            // Generate form fields and pre-populate with current data
            const fields = this.data.schema.generateFormFields('people');

            fields.forEach(field => {
                if (person[field.name] !== undefined) {
                    field.value = person[field.name];
                }
            });

            this.ui.showForm('Edit Person Record', fields, async (formData) => {
                try {
                    const dbData = this.data.schema.convertFormToDatabase('people', formData);
                    const updatedData = {
                        ...dbData,
                        updated_by: this.auth.getCurrentUser()?.email,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('people', personId, updatedData);

                    this.ui.showDialog(
                        'Person Updated',
                        `Person record for ${formData.first_name} ${formData.last_name} has been updated.`,
                        'success'
                    );

                    // Show post-edit options including alias management
                    this.showPersonEditOptions(person, formData.first_name, formData.last_name);

                    // Trigger change callback
                    if (this.onPersonChanged) {
                        this.onPersonChanged();
                    }

                    return updatedData;
                } catch (error) {
                    console.error('Error updating person:', error);
                    this.ui.showDialog('Error', `Failed to update person: ${error.message}`, 'error');
                    throw error;
                }
            });
        } catch (error) {
            console.error('Error editing person:', error);
            this.ui.showDialog('Error', 'Failed to load person for editing', 'error');
            throw error;
        }
    }

    /**
     * Add person
     */
    async addPerson(personData) {
        try {
            const fullPersonData = {
                ...personData,
                created_by: this.auth.getCurrentUser()?.email,
                created_at: new Date().toISOString()
            };

            const newPerson = await this.data.insert('people', fullPersonData);

            // Trigger change callback
            if (this.onPersonChanged) {
                this.onPersonChanged();
            }

            return newPerson;
        } catch (error) {
            console.error('Error adding person:', error);
            throw error;
        }
    }

    /**
     * Update person
     */
    async updatePerson(personId, updateData) {
        try {
            const updatedData = {
                ...updateData,
                updated_by: this.auth.getCurrentUser()?.email,
                updated_at: new Date().toISOString()
            };

            const result = await this.data.update('people', personId, updatedData);

            // Trigger change callback
            if (this.onPersonChanged) {
                this.onPersonChanged();
            }

            return result;
        } catch (error) {
            console.error('Error updating person:', error);
            throw error;
        }
    }

    /**
     * Delete person
     */
    async deletePerson(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (!person) {
                throw new Error('Person not found');
            }

            this.ui.showConfirmDialog(
                'Delete Person',
                `Are you sure you want to delete ${person.first_name} ${person.last_name}? This action cannot be undone.`,
                async () => {
                    try {
                        await this.data.delete('people', personId);

                        this.ui.showDialog(
                            'Person Deleted',
                            `Person record for ${person.first_name} ${person.last_name} has been deleted.`,
                            'success'
                        );

                        // Trigger change callback
                        if (this.onPersonChanged) {
                            this.onPersonChanged();
                        }

                        return true;
                    } catch (error) {
                        console.error('Error deleting person:', error);
                        this.ui.showDialog('Error', `Failed to delete person: ${error.message}`, 'error');
                        throw error;
                    }
                }
            );
        } catch (error) {
            console.error('Error deleting person:', error);
            this.ui.showDialog('Error', 'Failed to delete person', 'error');
            throw error;
        }
    }

    /**
     * Show person creation options after successful creation
     */
    showPersonCreationOptions(person, firstName, lastName) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Person Created Successfully</h3>
                </div>
                <div class="modal-body">
                    <p><strong>${firstName} ${lastName}</strong> has been added to the system.</p>
                    <p>What would you like to do next?</p>
                    <div class="option-buttons">
                        <button class="primary-button" data-action="view-person-detail" data-person-id="${person.id}">
                            👁️ View Person Details
                        </button>
                        <button class="secondary-button" data-action="add-person">
                            👤 Add Another Person
                        </button>
                        <button class="secondary-button" data-action="manage-people">
                            📋 Back to People List
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="cancel-button" id="close-creation-options">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Set up event handlers
        const closeBtn = modal.querySelector('#close-creation-options');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        }

        // Handle option button clicks
        const optionButtons = modal.querySelectorAll('.option-buttons button');
        optionButtons.forEach(button => {
            button.addEventListener('click', () => {
                document.body.removeChild(modal);
                // Let the app handle the action
                const action = button.getAttribute('data-action');
                const personId = button.getAttribute('data-person-id');
                
                if (action && window.app && window.app.commands) {
                    if (personId) {
                        window.app.commands.executeCommand(action, { personId });
                    } else {
                        window.app.commands.executeCommand(action);
                    }
                }
            });
        });

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Show person edit options after successful update
     */
    showPersonEditOptions(personRecord, firstName, lastName) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Person Updated Successfully</h3>
                </div>
                <div class="modal-body">
                    <p><strong>${firstName} ${lastName}</strong> has been updated.</p>
                    <p>Additional options:</p>
                    <div class="option-buttons">
                        <button class="primary-button" data-action="view-person-detail" data-person-id="${personRecord.id}">
                            👁️ View Updated Details
                        </button>
                        <button class="secondary-button" data-action="manage-aliases" data-person-id="${personRecord.id}">
                            🏷️ Manage Aliases
                        </button>
                        <button class="secondary-button" data-action="manage-people">
                            📋 Back to People List
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="cancel-button" id="close-edit-options">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Set up event handlers
        const closeBtn = modal.querySelector('#close-edit-options');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        }

        // Handle option button clicks
        const optionButtons = modal.querySelectorAll('.option-buttons button');
        optionButtons.forEach(button => {
            button.addEventListener('click', () => {
                document.body.removeChild(modal);
                // Let the app handle the action
                const action = button.getAttribute('data-action');
                const personId = button.getAttribute('data-person-id');
                
                if (action && window.app && window.app.commands) {
                    if (personId) {
                        window.app.commands.executeCommand(action, { personId });
                    } else {
                        window.app.commands.executeCommand(action);
                    }
                }
            });
        });

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners or intervals if needed
    }
}