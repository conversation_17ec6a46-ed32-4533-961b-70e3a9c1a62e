// Refactored Property Manager - Demonstrates elimination of code duplication
// Uses new utility classes to significantly reduce code duplication

import { BaseCrudManager } from '../../shared/base-crud-manager.js';
import { IdGenerator } from '../../shared/index.js';

export class PropertyManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager = null) {
        super(dataManager, authManager, 'property_records', 'property', uiManager);
    }

    /**
     * Log new property find - eliminates duplicate metadata and ID generation
     * @param {Object} propertyData - Property data
     * @returns {Promise<Object>} Created property record
     */
    async logProperty(propertyData) {
        const context = this.getCurrentUserContext();
        
        const propertyRecord = {
            property_number: IdGenerator.generatePropertyId(),
            incident_id: propertyData.incident_id || null,
            property_type: propertyData.property_type,
            category: propertyData.category || 'found',
            description: propertyData.description,
            brand: propertyData.brand || null,
            model: propertyData.model || null,
            serial_number: propertyData.serial_number || null,
            color: propertyData.color || null,
            estimated_value: propertyData.estimated_value || null,
            condition: propertyData.condition || 'unknown',
            found_location: propertyData.found_location,
            found_coordinates: propertyData.found_coordinates || null,
            found_date: propertyData.found_date || new Date().toISOString().split('T')[0],
            found_time: propertyData.found_time || new Date().toTimeString().split(' ')[0],
            found_by: propertyData.found_by || context.userEmail,
            status: 'found',
            investigation_notes: propertyData.investigation_notes || null,
            photos: propertyData.photos ? JSON.stringify(propertyData.photos) : null,
            disposition_type: propertyData.disposition_type || null,
            disposition_date: propertyData.disposition_date || null,
            disposition_notes: propertyData.disposition_notes || null,
            owner_name: propertyData.owner_name || null,
            owner_phone: propertyData.owner_phone || null,
            owner_email: propertyData.owner_email || null,
            owner_address: propertyData.owner_address || null,
            return_date: propertyData.return_date || null,
            return_method: propertyData.return_method || null,
            return_notes: propertyData.return_notes || null,
            tags: propertyData.tags ? JSON.stringify(propertyData.tags) : null
        };

        return await this.create(propertyRecord, {
            logActivity: true,
            validate: true
        });
    }

    /**
     * Update property status - simplified with base class
     * @param {string} propertyId - Property ID
     * @param {string} newStatus - New status
     * @param {Object} additionalData - Additional update data
     * @returns {Promise<Object>} Updated property
     */
    async updateStatus(propertyId, newStatus, additionalData = {}) {
        const updateData = {
            status: newStatus,
            ...additionalData
        };

        const result = await this.update(propertyId, updateData);
        
        // Log status change
        await this.logAction(propertyId, 'status_change', 
            `Status changed to ${newStatus}`, additionalData);

        return result;
    }

    /**
     * Update investigation details - simplified
     * @param {string} propertyId - Property ID
     * @param {Object} investigationData - Investigation data
     * @returns {Promise<Object>} Updated property
     */
    async updateInvestigation(propertyId, investigationData) {
        const result = await this.update(propertyId, investigationData);
        
        // Log investigation update
        await this.logAction(propertyId, 'investigation_update', 
            'Investigation details updated', investigationData);

        return result;
    }

    /**
     * Return property to owner - simplified workflow
     * @param {string} propertyId - Property ID
     * @param {Object} returnData - Return details
     * @returns {Promise<Object>} Updated property
     */
    async returnProperty(propertyId, returnData) {
        const context = this.getCurrentUserContext();
        
        const updateData = {
            status: 'returned',
            return_date: returnData.return_date || context.timestamp.split('T')[0],
            return_method: returnData.return_method,
            return_notes: returnData.return_notes,
            returned_by: context.userEmail,
            owner_name: returnData.owner_name,
            owner_phone: returnData.owner_phone,
            owner_email: returnData.owner_email,
            owner_address: returnData.owner_address
        };

        const result = await this.update(propertyId, updateData);
        
        // Log return action
        await this.logAction(propertyId, 'returned', 
            `Property returned to ${returnData.owner_name}`, returnData);

        return result;
    }

    /**
     * Simplified activity logging
     * @param {string} propertyId - Property ID
     * @param {string} action - Action type
     * @param {string} description - Action description
     * @param {Object} details - Additional details
     * @returns {Promise<Object>} Activity record
     */
    async logAction(propertyId, action, description, details = {}) {
        const context = this.getCurrentUserContext();
        
        const activityData = {
            property_id: propertyId,
            action_type: action,
            description: description,
            performed_by: context.userEmail,
            performed_at: context.timestamp,
            details: JSON.stringify(details)
        };

        return await this.executeWithErrorHandling(async () => {
            return await this.data.insert('property_activities', activityData);
        }, 'logging property action');
    }

    /**
     * Get property by ID - inherited from base class
     * @param {string} propertyId - Property ID
     * @returns {Promise<Object|null>} Property record
     */
    async getProperty(propertyId) {
        return await this.getById(propertyId);
    }

    /**
     * Get all properties - now uses SearchUtility for filtering
     * @param {Object} filters - Filter criteria
     * @returns {Promise<Array>} Filtered properties
     */
    async getAllProperties(filters = {}) {
        return await this.getAll(filters);
    }

    /**
     * Search properties - now uses SearchUtility
     * @param {string} searchTerm - Search term
     * @param {Object} options - Search options
     * @returns {Promise<Array>} Search results
     */
    async searchProperties(searchTerm, options = {}) {
        return await this.search(searchTerm, options);
    }

    /**
     * Get property statistics - now uses StatsGenerator
     * @returns {Promise<Object>} Property statistics
     */
    async getPropertyStats() {
        const stats = await this.getStats();
        
        // Transform to match original format for compatibility
        return {
            total: stats.basic.total,
            found: stats.basic.status_found || 0,
            returned: stats.basic.status_returned || 0,
            disposed: stats.basic.status_disposed || 0,
            claimed: stats.basic.status_claimed || 0
        };
    }

    /**
     * Get recent property finds - uses SearchUtility with time filtering
     * @param {number} days - Number of days to look back
     * @returns {Promise<Array>} Recent property finds
     */
    async getRecentFinds(days = 7) {
        return await this.executeWithErrorHandling(async () => {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - days);
            const cutoffString = cutoffDate.toISOString().split('T')[0];

            const allProperties = await this.getAll();
            return allProperties.filter(property => 
                property.found_date >= cutoffString
            );
        }, 'getting recent property finds');
    }

    /**
     * Bulk update properties - uses base class bulk operations
     * @param {Array} updates - Array of {id, data} objects
     * @param {Object} options - Update options
     * @returns {Promise<Array>} Update results
     */
    async bulkUpdateProperties(updates, options = {}) {
        return await this.bulkUpdate(updates, options);
    }

    /**
     * Get property activities/history
     * @param {string} propertyId - Property ID
     * @returns {Promise<Array>} Property activities
     */
    async getPropertyActivities(propertyId) {
        return await this.executeWithErrorHandling(async () => {
            const activities = await this.data.getAll('property_activities');
            if (!activities) return [];
            
            return activities
                .filter(activity => activity.property_id === propertyId)
                .sort((a, b) => new Date(b.performed_at) - new Date(a.performed_at));
        }, 'getting property activities');
    }

    /**
     * Validation methods for base class
     */
    validateCreate(propertyData) {
        const requiredFields = ['property_type', 'description', 'found_location'];
        return this.validateRequiredFields(propertyData, requiredFields);
    }

    validateUpdate(updateData) {
        // Minimal validation for updates
        const errors = [];
        
        if (updateData.estimated_value && updateData.estimated_value < 0) {
            errors.push('Estimated value cannot be negative');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

// Comparison: Original PropertyManager was ~400+ lines, refactored is ~200 lines
// Eliminates duplication of:
// - ID generation (now uses IdGenerator)
// - User context and timestamp handling (now uses BaseManager)
// - Error handling patterns (now uses executeWithErrorHandling)
// - Basic CRUD operations (now uses BaseCrudManager)
// - Search and filtering logic (now uses SearchUtility)
// - Statistics generation (now uses StatsGenerator)