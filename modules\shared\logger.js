// Centralized Logger System - Replaces scattered console.log calls
// Provides consistent logging with module context and optional monitoring integration

export class Logger {
    /**
     * Log informational messages
     * @param {string} module - Module name for context
     * @param {string} message - Log message
     * @param {*} data - Optional data to log
     */
    static info(module, message, data = null) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${module}] INFO: ${message}`;
        
        if (data !== null) {
            console.log(logMessage, data);
        } else {
            console.log(logMessage);
        }
    }

    /**
     * Log error messages with stack traces
     * @param {string} module - Module name for context
     * @param {Error|string} error - Error object or error message
     * @param {Object} context - Optional context data
     */
    static error(module, error, context = {}) {
        const timestamp = new Date().toISOString();
        const errorMessage = error instanceof Error ? error.message : error;
        const logMessage = `[${timestamp}] [${module}] ERROR: ${errorMessage}`;
        
        if (error instanceof Error) {
            console.error(logMessage, error, context);
        } else {
            console.error(logMessage, context);
        }
        
        // Optional: Send to monitoring service
        this._sendToMonitoring('error', module, errorMessage, context);
    }

    /**
     * Log warning messages
     * @param {string} module - Module name for context
     * @param {string} message - Warning message
     * @param {*} data - Optional data to log
     */
    static warn(module, message, data = null) {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${module}] WARN: ${message}`;
        
        if (data !== null) {
            console.warn(logMessage, data);
        } else {
            console.warn(logMessage);
        }
    }

    /**
     * Log debug messages (only in development)
     * @param {string} module - Module name for context
     * @param {string} message - Debug message
     * @param {*} data - Optional data to log
     */
    static debug(module, message, data = null) {
        // Only log debug messages in development mode
        const isDev = process?.argv?.includes('--dev') || window?.location?.hostname === 'localhost';
        
        if (!isDev) return;
        
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] [${module}] DEBUG: ${message}`;
        
        if (data !== null) {
            console.debug(logMessage, data);
        } else {
            console.debug(logMessage);
        }
    }

    /**
     * Log operation start
     * @param {string} module - Module name
     * @param {string} operation - Operation name
     * @param {*} params - Optional parameters
     * @returns {function} End logging function
     */
    static startOperation(module, operation, params = null) {
        const startTime = Date.now();
        this.debug(module, `Starting ${operation}`, params);
        
        return (result = null, error = null) => {
            const duration = Date.now() - startTime;
            
            if (error) {
                this.error(module, `Failed ${operation} (${duration}ms)`, { error, params });
            } else {
                this.debug(module, `Completed ${operation} (${duration}ms)`, { result, params });
            }
        };
    }

    /**
     * Log data operations (create, read, update, delete)
     * @param {string} module - Module name
     * @param {string} operation - CRUD operation (create, read, update, delete)
     * @param {string} entityType - Type of entity
     * @param {string|number} entityId - Entity ID
     * @param {*} data - Optional operation data
     */
    static data(module, operation, entityType, entityId, data = null) {
        const message = `${operation.toUpperCase()} ${entityType} [${entityId}]`;
        this.info(module, message, data);
    }

    /**
     * Log user actions for audit trail
     * @param {string} module - Module name
     * @param {string} action - User action
     * @param {string} userEmail - User email
     * @param {*} details - Action details
     */
    static userAction(module, action, userEmail, details = null) {
        const message = `User ${userEmail} performed: ${action}`;
        this.info(module, message, details);
    }

    /**
     * Log performance metrics
     * @param {string} module - Module name
     * @param {string} metric - Metric name
     * @param {number} value - Metric value
     * @param {string} unit - Unit of measurement
     */
    static performance(module, metric, value, unit = 'ms') {
        this.debug(module, `Performance: ${metric} = ${value}${unit}`);
    }

    /**
     * Log API calls
     * @param {string} module - Module name
     * @param {string} method - HTTP method
     * @param {string} endpoint - API endpoint
     * @param {number} statusCode - Response status code
     * @param {number} duration - Request duration in ms
     */
    static api(module, method, endpoint, statusCode, duration) {
        const message = `${method} ${endpoint} → ${statusCode} (${duration}ms)`;
        
        if (statusCode >= 400) {
            this.error(module, `API Error: ${message}`);
        } else {
            this.debug(module, `API Call: ${message}`);
        }
    }

    /**
     * Private method to send logs to monitoring service
     * @param {string} level - Log level
     * @param {string} module - Module name
     * @param {string} message - Log message
     * @param {Object} context - Context data
     * @private
     */
    static _sendToMonitoring(level, module, message, context) {
        // Future implementation: Send to external monitoring service
        // For now, this is a placeholder for future integration
        
        // Example integration points:
        // - Send to Supabase logging table
        // - Send to external service like Sentry or LogRocket
        // - Write to local log file for debugging
        
        try {
            // Placeholder for monitoring integration
            if (window?.app?.config?.enableRemoteLogging) {
                // Future: Implement remote logging
            }
        } catch (error) {
            // Silently fail - don't break the application for logging failures
            console.warn('Failed to send log to monitoring service:', error);
        }
    }

    /**
     * Create a module-specific logger instance
     * @param {string} module - Module name
     * @returns {Object} Module logger with bound methods
     */
    static forModule(module) {
        return {
            info: (message, data) => this.info(module, message, data),
            error: (error, context) => this.error(module, error, context),
            warn: (message, data) => this.warn(module, message, data),
            debug: (message, data) => this.debug(module, message, data),
            startOperation: (operation, params) => this.startOperation(module, operation, params),
            data: (operation, entityType, entityId, data) => this.data(module, operation, entityType, entityId, data),
            userAction: (action, userEmail, details) => this.userAction(module, action, userEmail, details),
            performance: (metric, value, unit) => this.performance(module, metric, value, unit),
            api: (method, endpoint, statusCode, duration) => this.api(module, method, endpoint, statusCode, duration)
        };
    }
}

// Export convenience method for creating module loggers
export const createLogger = (module) => Logger.forModule(module);