// Vehicle List Templates
// Extracted from commands.js for better maintainability

export const vehicleListTemplates = {

    // Main vehicle list view modal
    vehicleListModal: (vehicles) => {
        return `
            <div class="modal-dialog vehicle-list-modal" style="max-width: 90%; max-height: 90%; width: 1000px;">
                <div class="modal-header">
                    <h3>🚗 Vehicle Database - ${vehicles.length} Vehicles</h3>
                    <button type="button" class="btn-secondary back-to-records" id="back-to-records-btn">
                        ← Back to Records
                    </button>
                </div>
                <div class="modal-body vehicle-list-body">
                    ${vehicleListTemplates.vehicleListContent(vehicles)}
                </div>
            </div>
        `;
    },

    // Vehicle list content with controls and grid
    vehicleListContent: (vehicles) => {
        if (vehicles.length === 0) {
            return vehicleListTemplates.noVehiclesMessage();
        }

        return `
            <div class="vehicle-list-container">
                <div class="vehicle-list-header">
                    <div class="list-controls">
                        <div class="search-container">
                            <input type="text" id="vehicle-filter" placeholder="Filter vehicles..." class="filter-input">
                            <button type="button" id="clear-filter" class="btn-small">Clear</button>
                        </div>
                        <div class="sort-container">
                            <select id="vehicle-sort" class="sort-select">
                                <option value="plate_number">Sort by License Plate</option>
                                <option value="make">Sort by Make</option>
                                <option value="created_at">Sort by Date Added</option>
                                <option value="owner_name">Sort by Owner</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="vehicle-list-grid">
                    ${vehicles.map(vehicle => vehicleListTemplates.vehicleCard(vehicle)).join('')}
                </div>
            </div>
        `;
    },

    // Individual vehicle card template
    vehicleCard: (vehicle) => {
        return `
            <div class="vehicle-list-item" data-vehicle-id="${vehicle.id}">
                <div class="vehicle-card">
                    <div class="vehicle-header">
                        <div class="plate-number">${vehicle.plate_number || 'Unknown Plate'}</div>
                        <div class="vehicle-date">${new Date(vehicle.created_at).toLocaleDateString()}</div>
                    </div>
                    <div class="vehicle-info">
                        <div class="make-model">
                            <strong>${vehicle.make || 'Unknown'} ${vehicle.model || ''}</strong>
                        </div>
                        <div class="vehicle-details">
                            <span class="year">${vehicle.year || 'Unknown Year'}</span>
                            <span class="color">${vehicle.color || 'Unknown Color'}</span>
                        </div>
                        <div class="owner-info">
                            <strong>Owner:</strong> ${vehicle.owner_name || 'Unknown'}
                        </div>
                        ${vehicle.vin ? `<div class="vin-info"><strong>VIN:</strong> ${vehicle.vin}</div>` : ''}
                        ${vehicle.notes ? `<div class="notes-preview">${vehicle.notes.substring(0, 100)}${vehicle.notes.length > 100 ? '...' : ''}</div>` : ''}
                    </div>
                    <div class="vehicle-actions">
                        <span class="click-hint">Click to view details</span>
                    </div>
                </div>
            </div>
        `;
    },

    // No vehicles message
    noVehiclesMessage: () => {
        return `
            <div class="no-vehicles">
                <h4>No Vehicles Found</h4>
                <p>No vehicle records are currently in the database.</p>
                <p>Use the "Add Vehicle" button to register a new vehicle.</p>
            </div>
        `;
    },

    // Vehicle detail modal
    vehicleDetailModal: (vehicle) => {
        return `
            <div class="modal-dialog vehicle-detail-modal">
                <div class="modal-header">
                    <h3>🚗 Vehicle Details: ${vehicle.plate_number || 'Unknown Plate'}</h3>
                    <button type="button" class="btn-secondary" id="close-detail-btn">
                        ← Back to List
                    </button>
                </div>
                <div class="modal-body">
                    ${vehicleListTemplates.vehicleDetailContent(vehicle)}
                </div>
            </div>
        `;
    },

    // Vehicle detail content grid
    vehicleDetailContent: (vehicle) => {
        return `
            <div class="vehicle-detail-grid">
                <div class="detail-section">
                    <h4>Vehicle Information</h4>
                    <div class="detail-row">
                        <label>License Plate:</label>
                        <span>${vehicle.plate_number || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Make:</label>
                        <span>${vehicle.make || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Model:</label>
                        <span>${vehicle.model || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Year:</label>
                        <span>${vehicle.year || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Color:</label>
                        <span>${vehicle.color || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>VIN:</label>
                        <span>${vehicle.vin || 'Not provided'}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>Owner Information</h4>
                    <div class="detail-row">
                        <label>Owner Name:</label>
                        <span>${vehicle.owner_name || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Owner Phone:</label>
                        <span>${vehicle.owner_phone || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Owner Address:</label>
                        <span>${vehicle.owner_address || 'Not provided'}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>Record Information</h4>
                    <div class="detail-row">
                        <label>Created:</label>
                        <span>${new Date(vehicle.created_at).toLocaleString()}</span>
                    </div>
                    <div class="detail-row">
                        <label>Created By:</label>
                        <span>${vehicle.created_by || 'Unknown'}</span>
                    </div>
                    ${vehicle.updated_at ? `
                    <div class="detail-row">
                        <label>Last Updated:</label>
                        <span>${new Date(vehicle.updated_at).toLocaleString()}</span>
                    </div>
                    ` : ''}
                </div>
                
                ${vehicle.notes ? `
                <div class="detail-section full-width">
                    <h4>Notes</h4>
                    <div class="notes-content">
                        ${vehicle.notes}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }
};