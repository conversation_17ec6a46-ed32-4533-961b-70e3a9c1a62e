// Native Location Service for Windows
// Uses Windows Location API directly via PowerShell/WMI
import { spawn, exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class NativeLocationService {
    constructor() {
        this.isWindows = process.platform === 'win32';
    }

    async getCurrentPosition() {
        try {
            console.log('🌍 Getting location from Windows Location Services...');
            
            if (!this.isWindows) {
                throw new Error('Native location service only supported on Windows');
            }

            // First test basic PowerShell execution
            console.log('🧪 Testing basic PowerShell execution...');
            try {
                const testResult = await execAsync('powershell -Command "Write-Host TEST_SUCCESS; Get-Date"', { timeout: 5000 });
                console.log('✅ PowerShell test result:', testResult.stdout);
            } catch (testError) {
                console.error('❌ PowerShell test failed:', testError.message);
                throw new Error('PowerShell execution failed. Please ensure PowerShell is available and execution policy allows scripts.');
            }

            // Try Windows Location API directly
            console.log('🔄 Attempting Windows Location API...');
            const windowsLocation = await this.getWindowsLocation();
            if (windowsLocation) {
                return windowsLocation;
            }

            console.log('⚠️ Windows Location API returned no data, checking for alternative methods...');
            
            // Fallback to C# approach
            try {
                console.log('🔄 Trying C# approach...');
                const csharpLocation = await this.getWMILocation();
                if (csharpLocation) {
                    return csharpLocation;
                }
            } catch (csharpError) {
                console.warn('C# location also failed:', csharpError.message);
            }

            throw new Error('No location sources available. Windows Location Services may be disabled or no GPS/WiFi location data is available.');

        } catch (error) {
            console.error('Native location service failed:', error.message);
            throw error;
        }
    }

    async getWindowsLocation() {
        try {
            console.log('🔍 Attempting to get Windows location directly...');

            // Debug PowerShell script with extensive logging
            const psScript = `
                Write-Host "DEBUG: Starting location script..."
                
                try {
                    Write-Host "DEBUG: Loading System.Device assembly..."
                    Add-Type -AssemblyName System.Device -ErrorAction Stop
                    Write-Host "DEBUG: Assembly loaded successfully"
                    
                    Write-Host "DEBUG: Creating GeoCoordinateWatcher..."
                    $watcher = New-Object System.Device.Location.GeoCoordinateWatcher -ErrorAction Stop
                    Write-Host "DEBUG: Watcher created. Initial status: $($watcher.Status.ToString())"
                    
                    Write-Host "DEBUG: Starting watcher with high accuracy..."
                    $watcher.Start($true)
                    Write-Host "DEBUG: Watcher started. Status after start: $($watcher.Status.ToString())"
                    
                    $maxWaitTime = 15000
                    $startTime = Get-Date
                    $iteration = 0
                    
                    while (((Get-Date) - $startTime).TotalMilliseconds -lt $maxWaitTime) {
                        $iteration++
                        $currentStatus = $watcher.Status.ToString()
                        
                        if ($iteration % 10 -eq 1) {  # Log every 5 seconds
                            $elapsed = ((Get-Date) - $startTime).TotalMilliseconds
                            Write-Host "DEBUG: Iteration $iteration, Status: $currentStatus, Elapsed: $elapsed ms"
                        }
                        
                        if ($watcher.Status -eq [System.Device.Location.GeoPositionStatus]::Ready) {
                            Write-Host "DEBUG: Status is Ready! Getting position..."
                            $position = $watcher.Position
                            Write-Host "DEBUG: Position object: $($position -ne $null)"
                            
                            if ($position -ne $null) {
                                $coord = $position.Location
                                Write-Host "DEBUG: Coordinate object: $($coord -ne $null)"
                                Write-Host "DEBUG: IsUnknown: $($coord.IsUnknown)"
                                Write-Host "DEBUG: Latitude: $($coord.Latitude)"
                                Write-Host "DEBUG: Longitude: $($coord.Longitude)"
                                Write-Host "DEBUG: HorizontalAccuracy: $($coord.HorizontalAccuracy)"
                                
                                if ($coord.IsUnknown -eq $false -and 
                                    $coord.Latitude -ne 0 -and 
                                    $coord.Longitude -ne 0 -and
                                    $coord.Latitude -ge -90 -and $coord.Latitude -le 90 -and
                                    $coord.Longitude -ge -180 -and $coord.Longitude -le 180) {
                                    
                                    $lat = $coord.Latitude
                                    $lng = $coord.Longitude
                                    $acc = if ($coord.HorizontalAccuracy -ne [double]::NaN) { $coord.HorizontalAccuracy } else { 0 }
                                    
                                    Write-Host "DEBUG: Valid coordinates found!"
                                    Write-Output "SUCCESS:$lat,$lng,$acc"
                                    break
                                } else {
                                    Write-Host "DEBUG: Coordinates are invalid or unknown"
                                }
                            } else {
                                Write-Host "DEBUG: Position is null"
                            }
                        }
                        elseif ($watcher.Status -eq [System.Device.Location.GeoPositionStatus]::Disabled) {
                            Write-Host "DEBUG: Status is Disabled"
                        }
                        elseif ($watcher.Status -eq [System.Device.Location.GeoPositionStatus]::NoData) {
                            Write-Host "DEBUG: Status is NoData"
                        }
                        elseif ($watcher.Status -eq [System.Device.Location.GeoPositionStatus]::Initializing) {
                            Write-Host "DEBUG: Status is Initializing"
                        }
                        
                        Start-Sleep -Milliseconds 500
                    }
                    
                    Write-Host "DEBUG: Loop ended. Final status: $($watcher.Status.ToString())"
                    
                    if ($watcher.Status -ne [System.Device.Location.GeoPositionStatus]::Ready) {
                        Write-Output "TIMEOUT:Final status was $($watcher.Status.ToString())"
                    }
                    
                    Write-Host "DEBUG: Stopping and disposing watcher..."
                    $watcher.Stop()
                    $watcher.Dispose()
                    Write-Host "DEBUG: Cleanup complete"
                    
                } catch {
                    Write-Host "DEBUG: Exception caught: $($_.Exception.Message)"
                    Write-Host "DEBUG: Exception type: $($_.Exception.GetType().Name)"
                    Write-Host "DEBUG: Stack trace: $($_.ScriptStackTrace)"
                    Write-Output "ERROR:$($_.Exception.Message)"
                }
                
                Write-Host "DEBUG: Script complete"
            `;

            console.log('🔄 Executing PowerShell location script...');
            const { stdout, stderr } = await execAsync(`powershell -ExecutionPolicy Bypass -Command "${psScript}"`, {
                timeout: 25000
            });
            
            console.log('PowerShell stdout:', stdout);
            if (stderr) console.log('PowerShell stderr:', stderr);
            
            // Parse the result
            const lines = stdout.split('\n');
            
            // Look for success
            const successLine = lines.find(line => line.trim().startsWith('SUCCESS:'));
            if (successLine) {
                const data = successLine.replace('SUCCESS:', '').trim();
                const [lat, lng, accuracy] = data.split(',');
                
                if (lat && lng && !isNaN(parseFloat(lat)) && !isNaN(parseFloat(lng))) {
                    console.log(`✅ Got Windows location: ${lat}, ${lng} (accuracy: ${accuracy}m)`);
                    return {
                        latitude: parseFloat(lat),
                        longitude: parseFloat(lng),
                        accuracy: parseFloat(accuracy) || 0
                    };
                }
            }
            
            // Look for timeout
            const timeoutLine = lines.find(line => line.trim().startsWith('TIMEOUT:'));
            if (timeoutLine) {
                const message = timeoutLine.replace('TIMEOUT:', '').trim();
                console.warn('⏱️ Location timeout:', message);
                throw new Error(`Location timeout: ${message}. Try enabling high-accuracy location in Windows Settings.`);
            }
            
            // Look for error
            const errorLine = lines.find(line => line.trim().startsWith('ERROR:'));
            if (errorLine) {
                const message = errorLine.replace('ERROR:', '').trim();
                console.error('❌ Location error:', message);
                throw new Error(`Location error: ${message}`);
            }

            console.warn('⚠️ No valid location data received from Windows Location Services');
            return null;
            
        } catch (error) {
            console.error('Windows Location API failed:', error.message);
            
            // Don't re-throw timeout and specific errors, they're already formatted
            if (error.message.includes('Location timeout') || error.message.includes('Location error')) {
                throw error;
            }
            
            // Handle PowerShell execution issues
            if (error.message.includes('ExecutionPolicy')) {
                throw new Error('PowerShell execution blocked. Please run app as administrator.');
            }
            
            // Generic error
            throw new Error(`Windows Location Services error: ${error.message}`);
        }
    }

    async getWMILocation() {
        try {
            console.log('🔄 Trying C# location approach...');
            
            // C# script that uses Windows Location API directly
            const csharpScript = `
using System;
using System.Device.Location;
using System.Threading;

class LocationGetter {
    static void Main() {
        try {
            Console.WriteLine("CSHARP: Starting location watcher...");
            
            GeoCoordinateWatcher watcher = new GeoCoordinateWatcher(GeoPositionAccuracy.High);
            watcher.MovementThreshold = 1.0;
            
            bool gotLocation = false;
            GeoCoordinate location = null;
            
            watcher.PositionChanged += (sender, e) => {
                if (!e.Position.Location.IsUnknown) {
                    location = e.Position.Location;
                    gotLocation = true;
                    Console.WriteLine("CSHARP: Position changed: " + location.Latitude + "," + location.Longitude);
                }
            };
            
            watcher.StatusChanged += (sender, e) => {
                Console.WriteLine("CSHARP: Status changed to: " + e.Status);
            };
            
            Console.WriteLine("CSHARP: Starting watcher...");
            watcher.Start();
            
            // Wait up to 15 seconds
            for (int i = 0; i < 30 && !gotLocation; i++) {
                Thread.Sleep(500);
                if (watcher.Position != null && !watcher.Position.Location.IsUnknown) {
                    location = watcher.Position.Location;
                    gotLocation = true;
                    break;
                }
            }
            
            if (gotLocation && location != null) {
                Console.WriteLine("CSHARP_SUCCESS:" + location.Latitude + "," + location.Longitude + "," + location.HorizontalAccuracy);
            } else {
                Console.WriteLine("CSHARP_TIMEOUT:No location data available");
            }
            
            watcher.Stop();
            watcher.Dispose();
            
        } catch (Exception ex) {
            Console.WriteLine("CSHARP_ERROR:" + ex.Message);
        }
    }
}
            `;
            
            // Write C# script to temp file
            const fs = await import('fs');
            const path = await import('path');
            const os = await import('os');
            
            const tempDir = os.tmpdir();
            const scriptPath = path.join(tempDir, 'LocationGetter.cs');
            const exePath = path.join(tempDir, 'LocationGetter.exe');
            
            // Write the C# file
            fs.writeFileSync(scriptPath, csharpScript);
            
            // Compile the C# script
            console.log('🔄 Compiling C# location script...');
            const compileResult = await execAsync(`csc.exe /reference:System.Device.dll "${scriptPath}" /out:"${exePath}"`);
            
            if (compileResult.stderr && !compileResult.stderr.includes('warning')) {
                throw new Error(`C# compilation failed: ${compileResult.stderr}`);
            }
            
            // Execute the compiled program
            console.log('🔄 Executing C# location program...');
            const { stdout, stderr } = await execAsync(`"${exePath}"`, { timeout: 20000 });
            
            console.log('C# stdout:', stdout);
            if (stderr) console.log('C# stderr:', stderr);
            
            // Parse result
            const lines = stdout.split('\n');
            const successLine = lines.find(line => line.trim().startsWith('CSHARP_SUCCESS:'));
            
            if (successLine) {
                const data = successLine.replace('CSHARP_SUCCESS:', '').trim();
                const [lat, lng, accuracy] = data.split(',');
                
                if (lat && lng && !isNaN(parseFloat(lat)) && !isNaN(parseFloat(lng))) {
                    console.log(`✅ Got C# location: ${lat}, ${lng} (accuracy: ${accuracy}m)`);
                    
                    // Cleanup
                    try {
                        fs.unlinkSync(scriptPath);
                        fs.unlinkSync(exePath);
                    } catch (cleanupError) {
                        console.warn('Cleanup failed:', cleanupError.message);
                    }
                    
                    return {
                        latitude: parseFloat(lat),
                        longitude: parseFloat(lng),
                        accuracy: parseFloat(accuracy) || 0
                    };
                }
            }
            
            // Cleanup on failure
            try {
                fs.unlinkSync(scriptPath);
                if (fs.existsSync(exePath)) fs.unlinkSync(exePath);
            } catch (cleanupError) {
                console.warn('Cleanup failed:', cleanupError.message);
            }
            
            return null;
            
        } catch (error) {
            console.warn('C# location method failed:', error.message);
            return null;
        }
    }

    // Helper method to guide users on enabling location services
    async getLocationSetupInstructions() {
        const instructions = [
            "To enable Windows Location Services:",
            "1. Open Windows Settings (Win + I)",
            "2. Go to Privacy & Security > Location",
            "3. Turn ON 'Location services'",
            "4. Turn ON 'Allow apps to access your location'",
            "5. Restart the application",
            "",
            "Alternative: Run this app as Administrator for enhanced location access"
        ];
        
        return instructions.join('\n');
    }

    // Check if Windows Location Services are enabled
    async isLocationServiceEnabled() {
        try {
            const psScript = `
                try {
                    # Check global location setting
                    $globalKey = "HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\location"
                    $userKey = "HKCU:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\CapabilityAccessManager\\ConsentStore\\location"
                    
                    $globalEnabled = $false
                    $userEnabled = $false
                    
                    # Check global (system-wide) location setting
                    if (Test-Path $globalKey) {
                        $globalValue = Get-ItemProperty -Path $globalKey -Name "Value" -ErrorAction SilentlyContinue
                        if ($globalValue -and $globalValue.Value -eq "Allow") {
                            $globalEnabled = $true
                        }
                    }
                    
                    # Check user location setting
                    if (Test-Path $userKey) {
                        $userValue = Get-ItemProperty -Path $userKey -Name "Value" -ErrorAction SilentlyContinue
                        if ($userValue -and $userValue.Value -eq "Allow") {
                            $userEnabled = $true
                        }
                    }
                    
                    # Also check the newer Windows 11 location settings
                    $locationServiceKey = "HKLM:\\SYSTEM\\CurrentControlSet\\Services\\lfsvc\\Service\\Configuration"
                    $serviceEnabled = $false
                    
                    if (Test-Path $locationServiceKey) {
                        $serviceValue = Get-ItemProperty -Path $locationServiceKey -Name "Status" -ErrorAction SilentlyContinue
                        if ($serviceValue -and $serviceValue.Status -eq 1) {
                            $serviceEnabled = $true
                        }
                    }
                    
                    Write-Host "Global location: $globalEnabled, User location: $userEnabled, Service: $serviceEnabled"
                    
                    if ($globalEnabled -or $userEnabled -or $serviceEnabled) {
                        Write-Output "enabled"
                    } else {
                        Write-Output "disabled"
                    }
                    
                } catch {
                    Write-Host "Error checking location services: $($_.Exception.Message)"
                    Write-Output "unknown"
                }
            `;

            const { stdout, stderr } = await execAsync(`powershell -ExecutionPolicy Bypass -Command "${psScript}"`);
            const result = stdout.trim().toLowerCase();
            
            console.log(`Windows Location Services check result: ${result}`);
            if (stderr) console.log('Location service check stderr:', stderr);
            
            // If we can't determine, assume it might be enabled and let the actual location call handle it
            return result === 'enabled' || result === 'unknown';
            
        } catch (error) {
            console.warn('Could not check Windows Location Service status:', error.message);
            // If we can't check, assume it might work and let the location call handle the error
            return true;
        }
    }
}