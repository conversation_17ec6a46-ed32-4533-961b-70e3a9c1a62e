// UpdateManager for S.T.E.V.I Retro - Handles application updates from Azure Blob Storage
import { app, shell } from 'electron';
import { createWriteStream, createReadStream, promises as fs, existsSync } from 'fs';
import { join, dirname } from 'path';
import { pipeline } from 'stream/promises';
import { createHash } from 'crypto';
import semver from 'semver';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export class UpdateManager {
    constructor() {
        this.currentVersion = app.getVersion();
        this.updateInProgress = false;
        this.downloadProgress = 0;
        this.latestRelease = null;
        this.isDevelopment = this.detectDevelopmentEnvironment();
        this.cleanupInterval = null; // Store reference to periodic cleanup timer

        // Perform startup cleanup of old update files
        this.performStartupCleanup().catch(error => {
            console.warn('Startup cleanup failed:', error.message);
        });

        // Set up periodic cleanup (every 24 hours)
        this.setupPeriodicCleanup();

        // Azure Blob Storage configuration
        this.azureConfig = {
            storageAccount: 'iharcpublicappblob',
            container: 'stevi',
            blobPath: 'stevi retro',
            baseUrl: 'https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro',
            endpoints: {
                latestVersion: 'https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-version.txt',
                latestMetadata: 'https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/latest-metadata.json',
                versionMetadata: (version) => `https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v${version}/metadata.json`,
                versionDownload: (version) => `https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v${version}/`,
                releaseNotes: (version) => `https://iharcpublicappblob.blob.core.windows.net/stevi/stevi%20retro/v${version}/release-notes.md`
            }
        };

        console.log(`UpdateManager initialized - Current version: ${this.currentVersion}`);
        console.log(`Development environment: ${this.isDevelopment}`);

        if (this.isDevelopment) {
            console.warn('Development environment detected - Updates will be disabled for safety');
        }
    }

    /**
     * Fetch latest version from Azure Blob Storage
     * @returns {Promise<string>} Latest version string
     */
    async fetchLatestVersion() {
        try {
            const response = await fetch(this.azureConfig.endpoints.latestVersion, {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch latest version: ${response.status} ${response.statusText}`);
            }

            const version = (await response.text()).trim();
            console.log(`Latest version from Azure: ${version}`);
            return version;
        } catch (error) {
            console.error('Error fetching latest version:', error);
            throw new Error(`Unable to check for updates: ${error.message}`);
        }
    }

    /**
     * Fetch metadata for a specific version
     * @param {string} version - Version to fetch metadata for
     * @returns {Promise<Object>} Version metadata
     */
    async fetchVersionMetadata(version) {
        try {
            const metadataUrl = version === 'latest'
                ? this.azureConfig.endpoints.latestMetadata
                : this.azureConfig.endpoints.versionMetadata(version);

            const response = await fetch(metadataUrl, {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch metadata: ${response.status} ${response.statusText}`);
            }

            const metadata = await response.json();
            console.log(`Fetched metadata for version ${version}:`, metadata);
            return metadata;
        } catch (error) {
            console.error(`Error fetching metadata for version ${version}:`, error);
            throw new Error(`Unable to fetch version information: ${error.message}`);
        }
    }

    /**
     * Fetch release notes for a specific version
     * @param {string} version - Version to fetch release notes for
     * @returns {Promise<string>} Release notes in markdown format
     */
    async fetchReleaseNotes(version) {
        try {
            const response = await fetch(this.azureConfig.endpoints.releaseNotes(version), {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });

            if (!response.ok) {
                console.warn(`Release notes not found for version ${version}`);
                return `# S.T.E.V.I Retro v${version}\n\nRelease notes not available for this version.`;
            }

            const releaseNotes = await response.text();
            return releaseNotes;
        } catch (error) {
            console.warn(`Error fetching release notes for version ${version}:`, error);
            return `# S.T.E.V.I Retro v${version}\n\nRelease notes could not be loaded.`;
        }
    }

    /**
     * Detect if running in development environment
     * @returns {boolean} True if in development environment
     */
    detectDevelopmentEnvironment() {
        // Check for development indicators
        const indicators = [
            // Check if running with --dev flag
            process.argv.includes('--dev'),
            // Check if NODE_ENV is development
            process.env.NODE_ENV === 'development',
            // Check if we're running from source (node_modules exists in parent directory)
            existsSync(join(process.cwd(), 'node_modules')),
            // Check if package.json exists (indicating source installation)
            existsSync(join(process.cwd(), 'package.json')),
            // Check if .git directory exists
            existsSync(join(process.cwd(), '.git')),
            // Check if running from electron directly (not packaged)
            process.defaultApp === true,
            // Check if app is not packaged (additional safety check)
            !app.isPackaged
        ];

        const devCount = indicators.filter(Boolean).length;
        console.log(`Development indicators found: ${devCount}/7`);
        console.log(`App is packaged: ${app.isPackaged}`);
        console.log(`Process cwd: ${process.cwd()}`);
        console.log(`App path: ${app.getAppPath()}`);

        // If 2 or more indicators are present, consider it development
        // For packaged apps, this should be 0 or 1 (only NODE_ENV might be set)
        return devCount >= 2;
    }

    /**
     * Check for available updates from GitHub releases
     * @returns {Promise<Object>} Update information or null if no update available
     */
    async checkForUpdates() {
        // Prevent updates in development environment
        if (this.isDevelopment) {
            console.log('Update check blocked - Development environment detected');
            throw new Error('Updates are disabled in development environment to protect local changes');
        }
        try {
            console.log('Checking for updates from Azure Blob Storage...');

            // Fetch latest version
            const latestVersion = await this.fetchLatestVersion();
            const currentVersion = this.cleanVersion(this.currentVersion);
            const cleanLatestVersion = this.cleanVersion(latestVersion);

            console.log(`Current version: ${currentVersion}, Latest version: ${cleanLatestVersion}`);

            if (semver.gt(cleanLatestVersion, currentVersion)) {
                // Fetch metadata and release notes for the latest version
                const [metadata, releaseNotes] = await Promise.all([
                    this.fetchVersionMetadata(latestVersion),
                    this.fetchReleaseNotes(latestVersion)
                ]);

                const updateInfo = {
                    available: true,
                    currentVersion: currentVersion,
                    latestVersion: cleanLatestVersion,
                    releaseNotes: releaseNotes,
                    publishedAt: metadata.buildDate,
                    downloadUrl: this.getDownloadUrlFromMetadata(metadata),
                    metadata: metadata,
                    checksum: metadata.checksums?.[this.getPlatformKey()] || null
                };

                console.log('Update available:', updateInfo);
                this.latestRelease = { metadata, releaseNotes };
                return updateInfo;
            } else {
                console.log('No update available');
                return { available: false, currentVersion, latestVersion: cleanLatestVersion };
            }
        } catch (error) {
            console.error('Error checking for updates:', error);
            throw new Error(`Failed to check for updates: ${error.message}`);
        }
    }

    /**
     * Get platform key for current operating system
     * @returns {string} Platform key (windows, macos, linux)
     */
    getPlatformKey() {
        const platform = process.platform;
        switch (platform) {
            case 'win32': return 'windows';
            case 'darwin': return 'macos';
            case 'linux': return 'linux';
            default: return 'windows'; // Default fallback
        }
    }

    /**
     * Get download URL from metadata
     * @param {Object} metadata - Version metadata
     * @returns {string} Download URL for current platform
     */
    getDownloadUrlFromMetadata(metadata) {
        const platformKey = this.getPlatformKey();
        const downloadUrl = metadata.downloadUrls?.[platformKey];

        if (!downloadUrl) {
            throw new Error(`No download available for platform: ${platformKey}`);
        }

        return downloadUrl;
    }

    /**
     * Clean version string to ensure semver compatibility
     * @param {string} version - Version string to clean
     * @returns {string} Cleaned version string
     */
    cleanVersion(version) {
        // Remove 'v' prefix if present and ensure valid semver format
        let cleaned = version.replace(/^v/, '');
        
        // If version doesn't have patch number, add .0
        const parts = cleaned.split('.');
        if (parts.length === 2) {
            cleaned += '.0';
        }

        return cleaned;
    }

    /**
     * Download the update file with checksum verification
     * @param {string} downloadUrl - URL to download from
     * @param {Function} progressCallback - Callback for progress updates
     * @param {string} expectedChecksum - Expected SHA256 checksum (optional)
     * @returns {Promise<string>} Path to downloaded file
     */
    async downloadUpdate(downloadUrl, progressCallback, expectedChecksum = null) {
        if (this.updateInProgress) {
            throw new Error('Update already in progress');
        }

        this.updateInProgress = true;
        this.downloadProgress = 0;

        try {
            console.log(`Downloading update from: ${downloadUrl}`);
            
            const response = await fetch(downloadUrl);
            if (!response.ok) {
                throw new Error(`Download failed: ${response.status} ${response.statusText}`);
            }

            const totalSize = parseInt(response.headers.get('content-length') || '0');
            let downloadedSize = 0;

            // Create download directory and clean up old files
            const tempDir = join(app.getPath('temp'), 'stevi-updates');
            await fs.mkdir(tempDir, { recursive: true });

            // Clean up old update files before downloading new one
            await this.cleanupOldFiles(tempDir);

            // Generate filename from URL
            const filename = downloadUrl.split('/').pop() || 'update-installer';
            const filePath = join(tempDir, filename);

            // Create write stream
            const fileStream = createWriteStream(filePath);

            // Track download progress
            const reader = response.body.getReader();
            
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                downloadedSize += value.length;
                this.downloadProgress = totalSize > 0 ? (downloadedSize / totalSize) * 100 : 0;
                
                if (progressCallback) {
                    progressCallback({
                        progress: this.downloadProgress,
                        downloadedSize,
                        totalSize
                    });
                }
                
                fileStream.write(value);
            }

            fileStream.end();

            console.log(`Update downloaded to: ${filePath}`);

            // Verify checksum if provided
            if (expectedChecksum) {
                console.log('Verifying download integrity...');
                const actualChecksum = await this.calculateFileChecksum(filePath);

                if (actualChecksum.toLowerCase() !== expectedChecksum.toLowerCase()) {
                    // Delete corrupted file
                    await fs.unlink(filePath);
                    throw new Error(
                        `Download verification failed. Expected checksum: ${expectedChecksum}, ` +
                        `Actual checksum: ${actualChecksum}. The download may be corrupted.`
                    );
                }

                console.log('Download integrity verified successfully');
            }

            return filePath;

        } catch (error) {
            console.error('Error downloading update:', error);
            throw error;
        } finally {
            this.updateInProgress = false;
        }
    }

    /**
     * Install the downloaded update
     * @param {string} installerPath - Path to the installer file
     * @returns {Promise<void>}
     */
    async installUpdate(installerPath) {
        try {
            console.log(`Installing update from: ${installerPath}`);

            // Verify file exists
            await fs.access(installerPath);

            // Platform-specific installation
            const platform = process.platform;

            if (platform === 'win32') {
                // On Windows, run the installer with proper parameters
                console.log('Starting Windows installer...');

                // Use spawn to run installer with proper parameters
                const { spawn } = await import('child_process');

                // Run installer silently in background, then quit app
                const installer = spawn(installerPath, ['/S'], {
                    detached: true,
                    stdio: 'ignore'
                });

                installer.unref();

                console.log('Installer started, quitting application...');

                // Schedule cleanup after installer starts (but before app quits)
                setTimeout(async () => {
                    try {
                        await this.cleanup(false); // Remove all temp files since installer is running
                        console.log('Update files cleaned up before app quit');
                    } catch (error) {
                        console.warn('Failed to cleanup before quit:', error.message);
                    }
                    app.quit();
                }, 2000); // Clean up after 2 seconds, quit after 3

            } else if (platform === 'darwin') {
                // On macOS, open the DMG
                console.log('Opening macOS installer...');
                await shell.openPath(installerPath);

                // User will need to manually install
                console.log('DMG opened. User must manually install the update.');

            } else if (platform === 'linux') {
                // On Linux, make AppImage executable and run it
                console.log('Starting Linux installer...');
                await fs.chmod(installerPath, '755');

                const { spawn } = await import('child_process');
                const installer = spawn(installerPath, [], {
                    detached: true,
                    stdio: 'ignore'
                });

                installer.unref();

                setTimeout(() => {
                    app.quit();
                }, 3000);
            }

            console.log('Update installation initiated');

        } catch (error) {
            console.error('Error installing update:', error);
            throw new Error(`Failed to install update: ${error.message}`);
        } finally {
            this.updateInProgress = false;
        }
    }

    /**
     * Get current update progress
     * @returns {number} Progress percentage (0-100)
     */
    getProgress() {
        return this.downloadProgress;
    }

    /**
     * Check if update is in progress
     * @returns {boolean} True if update is in progress
     */
    isUpdateInProgress() {
        return this.updateInProgress;
    }

    /**
     * Get the latest release information
     * @returns {Object|null} Latest release data
     */
    getLatestRelease() {
        return this.latestRelease;
    }

    /**
     * Open the GitHub releases page in the default browser
     */
    openReleasesPage() {
        // Since we're using Azure Blob Storage for updates, open the general releases info
        const url = 'https://github.com/iharc-jordan/stevi_retro/releases';
        shell.openExternal(url);
    }

    /**
     * Override development environment detection (for testing)
     * @param {boolean} isDev - Force development mode on/off
     */
    setDevelopmentMode(isDev) {
        this.isDevelopment = isDev;
        console.log(`Development mode manually set to: ${isDev}`);
    }

    /**
     * Check if updates are allowed
     * @returns {boolean} True if updates are allowed
     */
    areUpdatesAllowed() {
        return !this.isDevelopment;
    }

    /**
     * Get development environment status
     * @returns {boolean} True if in development environment
     */
    isDevelopmentEnvironment() {
        return this.isDevelopment;
    }

    /**
     * Calculate SHA256 checksum of a file
     * @param {string} filePath - Path to the file
     * @returns {Promise<string>} SHA256 checksum in hex format
     */
    async calculateFileChecksum(filePath) {
        return new Promise((resolve, reject) => {
            const hash = createHash('sha256');
            const stream = createReadStream(filePath);

            stream.on('data', (data) => {
                hash.update(data);
            });

            stream.on('end', () => {
                resolve(hash.digest('hex'));
            });

            stream.on('error', (error) => {
                reject(error);
            });
        });
    }

    /**
     * Clean up temporary update files
     * @param {boolean} keepCurrent - Whether to keep the most recent download
     */
    async cleanup(keepCurrent = false) {
        try {
            const tempDir = join(app.getPath('temp'), 'stevi-updates');

            if (!existsSync(tempDir)) {
                console.log('Update temp directory does not exist, nothing to clean');
                return;
            }

            if (keepCurrent) {
                // Keep only the most recent file, remove older ones
                await this.cleanupOldFiles(tempDir);
            } else {
                // Remove entire temp directory
                await fs.rmdir(tempDir, { recursive: true });
                console.log('Update cleanup completed - all temporary files removed');
            }
        } catch (error) {
            console.warn('Error during update cleanup:', error.message);
        }
    }

    /**
     * Clean up old update files while keeping the most recent
     * @param {string} tempDir - Temporary directory path
     */
    async cleanupOldFiles(tempDir) {
        try {
            const files = await fs.readdir(tempDir);

            if (files.length <= 1) {
                console.log('Only one or no update files found, nothing to clean');
                return;
            }

            // Get file stats and sort by modification time (newest first)
            const fileStats = await Promise.all(
                files.map(async (file) => {
                    const filePath = join(tempDir, file);
                    const stats = await fs.stat(filePath);
                    return {
                        name: file,
                        path: filePath,
                        mtime: stats.mtime,
                        size: stats.size
                    };
                })
            );

            // Sort by modification time (newest first)
            fileStats.sort((a, b) => b.mtime - a.mtime);

            // Keep the newest file, remove the rest
            const filesToRemove = fileStats.slice(1);
            let totalSizeFreed = 0;

            for (const file of filesToRemove) {
                try {
                    await fs.unlink(file.path);
                    totalSizeFreed += file.size;
                    console.log(`Removed old update file: ${file.name} (${this.formatFileSize(file.size)})`);
                } catch (error) {
                    console.warn(`Failed to remove file ${file.name}:`, error.message);
                }
            }

            if (totalSizeFreed > 0) {
                console.log(`Update cleanup completed - freed ${this.formatFileSize(totalSizeFreed)} of storage`);
            }
        } catch (error) {
            console.warn('Error during selective cleanup:', error.message);
        }
    }

    /**
     * Format file size for human-readable display
     * @param {number} bytes - File size in bytes
     * @returns {string} Formatted size string
     */
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    /**
     * Perform startup cleanup to remove any leftover update files
     */
    async performStartupCleanup() {
        try {
            console.log('Performing startup cleanup of update files...');

            const tempDir = join(app.getPath('temp'), 'stevi-updates');

            if (!existsSync(tempDir)) {
                return;
            }

            const files = await fs.readdir(tempDir);

            if (files.length === 0) {
                return;
            }

            // Check if any files are older than 24 hours
            const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
            let oldFilesFound = false;
            let totalSizeFreed = 0;

            for (const file of files) {
                const filePath = join(tempDir, file);
                try {
                    const stats = await fs.stat(filePath);

                    if (stats.mtime.getTime() < oneDayAgo) {
                        await fs.unlink(filePath);
                        totalSizeFreed += stats.size;
                        oldFilesFound = true;
                        console.log(`Removed old update file from startup cleanup: ${file} (${this.formatFileSize(stats.size)})`);
                    }
                } catch (error) {
                    console.warn(`Error processing file ${file} during startup cleanup:`, error.message);
                }
            }

            if (oldFilesFound) {
                console.log(`Startup cleanup completed - freed ${this.formatFileSize(totalSizeFreed)} of storage`);
            } else {
                console.log('Startup cleanup completed - no old files found');
            }
        } catch (error) {
            console.warn('Error during startup cleanup:', error.message);
        }
    }

    /**
     * Set up periodic cleanup to run every 24 hours
     */
    setupPeriodicCleanup() {
        // Clear any existing interval first
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }

        // Clean up every 24 hours (86400000 ms)
        const cleanupIntervalMs = 24 * 60 * 60 * 1000;

        this.cleanupInterval = setInterval(async () => {
            try {
                console.log('Running periodic update cleanup...');
                await this.performStartupCleanup();
            } catch (error) {
                console.warn('Periodic cleanup failed:', error.message);
            }
        }, cleanupIntervalMs);

        console.log('Periodic update cleanup scheduled (every 24 hours)');
    }

    /**
     * Stop periodic cleanup and clean up resources
     */
    stopPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
            console.log('Periodic update cleanup stopped');
        }
    }

    /**
     * Get storage usage information for update files
     * @returns {Promise<Object>} Storage usage information
     */
    async getStorageUsage() {
        try {
            const tempDir = join(app.getPath('temp'), 'stevi-updates');

            if (!existsSync(tempDir)) {
                return {
                    totalFiles: 0,
                    totalSize: 0,
                    formattedSize: '0 B',
                    files: []
                };
            }

            const files = await fs.readdir(tempDir);
            let totalSize = 0;
            const fileDetails = [];

            for (const file of files) {
                const filePath = join(tempDir, file);
                try {
                    const stats = await fs.stat(filePath);
                    totalSize += stats.size;
                    fileDetails.push({
                        name: file,
                        size: stats.size,
                        formattedSize: this.formatFileSize(stats.size),
                        modified: stats.mtime
                    });
                } catch (error) {
                    console.warn(`Error getting stats for ${file}:`, error.message);
                }
            }

            return {
                totalFiles: files.length,
                totalSize,
                formattedSize: this.formatFileSize(totalSize),
                files: fileDetails.sort((a, b) => b.modified - a.modified)
            };
        } catch (error) {
            console.warn('Error getting storage usage:', error.message);
            return {
                totalFiles: 0,
                totalSize: 0,
                formattedSize: '0 B',
                files: []
            };
        }
    }
}
