// Warrant Event Modal
// Handles adding warrant issued/executed events

import { addEventTemplates } from '../../../templates/add-event-templates.js';

export class WarrantModal {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.episodeId = null;
        this.action = 'issue'; // 'issue' or 'execute'
    }
    
    async show(episodeId, action = 'issue') {
        this.episodeId = episodeId;
        this.action = action;
        
        try {
            // Create modal
            this.modal = this.justice.ui.createModal({
                id: 'add-warrant-modal',
                title: action === 'issue' ? 'Issue Warrant' : 'Execute Warrant',
                size: 'medium'
            });
            
            // Render form
            this.render();
            
            // Show modal
            this.justice.ui.showModal(this.modal);
            
            // Setup event handlers
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to show warrant modal:', error);
            this.justice.ui.showDialog('Error', `Failed to open warrant form: ${error.message}`, 'error');
        }
    }
    
    render() {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = addEventTemplates.warrantForm({
            episodeId: this.episodeId,
            action: this.action,
            defaultDateTime: new Date().toISOString().slice(0, 16)
        });
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#warrant-form');
        
        // Save button
        const saveBtn = this.modal.querySelector('#save-warrant-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveWarrant(form);
            });
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-warrant-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }
    
    async saveWarrant(form) {
        try {
            const formData = new FormData(form);
            
            const eventData = {
                event_dt: formData.get('warrant_dt'),
                payload: {
                    type: formData.get('warrant_type'),
                    notes: formData.get('notes')
                }
            };
            
            // Add action-specific fields
            if (this.action === 'execute') {
                eventData.payload.original_warrant_id = formData.get('original_warrant_id');
                eventData.payload.executing_officer = formData.get('executing_officer');
                eventData.payload.location = formData.get('execution_location');
            } else {
                eventData.payload.issuing_court = formData.get('issuing_court');
                eventData.payload.reason = formData.get('reason');
            }
            
            // Validate required fields
            if (!eventData.event_dt) {
                this.justice.ui.showDialog('Error', 'Warrant date/time is required.', 'error');
                return;
            }
            
            if (!eventData.payload.type) {
                this.justice.ui.showDialog('Error', 'Warrant type is required.', 'error');
                return;
            }
            
            // Show loading state
            const saveBtn = this.modal.querySelector('#save-warrant-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
            
            // Determine event type
            const eventType = this.action === 'issue' ? 'WARRANT_ISSUED' : 'WARRANT_EXECUTED';
            
            // Create the event
            const newEvent = await this.justice.api.addEvent(
                this.episodeId,
                eventType,
                eventData.event_dt,
                eventData.payload
            );
            
            // Update state
            this.justice.state.addEvent(newEvent);
            
            // Close modal
            this.close();
            
            // Show success
            const actionText = this.action === 'issue' ? 'issued' : 'executed';
            this.justice.ui.showDialog('Success', `Warrant ${actionText} successfully!`, 'success');
            
            // Refresh views
            await this.refreshViews();
            
        } catch (error) {
            console.error('Failed to save warrant:', error);
            this.justice.ui.showDialog('Error', `Failed to save warrant: ${error.message}`, 'error');
            
            // Reset button
            const saveBtn = this.modal.querySelector('#save-warrant-btn');
            if (saveBtn) {
                const actionText = this.action === 'issue' ? 'Issue' : 'Execute';
                saveBtn.textContent = `Save ${actionText} Warrant`;
                saveBtn.disabled = false;
            }
        }
    }
    
    async refreshViews() {
        // Refresh timeline if visible
        if (this.justice.timelineView && this.justice.timelineView.container) {
            await this.justice.timelineView.refresh();
        }
        
        // Refresh status ribbon if visible
        if (this.justice.statusRibbon && this.justice.statusRibbon.container) {
            await this.justice.statusRibbon.refresh();
        }
    }
    
    close() {
        if (this.modal) {
            this.justice.ui.closeModal(this.modal.id);
            this.modal = null;
        }
        this.episodeId = null;
        this.action = 'issue';
    }
}
