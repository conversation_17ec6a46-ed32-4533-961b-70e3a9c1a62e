// Security Headers and CSP Manager for S.T.E.V.I DOS Electron App
// Implements client-side security headers and Content Security Policy

export class SecurityHeadersManager {
    constructor() {
        this.cspDirectives = {
            'default-src': ["'self'"],
            'script-src': [
                "'self'",
                "'unsafe-inline'", // Required for dynamic script loading
                'https://cdn.jsdelivr.net', // Supabase CDN
                'https://maps.googleapis.com', // Google Maps
                'https://unpkg.com' // Chart.js and other libraries
            ],
            'style-src': [
                "'self'",
                "'unsafe-inline'", // Required for dynamic styles
                'https://fonts.googleapis.com'
            ],
            'font-src': [
                "'self'",
                'https://fonts.gstatic.com'
            ],
            'img-src': [
                "'self'",
                'data:', // For base64 images
                'blob:', // For generated images
                'https://*.supabase.co', // Supabase storage
                'https://maps.googleapis.com', // Google Maps
                'https://maps.gstatic.com' // Google Maps static
            ],
            'connect-src': [
                "'self'",
                'https://*.supabase.co', // Supabase API
                'https://maps.googleapis.com', // Google Maps API
                'https://weather.googleapis.com', // Google Weather API
                'https://generativelanguage.googleapis.com', // Google Gemini API
                'wss://*.supabase.co' // Supabase realtime
            ],
            'frame-src': [
                "'none'"
            ],
            'object-src': [
                "'none'"
            ],
            'base-uri': [
                "'self'"
            ],
            'form-action': [
                "'self'"
            ]
        };

        this.securityHeaders = {
            'X-Content-Type-Options': 'nosniff',
            // 'X-Frame-Options': 'DENY', // Cannot be set via meta tag, not needed in Electron
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        };

        this.initialized = false;
    }

    /**
     * Initialize security headers and CSP
     */
    initialize() {
        if (this.initialized) {
            return;
        }

        try {
            this.applyCsp();
            this.applySecurityHeaders();
            this.setupSecurityEventListeners();
            this.initialized = true;
            
            console.log('✅ Security headers and CSP initialized');
        } catch (error) {
            console.error('❌ Failed to initialize security headers:', error);
        }
    }

    /**
     * Apply Content Security Policy
     */
    applyCsp() {
        // Build CSP string
        const cspString = Object.entries(this.cspDirectives)
            .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
            .join('; ');

        // Apply CSP via meta tag (for Electron renderer)
        let cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (!cspMeta) {
            cspMeta = document.createElement('meta');
            cspMeta.setAttribute('http-equiv', 'Content-Security-Policy');
            document.head.appendChild(cspMeta);
        }
        cspMeta.setAttribute('content', cspString);

        console.log('🛡️ CSP applied:', cspString);
    }

    /**
     * Apply additional security headers via meta tags
     */
    applySecurityHeaders() {
        Object.entries(this.securityHeaders).forEach(([header, value]) => {
            let meta = document.querySelector(`meta[http-equiv="${header}"]`);
            if (!meta) {
                meta = document.createElement('meta');
                meta.setAttribute('http-equiv', header);
                document.head.appendChild(meta);
            }
            meta.setAttribute('content', value);
        });

        console.log('🛡️ Security headers applied');
    }

    /**
     * Setup security event listeners
     */
    setupSecurityEventListeners() {
        // CSP violation reporting
        document.addEventListener('securitypolicyviolation', (event) => {
            console.warn('🚨 CSP Violation:', {
                directive: event.violatedDirective,
                blockedURI: event.blockedURI,
                lineNumber: event.lineNumber,
                sourceFile: event.sourceFile
            });

            // Log to audit system if available
            if (window.app && window.app.auditLogger) {
                window.app.auditLogger.logSecurityEvent('CSP_VIOLATION', {
                    directive: event.violatedDirective,
                    blockedURI: event.blockedURI,
                    timestamp: new Date().toISOString()
                });
            }
        });

        // Monitor for suspicious script injections
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check for suspicious script tags
                            if (node.tagName === 'SCRIPT' && !this.isAllowedScript(node)) {
                                console.warn('🚨 Suspicious script detected:', node);
                                this.handleSuspiciousScript(node);
                            }
                            
                            // Check for suspicious iframes
                            if (node.tagName === 'IFRAME') {
                                console.warn('🚨 Iframe injection detected:', node);
                                this.handleSuspiciousIframe(node);
                            }
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('🛡️ Security event listeners setup');
    }

    /**
     * Check if a script is allowed
     */
    isAllowedScript(scriptElement) {
        const src = scriptElement.src;
        if (!src) {
            // Inline scripts - check if they're from trusted sources
            return scriptElement.hasAttribute('data-trusted');
        }

        // Check against allowed script sources
        const allowedSources = this.cspDirectives['script-src'];
        return allowedSources.some(source => {
            if (source === "'self'") {
                return src.startsWith(window.location.origin);
            }
            if (source.startsWith('https://')) {
                return src.startsWith(source);
            }
            return false;
        });
    }

    /**
     * Handle suspicious script injection
     */
    handleSuspiciousScript(scriptElement) {
        // Remove the script
        scriptElement.remove();
        
        // Log security event
        console.error('🚨 Removed suspicious script:', scriptElement.src || 'inline');
        
        if (window.app && window.app.auditLogger) {
            window.app.auditLogger.logSecurityEvent('SCRIPT_INJECTION_BLOCKED', {
                src: scriptElement.src || 'inline',
                content: scriptElement.textContent?.substring(0, 100),
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Handle suspicious iframe injection
     */
    handleSuspiciousIframe(iframeElement) {
        // Remove the iframe
        iframeElement.remove();
        
        // Log security event
        console.error('🚨 Removed suspicious iframe:', iframeElement.src);
        
        if (window.app && window.app.auditLogger) {
            window.app.auditLogger.logSecurityEvent('IFRAME_INJECTION_BLOCKED', {
                src: iframeElement.src,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Add allowed source to CSP directive
     */
    addAllowedSource(directive, source) {
        if (this.cspDirectives[directive]) {
            if (!this.cspDirectives[directive].includes(source)) {
                this.cspDirectives[directive].push(source);
                this.applyCsp(); // Reapply CSP
                console.log(`🛡️ Added ${source} to ${directive}`);
            }
        }
    }

    /**
     * Remove allowed source from CSP directive
     */
    removeAllowedSource(directive, source) {
        if (this.cspDirectives[directive]) {
            const index = this.cspDirectives[directive].indexOf(source);
            if (index > -1) {
                this.cspDirectives[directive].splice(index, 1);
                this.applyCsp(); // Reapply CSP
                console.log(`🛡️ Removed ${source} from ${directive}`);
            }
        }
    }

    /**
     * Get current CSP configuration
     */
    getCurrentCsp() {
        return { ...this.cspDirectives };
    }

    /**
     * Validate current security configuration
     */
    validateSecurity() {
        const issues = [];
        const warnings = [];

        // Check if CSP is applied
        const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
        if (!cspMeta) {
            issues.push('CSP not applied');
        } else {
            console.log('✅ CSP is active');
        }

        // Check for HTTPS
        if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
            warnings.push('Application not running over HTTPS');
        } else {
            console.log('✅ Secure protocol in use');
        }

        // Check for mixed content
        if (window.location.protocol === 'https:') {
            const insecureElements = document.querySelectorAll('[src^="http:"], [href^="http:"]');
            if (insecureElements.length > 0) {
                warnings.push(`${insecureElements.length} insecure resources detected`);
            } else {
                console.log('✅ No mixed content detected');
            }
        }

        return {
            valid: issues.length === 0,
            issues,
            warnings
        };
    }
}
