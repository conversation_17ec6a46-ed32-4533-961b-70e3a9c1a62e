export const addressManagementTemplates = {
    addressListView: (addresses) => {
        return `
            <style>
                .address-list-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .address-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .address-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .address-controls {
                    display: flex;
                    gap: 1rem;
                }
                .address-list {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    margin-bottom: 2rem;
                }
                .address-list-header {
                    display: grid;
                    grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr;
                    gap: 1rem;
                    padding: 1rem;
                    background-color: rgba(255, 0, 0, 0.1);
                    border-bottom: 1px solid #ff0000;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .address-list-item {
                    display: grid;
                    grid-template-columns: 2fr 1.5fr 1fr 1fr 1.5fr;
                    gap: 1rem;
                    padding: 1rem;
                    border-bottom: 1px dashed rgba(255, 0, 0, 0.3);
                    transition: background-color 0.2s;
                }
                .address-list-item:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .address-street {
                    font-weight: bold;
                    color: #ff0000;
                }
                .address-city {
                    color: #cccccc;
                }
                .address-province {
                    color: #999999;
                    text-align: center;
                }
                .address-type {
                    color: #999999;
                    text-align: center;
                }
                .address-actions {
                    display: flex;
                    gap: 0.5rem;
                }
                .search-container {
                    margin-bottom: 1.5rem;
                }
                .search-input {
                    width: 100%;
                    padding: 0.75rem;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1rem;
                }
                .search-input::placeholder {
                    color: rgba(255, 0, 0, 0.5);
                }
                .no-addresses {
                    text-align: center;
                    padding: 3rem;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.2rem;
                    line-height: 1.6;
                }
                .secondary-button {
                    background-color: transparent;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .secondary-button:hover {
                    background-color: #ff0000;
                    color: #000000;
                }
                .action-button {
                    background-color: #ff0000;
                    border: 1px solid #ff0000;
                    color: #000000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .action-button:hover {
                    background-color: transparent;
                    color: #ff0000;
                }
                .action-button.primary {
                    font-weight: bold;
                }
            </style>
            
            <div class="address-list-container">
                <div class="address-header">
                    <div class="address-title">ADDRESS DIRECTORY</div>
                    <div class="address-controls">
                        <button class="secondary-button" data-action="back-to-records">← Back to Records</button>
                        <button class="action-button primary" data-action="add-address">+ Add Address</button>
                    </div>
                </div>
                
                <div class="search-container">
                    <input type="text" 
                           class="search-input" 
                           placeholder="Search addresses by street, city, province, or type..."
                           data-action="search-addresses-input">
                </div>
                
                <div class="address-list" id="address-list">
                    ${addresses && addresses.length > 0 
                        ? `<div class="address-list-header">
                            <div>Street Address</div>
                            <div>City</div>
                            <div>Province</div>
                            <div>Type</div>
                            <div>Actions</div>
                           </div>
                           ${addresses.map(address => addressManagementTemplates.addressListItem(address)).join('')}`
                        : '<div class="no-addresses">NO ADDRESSES FOUND<br><br>Click "Add Address" to create the first entry.</div>'
                    }
                </div>
            </div>
        `;
    },

    addressListItem: (address) => {
        const streetAddress = address.street_address || 'Unknown Address';
        const city = address.city || 'Unknown City';
        const province = address.province || 'N/A';
        const addressType = address.address_type || 'Address';

        return `
            <div class="address-list-item" data-address-id="${address.id}">
                <div class="address-street">${streetAddress}</div>
                <div class="address-city">${city}</div>
                <div class="address-province">${province}</div>
                <div class="address-type">${addressType}</div>
                <div class="address-actions">
                    <button class="secondary-button" data-action="view-address-detail" data-address-id="${address.id}">View</button>
                    <button class="action-button" data-action="edit-address" data-address-id="${address.id}">Edit</button>
                </div>
            </div>
        `;
    },

};