export const organizationSearchTemplates = {
    searchModal: () => {
        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🔍 SEARCH ORGANIZATIONS</h3>
                    <button class="close-btn" data-action="close-modal">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <label>Organization Name:</label>
                            <input type="text" id="search-org-name" placeholder="Enter organization name...">
                        </div>
                        <div class="form-row">
                            <label>Organization Type:</label>
                            <select id="search-org-type">
                                <option value="">All Types</option>
                                <option value="government">Government</option>
                                <option value="non_profit">Non-Profit</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="shelter">Shelter</option>
                                <option value="food_bank">Food Bank</option>
                                <option value="mental_health">Mental Health</option>
                                <option value="addiction_services">Addiction Services</option>
                                <option value="housing">Housing</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label>City:</label>
                            <input type="text" id="search-org-city" placeholder="Enter city...">
                        </div>
                        <div class="form-row">
                            <label>Services:</label>
                            <input type="text" id="search-org-services" placeholder="Enter services offered...">
                        </div>
                        <div class="form-actions">
                            <button id="execute-organization-search">Search</button>
                            <button id="clear-organization-search">Clear</button>
                        </div>
                    </div>
                    <div id="organization-search-results" class="search-results"></div>
                </div>
            </div>
        `;
    },

    searchResults: (organizations) => {
        if (!organizations || organizations.length === 0) {
            return `
                <div class="no-results">
                    <p>No organizations found matching your search criteria.</p>
                </div>
            `;
        }

        return `
            <div class="results-header">
                <h4>Search Results (${organizations.length} found)</h4>
            </div>
            <div class="results-grid">
                ${organizations.map(org => organizationSearchTemplates.organizationCard(org)).join('')}
            </div>
        `;
    },

    organizationCard: (organization) => {
        return `
            <div class="result-card" data-organization-id="${organization.id}">
                <div class="card-header">
                    <strong>🏢 ${organization.name}</strong>
                    ${organization.organization_type ? `<span class="org-type">${organization.organization_type.replace('_', ' ').toUpperCase()}</span>` : ''}
                </div>
                <div class="card-body">
                    ${organization.phone ? `<div class="contact-info">📞 ${organization.phone}</div>` : ''}
                    ${organization.email ? `<div class="contact-info">📧 ${organization.email}</div>` : ''}
                    ${organization.website ? `<div class="contact-info">🌐 ${organization.website}</div>` : ''}
                    ${organization.address ? `<div class="address-info">📍 ${organization.address}</div>` : ''}
                    ${organization.services ? `<div class="services-info">🔧 ${organization.services}</div>` : ''}
                    <div class="metadata">
                        Created: ${new Date(organization.created_at).toLocaleDateString()}
                        ${organization.created_by ? ` by ${organization.created_by}` : ''}
                    </div>
                </div>
                <div class="card-actions">
                    <button data-action="view-organization-details" data-organization-id="${organization.id}">View Details</button>
                    <button data-action="edit-organization" data-organization-id="${organization.id}">Edit</button>
                </div>
            </div>
        `;
    },

    organizationDetailModal: (organization) => {
        return `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🏢 ORGANIZATION DETAILS</h3>
                    <button class="close-btn" data-action="close-modal">×</button>
                </div>
                <div class="modal-body">
                    <div class="detail-section">
                        <h4>Organization Information</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Name:</label>
                                <span>${organization.name || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Type:</label>
                                <span>${organization.organization_type ? organization.organization_type.replace('_', ' ') : 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Status:</label>
                                <span>${organization.status || 'Active'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="detail-section">
                        <h4>Contact Information</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Phone:</label>
                                <span>${organization.phone || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Email:</label>
                                <span>${organization.email || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Website:</label>
                                <span>${organization.website || 'Not specified'}</span>
                            </div>
                            ${organization.address ? `
                            <div class="detail-item full-width">
                                <label>Address:</label>
                                <span>${organization.address}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="detail-section">
                        <h4>Services & Operations</h4>
                        <div class="detail-grid">
                            ${organization.services ? `
                            <div class="detail-item full-width">
                                <label>Services Offered:</label>
                                <span>${organization.services}</span>
                            </div>
                            ` : ''}
                            ${organization.hours_of_operation ? `
                            <div class="detail-item full-width">
                                <label>Hours of Operation:</label>
                                <span>${organization.hours_of_operation}</span>
                            </div>
                            ` : ''}
                            ${organization.notes ? `
                            <div class="detail-item full-width">
                                <label>Notes:</label>
                                <span>${organization.notes}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="detail-section">
                        <h4>Record Information</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <label>Created:</label>
                                <span>${new Date(organization.created_at).toLocaleString()}</span>
                            </div>
                            <div class="detail-item">
                                <label>Created By:</label>
                                <span>${organization.created_by || 'Unknown'}</span>
                            </div>
                            ${organization.updated_at ? `
                            <div class="detail-item">
                                <label>Last Updated:</label>
                                <span>${new Date(organization.updated_at).toLocaleString()}</span>
                            </div>
                            ` : ''}
                            ${organization.updated_by ? `
                            <div class="detail-item">
                                <label>Updated By:</label>
                                <span>${organization.updated_by}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button data-action="edit-organization" data-organization-id="${organization.id}">Edit Organization</button>
                        <button data-action="close-modal">Close</button>
                    </div>
                </div>
            </div>
        `;
    }
};