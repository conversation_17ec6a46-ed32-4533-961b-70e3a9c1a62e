/**
 * Incident File Manager
 * Handles incident file uploads, attachments, and file management
 * Extracted from app.js - manages ~5 file-related functions
 */

import { BaseManager } from '../../shared/base-manager.js';

export class IncidentFileManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.uploadedFiles = [];
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.allowedFileTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf',
            'text/plain',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'video/mp4', 'video/quicktime', 'video/x-msvideo'
        ];
    }

    setupFileUpload() {
        console.log('Setting up incident file upload...');
        
        // Set up file input handler
        const fileInput = document.getElementById('incident-files');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }

        // Set up drop zone
        const dropZone = document.getElementById('file-drop-zone');
        if (dropZone) {
            this.setupDropZone(dropZone);
        }

        // Set up file list management
        this.setupFileListHandlers();
        
        console.log('File upload setup complete');
    }

    setupDropZone(dropZone) {
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('drag-over');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('drag-over');
            });
        });

        // Handle dropped files
        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileSelection(files);
        });

        // Make drop zone clickable
        dropZone.addEventListener('click', () => {
            const fileInput = document.getElementById('incident-files');
            if (fileInput) {
                fileInput.click();
            }
        });
    }

    setupFileListHandlers() {
        // Set up remove file buttons (delegated event handling)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-file-btn')) {
                const fileIndex = parseInt(e.target.dataset.fileIndex);
                this.removeFile(fileIndex);
            }
        });
    }

    async handleFileSelection(files) {
        try {
            console.log('Handling file selection:', files.length, 'files');

            if (files.length === 0) return;

            // Validate and process each file
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                // Validate file
                const validationResult = this.validateFile(file);
                if (!validationResult.isValid) {
                    this.ui.showDialog('File Validation Error', 
                        `File "${file.name}": ${validationResult.error}`, 'error');
                    continue;
                }

                // Check for duplicates
                const isDuplicate = this.uploadedFiles.some(existingFile => 
                    existingFile.name === file.name && 
                    existingFile.size === file.size
                );

                if (isDuplicate) {
                    this.ui.showDialog('Duplicate File', 
                        `File "${file.name}" is already selected.`, 'warning');
                    continue;
                }

                // Process file
                const processedFile = await this.processFile(file);
                this.uploadedFiles.push(processedFile);
            }

            // Update file list display
            this.updateFileListDisplay();

            // Show success message
            if (files.length === 1) {
                this.uiUtilities.showToast('File added successfully', 'success');
            } else {
                this.uiUtilities.showToast(`${files.length} files added successfully`, 'success');
            }

        } catch (error) {
            console.error('Error handling file selection:', error);
            this.ui.showDialog('Error', `Failed to process files: ${error.message}`, 'error');
        }
    }

    validateFile(file) {
        // Check file size
        if (file.size > this.maxFileSize) {
            return {
                isValid: false,
                error: `File size (${this.formatFileSize(file.size)}) exceeds maximum allowed size (${this.formatFileSize(this.maxFileSize)})`
            };
        }

        // Check file type
        if (!this.allowedFileTypes.includes(file.type)) {
            return {
                isValid: false,
                error: `File type "${file.type}" is not allowed. Allowed types: images, PDF, text, Word documents, videos`
            };
        }

        // Check filename
        if (file.name.length > 255) {
            return {
                isValid: false,
                error: 'Filename is too long (maximum 255 characters)'
            };
        }

        return { isValid: true };
    }

    async processFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const processedFile = {
                    id: Date.now() + Math.random(), // Temporary ID
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    lastModified: file.lastModified,
                    data: e.target.result, // Base64 data
                    file: file, // Keep original file object for upload
                    preview: null,
                    uploaded: false
                };

                // Generate preview for images
                if (file.type.startsWith('image/')) {
                    processedFile.preview = e.target.result;
                }

                resolve(processedFile);
            };

            reader.onerror = () => {
                reject(new Error(`Failed to read file: ${file.name}`));
            };

            reader.readAsDataURL(file);
        });
    }

    updateFileListDisplay() {
        const fileList = document.getElementById('uploaded-files-list');
        if (!fileList) return;

        if (this.uploadedFiles.length === 0) {
            fileList.innerHTML = '<div class="no-files">No files attached</div>';
            return;
        }

        const filesHTML = this.uploadedFiles.map((file, index) => `
            <div class="uploaded-file-item" data-file-index="${index}">
                <div class="file-info">
                    ${file.preview ? 
                        `<img src="${file.preview}" alt="${file.name}" class="file-preview">` :
                        `<div class="file-icon">${this.getFileIcon(file.type)}</div>`
                    }
                    <div class="file-details">
                        <div class="file-name" title="${file.name}">${file.name}</div>
                        <div class="file-meta">
                            <span class="file-size">${this.formatFileSize(file.size)}</span>
                            <span class="file-type">${this.getFileTypeLabel(file.type)}</span>
                            ${file.uploaded ? 
                                '<span class="upload-status uploaded">✓ Uploaded</span>' :
                                '<span class="upload-status pending">⏳ Pending</span>'
                            }
                        </div>
                    </div>
                </div>
                <div class="file-actions">
                    ${!file.uploaded ? 
                        `<button class="remove-file-btn secondary-button" data-file-index="${index}">
                            Remove
                        </button>` :
                        `<button class="download-file-btn secondary-button" data-file-index="${index}">
                            Download
                        </button>`
                    }
                </div>
            </div>
        `).join('');

        fileList.innerHTML = filesHTML;

        // Update file count display
        const fileCount = document.getElementById('files-count');
        if (fileCount) {
            fileCount.textContent = this.uploadedFiles.length;
        }
    }

    removeFile(fileIndex) {
        if (fileIndex < 0 || fileIndex >= this.uploadedFiles.length) return;

        const file = this.uploadedFiles[fileIndex];
        
        // Show confirmation for uploaded files
        if (file.uploaded) {
            if (!confirm(`Are you sure you want to remove "${file.name}"? This will delete the file from the server.`)) {
                return;
            }
        }

        // Remove from array
        this.uploadedFiles.splice(fileIndex, 1);

        // Update display
        this.updateFileListDisplay();

        // Show success message
        this.uiUtilities.showToast(`File "${file.name}" removed`, 'info');
    }

    async uploadIncidentFiles(incidentId) {
        try {
            console.log('Uploading incident files for incident:', incidentId);

            const filesToUpload = this.uploadedFiles.filter(file => !file.uploaded);
            
            if (filesToUpload.length === 0) {
                console.log('No files to upload');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Upload each file
            const uploadResults = [];
            
            for (let i = 0; i < filesToUpload.length; i++) {
                const file = filesToUpload[i];
                
                try {
                    console.log(`Uploading file ${i + 1}/${filesToUpload.length}: ${file.name}`);
                    
                    // Update UI to show progress
                    this.updateFileUploadProgress(file.id, `Uploading... (${i + 1}/${filesToUpload.length})`);
                    
                    // Upload file to Azure Blob Storage or similar
                    const uploadResult = await this.uploadFileToStorage(file, incidentId);
                    
                    // Create database record
                    const attachmentData = {
                        incident_id: incidentId,
                        filename: file.name,
                        original_filename: file.name,
                        file_size: file.size,
                        file_type: file.type,
                        file_path: uploadResult.file_path,
                        file_url: uploadResult.file_url || null,
                        storage_provider: uploadResult.storage_provider || 'azure_blob',
                        uploaded_at: new Date().toISOString(),
                        ...this.addCreateMetadata()
                    };

                    const attachment = await this.data.insert('incident_attachments', attachmentData);
                    
                    // Update file status
                    const uploadedFileIndex = this.uploadedFiles.findIndex(f => f.id === file.id);
                    if (uploadedFileIndex !== -1) {
                        this.uploadedFiles[uploadedFileIndex].uploaded = true;
                        this.uploadedFiles[uploadedFileIndex].attachment_id = attachment.id;
                        this.uploadedFiles[uploadedFileIndex].file_url = uploadResult.file_url;
                    }
                    
                    uploadResults.push({ file: file, success: true, attachment: attachment });
                    
                    console.log(`File uploaded successfully: ${file.name}`);
                    
                } catch (error) {
                    console.error(`Error uploading file ${file.name}:`, error);
                    uploadResults.push({ file: file, success: false, error: error.message });
                    
                    // Update UI to show error
                    this.updateFileUploadProgress(file.id, 'Upload failed');
                }
            }

            // Update display
            this.updateFileListDisplay();

            // Show results
            const successCount = uploadResults.filter(r => r.success).length;
            const failureCount = uploadResults.length - successCount;

            if (failureCount === 0) {
                this.uiUtilities.showToast(`${successCount} file(s) uploaded successfully`, 'success');
            } else {
                this.ui.showDialog('Upload Results', 
                    `${successCount} file(s) uploaded successfully\n${failureCount} file(s) failed to upload`, 
                    failureCount > successCount ? 'error' : 'warning');
            }

            // Log the file uploads
            await this.logFileUploadActivity(incidentId, uploadResults);

            console.log('File upload process completed');

        } catch (error) {
            console.error('Error uploading incident files:', error);
            this.ui.showDialog('Error', `Failed to upload files: ${error.message}`, 'error');
        }
    }

    async uploadIncidentFiles(incidentId) {
        if (!this.uploadedFiles || this.uploadedFiles.length === 0) return;

        try {
            // Check if IPC is available (Electron environment)
            const { ipcRenderer } = window.require ? window.require('electron') : null;
            if (!ipcRenderer) {
                throw new Error('File upload not available in this environment');
            }

            // Prepare files for upload (convert to serializable format)
            const filesToUpload = await Promise.all(
                this.uploadedFiles.map(async (uploadedFile) => {
                    const file = uploadedFile.file;
                    const arrayBuffer = await file.arrayBuffer();
                    return {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        data: Array.from(new Uint8Array(arrayBuffer)) // Convert to array for IPC
                    };
                })
            );

            // Listen for progress updates
            ipcRenderer.on('upload-progress', (event, progress) => {
                const statusElement = document.getElementById(`status-${this.uploadedFiles[progress.fileIndex]?.id}`);
                if (statusElement) {
                    statusElement.textContent = `Uploading... ${progress.percentage}%`;
                }
            });

            // Upload files via IPC to main process
            const uploadResults = await ipcRenderer.invoke('upload-files-to-azure', filesToUpload, incidentId);

            // Save file metadata to database
            for (const fileResult of uploadResults.successful) {
                await this.data.insert('media', {
                    record_type: 'incident',
                    record_id: incidentId,
                    filename: fileResult.fileName,
                    stored_at: fileResult.url,
                    description: `Incident attachment: ${fileResult.fileName}`,
                    file_size: fileResult.fileSize,
                    content_type: fileResult.contentType,
                    uploaded_at: fileResult.uploadedAt
                });
            }

            // Clean up progress listener
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.removeAllListeners('upload-progress');
            }

            // Show upload results
            if (uploadResults.failed.length > 0) {
                console.warn('Some files failed to upload:', uploadResults.failed);
                this.ui.showDialog(
                    'Upload Warning',
                    `${uploadResults.totalUploaded} files uploaded successfully. ${uploadResults.totalFailed} files failed to upload.`,
                    'warning'
                );
            }

        } catch (error) {
            console.error('Error uploading files:', error);
            this.ui.showDialog('Upload Error', `Files could not be uploaded: ${error.message}`, 'error');

            // Clean up progress listener on error too
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.removeAllListeners('upload-progress');
            }
        }
    }

    async loadIncidentAttachments(incidentId) {
        try {
            // Get media records for this incident
            const attachments = await this.data.search('media', {
                record_type: 'incident',
                record_id: incidentId
            });

            return attachments || [];
        } catch (error) {
            console.error('Error loading incident attachments:', error);
            return [];
        }
    }

    async downloadIncidentAttachment(attachment) {
        try {
            console.log('Downloading attachment:', attachment.filename);

            if (!attachment.file_url && !attachment.file_path) {
                this.ui.showDialog('Error', 'File location not found.', 'error');
                return;
            }

            // For web downloads, we can use the file URL directly
            if (attachment.file_url) {
                const link = document.createElement('a');
                link.href = attachment.file_url;
                link.download = attachment.original_filename || attachment.filename;
                link.target = '_blank';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                this.uiUtilities.showToast('Download started', 'success');
            } else {
                // For local files or other storage, implement appropriate download logic
                this.ui.showDialog('Info', 'File download not available for this attachment type.', 'info');
            }

        } catch (error) {
            console.error('Error downloading attachment:', error);
            this.ui.showDialog('Error', `Failed to download file: ${error.message}`, 'error');
        }
    }

    updateFileUploadProgress(fileId, status) {
        const fileItems = document.querySelectorAll('.uploaded-file-item');
        fileItems.forEach(item => {
            const fileIndex = parseInt(item.dataset.fileIndex);
            const file = this.uploadedFiles[fileIndex];
            
            if (file && file.id === fileId) {
                const statusElement = item.querySelector('.upload-status');
                if (statusElement) {
                    statusElement.textContent = status;
                    statusElement.className = 'upload-status uploading';
                }
            }
        });
    }

    async logFileUploadActivity(incidentId, uploadResults) {
        try {
            const userContext = this.getCurrentUserContext();
            
            const activityData = {
                action: 'Files Uploaded',
                details: `${uploadResults.length} file(s) processed`,
                performed_by: userContext.display_name,
                upload_results: uploadResults.map(r => ({
                    filename: r.file.name,
                    success: r.success,
                    error: r.error || null
                }))
            };

            await this.logIncidentActivity(incidentId, 'note_added', activityData);

        } catch (error) {
            console.error('Error logging file upload activity:', error);
        }
    }

    async logIncidentActivity(incidentId, activityType, activityData) {
        try {
            const userContext = this.getCurrentUserContext();

            const logEntry = {
                activity_type: activityType,
                description: activityData.details || activityData.action || `Incident ${activityType}`,
                table_name: 'incidents',
                record_id: incidentId.toString(),
                field_name: null,
                old_value: null,
                new_value: JSON.stringify(activityData),
                user_email: userContext.email || '<EMAIL>',
                user_name: userContext.display_name || 'Unknown User',
                notes: activityData.details || activityData.action || '',
                timestamp: new Date().toISOString(),
                ...this.addCreateMetadata()
            };

            await this.data.insert('activity_logs', logEntry);

        } catch (error) {
            console.error('Error logging incident activity:', error);
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getFileIcon(fileType) {
        if (fileType.startsWith('image/')) return '🖼️';
        if (fileType === 'application/pdf') return '📄';
        if (fileType.startsWith('video/')) return '🎥';
        if (fileType.startsWith('audio/')) return '🎵';
        if (fileType.includes('word')) return '📝';
        if (fileType.includes('excel')) return '📊';
        if (fileType.includes('powerpoint')) return '📽️';
        if (fileType.startsWith('text/')) return '📃';
        return '📁';
    }

    getFileTypeLabel(fileType) {
        const typeMap = {
            'image/jpeg': 'JPEG Image',
            'image/jpg': 'JPG Image',
            'image/png': 'PNG Image',
            'image/gif': 'GIF Image',
            'image/webp': 'WebP Image',
            'application/pdf': 'PDF Document',
            'text/plain': 'Text File',
            'application/msword': 'Word Document',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
            'video/mp4': 'MP4 Video',
            'video/quicktime': 'QuickTime Video',
            'video/x-msvideo': 'AVI Video'
        };
        
        return typeMap[fileType] || fileType;
    }

    // Methods for external access
    getUploadedFiles() {
        return this.uploadedFiles;
    }

    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.updateFileListDisplay();
    }

    hasUnuploadedFiles() {
        return this.uploadedFiles.some(file => !file.uploaded);
    }

    getFileCount() {
        return this.uploadedFiles.length;
    }

    getUploadedFileCount() {
        return this.uploadedFiles.filter(file => file.uploaded).length;
    }

    async downloadIncidentAttachment(attachment) {
        try {
            // Import Azure storage service
            const { azureStorage } = await import('../../../renderer/js/azure-storage.js');

            // Initialize Azure storage
            await azureStorage.initialize();

            // Extract file path from stored_at URL
            const url = new URL(attachment.stored_at);
            const filePath = url.pathname.split('/').slice(2).join('/'); // Remove container name

            // Generate secure download URL
            const downloadUrl = await azureStorage.generateSecureFileUrl(filePath);

            // Open download in new window/tab
            window.open(downloadUrl, '_blank');

        } catch (error) {
            console.error('Error downloading attachment:', error);
            this.ui.showDialog('Download Error', `Failed to download file: ${error.message}`, 'error');
        }
    }

    generateAttachmentsHTML(attachments) {
        if (!attachments || attachments.length === 0) {
            return '<div class="no-attachments">No attachments found</div>';
        }

        return attachments.map(attachment => `
            <div class="attachment-item" data-attachment-id="${attachment.id}">
                <div class="attachment-icon">${this.getFileIcon(attachment.content_type)}</div>
                <div class="attachment-info">
                    <div class="attachment-name">${attachment.filename}</div>
                    <div class="attachment-details">
                        ${this.formatFileSize(attachment.file_size)} •
                        ${this.uiUtilities.formatDate(attachment.uploaded_at)}
                    </div>
                    ${attachment.description ? `<div class="attachment-description">${attachment.description}</div>` : ''}
                </div>
                <div class="attachment-actions">
                    <button class="action-button" onclick="app.incidentManagement.fileManager.downloadIncidentAttachment(${JSON.stringify(attachment).replace(/"/g, '&quot;')})">
                        Download
                    </button>
                </div>
            </div>
        `).join('');
    }
}