/**
 * Network Monitor
 * 
 * Handles network connectivity monitoring and status updates.
 * Extracted from app.js checkNetworkConnection(), setupNetworkMonitoring(), and updateNetworkStatus() methods.
 */

export class NetworkMonitor {
    constructor(authManager = null) {
        this.authManager = authManager;
    }

    /**
     * Setup network monitoring with event listeners and periodic checks
     */
    setupNetworkMonitoring() {
        // Initial network status check
        this.updateNetworkStatus();

        // Listen for online/offline events
        window.addEventListener('online', () => {
            console.log('Network: Online');
            this.updateNetworkStatus();
        });

        window.addEventListener('offline', () => {
            console.log('Network: Offline');
            this.updateNetworkStatus();
        });

        // Periodic network check (every 30 seconds)
        setInterval(() => {
            this.checkNetworkConnection();
        }, 30000);
    }

    /**
     * Update the network status display in the UI
     */
    updateNetworkStatus() {
        const networkStatus = document.getElementById('network-status');
        const networkText = document.getElementById('network-text');
        const isOnline = navigator.onLine;

        if (networkStatus && networkText) {
            if (isOnline) {
                networkStatus.className = 'network-status online';
                networkText.textContent = 'ONLINE';
            } else {
                networkStatus.className = 'network-status offline';
                networkText.textContent = 'OFFLINE';
            }
        }
    }

    /**
     * Check network connectivity by testing Supabase connection
     * @returns {Promise<boolean>} True if connected, false otherwise
     */
    async checkNetworkConnection() {
        // Additional check by trying to reach Supabase
        try {
            if (navigator.onLine && this.authManager?.supabase) {
                // Try a simple query to verify actual connectivity
                const { error } = await this.authManager.supabase
                    .schema('case_mgmt')
                    .from('incidents')
                    .select('count')
                    .limit(1);

                // If we get here without error, we're truly online
                if (!error) {
                    this.updateNetworkStatus();
                    return true;
                }
            }
        } catch (error) {
            // Network might be down even if navigator.onLine is true
            console.log('Network connectivity check failed:', error.message);
        }

        // Update status based on navigator.onLine
        this.updateNetworkStatus();
        return navigator.onLine;
    }

    /**
     * Set the auth manager for network connectivity testing
     * @param {Object} authManager - Auth manager instance with Supabase client
     */
    setAuthManager(authManager) {
        this.authManager = authManager;
    }

    /**
     * Static methods for standalone usage
     */
    static updateNetworkStatus() {
        const instance = new NetworkMonitor();
        return instance.updateNetworkStatus();
    }

    static async checkNetworkConnection(authManager = null) {
        const instance = new NetworkMonitor(authManager);
        return await instance.checkNetworkConnection();
    }
}