/**
 * Encampment Templates
 * Standardized templates following the modular pattern used by other modules
 */

export const encampmentListTemplates = {
    /**
     * Main encampment list view
     */
    encampmentList: (encampments) => {
        return `
            <style>
                .encampment-list-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .encampment-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .encampment-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .encampment-controls {
                    display: flex;
                    gap: 1rem;
                }
                .encampment-search {
                    margin-bottom: 1rem;
                }
                .encampment-search input {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem;
                    font-family: 'Courier New', monospace;
                    width: 300px;
                }
                .encampment-list {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    margin-bottom: 2rem;
                }
                .encampment-list-header {
                    background-color: rgba(255, 0, 0, 0.1);
                    border-bottom: 1px solid #ff0000;
                    padding: 0.75rem;
                    display: grid;
                    grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 120px;
                    gap: 1rem;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    font-size: 0.8rem;
                    color: #ff0000;
                    text-transform: uppercase;
                }
                .encampment-list-item {
                    padding: 0.75rem;
                    border-bottom: 1px solid rgba(255, 0, 0, 0.2);
                    display: grid;
                    grid-template-columns: 2fr 1.5fr 1fr 1fr 1fr 120px;
                    gap: 1rem;
                    align-items: center;
                    cursor: pointer;
                    font-family: 'Courier New', monospace;
                    font-size: 0.85rem;
                }
                .encampment-list-item:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .encampment-status {
                    padding: 0.2rem 0.5rem;
                    border-radius: 3px;
                    font-size: 0.7rem;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .status-active { background-color: #ff6b6b; color: #000; }
                .status-monitoring { background-color: #ffd93d; color: #000; }
                .status-inactive { background-color: #6c757d; color: #fff; }
                .status-cleared { background-color: #4dabf7; color: #000; }
                .encampment-actions {
                    display: flex;
                    gap: 0.5rem;
                }
                .encampment-count {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    margin-bottom: 1rem;
                    font-size: 0.9rem;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .retro-button.primary {
                    background-color: #ff0000;
                    color: #000000;
                }
                .retro-button.primary:hover {
                    background-color: #cc0000;
                }
                .action-btn {
                    padding: 0.25rem 0.5rem;
                    font-size: 0.7rem;
                    border: 1px solid #ff0000;
                    background-color: transparent;
                    color: #ff0000;
                    cursor: pointer;
                    font-family: 'Courier New', monospace;
                }
                .action-btn:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
            </style>
            
            <div class="encampment-list-container">
                <div class="encampment-header">
                    <div class="encampment-title">🏕️ Encampments Management</div>
                    <div class="encampment-controls">
                        <button class="retro-button primary" data-action="add-encampment">Add Encampment</button>
                        <button class="retro-button" data-action="search-encampments">Search</button>
                    </div>
                </div>
                
                <div class="encampment-search">
                    <input type="text" id="encampment-search-input" placeholder="Search encampments by name, location..." 
                           data-action="search-encampments-input" onkeyup="app.commands.execute('search-encampments-input', this.value)">
                </div>
                
                <div class="encampment-count">
                    ${encampments.length} encampment${encampments.length !== 1 ? 's' : ''} found
                </div>
                
                <div class="encampment-list">
                    <div class="encampment-list-header">
                        <div>Name</div>
                        <div>Location</div>
                        <div>Status</div>
                        <div>Population</div>
                        <div>Last Visit</div>
                        <div>Actions</div>
                    </div>
                    ${encampments.map(encampment => encampmentListTemplates.encampmentListItem(encampment)).join('')}
                </div>
            </div>
        `;
    },

    /**
     * Individual encampment list item
     */
    encampmentListItem: (encampment) => {
        const statusClass = `status-${encampment.status || 'active'}`;
        const lastVisit = encampment.last_visited ? 
            new Date(encampment.last_visited).toLocaleDateString() : 'Never';
        
        return `
            <div class="encampment-list-item" data-action="view-encampment-detail" data-encampment-id="${encampment.id}">
                <div class="encampment-name">${encampment.name || 'Unnamed'}</div>
                <div class="encampment-location">${encampment.location || 'No location'}</div>
                <div class="encampment-status ${statusClass}">${(encampment.status || 'active').toUpperCase()}</div>
                <div class="encampment-population">${encampment.estimated_population || 'Unknown'}</div>
                <div class="encampment-last-visit">${lastVisit}</div>
                <div class="encampment-actions">
                    <button class="action-btn" data-action="edit-encampment" data-encampment-id="${encampment.id}" 
                            onclick="event.stopPropagation()">Edit</button>
                    <button class="action-btn" data-action="delete-encampment" data-encampment-id="${encampment.id}" 
                            onclick="event.stopPropagation()">Delete</button>
                </div>
            </div>
        `;
    },

    /**
     * Search results template
     */
    searchResults: (results, query) => {
        return `
            <style>
                .search-results-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .search-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .search-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .search-controls {
                    display: flex;
                    gap: 1rem;
                }
                .search-query {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    margin-bottom: 1rem;
                    font-size: 0.9rem;
                }
                .encampment-count {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    margin-bottom: 1rem;
                    font-size: 0.9rem;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
            </style>
            
            <div class="search-results-container">
                <div class="search-header">
                    <div class="search-title">🔍 Encampment Search Results</div>
                    <div class="search-controls">
                        <button class="retro-button" data-action="back-to-encampment-list">Back to List</button>
                        <button class="retro-button" data-action="add-encampment">Add Encampment</button>
                    </div>
                </div>
                
                <div class="search-query">
                    Search query: "${query}"
                </div>
                
                <div class="encampment-count">
                    ${results.length} encampment${results.length !== 1 ? 's' : ''} found
                </div>
                
                ${results.length > 0 ? `
                    <div class="encampment-list">
                        <div class="encampment-list-header">
                            <div>Name</div>
                            <div>Location</div>
                            <div>Status</div>
                            <div>Population</div>
                            <div>Last Visit</div>
                            <div>Actions</div>
                        </div>
                        ${results.map(encampment => encampmentListTemplates.encampmentListItem(encampment)).join('')}
                    </div>
                ` : `
                    <div style="text-align: center; margin-top: 2rem; color: #ff0000; font-family: 'Courier New', monospace;">
                        No encampments found matching "${query}"
                    </div>
                `}
            </div>
        `;
    }
};

export const encampmentFormTemplates = {
    /**
     * Add encampment form
     */
    addEncampmentForm: () => {
        return `
            <style>
                .encampment-form-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .form-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .form-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .form-controls {
                    display: flex;
                    gap: 1rem;
                }
                .encampment-form {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .form-group {
                    margin-bottom: 1.5rem;
                }
                .form-group label {
                    display: block;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    text-transform: uppercase;
                    font-size: 0.9rem;
                }
                .form-group input,
                .form-group select,
                .form-group textarea {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                }
                .form-group textarea {
                    height: 100px;
                    resize: vertical;
                }
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                    margin-top: 2rem;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem 1.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    min-width: 120px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .retro-button.primary {
                    background-color: #ff0000;
                    color: #000000;
                }
                .retro-button.primary:hover {
                    background-color: #cc0000;
                }
            </style>
            
            <div class="encampment-form-container">
                <div class="form-header">
                    <div class="form-title">🏕️ Add New Encampment</div>
                    <div class="form-controls">
                        <button class="retro-button" data-action="back-to-encampment-list">Cancel</button>
                    </div>
                </div>
                
                <form class="encampment-form" id="encampment-form" onsubmit="event.preventDefault(); app.commands.execute('save-encampment');">
                    <div class="form-group">
                        <label for="name">Encampment Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Location *</label>
                        <input type="text" id="location" name="location" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="coordinates">GPS Coordinates (lat, lng)</label>
                        <input type="text" id="coordinates" name="coordinates" placeholder="43.6532, -79.3832">
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="active">Active</option>
                            <option value="monitoring">Monitoring</option>
                            <option value="inactive">Inactive</option>
                            <option value="cleared">Cleared</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Type</label>
                        <select id="type" name="type">
                            <option value="">Select Type</option>
                            <option value="temporary">Temporary</option>
                            <option value="permanent">Permanent</option>
                            <option value="seasonal">Seasonal</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="estimated_population">Estimated Population</label>
                        <input type="number" id="estimated_population" name="estimated_population" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" placeholder="Describe the encampment..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="safety_concerns">Safety Concerns</label>
                        <textarea id="safety_concerns" name="safety_concerns" placeholder="List any safety concerns..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="services_needed">Services Needed</label>
                        <textarea id="services_needed" name="services_needed" placeholder="List services needed..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="retro-button" data-action="cancel-encampment-form">Cancel</button>
                        <button type="submit" class="retro-button primary" data-action="save-encampment">Save Encampment</button>
                    </div>
                </form>
            </div>
        `;
    },

    /**
     * Edit encampment form
     */
    editEncampmentForm: (encampment) => {
        return `
            <style>
                .encampment-form-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .form-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .form-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .form-controls {
                    display: flex;
                    gap: 1rem;
                }
                .encampment-form {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .form-group {
                    margin-bottom: 1.5rem;
                }
                .form-group label {
                    display: block;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    text-transform: uppercase;
                    font-size: 0.9rem;
                }
                .form-group input,
                .form-group select,
                .form-group textarea {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                }
                .form-group textarea {
                    height: 100px;
                    resize: vertical;
                }
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                    margin-top: 2rem;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem 1.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    min-width: 120px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .retro-button.primary {
                    background-color: #ff0000;
                    color: #000000;
                }
                .retro-button.primary:hover {
                    background-color: #cc0000;
                }
            </style>
            
            <div class="encampment-form-container">
                <div class="form-header">
                    <div class="form-title">🏕️ Edit Encampment</div>
                    <div class="form-controls">
                        <button class="retro-button" data-action="back-to-encampment-list">Cancel</button>
                    </div>
                </div>
                
                <form class="encampment-form" id="encampment-form" onsubmit="event.preventDefault(); app.commands.execute('save-encampment');">
                    <input type="hidden" name="id" value="${encampment.id}">
                    
                    <div class="form-group">
                        <label for="name">Encampment Name *</label>
                        <input type="text" id="name" name="name" value="${encampment.name || ''}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Location *</label>
                        <input type="text" id="location" name="location" value="${encampment.location || ''}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="coordinates">GPS Coordinates (lat, lng)</label>
                        <input type="text" id="coordinates" name="coordinates" value="${encampment.coordinates || ''}" placeholder="43.6532, -79.3832">
                    </div>
                    
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="active" ${encampment.status === 'active' ? 'selected' : ''}>Active</option>
                            <option value="monitoring" ${encampment.status === 'monitoring' ? 'selected' : ''}>Monitoring</option>
                            <option value="inactive" ${encampment.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                            <option value="cleared" ${encampment.status === 'cleared' ? 'selected' : ''}>Cleared</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Type</label>
                        <select id="type" name="type">
                            <option value="">Select Type</option>
                            <option value="temporary" ${encampment.type === 'temporary' ? 'selected' : ''}>Temporary</option>
                            <option value="permanent" ${encampment.type === 'permanent' ? 'selected' : ''}>Permanent</option>
                            <option value="seasonal" ${encampment.type === 'seasonal' ? 'selected' : ''}>Seasonal</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="estimated_population">Estimated Population</label>
                        <input type="number" id="estimated_population" name="estimated_population" value="${encampment.estimated_population || ''}" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" placeholder="Describe the encampment...">${encampment.description || ''}</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="safety_concerns">Safety Concerns</label>
                        <textarea id="safety_concerns" name="safety_concerns" placeholder="List any safety concerns...">${encampment.safety_concerns || ''}</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="services_needed">Services Needed</label>
                        <textarea id="services_needed" name="services_needed" placeholder="List services needed...">${encampment.services_needed || ''}</textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="retro-button" data-action="cancel-encampment-form">Cancel</button>
                        <button type="submit" class="retro-button primary" data-action="save-encampment">Update Encampment</button>
                    </div>
                </form>
            </div>
        `;
    }
};

export const encampmentDetailTemplates = {
    /**
     * Encampment detail view
     */
    encampmentDetail: (encampment, visits = []) => {
        const statusClass = `status-${encampment.status || 'active'}`;
        const lastVisit = encampment.last_visited ? 
            new Date(encampment.last_visited).toLocaleDateString() : 'Never';
        
        return `
            <style>
                .encampment-detail-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .detail-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .detail-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .detail-controls {
                    display: flex;
                    gap: 1rem;
                }
                .detail-info {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                    margin-bottom: 2rem;
                }
                .info-section {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    padding: 1rem;
                }
                .info-section h3 {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    margin: 0 0 1rem 0;
                    font-size: 1rem;
                    text-transform: uppercase;
                }
                .info-item {
                    display: flex;
                    margin-bottom: 0.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                }
                .info-label {
                    font-weight: bold;
                    min-width: 120px;
                }
                .info-value {
                    color: #ccc;
                }
                .encampment-status {
                    padding: 0.2rem 0.5rem;
                    border-radius: 3px;
                    font-size: 0.7rem;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .status-active { background-color: #ff6b6b; color: #000; }
                .status-monitoring { background-color: #ffd93d; color: #000; }
                .status-inactive { background-color: #6c757d; color: #fff; }
                .status-cleared { background-color: #4dabf7; color: #000; }
                .visits-section {
                    margin-top: 2rem;
                }
                .visits-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1rem;
                }
                .visits-list {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                }
                .visit-item {
                    padding: 0.75rem;
                    border-bottom: 1px solid rgba(255, 0, 0, 0.2);
                    font-family: 'Courier New', monospace;
                    font-size: 0.85rem;
                }
                .visit-date {
                    font-weight: bold;
                    color: #ff0000;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.8rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .retro-button.primary {
                    background-color: #ff0000;
                    color: #000000;
                }
                .retro-button.primary:hover {
                    background-color: #cc0000;
                }
                .no-visits {
                    text-align: center;
                    padding: 2rem;
                    color: #888;
                    font-family: 'Courier New', monospace;
                }
            </style>
            
            <div class="encampment-detail-container">
                <div class="detail-header">
                    <div class="detail-title">🏕️ ${encampment.name || 'Unnamed Encampment'}</div>
                    <div class="detail-controls">
                        <button class="retro-button" data-action="back-to-encampment-list">Back to List</button>
                        <button class="retro-button primary" data-action="edit-encampment" data-encampment-id="${encampment.id}">Edit</button>
                        <button class="retro-button primary" data-action="add-encampment-visit" data-encampment-id="${encampment.id}">Record Visit</button>
                    </div>
                </div>
                
                <div class="detail-info">
                    <div class="info-section">
                        <h3>Basic Information</h3>
                        <div class="info-item">
                            <div class="info-label">Name:</div>
                            <div class="info-value">${encampment.name || 'Unnamed'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Location:</div>
                            <div class="info-value">${encampment.location || 'No location'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Status:</div>
                            <div class="info-value">
                                <span class="encampment-status ${statusClass}">
                                    ${(encampment.status || 'active').toUpperCase()}
                                </span>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Type:</div>
                            <div class="info-value">${encampment.type || 'Not specified'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Population:</div>
                            <div class="info-value">${encampment.estimated_population || 'Unknown'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Last Visit:</div>
                            <div class="info-value">${lastVisit}</div>
                        </div>
                    </div>
                    
                    <div class="info-section">
                        <h3>Details</h3>
                        <div class="info-item">
                            <div class="info-label">Coordinates:</div>
                            <div class="info-value">${encampment.coordinates || 'Not provided'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Created:</div>
                            <div class="info-value">${encampment.created_at ? new Date(encampment.created_at).toLocaleDateString() : 'Unknown'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Created By:</div>
                            <div class="info-value">${encampment.created_by || 'Unknown'}</div>
                        </div>
                    </div>
                </div>
                
                ${encampment.description ? `
                    <div class="info-section">
                        <h3>Description</h3>
                        <div class="info-value">${encampment.description}</div>
                    </div>
                ` : ''}
                
                ${encampment.safety_concerns ? `
                    <div class="info-section">
                        <h3>Safety Concerns</h3>
                        <div class="info-value">${encampment.safety_concerns}</div>
                    </div>
                ` : ''}
                
                ${encampment.services_needed ? `
                    <div class="info-section">
                        <h3>Services Needed</h3>
                        <div class="info-value">${encampment.services_needed}</div>
                    </div>
                ` : ''}
                
                <div class="visits-section">
                    <div class="visits-header">
                        <h3 style="color: #ff0000; font-family: 'Courier New', monospace; margin: 0;">Recent Visits (${visits.length})</h3>
                        <button class="retro-button primary" data-action="add-encampment-visit" data-encampment-id="${encampment.id}">Record New Visit</button>
                    </div>
                    
                    <div class="visits-list">
                        ${visits.length > 0 ? visits.map(visit => `
                            <div class="visit-item">
                                <div class="visit-date">${new Date(visit.visit_date).toLocaleDateString()}</div>
                                <div>Visitor: ${visit.visitor_name || 'Unknown'}</div>
                                ${visit.estimated_population ? `<div>Population: ${visit.estimated_population}</div>` : ''}
                                ${visit.overall_condition ? `<div>Condition: ${visit.overall_condition}</div>` : ''}
                            </div>
                        `).join('') : `
                            <div class="no-visits">
                                No visits recorded for this encampment yet.
                            </div>
                        `}
                    </div>
                </div>
            </div>
        `;
    }
};

export const encampmentSearchTemplates = {
    /**
     * Search form template
     */
    searchForm: () => {
        return `
            <style>
                .search-form-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .search-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .search-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .search-controls {
                    display: flex;
                    gap: 1rem;
                }
                .search-form {
                    max-width: 600px;
                    margin: 0 auto;
                }
                .form-group {
                    margin-bottom: 1.5rem;
                }
                .form-group label {
                    display: block;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    text-transform: uppercase;
                    font-size: 0.9rem;
                }
                .form-group input {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem;
                    font-family: 'Courier New', monospace;
                    font-size: 1rem;
                }
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                    margin-top: 2rem;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem 1.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    min-width: 120px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .retro-button.primary {
                    background-color: #ff0000;
                    color: #000000;
                }
                .retro-button.primary:hover {
                    background-color: #cc0000;
                }
            </style>
            
            <div class="search-form-container">
                <div class="search-header">
                    <div class="search-title">🔍 Search Encampments</div>
                    <div class="search-controls">
                        <button class="retro-button" data-action="back-to-encampment-list">Back to List</button>
                    </div>
                </div>
                
                <form class="search-form" onsubmit="event.preventDefault(); app.commands.execute('search-encampments-input', document.getElementById('search-query').value);">
                    <div class="form-group">
                        <label for="search-query">Search Query</label>
                        <input type="text" id="search-query" name="query" placeholder="Enter name, location, or description..." autofocus>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="retro-button" data-action="back-to-encampment-list">Cancel</button>
                        <button type="submit" class="retro-button primary">Search</button>
                    </div>
                </form>
            </div>
        `;
    }
};

export const encampmentVisitTemplates = {
    /**
     * Add visit form template
     */
    addVisitForm: (encampment) => {
        return `
            <style>
                .visit-form-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .form-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .form-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .form-controls {
                    display: flex;
                    gap: 1rem;
                }
                .visit-form {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .form-group {
                    margin-bottom: 1.5rem;
                }
                .form-group label {
                    display: block;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                    text-transform: uppercase;
                    font-size: 0.9rem;
                }
                .form-group input,
                .form-group select,
                .form-group textarea {
                    width: 100%;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                }
                .form-group textarea {
                    height: 100px;
                    resize: vertical;
                }
                .form-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: center;
                    margin-top: 2rem;
                }
                .retro-button {
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.75rem 1.5rem;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                    cursor: pointer;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    min-width: 120px;
                }
                .retro-button:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .retro-button.primary {
                    background-color: #ff0000;
                    color: #000000;
                }
                .retro-button.primary:hover {
                    background-color: #cc0000;
                }
                .encampment-info {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    padding: 1rem;
                    margin-bottom: 2rem;
                    font-family: 'Courier New', monospace;
                }
            </style>
            
            <div class="visit-form-container">
                <div class="form-header">
                    <div class="form-title">📝 Record Encampment Visit</div>
                    <div class="form-controls">
                        <button class="retro-button" data-action="view-encampment-detail" data-encampment-id="${encampment.id}">Cancel</button>
                    </div>
                </div>
                
                <div class="encampment-info">
                    <strong>Encampment:</strong> ${encampment.name}<br>
                    <strong>Location:</strong> ${encampment.location}
                </div>
                
                <form class="visit-form" id="visit-form">
                    <input type="hidden" name="encampment_id" value="${encampment.id}">
                    
                    <div class="form-group">
                        <label for="visit_date">Visit Date *</label>
                        <input type="datetime-local" id="visit_date" name="visit_date" value="${new Date().toISOString().slice(0, 16)}" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="visitor_name">Visitor Name *</label>
                        <input type="text" id="visitor_name" name="visitor_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="estimated_population">Current Population</label>
                        <input type="number" id="estimated_population" name="estimated_population" min="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="overall_condition">Overall Condition</label>
                        <select id="overall_condition" name="overall_condition">
                            <option value="">Select Condition</option>
                            <option value="excellent">Excellent</option>
                            <option value="good">Good</option>
                            <option value="fair">Fair</option>
                            <option value="poor">Poor</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="safety_level">Safety Level</label>
                        <select id="safety_level" name="safety_level">
                            <option value="">Select Safety Level</option>
                            <option value="low">Low Risk</option>
                            <option value="moderate">Moderate Risk</option>
                            <option value="high">High Risk</option>
                            <option value="critical">Critical Risk</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="weather_conditions">Weather Conditions</label>
                        <input type="text" id="weather_conditions" name="weather_conditions" placeholder="Clear, rainy, cold, etc.">
                    </div>
                    
                    <div class="form-group">
                        <label for="services_provided">Services Provided</label>
                        <textarea id="services_provided" name="services_provided" placeholder="List services provided during this visit..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="immediate_needs">Immediate Needs Identified</label>
                        <textarea id="immediate_needs" name="immediate_needs" placeholder="List immediate needs identified..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="violations_observed">Violations/Issues Observed</label>
                        <textarea id="violations_observed" name="violations_observed" placeholder="List any violations or issues observed..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Additional Notes</label>
                        <textarea id="notes" name="notes" placeholder="Any additional observations or notes..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="retro-button" data-action="view-encampment-detail" data-encampment-id="${encampment.id}">Cancel</button>
                        <button type="submit" class="retro-button primary">Record Visit</button>
                    </div>
                </form>
            </div>
        `;
    }
};

// Legacy template object for backward compatibility
export const encampmentTemplates = {
    encampmentsDashboard: encampmentListTemplates.encampmentList,
    renderEncampmentsList: (encampments) => encampments.map(encampment => encampmentListTemplates.encampmentListItem(encampment)).join(''),
    encampmentDetail: encampmentDetailTemplates.encampmentDetail
};
