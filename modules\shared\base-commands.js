// Base Command Classes - Eliminates duplication in commands.js
// Provides base classes for common command patterns

import { BaseManager } from './base-manager.js';

/**
 * Base Command - already exists in commands.js, but enhanced version
 * This is reference implementation for comparison
 */
export class EnhancedBaseCommand {
    constructor(manager) {
        this.app = manager.app || manager;
        this.data = manager.data || manager.app?.data;
        this.ui = manager.ui || manager.app?.ui;
        this.auth = manager.auth || manager.app?.auth;
        this.config = manager.config || manager.app?.config;
    }

    async execute(args) {
        throw new Error('Command must implement execute method');
    }

    /**
     * Get current user context - eliminates duplication
     * @returns {Object} User context
     */
    getCurrentUserContext() {
        const currentUser = this.auth?.getCurrentUser();
        return {
            user: currentUser,
            userEmail: currentUser?.email || 'System',
            userId: currentUser?.id || null,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Execute with error handling - eliminates try-catch duplication
     * @param {Function} operation - Operation to execute
     * @param {string} operationName - Name for error logging
     * @returns {Promise} Operation result
     */
    async executeWithErrorHandling(operation, operationName) {
        try {
            return await operation();
        } catch (error) {
            console.error(`Error ${operationName}:`, error);
            if (this.ui) {
                this.ui.showDialog('Error', `Failed to ${operationName}: ${error.message}`, 'error');
            }
            throw error;
        }
    }

    /**
     * Validate required fields - common validation pattern
     * @param {Object} data - Data to validate
     * @param {Array} requiredFields - Required field names
     * @returns {Object} Validation result
     */
    validateRequiredFields(data, requiredFields) {
        const errors = [];
        
        for (const field of requiredFields) {
            if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
                errors.push(`${field} is required`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

/**
 * Add Entity Command Base Class - Eliminates duplication of 18 Add commands
 * Handles schema-driven form generation and entity creation
 */
class AddEntityCommand extends EnhancedBaseCommand {
    constructor(manager, tableName, entityType, options = {}) {
        super(manager);
        this.tableName = tableName;
        this.entityType = entityType;
        this.excludedFields = options.excludedFields || ['id', 'created_at', 'updated_at', 'created_by', 'updated_by'];
        this.requiredFields = options.requiredFields || [];
        this.parentIdField = options.parentIdField || null; // e.g., 'person_id'
        this.redirectAfter = options.redirectAfter || true;
    }

    async execute(args) {
        return this.executeWithErrorHandling(async () => {
            // Extract parent ID if required (e.g., person_id from args)
            const parentId = this.parentIdField ? args[this.parentIdField] || args[0] : null;
            
            if (this.parentIdField && !parentId) {
                throw new Error(`${this.parentIdField} is required`);
            }

            // Generate form fields from schema
            const fields = this.generateFormFields();
            
            // Show full-screen form
            await this.showAddForm(fields, parentId);
            
            return true;
        }, `showing add ${this.entityType} form`);
    }

    /**
     * Generate form fields from schema
     * @returns {Array} Form fields
     */
    generateFormFields() {
        if (this.data?.schema?.generateFormFields) {
            return this.data.schema.generateFormFields(this.tableName, this.excludedFields);
        }
        
        // Fallback to basic field generation
        return this.getDefaultFields();
    }

    /**
     * Show add form with generated fields
     * @param {Array} fields - Form fields
     * @param {string} parentId - Parent entity ID
     */
    async showAddForm(fields, parentId) {
        const content = this.generateFormHTML(fields, parentId);
        
        // Use app's content loading system
        if (this.app.loadContentIntoContainer) {
            this.app.loadContentIntoContainer('content-area', content);
        } else {
            document.getElementById('content-area').innerHTML = content;
        }
        
        // Set up form submission handling
        this.setupFormHandling(fields, parentId);
    }

    /**
     * Generate form HTML
     * @param {Array} fields - Form fields
     * @param {string} parentId - Parent entity ID
     * @returns {string} Form HTML
     */
    generateFormHTML(fields, parentId) {
        const fieldsHTML = fields.map(field => this.generateFieldHTML(field)).join('');
        
        return `
            <div class="content-section">
                <h2>Add ${this.entityType.toUpperCase()}</h2>
                <form id="add-${this.entityType}-form" class="schema-form">
                    ${parentId ? `<input type="hidden" name="${this.parentIdField}" value="${parentId}">` : ''}
                    ${fieldsHTML}
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save ${this.entityType}</button>
                        <button type="button" class="btn btn-secondary" onclick="history.back()">Cancel</button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Generate HTML for individual field
     * @param {Object} field - Field configuration
     * @returns {string} Field HTML
     */
    generateFieldHTML(field) {
        const { name, type, label, required, options } = field;
        const requiredAttr = required ? 'required' : '';
        const labelText = label || name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        switch (type) {
            case 'text':
            case 'email':
            case 'tel':
            case 'url':
                return `
                    <div class="form-group">
                        <label for="${name}">${labelText}${required ? ' *' : ''}</label>
                        <input type="${type}" id="${name}" name="${name}" ${requiredAttr}>
                    </div>
                `;
            
            case 'textarea':
                return `
                    <div class="form-group">
                        <label for="${name}">${labelText}${required ? ' *' : ''}</label>
                        <textarea id="${name}" name="${name}" rows="3" ${requiredAttr}></textarea>
                    </div>
                `;
            
            case 'select':
                const optionsHTML = options?.map(opt => 
                    `<option value="${opt.value}">${opt.label}</option>`
                ).join('') || '';
                return `
                    <div class="form-group">
                        <label for="${name}">${labelText}${required ? ' *' : ''}</label>
                        <select id="${name}" name="${name}" ${requiredAttr}>
                            <option value="">Select...</option>
                            ${optionsHTML}
                        </select>
                    </div>
                `;
            
            case 'checkbox':
                return `
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="${name}" name="${name}" value="true">
                            ${labelText}
                        </label>
                    </div>
                `;
            
            case 'date':
            case 'datetime-local':
            case 'time':
                return `
                    <div class="form-group">
                        <label for="${name}">${labelText}${required ? ' *' : ''}</label>
                        <input type="${type}" id="${name}" name="${name}" ${requiredAttr}>
                    </div>
                `;
            
            default:
                return `
                    <div class="form-group">
                        <label for="${name}">${labelText}${required ? ' *' : ''}</label>
                        <input type="text" id="${name}" name="${name}" ${requiredAttr}>
                    </div>
                `;
        }
    }

    /**
     * Set up form submission handling
     * @param {Array} fields - Form fields
     * @param {string} parentId - Parent entity ID
     */
    setupFormHandling(fields, parentId) {
        const form = document.getElementById(`add-${this.entityType}-form`);
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.handleFormSubmission(form, fields, parentId);
        });
    }

    /**
     * Handle form submission
     * @param {HTMLFormElement} form - Form element
     * @param {Array} fields - Form fields
     * @param {string} parentId - Parent entity ID
     */
    async handleFormSubmission(form, fields, parentId) {
        try {
            const formData = new FormData(form);
            const data = this.convertFormDataToObject(formData, fields);
            
            // Add parent ID if specified
            if (parentId && this.parentIdField) {
                data[this.parentIdField] = parentId;
            }
            
            // Validate required fields
            const validation = this.validateRequiredFields(data, this.requiredFields);
            if (!validation.isValid) {
                throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
            }
            
            // Add metadata
            const context = this.getCurrentUserContext();
            data.created_at = context.timestamp;
            data.created_by = context.userEmail;
            data.updated_at = context.timestamp;
            data.updated_by = context.userEmail;
            
            // Create the record
            const result = await this.data.insert(this.tableName, data);
            
            // Show success message
            if (this.ui) {
                this.ui.showDialog('Success', `${this.entityType} created successfully`, 'success');
            }
            
            // Redirect or refresh
            if (this.redirectAfter) {
                setTimeout(() => history.back(), 1500);
            }
            
        } catch (error) {
            console.error(`Error creating ${this.entityType}:`, error);
            if (this.ui) {
                this.ui.showDialog('Error', `Failed to create ${this.entityType}: ${error.message}`, 'error');
            }
        }
    }

    /**
     * Convert FormData to object with type conversion
     * @param {FormData} formData - Form data
     * @param {Array} fields - Field definitions
     * @returns {Object} Converted data object
     */
    convertFormDataToObject(formData, fields) {
        const data = {};
        const fieldTypes = fields.reduce((acc, field) => {
            acc[field.name] = field.type;
            return acc;
        }, {});

        for (const [key, value] of formData.entries()) {
            const fieldType = fieldTypes[key];
            
            switch (fieldType) {
                case 'checkbox':
                    data[key] = value === 'true';
                    break;
                case 'number':
                    data[key] = value ? Number(value) : null;
                    break;
                default:
                    data[key] = value || null;
            }
        }

        return data;
    }

    /**
     * Get default fields if schema not available
     * @returns {Array} Default fields
     */
    getDefaultFields() {
        // Override in subclasses for entity-specific fields
        return [
            { name: 'description', type: 'textarea', label: 'Description', required: true }
        ];
    }
}

/**
 * Delete Entity Command Base Class - Eliminates duplication of 9 Delete commands
 */
class DeleteEntityCommand extends EnhancedBaseCommand {
    constructor(manager, tableName, entityType, options = {}) {
        super(manager);
        this.tableName = tableName;
        this.entityType = entityType;
        this.confirmationMessage = options.confirmationMessage || 
            `Are you sure you want to delete this ${entityType}? This action cannot be undone.`;
        this.successMessage = options.successMessage || `${entityType} deleted successfully`;
        this.refreshCallback = options.refreshCallback || null;
    }

    async execute(args) {
        return this.executeWithErrorHandling(async () => {
            const recordId = args.recordId || args[0];
            
            if (!recordId) {
                throw new Error(`${this.entityType} ID is required for deletion`);
            }

            // Show confirmation dialog
            const confirmed = await this.showConfirmationDialog();
            if (!confirmed) {
                return false;
            }

            // Delete the record
            await this.data.delete(this.tableName, recordId);
            
            // Show success message
            if (this.ui) {
                this.ui.showDialog('Success', this.successMessage, 'success');
            }
            
            // Refresh UI if callback provided
            if (this.refreshCallback && typeof this.refreshCallback === 'function') {
                await this.refreshCallback();
            }
            
            return true;
        }, `deleting ${this.entityType}`);
    }

    /**
     * Show confirmation dialog
     * @returns {Promise<boolean>} User confirmation
     */
    async showConfirmationDialog() {
        return new Promise((resolve) => {
            if (this.ui && this.ui.showConfirmDialog) {
                this.ui.showConfirmDialog(
                    'Confirm Deletion',
                    this.confirmationMessage,
                    () => resolve(true),
                    () => resolve(false)
                );
            } else {
                // Fallback to browser confirm
                resolve(confirm(this.confirmationMessage));
            }
        });
    }
}

/**
 * Command Factory - Creates commands dynamically to eliminate repetitive command definitions
 */
class CommandFactory {
    /**
     * Create Add/Delete command pairs for case management entities
     * @param {Object} manager - Command manager instance
     * @param {Array} entities - Array of entity configurations
     * @returns {Map} Map of commands
     */
    static createCaseManagementCommands(manager, entities) {
        const commands = new Map();
        
        entities.forEach(entity => {
            const { name, tableName, requiredFields = [], parentIdField = 'person_id' } = entity;
            
            // Create Add command
            const addCommand = new AddEntityCommand(manager, tableName, name, {
                requiredFields,
                parentIdField
            });
            commands.set(`add-${name.replace('_', '-')}`, addCommand);
            
            // Create Delete command
            const deleteCommand = new DeleteEntityCommand(manager, tableName, name);
            commands.set(`delete-${name.replace('_', '-')}`, deleteCommand);
        });
        
        return commands;
    }

    /**
     * Create entity management commands (search, list, view, edit)
     * @param {Object} manager - Command manager instance
     * @param {Object} entity - Entity configuration
     * @returns {Map} Map of commands
     */
    static createEntityManagementCommands(manager, entity) {
        const commands = new Map();
        const { name, tableName, searchFields = [] } = entity;
        
        // Create Search command
        class SearchCommand extends EnhancedBaseCommand {
            async execute(args) {
                return this.executeWithErrorHandling(async () => {
                    const searchTerm = args.searchTerm || args[0] || '';
                    // Implementation would use SearchUtility
                    console.log(`Searching ${name} for: ${searchTerm}`);
                    return true;
                }, `searching ${name}`);
            }
        }
        
        // Create List command  
        class ListCommand extends EnhancedBaseCommand {
            async execute(args) {
                return this.executeWithErrorHandling(async () => {
                    // Implementation would show list view
                    console.log(`Listing all ${name}`);
                    return true;
                }, `listing ${name}`);
            }
        }
        
        commands.set(`search-${name.replace('_', '-')}`, new SearchCommand(manager));
        commands.set(`list-${name.replace('_', '-')}`, new ListCommand(manager));
        
        return commands;
    }
}

// Export all classes for use in commands.js refactoring
export {
    EnhancedBaseCommand as BaseCommand,
    AddEntityCommand,
    DeleteEntityCommand,
    CommandFactory
};