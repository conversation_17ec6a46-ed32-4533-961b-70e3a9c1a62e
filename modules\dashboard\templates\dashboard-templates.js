/**
 * Dashboard Templates - Redesigned Modular Layout
 * Provides: dashboardTemplates with layout and cards.
 * Notes:
 *  - Keeps legacy-compatible IDs for incidents/weather/stats containers.
 *  - Includes compact single-row 24h forecast slider hooks.
 */
export const dashboardTemplates = {
  dashboardLayout() {
    return `
      <style id="dashboard-inline-css">
        /* Scoped to redesigned container to avoid conflicts */
        .dashboard-container.redesigned { display: flex; flex-direction: column; gap: 0; }

        /* Layout with sidebar - minimal padding for maximum space usage */
        .dashboard-container.redesigned .dashboard-content { display: grid !important; grid-template-columns: 60px 1fr !important; gap: 4px !important; padding: 4px; height: calc(100vh - 80px); }
        .dashboard-container.redesigned .dashboard-sidebar { background: #111111; border: 2px solid #ff0000; display: flex; flex-direction: column; width: 60px !important; min-width: 60px !important; }
        .dashboard-container.redesigned .main-content-area { display: flex; flex-direction: column; gap: 4px; }
        .dashboard-container.redesigned .grid-top,
        .dashboard-container.redesigned .grid-bottom { display: grid; grid-template-columns: 1fr; gap: 4px; }

        @media (min-width: 900px) {
          .dashboard-container.redesigned .grid-top,
          .dashboard-container.redesigned .grid-bottom { grid-template-columns: 1fr 1fr; align-items: start; }
        }

        @media (max-width: 768px) {
          .dashboard-container.redesigned .dashboard-content { grid-template-columns: 1fr !important; gap: 10px !important; }
          .dashboard-container.redesigned .dashboard-sidebar { order: 1; width: 100% !important; min-width: 100% !important; height: auto; flex-direction: row; }
          .dashboard-container.redesigned .main-content-area { order: 2; }
        }

        /* Cards - minimal padding for maximum content space */
        .dashboard-container.redesigned .dashboard-section,
        .dashboard-container.redesigned .weather-widget { border: 1px solid #ff0000; background: #0a0a0a; padding: 4px; min-height: 0; }
        .dashboard-container.redesigned .section-header,
        .dashboard-container.redesigned .widget-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2px; }
        .dashboard-container.redesigned .section-header h3,
        .dashboard-container.redesigned .widget-header h3 { color: #ff0000; font-size: 16px; margin: 0; }
        .dashboard-container.redesigned .section-actions .action-btn,
        .dashboard-container.redesigned .widget-controls .view-alerts-btn,
        .dashboard-container.redesigned .view-all-alerts { background: transparent; border: 1px solid #ff0000; color: #ff0000; padding: 4px 8px; cursor: pointer; text-decoration: none; font-weight: bold; }
        .dashboard-container.redesigned .section-actions .action-btn:hover,
        .dashboard-container.redesigned .widget-controls .view-alerts-btn:hover,
        .dashboard-container.redesigned .view-all-alerts:hover { background: #330000; }
        .dashboard-container.redesigned .section-content { max-height: none; overflow: hidden; padding: 6px; border-top: 1px dashed #ff0000; border-bottom: 1px dashed #ff0000; }
        .dashboard-container.redesigned .section-footer { padding-top: 6px; color: #ff4444; font-size: 12px; }

        /* Sidebar Quick Actions */
        .dashboard-container.redesigned .sidebar-header { display: flex; justify-content: center; align-items: center; margin-bottom: 4px; padding: 4px 2px; border-bottom: 1px solid #ff4444; }
        .dashboard-container.redesigned .sidebar-header h3 { color: #ff0000; font-size: 10px; margin: 0; font-weight: bold; }
        .dashboard-container.redesigned .sidebar-content { flex: 1; overflow-y: auto; padding: 0 2px 2px 2px; }
        .dashboard-container.redesigned .quick-actions { display: grid; grid-template-columns: 1fr; gap: 4px; }
        .dashboard-container.redesigned .action-btn { background: transparent; border: 1px solid #ff0000; color: #ff0000; padding: 4px 2px; cursor: pointer; display: flex; flex-direction: column; align-items: center; gap: 1px; min-height: 40px; font-size: 8px; font-weight: bold; text-align: center; width: 100%; }
        .dashboard-container.redesigned .action-btn:hover { background: #330000; border-color: #ff4444; }
        .dashboard-container.redesigned .action-icon { font-size: 12px; }
        .dashboard-container.redesigned .action-text { font-size: 7px; line-height: 1.0; }

        @media (max-width: 768px) {
          .dashboard-container.redesigned .quick-actions { grid-template-columns: repeat(auto-fit, minmax(50px, 1fr)); gap: 3px; }
          .dashboard-container.redesigned .action-btn { min-height: 30px; padding: 3px 1px; }
          .dashboard-container.redesigned .action-icon { font-size: 10px; }
          .dashboard-container.redesigned .action-text { font-size: 6px; }
        }

        /* Incidents */
        .dashboard-container.redesigned .incidents-dashboard-list .incident-row { border: 1px solid #ff0000; padding: 6px; margin-bottom: 6px; cursor: pointer; min-height: 48px; display:flex; flex-direction:column; justify-content:center; }
        .dashboard-container.redesigned .incidents-dashboard-list .incident-row:hover { background:#220000; }
        .dashboard-container.redesigned .incidents-dashboard-list .row-main { display:flex; gap:8px; align-items:center; margin-bottom:2px; overflow:hidden; white-space:nowrap; text-overflow:ellipsis; }
        .dashboard-container.redesigned .badge { border:1px solid #ff0000; padding:1px 4px; font-size:11px; white-space:nowrap; }
        .dashboard-container.redesigned .status-open { color:#00ff00; }
        .dashboard-container.redesigned .priority-high { color:#ffaa00; }

        /* Alerts */
        .dashboard-container.redesigned .alerts-section .severity-dot { display:inline-block; width:8px; height:8px; background:#ffaa00; border-radius:50%; margin-right:6px; }
        .dashboard-container.redesigned .alerts-list .alert-row { display:grid; grid-template-columns:18px 1fr auto; gap:8px; align-items:center; border:1px solid #ff0000; padding:6px; margin-bottom:6px; }
        .dashboard-container.redesigned .alerts-list .alert-icon { text-align:center; }
        .dashboard-container.redesigned .alerts-list .dismiss-btn { background:transparent; border:1px solid #ff0000; color:#ff0000; padding:2px 6px; cursor:pointer; }

        /* Weather - ultra-compact current conditions */
        .dashboard-container.redesigned .weather-widget .widget-content { padding:6px; }

        /* Weather alert auto-display */
        .dashboard-container.redesigned .weather-alert {
          display:flex;
          align-items:center;
          gap:6px;
          background:rgba(255,165,0,0.1);
          border:1px solid #ffaa00;
          border-radius:3px;
          padding:4px 6px;
          margin-bottom:4px;
          font-size:11px;
          color:#ffaa00;
        }
        .dashboard-container.redesigned .weather-alert.severe {
          background:rgba(255,0,0,0.1);
          border-color:#ff0000;
          color:#ff4444;
        }
        .dashboard-container.redesigned .alert-icon {
          flex-shrink:0;
          font-size:12px;
        }
        .dashboard-container.redesigned .alert-text {
          flex:1;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
        }
        .dashboard-container.redesigned .weather-compact {
          display:flex;
          flex-direction:column;
          gap:6px;
          border:1px solid #ff0000;
          padding:8px;
          min-height:60px;
        }
        .dashboard-container.redesigned .wc-main {
          display:flex;
          align-items:center;
          gap:10px;
          flex:1;
          min-width:0;
        }
        .dashboard-container.redesigned .wc-icon {
          font-size:18px;
          flex-shrink:0;
          width:20px;
          text-align:center;
        }
        .dashboard-container.redesigned .wc-temp {
          font-size:20px;
          font-weight:bold;
          flex-shrink:0;
          min-width:45px;
        }
        .dashboard-container.redesigned .wc-desc {
          color:#ff4444;
          font-size:12px;
          overflow:hidden;
          text-overflow:ellipsis;
          white-space:nowrap;
          min-width:0;
          flex:1;
        }
        .dashboard-container.redesigned .wc-side {
          display:flex;
          flex-direction:row;
          align-items:center;
          justify-content:space-between;
          gap:8px;
          flex-shrink:0;
          flex-wrap:wrap;
        }
        .dashboard-container.redesigned .wc-loc {
          color:#ff4444;
          font-size:10px;
          white-space:nowrap;
          flex-shrink:0;
        }
        .dashboard-container.redesigned .wc-meta {
          display:flex;
          gap:6px;
          flex-wrap:wrap;
        }
        .dashboard-container.redesigned .wc-chip {
          border:1px dashed #ff0000;
          padding:2px 4px;
          font-size:9px;
          color:#ff4444;
          white-space:nowrap;
          flex-shrink:0;
          border-radius:2px;
          min-width:fit-content;
        }

        /* 24h forecast: Ultra-compact horizontal layout with auto-fit */
        .dashboard-container.redesigned .weather-forecast.compact-grid {
          margin-top:4px;
          overflow:hidden; /* Prevent any overflow */
        }
        .dashboard-container.redesigned #forecast-grid {
          display:grid;
          grid-template-columns: repeat(auto-fit, minmax(55px, 1fr));
          gap:3px;
          border:1px dashed #ff0000;
          padding:4px;
          overflow:hidden;
          width:100%;
          box-sizing:border-box;
        }

        /* Responsive adjustments for better fit */
        @media (min-width: 1200px) {
          .dashboard-container.redesigned #forecast-grid {
            grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
          }
          .dashboard-container.redesigned .forecast-hour {
            min-height:50px;
          }
        }
        @media (min-width: 900px) and (max-width: 1199px) {
          .dashboard-container.redesigned #forecast-grid {
            grid-template-columns: repeat(auto-fit, minmax(58px, 1fr));
          }
          .dashboard-container.redesigned .forecast-hour {
            min-height:52px;
          }
        }
        @media (max-width: 899px) {
          .dashboard-container.redesigned #forecast-grid {
            grid-template-columns: repeat(auto-fit, minmax(55px, 1fr));
          }
          .dashboard-container.redesigned .forecast-hour {
            min-height:55px;
            min-width:55px;
          }
        }

        /* Specific optimizations for 1024x768 (Panasonic Toughbook) */
        @media (width: 1024px) and (height: 768px) {
          /* Optimize dashboard layout for exact 1024x768 resolution */
          .dashboard-container.redesigned .dashboard-content {
            gap: 3px !important;
            padding: 3px;
            height: calc(100vh - 100px);
            grid-template-columns: 50px 1fr !important;
          }
          .dashboard-container.redesigned .dashboard-sidebar {
            width: 50px !important;
            min-width: 50px !important;
          }
          .dashboard-container.redesigned .main-content-area {
            gap: 3px;
          }
          .dashboard-container.redesigned .grid-top,
          .dashboard-container.redesigned .grid-bottom {
            gap: 3px;
            grid-template-columns: 1fr 1fr;
            width: 100%;
          }

          /* Optimize card heights for 1024x768 */
          .dashboard-container.redesigned .grid-top {
            height: 320px;
          }
          .dashboard-container.redesigned .grid-bottom {
            height: 280px;
          }
          .dashboard-container.redesigned .dashboard-section,
          .dashboard-container.redesigned .weather-widget {
            padding: 3px;
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
          }
          .dashboard-container.redesigned .section-content,
          .dashboard-container.redesigned .widget-content {
            flex: 1;
            overflow: hidden;
          }

          /* Ensure grid items take full width */
          .dashboard-container.redesigned .grid-top > *,
          .dashboard-container.redesigned .grid-bottom > * {
            width: 100%;
            box-sizing: border-box;
          }

          /* Weather widget optimizations for 1024x768 */
          .dashboard-container.redesigned .weather-widget,
          .dashboard-container.redesigned .weather-section {
            width: 100% !important;
            max-width: none !important;
            flex: 1 !important;
          }
          .dashboard-container.redesigned .weather-compact {
            min-height: 50px;
            padding: 6px;
            gap: 4px;
          }
          .dashboard-container.redesigned .wc-main {
            gap: 8px;
          }
          .dashboard-container.redesigned .wc-icon {
            font-size: 16px;
            width: 18px;
          }
          .dashboard-container.redesigned .wc-temp {
            font-size: 18px;
            min-width: 40px;
          }
          .dashboard-container.redesigned .wc-desc {
            font-size: 11px;
          }
          .dashboard-container.redesigned .wc-side {
            gap: 6px;
          }
          .dashboard-container.redesigned .wc-meta {
            gap: 4px;
          }
          .dashboard-container.redesigned .wc-chip {
            padding: 1px 3px;
            font-size: 8px;
          }

          /* Forecast grid optimizations for 1024x768 */
          .dashboard-container.redesigned #forecast-grid {
            grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
            gap: 2px;
            padding: 3px;
          }
          .dashboard-container.redesigned .forecast-hour {
            min-height: 45px;
            min-width: 50px;
          }
        }

        .dashboard-container.redesigned .forecast-hour {
          border:1px solid #ff0000;
          padding:3px 2px;
          display:flex;
          flex-direction:column;
          align-items:center;
          justify-content:space-between;
          min-width:50px;
          min-height:50px;
          text-align:center;
          background:rgba(255,0,0,0.05);
          box-sizing:border-box;
          position:relative;
          border-radius:3px;
        }

        .dashboard-container.redesigned .fh-time {
          color:#ff4444;
          font-size:9px;
          font-weight:bold;
          white-space:nowrap;
          overflow:hidden;
          text-overflow:ellipsis;
          width:100%;
          line-height:1;
          flex-shrink:0;
        }
        .dashboard-container.redesigned .fh-icon {
          font-size:14px;
          line-height:1;
          flex-shrink:0;
          margin:1px 0;
        }
        .dashboard-container.redesigned .fh-temp {
          font-size:9px;
          font-weight:bold;
          color:#ffffff;
          white-space:nowrap;
          overflow:hidden;
          text-overflow:ellipsis;
          width:100%;
          line-height:1;
          flex-shrink:0;
        }
        .dashboard-container.redesigned .forecast-nav { display:none; } /* no nav buttons needed with wrapping */

        /* Stats grid layout */
        .dashboard-container.redesigned .stats-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          padding: 4px;
        }
        .dashboard-container.redesigned .stat-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 6px;
          border: 1px dashed #ff0000;
          background: #111111;
        }
        .dashboard-container.redesigned .stat-label {
          font-size: 10px;
          color: #ff4444;
          margin-bottom: 2px;
        }
        .dashboard-container.redesigned .stat-value {
          font-size: 16px;
          color: #ffffff;
          font-weight: bold;
        }

        /* Utility */
        .dashboard-container.redesigned .visually-hidden { position:absolute !important; height:1px; width:1px; overflow:hidden; clip:rect(1px,1px,1px,1px); white-space:nowrap; }

        /* 1024x768 fitting without breaking larger screens */
        @media (min-width: 900px) and (max-width: 1280px) {
          .dashboard-container.redesigned .dashboard-content { padding:2px; gap:4px; }
          .dashboard-container.redesigned .grid-top,
          .dashboard-container.redesigned .grid-bottom { gap:4px; }
          .dashboard-container.redesigned .section-header,
          .dashboard-container.redesigned .widget-header { margin-bottom:2px; min-height:20px; }
          .dashboard-container.redesigned .section-header h3,
          .dashboard-container.redesigned .widget-header h3 { font-size:14px; }
          .dashboard-container.redesigned .section-footer,
          .dashboard-container.redesigned .weather-footer { padding-top:2px; min-height:14px; }
        }

        /* Larger screens - still keep minimal spacing */
        @media (min-width: 1280px) {
          .dashboard-container.redesigned .dashboard-content { gap:6px; }
          .dashboard-container.redesigned .grid-top,
          .dashboard-container.redesigned .grid-bottom { gap:6px; }
          .dashboard-container.redesigned .weather-main { grid-template-columns:32px auto; }
          .dashboard-container.redesigned .weather-icon { font-size:20px; }
          .dashboard-container.redesigned .weather-temp { font-size:20px; }
          .dashboard-container.redesigned #forecast-scroll { gap:6px; }
          .dashboard-container.redesigned .fh-time { font-size:12px; }
          .dashboard-container.redesigned .fh-temp { font-size:13px; }
        }
      </style>

      <div class="dashboard-container redesigned">
        <main class="dashboard-content" role="main" aria-labelledby="dashboard-main-title">
          <h2 id="dashboard-main-title" class="visually-hidden">Dashboard Main</h2>

          <!-- Quick Actions Sidebar -->
          <aside class="dashboard-sidebar" aria-label="Quick actions">
            ${this.quickActions()}
          </aside>

          <!-- Main Content Area -->
          <div class="main-content-area">
            <section class="grid-top" aria-label="Priority feed">
              ${this.incidentsCard()}
              ${this.alertsCard()}
            </section>

            <section class="grid-bottom" aria-label="Overview">
              ${this.weatherCard()}
              ${this.statsCard()}
            </section>
          </div>
        </main>
      </div>
    `;
  },

  quickActions() {
    return `
      <div class="sidebar-header">
        <h3>Quick</h3>
      </div>
      <div class="sidebar-content">
        <div class="quick-actions">
          <button class="action-btn" data-action="new-incident" aria-label="Create new incident">
            <span class="action-icon">✚</span>
            <span class="action-text">New Incident</span>
          </button>
          <button class="action-btn" data-action="log-activity" aria-label="Add person, vehicle, or property activity">
            <span class="action-icon">✎</span>
            <span class="action-text">Log Activity</span>
          </button>
          <button class="action-btn" data-action="new-outreach" aria-label="Create new outreach transaction">
            <span class="action-icon">📦</span>
            <span class="action-text">New Outreach</span>
          </button>
          <button class="action-btn" data-action="open-dispatch" aria-label="Open dispatch view">
            <span class="action-icon">☰</span>
            <span class="action-text">Dispatch</span>
          </button>
          <button class="action-btn" data-action="view-alerts" aria-label="View weather alerts">
            <span class="action-icon">⚠</span>
            <span class="action-text">Alerts</span>
          </button>
        </div>
      </div>
    `;
  },

  incidentsCard() {
    return `
      <section class="dashboard-section incidents-section" aria-labelledby="incidents-title">
        <div class="section-header">
          <h3 id="incidents-title">Incidents</h3>
          <div class="section-actions">
            <button class="action-btn" data-action="refresh-incidents" aria-label="Refresh incidents">⟳ Refresh</button>
          </div>
        </div>
        <div class="section-content" id="incidents-content" data-bind="incidents"></div>
        <div class="section-footer">
          <small>Tip: Use Dispatch for side-by-side triage</small>
        </div>
      </section>
    `;
  },

  alertsCard() {
    return `
      <section class="dashboard-section alerts-section" role="complementary" aria-labelledby="alerts-title">
        <div class="section-header">
          <h3 id="alerts-title">
            <span class="severity-dot" aria-hidden="true"></span>
            Alerts
          </h3>
          <div class="section-actions">
            <a href="#" class="view-all-alerts" data-action="view-alerts" role="button" aria-label="View all alerts">View all</a>
          </div>
        </div>
        <div class="section-content alerts-list" id="alerts-content" data-bind="alerts" tabindex="0" role="list" aria-label="Alerts list">
          <!-- App will inject alert rows (weather first by severity, then system/sync) -->
          <div class="placeholder">No alerts</div>
        </div>
        <div class="section-footer">
          <small>Weather and system alerts shown by priority</small>
        </div>
      </section>
    `;
  },

  weatherCard() {
    return `
      <section class="dashboard-section weather-section weather-widget" aria-label="Weather conditions">
        <div class="widget-content" id="weather-content">
          <!-- Weather alert (auto-display when present) -->
          <div class="weather-alert" id="weather-alert" style="display:none;" role="alert" aria-live="assertive">
            <span class="alert-icon">⚠️</span>
            <span class="alert-text" id="alert-text"></span>
          </div>

          <!-- Ultra-compact current conditions -->
          <div class="weather-compact" id="weather-current" role="group" aria-label="Current conditions">
            <div class="wc-main">
              <span class="wc-icon" id="wc-icon" aria-hidden="true">⛅</span>
              <span class="wc-temp" id="wc-temp">--°</span>
              <span class="wc-desc" id="wc-desc">Loading...</span>
            </div>
            <div class="wc-side">
              <div class="wc-loc" id="wc-loc" title="Location">--</div>
              <div class="wc-meta" id="wc-meta">
                <span class="wc-chip" id="wc-feels">Feels --°</span>
                <span class="wc-chip" id="wc-hum">Hum --%</span>
                <span class="wc-chip" id="wc-wind">Wind -- km/h</span>
              </div>
            </div>
          </div>

          <!-- Compact 24h forecast grid (no scroll) -->
          <div class="weather-forecast compact-grid" id="forecast-container" aria-label="24 hour forecast" role="region">
            <div class="forecast-grid" id="forecast-grid" role="list"></div>
          </div>
        </div>

      </section>
    `;
  },

  statsCard() {
    return `
      <section class="dashboard-section stats-section" aria-labelledby="stats-title">
        <div class="section-header">
          <h3 id="stats-title">Stats</h3>
          <div class="section-actions">
            <button class="action-btn" data-action="refresh-stats" aria-label="Refresh stats">⟳ Refresh</button>
          </div>
        </div>
        <div class="section-content" id="stats-content" data-bind="stats">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">Incidents</div>
              <div class="stat-value" id="stats-incidents">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">People</div>
              <div class="stat-value" id="stats-people">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Outreach</div>
              <div class="stat-value" id="stats-outreach">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Today</div>
              <div class="stat-value" id="stats-today">-</div>
            </div>
          </div>
        </div>
        <div class="section-footer">
          <small>System overview statistics</small>
        </div>
      </section>
    `;
  }
};