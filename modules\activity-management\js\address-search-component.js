/**
 * Address Search Component
 * Reusable component for searching and selecting addresses in activity forms
 */

import { BaseManager } from '../../shared/base-manager.js';
import { SearchUtility } from '../../shared/search-utility.js';

export class AddressSearchComponent extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.selectedAddress = null;
        this.onAddressSelected = null; // Callback function
    }

    /**
     * Create address search interface
     * @param {HTMLElement} container - Container element to append the search interface
     * @param {Object} options - Configuration options
     * @param {Function} onAddressSelected - Callback when address is selected
     */
    createAddressSearchInterface(container, options = {}, onAddressSelected = null) {
        this.onAddressSelected = onAddressSelected;
        
        const searchInterface = document.createElement('div');
        searchInterface.className = 'address-search-interface';
        
        searchInterface.innerHTML = `
            <div class="address-search-container">
                <div class="search-input-group">
                    <input type="text" 
                           id="address-search-input" 
                           class="address-search-input" 
                           placeholder="Search for an address..."
                           autocomplete="off">
                    <button type="button" id="address-search-btn" class="search-button">🔍</button>
                    <button type="button" id="create-new-address-btn" class="secondary-button">+ New Address</button>
                </div>
                
                <div id="address-search-results" class="address-search-results" style="display: none;">
                    <div class="results-header">
                        <h4>Search Results</h4>
                        <button type="button" id="clear-address-search" class="link-button">Clear</button>
                    </div>
                    <div id="address-results-container" class="address-results-container"></div>
                </div>
                
                <div id="selected-address-display" class="selected-address-display" style="display: none;">
                    <div class="selected-address-header">
                        <h4>Selected Address</h4>
                        <button type="button" id="change-address-btn" class="link-button">Change</button>
                    </div>
                    <div id="selected-address-info" class="selected-address-info"></div>
                </div>
            </div>
        `;
        
        container.appendChild(searchInterface);
        this.setupEventHandlers(searchInterface);
        
        return searchInterface;
    }

    /**
     * Set up event handlers for the address search interface
     */
    setupEventHandlers(container) {
        const searchInput = container.querySelector('#address-search-input');
        const searchBtn = container.querySelector('#address-search-btn');
        const createNewBtn = container.querySelector('#create-new-address-btn');
        const clearBtn = container.querySelector('#clear-address-search');
        const changeBtn = container.querySelector('#change-address-btn');

        // Search functionality
        const performSearch = async () => {
            const query = searchInput.value.trim();
            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }
            await this.searchAddresses(query, container);
        };

        // Event listeners
        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }
        });

        // Live search with debounce
        let searchTimeout;
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            const query = searchInput.value.trim();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    this.searchAddresses(query, container);
                }, 300);
            } else if (query.length === 0) {
                this.clearSearchResults(container);
            }
        });

        createNewBtn.addEventListener('click', () => this.showCreateAddressForm(container));
        clearBtn.addEventListener('click', () => this.clearSearchResults(container));
        changeBtn.addEventListener('click', () => this.clearSelection(container));
    }

    /**
     * Search for addresses
     */
    async searchAddresses(query, container) {
        try {
            const resultsContainer = container.querySelector('#address-results-container');
            const searchResults = container.querySelector('#address-search-results');
            
            // Show loading state
            resultsContainer.innerHTML = '<div class="loading">Searching addresses...</div>';
            searchResults.style.display = 'block';

            // Get all addresses and filter
            const allAddresses = await this.data.search('addresses', {});
            
            // Use SearchUtility for consistent search behavior
            const searchFields = ['street_address', 'city', 'province', 'postal_code', 'address_type', 'unit_number'];
            const results = SearchUtility.search(allAddresses, query, searchFields);

            this.displaySearchResults(results, container);

        } catch (error) {
            console.error('Error searching addresses:', error);
            const resultsContainer = container.querySelector('#address-results-container');
            resultsContainer.innerHTML = '<div class="error">Search failed. Please try again.</div>';
        }
    }

    /**
     * Display search results
     */
    displaySearchResults(results, container) {
        const resultsContainer = container.querySelector('#address-results-container');
        const searchResults = container.querySelector('#address-search-results');

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <p>No addresses found matching your search.</p>
                    <p>Try different search terms or create a new address.</p>
                </div>
            `;
            return;
        }

        resultsContainer.innerHTML = results.map(address => `
            <div class="address-result-item" data-address-id="${address.id}">
                <div class="address-info">
                    <div class="address-main">
                        ${address.unit_number ? `${address.unit_number} - ` : ''}${address.street_address || 'Unknown Address'}
                    </div>
                    <div class="address-details">
                        ${address.city || 'Unknown City'}, ${address.province || 'Unknown Province'}
                        ${address.postal_code ? ` ${address.postal_code}` : ''}
                    </div>
                    ${address.address_type ? `<div class="address-type">Type: ${address.address_type}</div>` : ''}
                </div>
                <div class="address-actions">
                    <button class="primary-button select-address-btn" data-address-id="${address.id}">Select</button>
                </div>
            </div>
        `).join('');

        // Set up click handlers for selection
        resultsContainer.querySelectorAll('.select-address-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const addressId = e.target.getAttribute('data-address-id');
                const address = results.find(a => a.id == addressId);
                if (address) {
                    this.selectAddress(address, container);
                }
            });
        });

        searchResults.style.display = 'block';
    }

    /**
     * Select an address
     */
    selectAddress(address, container) {
        this.selectedAddress = address;
        
        // Hide search results
        this.clearSearchResults(container);
        
        // Show selected address
        const selectedDisplay = container.querySelector('#selected-address-display');
        const selectedInfo = container.querySelector('#selected-address-info');
        
        selectedInfo.innerHTML = `
            <div class="selected-address-card">
                <div class="address-main">
                    ${address.unit_number ? `${address.unit_number} - ` : ''}${address.street_address || 'Unknown Address'}
                </div>
                <div class="address-details">
                    ${address.city || 'Unknown City'}, ${address.province || 'Unknown Province'}
                    ${address.postal_code ? ` ${address.postal_code}` : ''}
                </div>
                ${address.address_type ? `<div class="address-type">Type: ${address.address_type}</div>` : ''}
            </div>
        `;
        
        selectedDisplay.style.display = 'block';
        
        // Clear search input
        const searchInput = container.querySelector('#address-search-input');
        searchInput.value = '';
        
        // Call callback if provided
        if (this.onAddressSelected) {
            this.onAddressSelected(address);
        }
    }

    /**
     * Clear search results
     */
    clearSearchResults(container) {
        const searchResults = container.querySelector('#address-search-results');
        const resultsContainer = container.querySelector('#address-results-container');
        
        searchResults.style.display = 'none';
        resultsContainer.innerHTML = '';
    }

    /**
     * Clear selection
     */
    clearSelection(container) {
        this.selectedAddress = null;
        
        const selectedDisplay = container.querySelector('#selected-address-display');
        selectedDisplay.style.display = 'none';
        
        // Call callback with null to indicate no selection
        if (this.onAddressSelected) {
            this.onAddressSelected(null);
        }
    }

    /**
     * Show create address form
     */
    async showCreateAddressForm(container) {
        try {
            // Generate form fields for address creation
            const fields = this.data.schema.generateFormFields('addresses');

            this.ui.showForm('Create New Address', fields, async (formData) => {
                try {
                    const currentUser = this.auth.getCurrentUser();
                    const addressData = {
                        ...formData,
                        created_at: new Date().toISOString(),
                        created_by: currentUser?.email || 'System'
                    };

                    const newAddress = await this.data.insert('addresses', addressData);
                    this.ui.showDialog('Success', 'Address created successfully!', 'success');

                    // Automatically select the new address
                    this.selectAddress(newAddress, container);

                    return true; // Close the form
                } catch (error) {
                    console.error('Error creating address:', error);
                    this.ui.showDialog('Error', 'Failed to create address record', 'error');
                    return false; // Keep the form open
                }
            });
        } catch (error) {
            console.error('Error showing address creation form:', error);
            this.ui.showDialog('Error', 'Failed to show address creation form', 'error');
        }
    }

    /**
     * Get the currently selected address
     */
    getSelectedAddress() {
        return this.selectedAddress;
    }

    /**
     * Set a pre-selected address (for editing existing activities)
     */
    setSelectedAddress(address, container) {
        if (address && container) {
            this.selectAddress(address, container);
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.selectedAddress = null;
        this.onAddressSelected = null;
    }
}
