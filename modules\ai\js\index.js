/**
 * AI Module - Main Entry Point
 * Provides AI services including Gemini integration
 */

import { ServiceModuleInterface } from '../../shared/module-interface.js';
import { GeminiService } from './gemini-service.js';
import { Logger } from '../../shared/logger.js';

export class AIModule extends ServiceModuleInterface {
    constructor(dataManager = null, authManager = null, uiManager = null, configManager = null) {
        super('AIModule', '1.0.0', [], 'integration');
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.config = configManager;
        this.logger = Logger.forModule('AIModule');

        // Initialize AI services
        this.geminiService = new GeminiService(dataManager, configManager);
    }

    /**
     * Initialize the AI module
     * @returns {Promise<void>}
     */
    async initialize() {
        this.logger.info('Initializing AI Module');
        try {
            // Initialize Gemini service if API key is available
            if (this.config && this.config.getGeminiApiKey) {
                await this.geminiService.initializeWithApiKey();
                this.logger.info('Gemini service initialized successfully');
            } else {
                this.logger.warn('No API key available for Gemini service');
            }
            this.initialized = true;
        } catch (error) {
            this.logger.error('Failed to initialize AI module', error);
            throw error;
        }
    }

    /**
     * Cleanup module resources
     * @returns {Promise<void>}
     */
    async cleanup() {
        this.logger.info('Cleaning up AI Module');
        // Clear any caches or connections
        if (this.geminiService && this.geminiService.cache) {
            this.geminiService.cache.clear();
        }
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // AI module typically doesn't provide direct commands
        return new Map();
    }

    /**
     * Get module status
     * @returns {Object} Module status
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            initialized: this.initialized,
            uptime: Date.now() - this.startTime,
            serviceType: this.serviceType
        };
    }

    /**
     * Get service health status
     * @returns {Promise<Object>} Service health information
     */
    async getHealthStatus() {
        try {
            const baseHealth = await super.getHealthStatus();
            
            // Check Gemini service health
            const geminiHealth = {
                available: !!this.geminiService,
                apiKeyConfigured: !!(this.config && this.config.getGeminiApiKey),
                cacheSize: this.geminiService?.cache?.size || 0
            };

            return {
                ...baseHealth,
                services: {
                    gemini: geminiHealth
                }
            };
        } catch (error) {
            this.logger.error('Failed to get AI module health status', error);
            return {
                healthy: false,
                error: error.message,
                lastCheck: new Date().toISOString()
            };
        }
    }

    // Expose service methods for direct access
    async generateText(prompt, options = {}) {
        if (!this.geminiService) {
            throw new Error('Gemini service not available');
        }
        return await this.geminiService.generateText(prompt, options);
    }

    async generateJSONResponse(prompt, schema = null) {
        if (!this.geminiService) {
            throw new Error('Gemini service not available');
        }
        return await this.geminiService.generateJSONResponse(prompt, schema);
    }

    async generateStructuredIncidentNarrative(incidentData) {
        if (!this.geminiService) {
            throw new Error('Gemini service not available');
        }
        return await this.geminiService.generateStructuredIncidentNarrative(incidentData);
    }
}

// Export both for compatibility
export { GeminiService } from './gemini-service.js';
