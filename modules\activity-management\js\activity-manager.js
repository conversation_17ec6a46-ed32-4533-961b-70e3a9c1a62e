/**
 * Activity Manager
 * Handles activity creation and management for different record types
 */

import { BaseManager } from '../../shared/base-manager.js';
import { AddressSearchComponent } from './address-search-component.js';

export class ActivityManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    /**
     * Show activity type selection modal
     */
    async showActivityTypeSelection() {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay activity-type-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Add Activity - Select Type</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <p>What type of activity would you like to add?</p>
                    <div class="activity-type-selection">
                        <div class="menu-grid">
                            <div class="menu-item" data-activity-type="people">
                                <div class="menu-icon">👤</div>
                                <div class="menu-title">Person Activity</div>
                                <div class="menu-desc">Add activity for a person record</div>
                            </div>
                            <div class="menu-item" data-activity-type="addresses">
                                <div class="menu-icon">🏠</div>
                                <div class="menu-title">Address Activity</div>
                                <div class="menu-desc">Add activity for an address record</div>
                            </div>
                            <div class="menu-item" data-activity-type="license_plates">
                                <div class="menu-icon">🚗</div>
                                <div class="menu-title">Vehicle Activity</div>
                                <div class="menu-desc">Add activity for a vehicle record</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle activity type selection
        modal.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const activityType = menuItem.dataset.activityType;
                modal.remove();
                
                // Import and use the search manager
                import('./activity-search-manager.js').then(({ ActivitySearchManager }) => {
                    const searchManager = new ActivitySearchManager(this.data, this.auth, this.ui, this.uiUtilities);
                    searchManager.showRecordSearch(activityType);
                });
            }
        });
    }

    /**
     * Show activity form for a specific record
     */
    async showActivityForm(recordType, record) {
        try {
            console.log(`Showing add activity form for ${recordType} record:`, record);

            const activityTable = this.getActivityTableName(recordType);
            const foreignKey = this.getForeignKeyName(recordType);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            // For people activities, use custom form with address search
            if (recordType === 'people') {
                await this.showPeopleActivityFormWithAddressSearch(record);
                return;
            }

            // For other record types, use the standard form
            await this.showStandardActivityForm(recordType, record);

        } catch (error) {
            console.error('Error showing activity form:', error);
            this.ui.showDialog('Error', `Failed to show activity form: ${error.message}`, 'error');
        }
    }

    /**
     * Show people activity form with address search
     */
    async showPeopleActivityFormWithAddressSearch(person) {
        try {
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                person_id: person.id,
                activity_date: new Date().toISOString().split('T')[0],
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            // Generate form fields for people_activities table, excluding address_id since we'll handle it separately
            const excludeFields = ['id', 'created_at', 'created_by', 'person_id', 'address_id'];
            const fields = this.data.schema.generateFormFields('people_activities', excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            const personName = `${person.first_name || ''} ${person.last_name || ''}`.trim() || 'Unknown Person';

            // Create custom form with address search
            this.showCustomActivityForm(
                `Add Activity - ${personName}`,
                fields,
                async (formData, selectedAddress) => {
                    try {
                        // Add the person_id and address_id to the form data
                        formData.person_id = person.id;
                        formData.created_by = currentUser?.email || 'unknown';

                        // Add address_id if an address was selected
                        if (selectedAddress) {
                            formData.address_id = selectedAddress.id;
                        }

                        console.log('Submitting people activity data:', formData);

                        // Insert the activity
                        const result = await this.data.insert('people_activities', formData);
                        console.log('People activity inserted successfully:', result);

                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');
                        return true; // Close the form

                    } catch (error) {
                        console.error('Error adding people activity:', error);
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false; // Keep the form open
                    }
                }
            );

        } catch (error) {
            console.error('Error showing people activity form:', error);
            this.ui.showDialog('Error', `Failed to show people activity form: ${error.message}`, 'error');
        }
    }

    /**
     * Show standard activity form (for non-people records)
     */
    async showStandardActivityForm(recordType, record) {
        try {
            const activityTable = this.getActivityTableName(recordType);
            const foreignKey = this.getForeignKeyName(recordType);
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                [foreignKey]: record.id,
                activity_date: new Date().toISOString().split('T')[0],
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            // Generate form fields for the activity table, excluding system fields and foreign keys
            const excludeFields = ['id', 'created_at', 'created_by', foreignKey];
            const fields = this.data.schema.generateFormFields(activityTable, excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            // Show the form
            this.ui.showForm(`Add Activity - ${this.getRecordDisplayName(recordType, record)}`, fields, async (formData) => {
                try {
                    // Add the foreign key to the form data
                    formData[foreignKey] = record.id;
                    formData.created_by = currentUser?.email || 'unknown';

                    console.log('Submitting activity data:', formData);

                    // Insert the activity
                    const result = await this.data.insert(activityTable, formData);
                    console.log('Activity inserted successfully:', result);

                    this.ui.showDialog('Success', 'Activity added successfully!', 'success');
                    return true; // Close the form

                } catch (error) {
                    console.error('Error adding activity:', error);
                    this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                    return false; // Keep the form open
                }
            });

        } catch (error) {
            console.error('Error showing standard activity form:', error);
            this.ui.showDialog('Error', `Failed to show standard activity form: ${error.message}`, 'error');
        }
    }

    /**
     * Add activity for a record
     */
    async addActivity(recordType, recordId, activityData) {
        try {
            const activityTable = this.getActivityTableName(recordType);
            const foreignKey = this.getForeignKeyName(recordType);
            const currentUser = this.auth.getCurrentUser();

            const fullActivityData = {
                ...activityData,
                [foreignKey]: recordId,
                created_by: currentUser?.email || 'unknown',
                created_at: new Date().toISOString()
            };

            return await this.data.insert(activityTable, fullActivityData);
        } catch (error) {
            console.error('Error adding activity:', error);
            throw error;
        }
    }

    /**
     * Show custom activity form with address search component
     */
    showCustomActivityForm(title, fields, onSubmit) {
        // Create modal overlay
        const modal = document.createElement('div');
        modal.className = 'modal-overlay activity-form-overlay';

        // Build form fields HTML
        const fieldsHTML = fields.map(field => {
            let fieldHTML = '';

            switch (field.type) {
                case 'select':
                    const options = field.options || [];
                    fieldHTML = `
                        <div class="form-field">
                            <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
                            <select id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                                ${options.map(opt => `<option value="${opt.value}" ${field.value === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
                            </select>
                        </div>
                    `;
                    break;
                case 'textarea':
                    fieldHTML = `
                        <div class="form-field">
                            <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
                            <textarea id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''} placeholder="${field.placeholder || ''}">${field.value || ''}</textarea>
                        </div>
                    `;
                    break;
                case 'checkbox':
                    fieldHTML = `
                        <div class="form-field checkbox-field">
                            <label>
                                <input type="checkbox" id="${field.name}" name="${field.name}" ${field.value ? 'checked' : ''}>
                                ${field.label}
                            </label>
                        </div>
                    `;
                    break;
                default:
                    fieldHTML = `
                        <div class="form-field">
                            <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
                            <input type="${field.type}" id="${field.name}" name="${field.name}"
                                   value="${field.value || ''}" ${field.required ? 'required' : ''}
                                   placeholder="${field.placeholder || ''}">
                        </div>
                    `;
                    break;
            }

            return fieldHTML;
        }).join('');

        modal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <form id="activity-form" class="activity-form">
                        ${fieldsHTML}

                        <div class="form-field">
                            <label>Address Location (Optional)</label>
                            <div id="address-search-container" class="address-search-container"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button type="submit" id="submit-activity" class="primary-button">Add Activity</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Initialize address search component
        const addressSearchContainer = modal.querySelector('#address-search-container');
        const addressSearch = new AddressSearchComponent(this.data, this.auth, this.ui, this.uiUtilities);
        let selectedAddress = null;

        addressSearch.createAddressSearchInterface(
            addressSearchContainer,
            {},
            (address) => {
                selectedAddress = address;
                console.log('Address selected:', address);
            }
        );

        // Handle form submission
        const submitBtn = modal.querySelector('#submit-activity');
        const form = modal.querySelector('#activity-form');

        const handleSubmit = async (e) => {
            e.preventDefault();

            // Collect form data
            const formData = new FormData(form);
            const data = {};

            for (let [key, value] of formData.entries()) {
                // Handle checkboxes
                const field = form.querySelector(`[name="${key}"]`);
                if (field && field.type === 'checkbox') {
                    data[key] = field.checked;
                } else {
                    data[key] = value;
                }
            }

            // Call the submit handler
            const success = await onSubmit(data, selectedAddress);

            if (success) {
                modal.remove();
            }
        };

        submitBtn.addEventListener('click', handleSubmit);
        form.addEventListener('submit', handleSubmit);
    }

    /**
     * Load activities for a record
     */
    async loadActivities(recordType, recordId) {
        try {
            const activityTable = this.getActivityTableName(recordType);
            const foreignKey = this.getForeignKeyName(recordType);

            return await this.data.search(activityTable, { [foreignKey]: recordId });
        } catch (error) {
            console.error('Error loading activities:', error);
            throw error;
        }
    }

    // === UTILITY METHODS ===

    getActivityTableName(recordType) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities',
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordType] || 'activities';
    }

    getForeignKeyName(recordType) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[recordType] || 'record_id';
    }

    getRecordDisplayName(recordType, record) {
        switch (recordType) {
            case 'people':
                return `${record.first_name || ''} ${record.last_name || ''}`.trim() || `Person #${record.id}`;
            case 'addresses':
                return record.address || `Address #${record.id}`;
            case 'license_plates':
                return record.plate_number || `Vehicle #${record.id}`;
            default:
                return `Record #${record.id}`;
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners if needed
    }
}
