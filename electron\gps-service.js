// GPS Service for Windows COM3 Serial Port
// Handles NMEA sentence parsing and provides live GPS coordinates
import { EventEmitter } from 'events';

export class GPSService extends EventEmitter {
    constructor() {
        super();
        this.port = null;
        this.parser = null;
        this.isConnected = false;
        this.lastKnownPosition = null;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 3;
        this.reconnectDelay = 5000; // 5 seconds
        this.dataTimeout = 30000; // 30 seconds without data before reconnect
        this.lastDataTime = null;
        this.watchdogTimer = null;
        
        // GPS configuration
        this.config = {
            port: 'COM3',
            baudRate: 4800,
            dataBits: 8,
            parity: 'none',
            stopBits: 1,
            flowControl: false
        };
    }

    async initialize() {
        try {
            console.log('🛰️ GPS Service: Initializing...');
            
            // Try to import serialport modules
            const { SerialPort } = await import('serialport');
            const { ReadlineParser } = await import('@serialport/parser-readline');
            
            this.SerialPort = SerialPort;
            this.ReadlineParser = ReadlineParser;
            
            console.log('✅ GPS Service: Serial port modules loaded successfully');
            
            // Start connection
            await this.connect();
            
            // Start watchdog timer
            this.startWatchdog();
            
            return true;
        } catch (error) {
            console.error('❌ GPS Service: Failed to initialize:', error.message);
            
            // If serialport is not available, emit error but don't crash
            this.emit('error', new Error(`GPS Service initialization failed: ${error.message}`));
            return false;
        }
    }

    async connect() {
        if (this.isConnected) {
            console.log('🛰️ GPS Service: Already connected');
            return;
        }

        try {
            console.log(`🛰️ GPS Service: Attempting to connect to ${this.config.port} at ${this.config.baudRate} baud...`);
            
            // Create serial port connection
            this.port = new this.SerialPort({
                path: this.config.port,
                baudRate: this.config.baudRate,
                dataBits: this.config.dataBits,
                parity: this.config.parity,
                stopBits: this.config.stopBits,
                flowControl: this.config.flowControl,
                autoOpen: false
            });

            // Create readline parser for NMEA sentences
            this.parser = this.port.pipe(new this.ReadlineParser({ 
                delimiter: '\r\n',
                encoding: 'ascii'
            }));

            // Set up event handlers
            this.setupEventHandlers();

            // Open the port
            await new Promise((resolve, reject) => {
                this.port.open((error) => {
                    if (error) {
                        reject(error);
                    } else {
                        resolve();
                    }
                });
            });

            this.isConnected = true;
            this.connectionAttempts = 0;
            console.log('✅ GPS Service: Connected successfully');
            this.emit('connected');

        } catch (error) {
            console.error(`❌ GPS Service: Connection failed:`, error.message);
            this.connectionAttempts++;
            
            if (this.connectionAttempts < this.maxConnectionAttempts) {
                console.log(`🔄 GPS Service: Retrying connection in ${this.reconnectDelay/1000}s (attempt ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
                setTimeout(() => this.connect(), this.reconnectDelay);
            } else {
                this.emit('error', new Error(`Failed to connect to GPS after ${this.maxConnectionAttempts} attempts: ${error.message}`));
            }
        }
    }

    setupEventHandlers() {
        // Handle incoming NMEA data
        this.parser.on('data', (line) => {
            this.lastDataTime = Date.now();
            this.processNMEALine(line.trim());
        });

        // Handle port errors
        this.port.on('error', (error) => {
            console.error('🛰️ GPS Service: Port error:', error.message);
            this.handleDisconnection();
        });

        // Handle port close
        this.port.on('close', () => {
            console.log('🛰️ GPS Service: Port closed');
            this.handleDisconnection();
        });

        // Handle parser errors
        this.parser.on('error', (error) => {
            console.error('🛰️ GPS Service: Parser error:', error.message);
        });
    }

    processNMEALine(line) {
        try {
            if (!line || !line.startsWith('$')) {
                return; // Not a valid NMEA sentence
            }

            console.log(`🛰️ GPS Service: Received NMEA: ${line}`);

            // Parse different NMEA sentence types
            if (line.startsWith('$GPRMC') || line.startsWith('$GNRMC')) {
                this.parseRMC(line);
            } else if (line.startsWith('$GPGGA') || line.startsWith('$GNGGA')) {
                this.parseGGA(line);
            }

        } catch (error) {
            console.error('🛰️ GPS Service: Error processing NMEA line:', error.message);
        }
    }

    parseRMC(sentence) {
        // $GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A
        const parts = sentence.split(',');
        
        if (parts.length < 12) {
            return; // Invalid sentence structure
        }

        try {
            const status = parts[2]; // 'A' = Active/Valid, 'V' = Void/Invalid
            const latitude = this.parseCoordinate(parts[3], parts[4]);
            const longitude = this.parseCoordinate(parts[5], parts[6]);
            const speed = parseFloat(parts[7]) || 0; // Speed in knots
            const course = parseFloat(parts[8]) || 0; // Course in degrees
            
            if (latitude !== null && longitude !== null) {
                const position = {
                    latitude,
                    longitude,
                    speed: speed * 1.852, // Convert knots to km/h
                    course,
                    timestamp: Date.now(),
                    source: status === 'A' ? 'RMC' : 'RMC-NoFix',
                    accuracy: status === 'A' ? 100 : 200, // 100m accuracy when GPS fix, 200m when no fix
                    fixStatus: status
                };

                console.log(`🛰️ GPS Service: RMC Position (${status === 'A' ? 'Valid' : 'No Fix'}): ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                this.updatePosition(position);
            }
        } catch (error) {
            console.error('🛰️ GPS Service: Error parsing RMC:', error.message);
        }
    }

    parseGGA(sentence) {
        // $GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47
        const parts = sentence.split(',');
        
        if (parts.length < 15) {
            return; // Invalid sentence structure
        }

        try {
            const latitude = this.parseCoordinate(parts[2], parts[3]);
            const longitude = this.parseCoordinate(parts[4], parts[5]);
            const quality = parseInt(parts[6]) || 0;
            const satellites = parseInt(parts[7]) || 0;
            const hdop = parseFloat(parts[8]) || 0;
            const altitude = parseFloat(parts[9]) || 0;
            
            if (latitude !== null && longitude !== null) {
                const position = {
                    latitude,
                    longitude,
                    altitude,
                    quality,
                    satellites,
                    hdop,
                    timestamp: Date.now(),
                    source: quality > 0 ? 'GGA' : 'GGA-NoFix',
                    accuracy: quality > 0 ? Math.max(100, hdop * 10) : 200, // 100m minimum accuracy when fix, 200m when no fix
                    fixQuality: quality
                };

                console.log(`🛰️ GPS Service: GGA Position (Quality: ${quality}): ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                this.updatePosition(position);
            }
        } catch (error) {
            console.error('🛰️ GPS Service: Error parsing GGA:', error.message);
        }
    }

    parseCoordinate(coord, direction) {
        if (!coord || !direction) return null;
        
        const degrees = Math.floor(parseFloat(coord) / 100);
        const minutes = parseFloat(coord) % 100;
        let decimal = degrees + (minutes / 60);
        
        if (direction === 'S' || direction === 'W') {
            decimal = -decimal;
        }
        
        return decimal;
    }

    updatePosition(position) {
        this.lastKnownPosition = position;
        console.log(`🛰️ GPS Service: Position update: ${position.latitude.toFixed(6)}, ${position.longitude.toFixed(6)} (${position.source})`);
        this.emit('position', position);
    }

    startWatchdog() {
        this.watchdogTimer = setInterval(() => {
            if (this.isConnected && this.lastDataTime) {
                const timeSinceLastData = Date.now() - this.lastDataTime;
                if (timeSinceLastData > this.dataTimeout) {
                    console.log('🛰️ GPS Service: No data received for 30s, reconnecting...');
                    this.handleDisconnection();
                }
            }
        }, 10000); // Check every 10 seconds
    }

    handleDisconnection() {
        this.isConnected = false;

        if (this.port && this.port.isOpen) {
            this.port.close();
        }

        // Only attempt to reconnect if we're not shutting down
        // (port will be null during shutdown)
        if (this.port !== null) {
            // Attempt to reconnect after delay
            setTimeout(() => {
                if (!this.isConnected && this.port !== null) {
                    this.connect();
                }
            }, this.reconnectDelay);
        }
    }

    getCurrentPosition() {
        return this.lastKnownPosition;
    }

    isGPSConnected() {
        return this.isConnected;
    }

    async disconnect() {
        console.log('🛰️ GPS Service: Disconnecting...');

        try {
            // Clear watchdog timer first
            if (this.watchdogTimer) {
                clearInterval(this.watchdogTimer);
                this.watchdogTimer = null;
                console.log('🛰️ GPS Service: Watchdog timer cleared');
            }

            // Set disconnected state to prevent reconnection attempts
            this.isConnected = false;

            // Close serial port connection
            if (this.port) {
                if (this.port.isOpen) {
                    console.log('🛰️ GPS Service: Closing serial port...');
                    await new Promise((resolve, reject) => {
                        const timeout = setTimeout(() => {
                            reject(new Error('Port close timeout'));
                        }, 5000); // 5 second timeout

                        this.port.close((error) => {
                            clearTimeout(timeout);
                            if (error) {
                                console.error('🛰️ GPS Service: Error closing port:', error.message);
                                reject(error);
                            } else {
                                console.log('🛰️ GPS Service: Serial port closed successfully');
                                resolve();
                            }
                        });
                    });
                } else {
                    console.log('🛰️ GPS Service: Port already closed');
                }

                // Clean up port reference
                this.port = null;
            }

            // Clean up parser reference
            if (this.parser) {
                this.parser = null;
                console.log('🛰️ GPS Service: Parser cleaned up');
            }

            // Remove all event listeners
            this.removeAllListeners();
            console.log('🛰️ GPS Service: Event listeners removed');

            console.log('✅ GPS Service: Disconnected successfully');
        } catch (error) {
            console.error('❌ GPS Service: Error during disconnect:', error.message);
            // Force cleanup even if there were errors
            this.isConnected = false;
            this.port = null;
            this.parser = null;
            this.removeAllListeners();
        }
    }
}
