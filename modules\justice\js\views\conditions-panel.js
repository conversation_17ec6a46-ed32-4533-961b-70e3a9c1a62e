// Conditions Panel View
// Displays active conditions and allows ending conditions

import { conditionsTemplates } from '../../templates/conditions-templates.js';

export class ConditionsPanel {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.container = null;
        this.episode = null;
        this.conditions = [];
    }
    
    async mount(container, episode) {
        this.container = container;
        this.episode = episode;
        
        try {
            // Load active conditions for the episode
            this.conditions = await this.justice.api.getActiveConditions(episode.id);
            
            // Update state
            this.justice.state.setConditions(this.conditions);
            
            // Render conditions panel
            this.render();
            
        } catch (error) {
            console.error('Failed to mount conditions panel:', error);
            this.renderError(error.message);
        }
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = conditionsTemplates.conditionsPanel({
            episode: this.episode,
            conditions: this.conditions
        });
        
        this.setupEventHandlers();
    }
    
    renderError(message) {
        if (!this.container) return;
        
        this.container.innerHTML = conditionsTemplates.errorView(message);
    }
    
    setupEventHandlers() {
        if (!this.container) return;
        
        // End condition buttons
        const endConditionBtns = this.container.querySelectorAll('.end-condition-btn');
        endConditionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const conditionId = e.target.dataset.conditionId;
                this.showEndConditionModal(conditionId);
            });
        });
        
        // View condition details buttons
        const viewDetailsBtns = this.container.querySelectorAll('.view-condition-details-btn');
        viewDetailsBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const conditionId = e.target.dataset.conditionId;
                this.showConditionDetails(conditionId);
            });
        });
        
        // Add new conditions button
        const addConditionsBtn = this.container.querySelector('#add-conditions-btn');
        if (addConditionsBtn) {
            addConditionsBtn.addEventListener('click', () => {
                this.showAddConditionsModal();
            });
        }
        
        // Refresh button
        const refreshBtn = this.container.querySelector('[data-action="refresh-conditions"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.refresh();
            });
        }
        
        // Condition toggle buttons for expanding/collapsing details
        const toggleBtns = this.container.querySelectorAll('.condition-toggle');
        toggleBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.toggleConditionDetails(e.target.closest('.condition-item'));
            });
        });
    }
    
    toggleConditionDetails(conditionItem) {
        const details = conditionItem.querySelector('.condition-details');
        const toggle = conditionItem.querySelector('.condition-toggle');
        
        if (details && toggle) {
            const isExpanded = details.style.display !== 'none';
            details.style.display = isExpanded ? 'none' : 'block';
            toggle.textContent = isExpanded ? '▶' : '▼';
        }
    }
    
    showConditionDetails(conditionId) {
        const condition = this.conditions.find(c => c.id === conditionId);
        if (!condition) return;
        
        const modal = this.justice.ui.createModal({
            id: 'condition-details-modal',
            title: 'Condition Details',
            size: 'medium'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = conditionsTemplates.conditionDetailsModal(condition);
        
        this.justice.ui.showModal(modal);
        
        // Close button
        const closeBtn = modal.querySelector('#close-details-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.justice.ui.closeModal(modal.id);
            });
        }
    }
    
    showEndConditionModal(conditionId) {
        const condition = this.conditions.find(c => c.id === conditionId);
        if (!condition) return;
        
        const modal = this.justice.ui.createModal({
            id: 'end-condition-modal',
            title: 'End Condition',
            size: 'medium'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = conditionsTemplates.endConditionModal(condition);
        
        this.justice.ui.showModal(modal);
        
        // Setup form handlers
        const form = modal.querySelector('#end-condition-form');
        const endBtn = modal.querySelector('#end-condition-btn');
        const cancelBtn = modal.querySelector('#cancel-end-btn');
        
        endBtn.addEventListener('click', async () => {
            await this.endCondition(form, modal, conditionId);
        });
        
        cancelBtn.addEventListener('click', () => {
            this.justice.ui.closeModal(modal.id);
        });
    }
    
    async endCondition(form, modal, conditionId) {
        try {
            const formData = new FormData(form);
            const endDt = formData.get('end_dt');
            const reason = formData.get('reason');
            
            if (!endDt) {
                this.justice.ui.showDialog('Error', 'End date/time is required.', 'error');
                return;
            }
            
            // Create an event to end the condition
            await this.justice.api.addEvent(
                this.episode.id,
                'CONDITIONS_ENDED',
                endDt,
                {
                    condition_id: conditionId,
                    reason: reason
                }
            );
            
            // Update local state
            this.justice.state.endCondition(conditionId, endDt);
            
            // Refresh the view
            await this.refresh();
            
            // Close modal
            this.justice.ui.closeModal(modal.id);
            
            this.justice.ui.showDialog('Success', 'Condition ended successfully.', 'success');
            
        } catch (error) {
            console.error('Failed to end condition:', error);
            this.justice.ui.showDialog('Error', `Failed to end condition: ${error.message}`, 'error');
        }
    }
    
    showAddConditionsModal() {
        // Delegate to the release conditions modal
        if (window.app && window.app.showAddReleaseConditionsModal) {
            window.app.showAddReleaseConditionsModal(this.episode.id);
        }
    }
    
    async refresh() {
        if (!this.episode) return;
        
        try {
            // Reload conditions
            this.conditions = await this.justice.api.getActiveConditions(this.episode.id);
            this.justice.state.setConditions(this.conditions);
            
            // Re-render
            this.render();
            
        } catch (error) {
            console.error('Failed to refresh conditions:', error);
            this.justice.ui.showDialog('Error', `Failed to refresh conditions: ${error.message}`, 'error');
        }
    }
    
    formatConditionType(type) {
        const labels = {
            'NO_CONTACT': 'No Contact',
            'NO_GO_ZONE': 'No-Go Zone',
            'CURFEW': 'Curfew',
            'REPORTING': 'Reporting',
            'RESIDENCE': 'Residence Requirement',
            'SURETY': 'Surety',
            'ABSTAIN_ALCOHOL': 'Abstain from Alcohol',
            'ABSTAIN_DRUGS': 'Abstain from Drugs',
            'SURRENDER_PASSPORT': 'Surrender Passport',
            'NO_WEAPONS': 'No Weapons',
            'KEEP_PEACE': 'Keep the Peace',
            'OTHER': 'Other'
        };
        
        return labels[type] || type;
    }
    
    formatConditionDetails(condition) {
        try {
            const details = typeof condition.details === 'string' 
                ? JSON.parse(condition.details) 
                : condition.details;
            
            switch (condition.type) {
                case 'NO_CONTACT':
                    return `No contact with: ${details.person || 'specified person'}`;
                case 'NO_GO_ZONE':
                    return `No-go zone: ${details.location || 'specified area'}`;
                case 'CURFEW':
                    return `Curfew: ${details.start_time || '22:00'} - ${details.end_time || '06:00'}`;
                case 'REPORTING':
                    return `Report to: ${details.officer || 'probation officer'} ${details.frequency || 'weekly'}`;
                case 'RESIDENCE':
                    return `Reside at: ${details.address || 'approved address'}`;
                case 'SURETY':
                    return `Surety: ${details.name || 'approved surety'} ($${details.amount || '0'})`;
                default:
                    return details.description || 'See full details';
            }
        } catch (error) {
            return 'See full details';
        }
    }
    
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    unmount() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.container = null;
        this.episode = null;
        this.conditions = [];
    }
}
