{"permissions": {"allow": ["Bash(claude mcp add:*)", "Bash(find:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__context7__get-library-docs", "Bash(npx @supabase/mcp-server-supabase@latest:*)", "<PERSON><PERSON>(tail:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(rm:*)", "<PERSON><PERSON>(sed:*)", "Bash(node:*)", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "Bash(npm run dev:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__supabase__list_tables", "Bash(timeout 30 npm run dev)", "Bash(asar extract:*)", "Bash(npm run clean:*)", "Bash(npm run build:*)", "Bash(npm install:*)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(dir:*)", "Bash(del cache.db cache.db-shm cache.db-wal)", "<PERSON><PERSON>(move:*)", "<PERSON><PERSON>(mv:*)", "Bash(timeout 30s npm run dev)", "Bash(wc:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(npx electron:*)", "<PERSON><PERSON>(launch.bat)", "Bash(git rm:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(sqlite3:*)", "Bash(DEBUG=electron-builder npx electron-builder --win)", "WebFetch(domain:developers.google.com)", "Bash(cp:*)", "Bash(del /f /q nul)", "Bash(for file in renderer/js/modules/incident-management/*.js)", "Bash(do echo \"Checking $file\")", "<PERSON><PERSON>(echo:*)", "Bash(done)", "Bash(awk:*)"], "deny": []}}