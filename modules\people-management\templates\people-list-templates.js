/**
 * People List Templates
 * Template functions for people list displays
 */

export const peopleListTemplates = {
    /**
     * People list modal template
     */
    peopleListModal(people) {
        return `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h2>People Directory</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="add-person">
                            <span class="button-icon">👤</span>
                            Add Person
                        </button>
                        <button class="secondary-button" id="back-to-records-btn">
                            <span class="button-icon">←</span>
                            Back
                        </button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="search-section">
                        <div class="search-bar">
                            <input type="text" id="people-filter" placeholder="Search people by name, email, phone..." class="search-input">
                            <button class="search-button">🔍</button>
                        </div>
                    </div>
                    
                    <div class="people-count">
                        <span>${people.length}</span> people found
                    </div>
                    
                    <div class="people-list-grid">
                        ${people.map(person => this.personCard(person)).join('')}
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * Individual person card template
     */
    personCard(person) {
        return `
            <div class="person-list-item" data-person-id="${person.id}">
                <div class="person-card-header">
                    <h3 class="person-name">${person.first_name} ${person.last_name}</h3>
                    <div class="person-actions">
                        <button class="action-btn small edit-btn" data-action="edit-person" data-person-id="${person.id}" title="Edit">✏️</button>
                        <button class="action-btn small view-btn" data-action="view-person-detail" data-person-id="${person.id}" title="View Details">👁️</button>
                    </div>
                </div>
                
                <div class="person-card-body">
                    ${person.email ? `<div class="detail-item"><span class="icon">📧</span> ${person.email}</div>` : ''}
                    ${person.phone ? `<div class="detail-item"><span class="icon">📱</span> ${person.phone}</div>` : ''}
                    ${person.housing_status ? `<div class="detail-item"><span class="icon">🏠</span> ${person.housing_status}</div>` : ''}
                    ${person.date_of_birth ? `<div class="detail-item"><span class="icon">🎂</span> ${new Date(person.date_of_birth).toLocaleDateString()}</div>` : ''}
                </div>
                
                <div class="person-card-footer">
                    <small>Created: ${new Date(person.created_at).toLocaleDateString()}</small>
                    ${person.updated_at ? `<small>Updated: ${new Date(person.updated_at).toLocaleDateString()}</small>` : ''}
                </div>
            </div>
        `;
    },

    /**
     * People list content template (for main screen integration)
     */
    peopleListContent(people) {
        return `
            <div class="people-list-container">
                <div class="people-count">
                    <span id="people-count">${people.length}</span> people found
                </div>
                <div class="people-list" id="people-list">
                    ${people.map(person => this.personListItem(person)).join('')}
                </div>
            </div>
        `;
    },

    /**
     * Person list item template (for main screen) - Updated to match address/organization pattern
     */
    personListItem(person) {
        const personName = `${person.first_name || ''} ${person.last_name || ''}`.trim() || 'Unknown Person';
        const contactInfo = [
            person.email ? `Email: ${person.email}` : '',
            person.phone ? `Phone: ${person.phone}` : ''
        ].filter(Boolean).join('<br>');

        const housingStatus = person.housing_status || 'Unknown';
        const createdDate = person.created_at ? new Date(person.created_at).toLocaleDateString() : 'Unknown';

        return `
            <div class="person-list-item" data-person-id="${person.id}">
                <div class="person-name">${personName}</div>
                <div class="person-contact">${contactInfo || 'No contact info'}</div>
                <div class="person-housing">${housingStatus}</div>
                <div class="person-created">${createdDate}</div>
                <div class="person-actions">
                    <button class="secondary-button" data-action="view-person-detail" data-person-id="${person.id}">View</button>
                    <button class="action-button" data-action="edit-person" data-person-id="${person.id}">Edit</button>
                </div>
            </div>
        `;
    },

    /**
     * Person list item template (legacy card style - kept for compatibility)
     */
    personCard(person) {
        return `
            <div class="person-card" data-person-id="${person.id}" data-action="view-person-detail">
                <div class="person-header">
                    <h3 class="person-name">${person.first_name} ${person.last_name}</h3>
                    <div class="person-actions">
                        <button class="action-btn edit-btn" data-action="edit-person" data-person-id="${person.id}" title="Edit Person">✏️</button>
                    </div>
                </div>
                <div class="person-details">
                    ${person.email ? `<div class="detail-item"><span class="label">📧</span> ${person.email}</div>` : ''}
                    ${person.phone ? `<div class="detail-item"><span class="label">📱</span> ${person.phone}</div>` : ''}
                    ${person.housing_status ? `<div class="detail-item"><span class="label">🏠</span> ${person.housing_status}</div>` : ''}
                    ${person.date_of_birth ? `<div class="detail-item"><span class="label">🎂</span> ${new Date(person.date_of_birth).toLocaleDateString()}</div>` : ''}
                </div>
                <div class="person-meta">
                    <small>Created: ${new Date(person.created_at).toLocaleDateString()}</small>
                </div>
            </div>
        `;
    },

    /**
     * Empty state template
     */
    emptyState() {
        return `
            <div class="empty-state">
                <div class="empty-icon">👥</div>
                <h3>No People Found</h3>
                <p>Get started by adding your first person record.</p>
                <button class="primary-button" data-action="add-person">
                    <span class="button-icon">👤</span>
                    Add Person
                </button>
            </div>
        `;
    }
};