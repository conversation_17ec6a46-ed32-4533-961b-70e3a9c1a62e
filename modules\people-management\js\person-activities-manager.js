/**
 * Person Activities Manager
 * Handles person activity operations with address integration
 * Follows the pattern established by vehicle-activities-manager.js
 */

import { BaseManager } from '../../shared/base-manager.js';
import { AddressSearchComponent } from '../../activity-management/js/address-search-component.js';

export class PersonActivitiesManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    /**
     * Load person activities
     */
    async loadPersonActivities(personId) {
        try {
            // Access people_activities from core schema
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: activities, error } = await supabase
                .schema('core')
                .from('people_activities')
                .select('*')
                .eq('person_id', String(personId))
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            return activities || [];
        } catch (error) {
            console.error('Error loading person activities:', error);
            throw error;
        }
    }

    /**
     * Add person activity
     */
    async addPersonActivity(personId, activityData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            
            const activityRecord = {
                person_id: String(personId),
                activity_type: activityData.activity_type,
                title: activityData.title || activityData.activity_type,
                description: activityData.description,
                location: activityData.location,
                coordinates: activityData.coordinates,
                activity_date: activityData.activity_date,
                activity_time: activityData.activity_time,
                staff_member: activityData.staff_member || currentUser?.name || currentUser?.email || 'Unknown Staff',
                outcome: activityData.outcome,
                follow_up_required: activityData.follow_up_required || false,
                follow_up_date: activityData.follow_up_date,
                priority: activityData.priority,
                tags: activityData.tags,
                attachments: activityData.attachments,
                address_id: activityData.address_id ? String(activityData.address_id) : null, // NEW: Address association
                created_by: currentUser?.email || 'unknown'
            };

            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data, error } = await supabase
                .schema('core')
                .from('people_activities')
                .insert([activityRecord])
                .select()
                .single();

            if (error) {
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Error adding person activity:', error);
            throw error;
        }
    }

    /**
     * Update person activity
     */
    async updatePersonActivity(activityId, updateData) {
        try {
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data, error } = await supabase
                .schema('core')
                .from('people_activities')
                .update(updateData)
                .eq('id', activityId)
                .select()
                .single();

            if (error) {
                throw error;
            }

            return data;
        } catch (error) {
            console.error('Error updating person activity:', error);
            throw error;
        }
    }

    /**
     * Delete person activity
     */
    async deletePersonActivity(activityId) {
        try {
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { error } = await supabase
                .schema('core')
                .from('people_activities')
                .delete()
                .eq('id', activityId);

            if (error) {
                throw error;
            }

            return true;
        } catch (error) {
            console.error('Error deleting person activity:', error);
            throw error;
        }
    }

    /**
     * Show add activity form with address integration
     */
    async showAddActivityForm(personId) {
        try {
            // Get person info for context
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: person, error } = await supabase
                .schema('case_mgmt')
                .from('people')
                .select('*')
                .eq('id', personId)
                .single();

            if (error || !person) {
                throw new Error('Person not found');
            }

            const currentUser = this.auth.getCurrentUser();
            
            // Pre-fill some fields
            const defaultData = {
                person_id: person.id,
                activity_date: new Date().toISOString().split('T')[0],
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            // Generate form fields for people_activities table, excluding address_id since we'll handle it separately
            const excludeFields = ['id', 'created_at', 'created_by', 'person_id', 'title', 'address_id'];
            const fields = this.data.schema.generateFormFields('people_activities', excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            const personName = `${person.first_name || ''} ${person.last_name || ''}`.trim() || 'Unknown Person';

            // Show custom form with address search
            this.showCustomActivityForm(
                `Add Activity - ${personName}`,
                fields,
                async (formData, selectedAddress) => {
                    try {
                        // Add address_id if an address was selected
                        if (selectedAddress) {
                            formData.address_id = selectedAddress.id;
                        }

                        await this.addPersonActivity(personId, formData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        // Refresh activities if detail view is open
                        const activitiesList = document.getElementById('activities-list');
                        if (activitiesList) {
                            const activities = await this.loadPersonActivities(personId);
                            this.displayActivities(activities, activitiesList);
                        }

                        return true; // Success
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false; // Stay on form
                    }
                }
            );

        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show form: ${error.message}`, 'error');
        }
    }

    /**
     * Show custom activity form with address search component
     */
    showCustomActivityForm(title, fields, onSubmit) {
        // Create modal overlay
        const modal = document.createElement('div');
        modal.className = 'modal-overlay activity-form-overlay';

        // Build form fields HTML
        const fieldsHTML = fields.map(field => {
            let fieldHTML = '';

            switch (field.type) {
                case 'select':
                    const options = field.options || [];
                    fieldHTML = `
                        <div class="form-field">
                            <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
                            <select id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                                ${options.map(opt => `<option value="${opt.value}" ${field.value === opt.value ? 'selected' : ''}>${opt.label}</option>`).join('')}
                            </select>
                        </div>
                    `;
                    break;
                case 'textarea':
                    fieldHTML = `
                        <div class="form-field">
                            <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
                            <textarea id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''} placeholder="${field.placeholder || ''}">${field.value || ''}</textarea>
                        </div>
                    `;
                    break;
                case 'checkbox':
                    fieldHTML = `
                        <div class="form-field checkbox-field">
                            <label>
                                <input type="checkbox" id="${field.name}" name="${field.name}" ${field.value ? 'checked' : ''}>
                                ${field.label}
                            </label>
                        </div>
                    `;
                    break;
                default:
                    fieldHTML = `
                        <div class="form-field">
                            <label for="${field.name}">${field.label}${field.required ? ' *' : ''}</label>
                            <input type="${field.type}" id="${field.name}" name="${field.name}"
                                   value="${field.value || ''}" ${field.required ? 'required' : ''}
                                   placeholder="${field.placeholder || ''}">
                        </div>
                    `;
                    break;
            }

            return fieldHTML;
        }).join('');

        modal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <form id="activity-form" class="activity-form">
                        ${fieldsHTML}

                        <div class="form-field">
                            <label>Address Location (Optional)</label>
                            <div id="address-search-container" class="address-search-container"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button type="submit" id="submit-activity" class="primary-button">Add Activity</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Initialize address search component
        const addressSearchContainer = modal.querySelector('#address-search-container');
        const addressSearch = new AddressSearchComponent(this.data, this.auth, this.ui, this.uiUtilities);
        let selectedAddress = null;

        addressSearch.createAddressSearchInterface(
            addressSearchContainer,
            {},
            (address) => {
                selectedAddress = address;
                console.log('Address selected:', address);
            }
        );

        // Handle form submission
        const submitBtn = modal.querySelector('#submit-activity');
        const form = modal.querySelector('#activity-form');

        const handleSubmit = async (e) => {
            e.preventDefault();

            // Collect form data
            const formData = new FormData(form);
            const data = {};

            for (let [key, value] of formData.entries()) {
                // Handle checkboxes
                const field = form.querySelector(`[name="${key}"]`);
                if (field && field.type === 'checkbox') {
                    data[key] = field.checked;
                } else {
                    data[key] = value;
                }
            }

            // Call the submit handler
            const success = await onSubmit(data, selectedAddress);

            if (success) {
                modal.remove();
            }
        };

        submitBtn.addEventListener('click', handleSubmit);
        form.addEventListener('submit', handleSubmit);
    }




    /**
     * Display activities in a container
     */
    async displayActivities(activities, container) {
        try {
            if (!activities || activities.length === 0) {
                container.innerHTML = '<div class="no-activities">No activities found.</div>';
                return;
            }

            // Import templates
            const { personActivityTemplates } = await import('../templates/person-activity-templates.js');
            
            container.innerHTML = activities.map(activity => 
                personActivityTemplates.activityItem(activity)
            ).join('');
        } catch (error) {
            console.error('Error displaying activities:', error);
            container.innerHTML = '<div class="error">Error loading activities.</div>';
        }
    }
}
