/**
 * Narrative Manager
 * 
 * Core narrative management functionality including entry persistence,
 * data validation, and coordination between form and render managers.
 */

import { BaseManager } from '../../shared/base-manager.js';
import { IdGenerator } from '../../shared/id-generator.js';

export class NarrativeManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, configManager) {
        super(dataManager, authManager, uiManager, configManager);
        
        this.formManager = null;
        this.renderManager = null;
    }

    init() {
        // Initialize Gemini service if available
        this.initializeGeminiService();
    }

    // Set manager references
    setFormManager(formManager) {
        this.formManager = formManager;
    }

    setRenderManager(renderManager) {
        this.renderManager = renderManager;
    }

    // Entry validation
    validateEntry(entryData) {
        const errors = [];

        if (!entryData.entry_type || entryData.entry_type.trim() === '') {
            errors.push('Entry type is required');
        }

        if (!entryData.content || entryData.content.trim() === '') {
            errors.push('Content is required');
        }

        if (!entryData.event_time) {
            errors.push('Event time is required');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // Entry creation
    createEntry(entryData) {
        const context = this.getCurrentUserContext();
        
        return {
            id: this.generateEntryId(),
            timestamp: new Date().toISOString(),
            event_time: entryData.event_time ? new Date(entryData.event_time).toISOString() : new Date().toISOString(),
            user: context.userEmail,
            entry_type: entryData.entry_type,
            content: entryData.content.trim()
        };
    }

    // Entry updating
    updateEntry(existingEntry, entryData) {
        return {
            ...existingEntry,
            event_time: entryData.event_time ? new Date(entryData.event_time).toISOString() : existingEntry.event_time,
            entry_type: entryData.entry_type,
            content: entryData.content.trim()
        };
    }

    // ID generation
    generateEntryId() {
        return IdGenerator.generateBase36Id('entry');
    }

    // Date/time utilities
    getCurrentDateTimeLocal() {
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        return now.toISOString().slice(0, 16);
    }

    formatDateTimeForInput(dateTime) {
        if (!dateTime) return this.getCurrentDateTimeLocal();
        
        const date = new Date(dateTime);
        date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
        return date.toISOString().slice(0, 16);
    }

    // Content formatting utilities
    convertHtmlToStorage(htmlContent) {
        if (!htmlContent) return '';

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        let text = tempDiv.innerHTML
            .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
            .replace(/<b>(.*?)<\/b>/g, '**$1**')
            .replace(/<em>(.*?)<\/em>/g, '*$1*')
            .replace(/<i>(.*?)<\/i>/g, '*$1*')
            .replace(/<u>(.*?)<\/u>/g, '__$1__')
            .replace(/<br\s*\/?>/g, '\n')
            .replace(/<\/div>/g, '\n')
            .replace(/<div>/g, '')
            .replace(/<ul>/g, '')
            .replace(/<\/ul>/g, '')
            .replace(/<ol>/g, '')
            .replace(/<\/ol>/g, '')
            .replace(/<li>(.*?)<\/li>/g, '• $1\n')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#x27;/g, "'");

        return text.replace(/\n\s*\n/g, '\n').trim();
    }

    convertStoredToHtml(content) {
        if (!content) return '';
        
        if (content.includes('<') && content.includes('>')) {
            return content;
        }

        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<u>$1</u>')
            .replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em>$1</em>')
            .replace(/^• (.+)$/gm, '<li>$1</li>')
            .replace(/^(\d+)\. (.+)$/gm, '<li>$1. $2</li>')
            .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
    }

    convertTextToHtml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // HTML utilities
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatTextForDisplay(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // Gemini AI integration
    initializeGeminiService() {
        if (!this.geminiService && this.data && this.config) {
            try {
                // Dynamically import GeminiService if available
                import('../../ai/js/index.js').then(({ GeminiService }) => {
                    this.geminiService = new GeminiService(this.data, this.config.vaultManager);
                    console.log('NarrativeManager: Gemini service initialized');
                }).catch(error => {
                    console.log('GeminiService not available:', error.message);
                });
            } catch (error) {
                console.log('Could not initialize Gemini service:', error.message);
            }
        }
    }

    async enhanceTextWithAI(originalText, incidentContext = null) {
        if (!this.geminiService) {
            throw new Error('AI enhancement service is not available');
        }

        return await this.geminiService.enhanceText(originalText, null, incidentContext);
    }

    // Error handling
    handleError(error, operation) {
        console.error(`Narrative Manager - ${operation}:`, error);
        
        if (this.ui?.showToast) {
            this.ui.showToast(`Failed to ${operation.toLowerCase()}. Please try again.`, 'error');
        }
        
        throw error;
    }
}