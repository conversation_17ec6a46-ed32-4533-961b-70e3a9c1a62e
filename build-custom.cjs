const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

console.log('🔧 Starting custom build process...');

const appName = 'S.T.E.V.I Retro';
const buildDir = path.join(__dirname, 'dist', 'win-unpacked');

try {
    // Clean previous builds
    console.log('📁 Cleaning previous builds...');
    if (fs.existsSync(path.join(__dirname, 'dist'))) {
        fs.removeSync(path.join(__dirname, 'dist'));
    }
    
    // Create build directory
    fs.ensureDirSync(buildDir);
    
    // Install electron-packager if needed
    console.log('📦 Installing electron-packager...');
    try {
        execSync('npm install --save-dev electron-packager', { stdio: 'inherit' });
    } catch (error) {
        console.log('electron-packager already installed or failed to install');
    }
    
    // Package the app
    console.log('⚡ Packaging Electron app...');
    const packageCommand = `npx electron-packager . "${appName}" --platform=win32 --arch=x64 --out=dist --overwrite --app-version=1.3.0 --build-version=1.3.0 --app-copyright="Copyright © 2024 I.H.A.R.C. All rights reserved." --version-string.CompanyName="I.H.A.R.C" --version-string.FileDescription="S.T.E.V.I Retro - Field Staff Terminal" --version-string.ProductName="S.T.E.V.I Retro"`;
    
    execSync(packageCommand, { stdio: 'inherit' });
    
    // Copy additional resources
    console.log('📋 Copying additional resources...');
    const sourceDir = path.join(__dirname, `dist/${appName}-win32-x64`);
    const targetDir = path.join(__dirname, 'dist/win-unpacked');
    
    if (fs.existsSync(sourceDir)) {
        fs.copySync(sourceDir, targetDir);
        fs.removeSync(sourceDir); // Remove the original folder
    }
    
    console.log('✅ Custom build completed successfully!');
    console.log(`📍 Build location: ${targetDir}`);
    
} catch (error) {
    console.error('❌ Build failed:', error.message);
    process.exit(1);
}