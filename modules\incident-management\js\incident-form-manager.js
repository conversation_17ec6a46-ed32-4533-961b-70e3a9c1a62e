/**
 * Incident Form Manager
 * Handles incident form creation, editing, validation, and submission
 * Extracted from app.js - manages ~15 form-related functions (~1500 lines)
 */

import { BaseManager } from '../../shared/base-manager.js';
import { incidentFormTemplates } from '../templates/index.js';

export class IncidentFormManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.uploadedFiles = [];
        this.currentIncident = null;
        this.googleMapsLoaded = false;
        this.autoSaveInterval = null;
        this.autoSaveActive = false;
    }

    async loadComprehensiveIncidentForm() {
        try {
            console.log('Loading comprehensive incident form...');
            
            // Get form template - fix: use correct method name
            const formHtml = incidentFormTemplates.createIncidentForm();
            
            // Initialize form state
            this.uploadedFiles = [];
            this.currentIncident = null;
            
            return formHtml;
            
        } catch (error) {
            console.error('Error loading comprehensive incident form:', error);
            return '<div class="error">Failed to load incident form</div>';
        }
    }

    async loadEditIncidentContent(incidentId) {
        try {
            console.log('Loading edit incident form for incident:', incidentId);
            
            if (!incidentId) {
                throw new Error('Incident ID is required for edit form');
            }
            
            // Get the incident data
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) {
                throw new Error(`Incident with ID ${incidentId} not found`);
            }
            
            // Get edit form template with incident data
            const formHtml = incidentFormTemplates.editIncidentForm(incident);
            
            return formHtml;
            
        } catch (error) {
            console.error('Error loading edit incident form:', error);
            return `<div class="error">Failed to load edit incident form: ${error.message}</div>`;
        }
    }

    async setupComprehensiveIncidentForm() {
        console.log('Setting up comprehensive incident form...');
        
        try {
            // Initialize form functionality
            this.initializeIncidentTabs();
            this.initializeConditionalFields();
            this.setupIncidentFormEventHandlers();
            this.setupIncidentAddressSearch();
            this.initializePeopleManagement();
            this.setupFileUpload();
            
            // Note: Draft loading is handled by the parent incident management system
            // to ensure proper manager communication setup
            
            console.log('Comprehensive incident form setup complete');
            
        } catch (error) {
            console.error('Error setting up comprehensive incident form:', error);
        }
    }

    async setupEditIncidentForm() {
        console.log('Setting up edit incident form...');
        
        try {
            // Get incident ID from URL or stored state
            const incidentId = this.getEditIncidentId();
            
            if (!incidentId) {
                this.ui.showDialog('Error', 'No incident selected for editing.', 'error');
                return;
            }
            
            // Load incident data
            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);
            
            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }
            
            // Store current incident
            this.currentIncident = incident;
            
            // Populate form with incident data
            await this.populateEditForm(incident);
            
            // Initialize form functionality
            this.initializeIncidentTabs();
            this.initializeConditionalFields();
            this.setupIncidentFormEventHandlers();
            
            // Set proper initial visibility for datetime fields (edit forms should show them by default)
            this.setInitialEditFormState();
            this.setupIncidentAddressSearch();
            this.initializePeopleManagement();
            this.setupFileUpload();
            
            // Load existing people relationships
            await this.loadExistingIncidentPeople();
            
            // Load existing attachments
            await this.loadIncidentAttachments(incidentId);
            
            console.log('Edit incident form setup complete');
            
        } catch (error) {
            console.error('Error setting up edit incident form:', error);
            this.ui.showDialog('Error', `Failed to setup edit form: ${error.message}`, 'error');
        }
    }

    initializeIncidentTabs() {
        const tabs = document.querySelectorAll('.incident-form-tab');
        const contents = document.querySelectorAll('.tab-content');

        // Tab switching
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const targetTab = tab.dataset.tab;
                console.log('Tab clicked:', targetTab);

                // Update tab appearance
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Update content visibility
                contents.forEach(content => {
                    content.classList.remove('active');
                    if (content.dataset.tab === targetTab) {
                        content.classList.add('active');
                    }
                });

                // Special handling for location tab
                if (targetTab === 'location') {
                    setTimeout(() => {
                        this.setupIncidentAddressSearch();
                    }, 100);
                }
            });
        });

        console.log('Found tabs:', tabs.length, 'Found contents:', contents.length);
    }

    initializeConditionalFields() {
        console.log('Initializing conditional fields...');
        
        // Use current time checkbox
        const useCurrentTimeCheckbox = document.querySelector('input[name="use_current_time"]');
        const datetimeGroup = document.getElementById('datetime-group');
        const datetimeGroupTime = document.getElementById('datetime-group-time');
        const incidentDateInput = document.getElementById('incident_date');
        const incidentTimeInput = document.getElementById('incident_time');
        
        if (useCurrentTimeCheckbox) {
            // Set initial state
            this.handleUseCurrentTimeChange(useCurrentTimeCheckbox, datetimeGroup, datetimeGroupTime, incidentDateInput, incidentTimeInput);
            
            // Add event listener
            useCurrentTimeCheckbox.addEventListener('change', () => {
                this.handleUseCurrentTimeChange(useCurrentTimeCheckbox, datetimeGroup, datetimeGroupTime, incidentDateInput, incidentTimeInput);
            });
        }
        
        // Property recovery conditional fields
        const propertyRecoveredCheckbox = document.getElementById('property_recovered');
        const propertyDetails = document.getElementById('property-details');
        
        if (propertyRecoveredCheckbox && propertyDetails) {
            propertyRecoveredCheckbox.addEventListener('change', () => {
                propertyDetails.style.display = propertyRecoveredCheckbox.checked ? 'block' : 'none';
            });
        }
        
        // Follow-up required conditional fields
        const followUpCheckbox = document.querySelector('input[name="follow_up_required"]');
        const followUpDetails = document.getElementById('followup-details');
        
        if (followUpCheckbox && followUpDetails) {
            followUpCheckbox.addEventListener('change', () => {
                followUpDetails.style.display = followUpCheckbox.checked ? 'block' : 'none';
            });
        }
        
        // Agency response conditional fields
        this.setupAgencyResponseFields();
    }
    
    handleUseCurrentTimeChange(checkbox, datetimeGroup, datetimeGroupTime, dateInput, timeInput) {
        if (checkbox.checked) {
            // Hide manual date/time inputs
            if (datetimeGroup) datetimeGroup.style.display = 'none';
            if (datetimeGroupTime) datetimeGroupTime.style.display = 'none';
            
            // Set current date and time
            const now = new Date();
            const currentDate = now.toISOString().split('T')[0];
            const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);
            
            if (dateInput) dateInput.value = currentDate;
            if (timeInput) timeInput.value = currentTime;
        } else {
            // Show manual date/time inputs
            if (datetimeGroup) datetimeGroup.style.display = 'block';
            if (datetimeGroupTime) datetimeGroupTime.style.display = 'block';
        }
    }
    
    setupAgencyResponseFields() {
        // Police notified conditional fields
        const policeCheckbox = document.querySelector('input[name="police_notified"]');
        const policeDetails = document.getElementById('police-response-details');
        
        if (policeCheckbox && policeDetails) {
            policeCheckbox.addEventListener('change', () => {
                policeDetails.style.display = policeCheckbox.checked ? 'block' : 'none';
            });
        }
        
        // Fire department conditional fields
        const fireCheckbox = document.querySelector('input[name="fire_department_called"]');
        const fireDetails = document.getElementById('fire-response-details');
        
        if (fireCheckbox && fireDetails) {
            fireCheckbox.addEventListener('change', () => {
                fireDetails.style.display = fireCheckbox.checked ? 'block' : 'none';
            });
        }
        
        // Paramedics conditional fields
        const paramedicCheckbox = document.querySelector('input[name="paramedics_called"]');
        const paramedicDetails = document.getElementById('paramedic-response-details');
        
        if (paramedicCheckbox && paramedicDetails) {
            paramedicCheckbox.addEventListener('change', () => {
                paramedicDetails.style.display = paramedicCheckbox.checked ? 'block' : 'none';
            });
        }
        
        // Bylaw conditional fields
        const bylawCheckbox = document.querySelector('input[name="bylaw_notified"]');
        const bylawDetails = document.getElementById('bylaw-response-details');
        
        if (bylawCheckbox && bylawDetails) {
            bylawCheckbox.addEventListener('change', () => {
                bylawDetails.style.display = bylawCheckbox.checked ? 'block' : 'none';
            });
        }
    }

    setupIncidentFormEventHandlers() {
        // Form submission handlers
        const createForm = document.getElementById('comprehensive-incident-form');
        const editForm = document.getElementById('edit-incident-form');
        
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleComprehensiveIncidentSubmit();
            });
        }
        
        if (editForm) {
            editForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEditIncidentFormSubmission();
            });
        }
        
        // Draft saving handlers
        const saveDraftButton = document.getElementById('save-draft');
        const saveEditDraftButton = document.getElementById('save-edit-draft');
        
        if (saveDraftButton) {
            saveDraftButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveIncidentAsDraft();
            });
        }

        if (saveEditDraftButton) {
            saveEditDraftButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.autoSaveIncident(); // For edit forms, just auto-save
            });
        }
        
        // Cancel button handlers
        const cancelButtons = document.querySelectorAll('.cancel-incident-form, [data-action="cancel-incident"]');
        cancelButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleCancelIncident();
            });
        });

        // Back button handlers
        const backButtons = document.querySelectorAll('.incident-tab-back, [data-action="back-to-incidents"]');
        backButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleBackToIncidents();
            });
        });

        // Auto-save functionality
        this.setupInputAutoSave();
    }

    handleCancelIncident() {
        // Confirm if user wants to cancel (if form has data)
        const hasFormData = this.checkForFormData();
        
        if (hasFormData) {
            const confirmed = confirm('You have unsaved changes. Are you sure you want to cancel?');
            if (!confirmed) {
                return;
            }
        }
        
        // Clear any drafts if user confirms cancellation
        this.clearDraft();
        
        // Navigate back to incidents
        this.handleBackToIncidents();
    }

    handleBackToIncidents() {
        // Navigate back to incidents list
        if (window.app && window.app.loadTabContent) {
            window.app.loadTabContent('incidents');
        } else {
            console.error('Cannot navigate back - app.loadTabContent not available');
        }
    }

    checkForFormData() {
        const form = document.getElementById('comprehensive-incident-form') || 
                    document.getElementById('edit-incident-form');
        
        if (!form) return false;
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Check if any field has a value
        return Object.values(data).some(value => value && value.toString().trim() !== '');
    }

    clearDraft() {
        // Clear any saved draft for this form
        if (this.draftManager) {
            this.draftManager.clearDraftIncident();
        }
    }

    async handleComprehensiveIncidentSubmit() {
        try {
            console.log('Handling comprehensive incident submission...');
            
            // Get form data
            const formData = this.collectIncidentFormData();
            console.log('Collected form data:', formData);
            
            // Validate form data
            const validationResult = await this.validateIncidentData(formData);
            console.log('Validation result:', validationResult);
            if (!validationResult.isValid) {
                this.ui.showDialog('Validation Error', validationResult.errors.join('\n'), 'error');
                return;
            }
            
            // Show loading state
            const submitButton = document.querySelector('#comprehensive-incident-form button[type="submit"]');
            if (submitButton) {
                submitButton.textContent = 'Creating Incident...';
                submitButton.disabled = true;
            }
            
            try {
                // Create incident
                const incident = await this.createIncident(formData);
                
                // Upload files if any
                if (this.uploadedFiles.length > 0) {
                    await this.uploadIncidentFiles(incident.id);
                }
                
                // Save people relationships if any
                if (formData.involvedPeople && formData.involvedPeople.length > 0) {
                    await this.saveIncidentPeopleRelationships(incident.id);
                }
                
                // Clear draft if exists
                await this.clearDraftIncident();
                
                // Show success message
                this.uiUtilities.showToast(`Incident #${incident.incident_number} created successfully`, 'success');
                
                // Navigate back to incidents view
                await this.navigateToIncidents();
                
                console.log('Incident created successfully:', incident.id);
                
            } finally {
                // Reset submit button
                if (submitButton) {
                    submitButton.textContent = 'Create Incident';
                    submitButton.disabled = false;
                }
            }
            
        } catch (error) {
            console.error('Error submitting incident:', error);
            this.ui.showDialog('Error', `Failed to create incident: ${error.message}`, 'error');
        }
    }

    async handleEditIncidentFormSubmission() {
        try {
            console.log('Handling edit incident submission...');
            
            if (!this.currentIncident) {
                this.ui.showDialog('Error', 'No incident data available for editing.', 'error');
                return;
            }
            
            // Get form data
            const formData = this.collectIncidentFormData();
            
            // Validate form data
            const validationResult = await this.validateIncidentData(formData);
            if (!validationResult.isValid) {
                this.ui.showDialog('Validation Error', validationResult.errors.join('\n'), 'error');
                return;
            }
            
            // Show loading state
            const submitButton = document.querySelector('#edit-incident-form button[type="submit"]');
            if (submitButton) {
                submitButton.textContent = 'Updating Incident...';
                submitButton.disabled = true;
            }
            
            try {
                // Update incident
                await this.updateIncident(this.currentIncident.id, formData);
                
                // Upload new files if any
                if (this.uploadedFiles.length > 0) {
                    await this.uploadIncidentFiles(this.currentIncident.id);
                }
                
                // Update people relationships
                await this.updateIncidentPeopleRelationships(this.currentIncident.id);
                
                // Show success message
                this.uiUtilities.showToast(`Incident #${this.currentIncident.incident_number} updated successfully`, 'success');
                
                // Navigate back to incidents view
                await this.navigateToIncidents();
                
                console.log('Incident updated successfully:', this.currentIncident.id);
                
            } finally {
                // Reset submit button
                if (submitButton) {
                    submitButton.textContent = 'Update Incident';
                    submitButton.disabled = false;
                }
            }
            
        } catch (error) {
            console.error('Error updating incident:', error);
            this.ui.showDialog('Error', `Failed to update incident: ${error.message}`, 'error');
        }
    }

    async createIncident(formData) {
        // Get current user context
        const userContext = this.getCurrentUserContext();
        
        // Generate incident number
        const incidentNumber = await this.generateIncidentNumber();
        
        // Prepare incident data - mapping form fields to Supabase schema
        const incidentData = {
            // Core incident fields
            incident_number: incidentNumber,
            incident_type: formData.incident_type,
            priority: formData.priority || 'medium',
            location: formData.location,
            street_address: formData.street_address,
            city: formData.city,
            province: formData.province,
            postal_code: formData.postal_code,
            coordinates: formData.coordinates,
            latitude: formData.latitude,
            longitude: formData.longitude,
            incident_date: formData.incident_date,
            incident_time: formData.incident_time,
            description: formData.description,
            narrative: formData.narrative || formData.description || 'Initial incident report',
            
            // Weather conditions
            weather_conditions: formData.weather_conditions || '',
            
            // Map form fields to existing Supabase columns
            property_involved: formData.property_involved || false,
            
            // Map EMS/Police/Fire fields to existing schema
            paramedics_called: formData.ems_called || false,
            police_notified: formData.police_called || false,
            fire_department_called: formData.fire_called || false,
            
            // Follow-up fields
            follow_up_required: formData.followup_required || false,
            follow_up_date: formData.follow_up_date,
            follow_up_notes: formData.follow_up_notes || '',
            
            // Safety fields
            safety_concerns: formData.safety_concerns || '',
            
            // Status
            status: 'open',
            
            // Reporter information (store in existing fields where possible)
            reported_by: formData.reporter_name || '',
            
            // Add creation metadata
            ...this.addCreateMetadata()
        };
        
        // Create incident in database
        const incident = await this.data.insert('incidents', incidentData);
        
        // Log incident creation
        await this.logIncidentActivity(incident.id, 'incident_created', {
            action: 'Incident Created',
            details: `New incident #${incident.incident_number} created`,
            performed_by: userContext.display_name
        });
        
        return incident;
    }

    async updateIncident(incidentId, formData) {
        // Get current user context
        const userContext = this.getCurrentUserContext();
        
        // Prepare update data - mapping form fields to Supabase schema
        const updateData = {
            // Core incident fields
            incident_type: formData.incident_type,
            priority: formData.priority,
            location: formData.location,
            street_address: formData.street_address,
            city: formData.city,
            province: formData.province,
            postal_code: formData.postal_code,
            coordinates: formData.coordinates,
            latitude: formData.latitude,
            longitude: formData.longitude,
            incident_date: formData.incident_date,
            incident_time: formData.incident_time,
            description: formData.description,
            narrative: formData.narrative || formData.description || 'Initial incident report',
            
            // Weather conditions
            weather_conditions: formData.weather_conditions || '',
            
            // Map form fields to existing Supabase columns
            property_involved: formData.property_involved || false,
            
            // Map EMS/Police/Fire fields to existing schema
            paramedics_called: formData.ems_called || false,
            police_notified: formData.police_called || false,
            fire_department_called: formData.fire_called || false,
            
            // Follow-up fields
            follow_up_required: formData.followup_required || false,
            follow_up_date: formData.follow_up_date,
            follow_up_notes: formData.follow_up_notes || '',
            
            // Safety fields
            safety_concerns: formData.safety_concerns || '',
            
            // Reporter information (store in existing fields where possible)
            reported_by: formData.reporter_name || '',
            
            // Add update metadata
            ...this.addUpdateMetadata()
        };
        
        // Update incident in database
        await this.data.update('incidents', incidentId, updateData);
        
        // Log incident update
        await this.logIncidentActivity(incidentId, 'incident_updated', {
            action: 'Incident Updated',
            details: `Incident #${this.currentIncident.incident_number} updated`,
            performed_by: userContext.display_name
        });
    }

    collectIncidentFormData() {
        const formData = {};
        
        // Get the active form (create or edit)
        const form = document.getElementById('comprehensive-incident-form') || 
                    document.getElementById('edit-incident-form');
        
        if (!form) {
            throw new Error('No incident form found');
        }
        
        // Define array fields that need special handling
        const arrayFields = [
            'services_offered', 'services_provided', 'resources_distributed',
            'referrals_made', 'substance_indicators', 'environmental_factors'
        ];

        // Initialize array fields
        arrayFields.forEach(field => {
            formData[field] = [];
        });

        // Collect all form fields
        const formElements = form.querySelectorAll('input, select, textarea');

        formElements.forEach(element => {
            const name = element.name || element.id;
            if (!name) return;

            if (element.type === 'checkbox') {
                if (arrayFields.includes(name)) {
                    // Handle array field checkboxes
                    if (element.checked) {
                        formData[name].push(element.value);
                    }
                } else {
                    // Handle regular checkboxes
                    formData[name] = element.checked;
                }
            } else if (element.type === 'radio') {
                if (element.checked) {
                    formData[name] = element.value;
                }
            } else {
                formData[name] = element.value;
            }
        });

        // Clean up empty arrays - remove array fields that have no values
        arrayFields.forEach(field => {
            if (formData[field] && formData[field].length === 0) {
                delete formData[field];
            }
        });
        
        // Handle date/time combination for incident_time
        const useCurrentTime = formData.use_current_time;
        const incidentDate = formData.incident_date;
        const incidentTime = formData.incident_time;

        if (useCurrentTime) {
            // Use current date and time
            const now = new Date();
            formData.incident_date = now.toISOString().split('T')[0];
            formData.incident_time = now.toTimeString().split(' ')[0];
            // Also set the combined timestamp
            formData.incident_timestamp = now.toISOString();
        } else if (incidentDate && incidentTime) {
            // Combine date and time
            formData.incident_timestamp = `${incidentDate}T${incidentTime}:00`;
        } else if (incidentDate) {
            // Just date, assume start of day
            formData.incident_timestamp = `${incidentDate}T00:00:00`;
        }

        // Extract coordinates into latitude/longitude if available
        if (formData.coordinates) {
            const coords = formData.coordinates.split(',');
            if (coords.length === 2) {
                formData.latitude = coords[0].trim();
                formData.longitude = coords[1].trim();
            }
        }
        
        // Collect people data if available
        const peopleData = this.collectInvolvedPeopleData();
        if (peopleData.length > 0) {
            formData.involvedPeople = peopleData;
        }
        
        return formData;
    }

    async validateIncidentData(incidentData) {
        const errors = [];
        
        // Required field validation
        if (!incidentData.incident_type || incidentData.incident_type.trim() === '') {
            errors.push('Incident type is required');
        }
        
        if (!incidentData.location || incidentData.location.trim() === '') {
            errors.push('Location is required');
        }
        
        // Time validation - only check if not using current time
        if (!incidentData.use_current_time) {
            if (!incidentData.incident_date) {
                errors.push('Incident date is required');
            }
            if (!incidentData.incident_time || !incidentData.incident_time.includes('T')) {
                errors.push('Incident time is required');
            }
        }
        
        if (!incidentData.description || incidentData.description.trim() === '') {
            errors.push('Description is required');
        }
        
        // Date validation
        if (incidentData.incident_time) {
            const incidentTime = new Date(incidentData.incident_time);
            const now = new Date();
            
            if (incidentTime > now) {
                errors.push('Incident time cannot be in the future');
            }
            
            // Check if incident is more than 30 days old
            const thirtyDaysAgo = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
            if (incidentTime < thirtyDaysAgo) {
                // This is a warning, not an error
                console.warn('Incident is more than 30 days old');
            }
        }
        
        // Follow-up date validation
        if (incidentData.followup_required && incidentData.follow_up_date) {
            const followUpDate = new Date(incidentData.follow_up_date);
            const now = new Date();
            
            if (followUpDate < now) {
                errors.push('Follow-up date cannot be in the past');
            }
        }
        
        // Contact validation
        if (incidentData.reporter_contact && incidentData.reporter_contact.trim()) {
            const contact = incidentData.reporter_contact.trim();
            // Basic phone/email validation
            const phoneRegex = /^[\d\s\-\(\)\+\.]+$/;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            if (!phoneRegex.test(contact) && !emailRegex.test(contact)) {
                errors.push('Reporter contact must be a valid phone number or email address');
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    async generateIncidentNumber() {
        try {
            // Get current date
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            
            // Base incident number format: YYYYMMDD-001
            const datePrefix = `${year}${month}${day}`;
            
            // Find existing incidents with the same date prefix
            const incidents = await this.data.search('incidents', {});
            const todayIncidents = incidents.filter(incident => 
                incident.incident_number && incident.incident_number.startsWith(datePrefix)
            );
            
            // Get the next sequence number
            let nextSequence = 1;
            if (todayIncidents.length > 0) {
                const sequences = todayIncidents
                    .map(incident => {
                        const parts = incident.incident_number.split('-');
                        return parts.length > 1 ? parseInt(parts[1]) : 0;
                    })
                    .filter(seq => !isNaN(seq));
                
                if (sequences.length > 0) {
                    nextSequence = Math.max(...sequences) + 1;
                }
            }
            
            // Format sequence with leading zeros
            const sequenceStr = String(nextSequence).padStart(3, '0');
            
            return `${datePrefix}-${sequenceStr}`;
            
        } catch (error) {
            console.error('Error generating incident number:', error);
            // Fallback to timestamp-based number
            return `INC-${Date.now()}`;
        }
    }

    async populateEditForm(incident) {
        try {
            console.log('Populating edit form with incident data...');
            
            // Get the edit form
            const form = document.getElementById('edit-incident-form');
            if (!form) {
                console.error('Edit form not found');
                return;
            }
            
            // Populate all form fields
            const formElements = form.querySelectorAll('input, select, textarea');
            
            formElements.forEach(element => {
                const name = element.name || element.id;
                if (!name || incident[name] === undefined) return;
                
                if (element.type === 'checkbox') {
                    element.checked = Boolean(incident[name]);
                } else if (element.type === 'radio') {
                    element.checked = element.value === incident[name];
                } else {
                    element.value = incident[name] || '';
                }
            });
            
            // Trigger conditional field visibility
            this.initializeConditionalFields();
            
            console.log('Edit form populated successfully');
            
        } catch (error) {
            console.error('Error populating edit form:', error);
        }
    }

    getEditIncidentId() {
        // Try to get incident ID from various sources
        
        // 1. From URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const urlIncidentId = urlParams.get('incidentId');
        if (urlIncidentId) return urlIncidentId;
        
        // 2. From stored state
        const storedId = sessionStorage.getItem('editIncidentId');
        if (storedId) return storedId;
        
        // 3. From global app state (if available)
        if (window.app && window.app.selectedIncident) {
            return window.app.selectedIncident.id;
        }
        
        return null;
    }

    async navigateToIncidents() {
        // Clear any stored edit state
        sessionStorage.removeItem('editIncidentId');
        
        // Navigate to incidents tab
        if (window.app && window.app.loadTabContent) {
            await window.app.loadTabContent('incidents');
        } else {
            // Fallback navigation
            window.location.hash = '#incidents';
            window.location.reload();
        }
    }

    async logIncidentActivity(incidentId, activityType, activityData) {
        try {
            // Get current user context for logging
            const userContext = this.getCurrentUserContext();

            const logEntry = {
                activity_type: activityType,
                description: activityData.details || activityData.action || `Incident ${activityType}`,
                table_name: 'incidents',
                record_id: incidentId.toString(),
                field_name: null, // Not applicable for incident-level actions
                old_value: null,
                new_value: JSON.stringify(activityData),
                user_email: userContext.email || '<EMAIL>',
                user_name: userContext.display_name || 'Unknown User',
                notes: activityData.details || activityData.action || '',
                timestamp: new Date().toISOString()
            };

            await this.data.insert('activity_logs', logEntry);

        } catch (error) {
            console.error('Error logging incident activity:', error);
        }
    }

    /**
     * Format time for database - extract time portion from timestamp
     * @param {string} timeString - Time string (could be full timestamp or time)
     * @returns {string} - Time in HH:MM:SS format
     */
    formatTimeForDatabase(timeString) {
        if (!timeString) return null;
        
        try {
            // If it's already a time string (HH:MM:SS format), return as is
            if (timeString.match(/^\d{2}:\d{2}:\d{2}$/)) {
                return timeString;
            }
            
            // If it's a full timestamp, extract the time portion
            const date = new Date(timeString);
            if (isNaN(date.getTime())) {
                console.warn('Invalid time format:', timeString);
                return null;
            }
            
            // Format as HH:MM:SS
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            
            return `${hours}:${minutes}:${seconds}`;
        } catch (error) {
            console.error('Error formatting time for database:', error);
            return null;
        }
    }

    startAutoSave() {
        console.log('Starting auto-save for incident form...');
        this.autoSaveActive = true;

        // Auto-save every 30 seconds
        this.autoSaveInterval = setInterval(() => {
            this.autoSaveIncident();
        }, 30000);

        // Also set up input-based auto-save
        this.setupInputAutoSave();
    }

    stopAutoSave() {
        console.log('Stopping auto-save for incident form...');
        this.autoSaveActive = false;

        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }

    setupInputAutoSave() {
        // Auto-save functionality for forms based on input changes
        let autoSaveTimeout;

        const form = document.getElementById('comprehensive-incident-form') ||
                    document.getElementById('edit-incident-form');

        if (!form) return;

        form.addEventListener('input', () => {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                this.autoSaveIncident();
            }, 5000); // Auto-save after 5 seconds of inactivity
        });
    }

    async autoSaveIncident() {
        if (!this.autoSaveActive) return;

        try {
            const formData = this.collectIncidentFormData();

            if (!formData || Object.keys(formData).length === 0) {
                return; // Nothing to save
            }

            // Determine if this is a new incident or editing existing
            const isEdit = !!this.currentIncident;

            if (isEdit) {
                // Update existing incident
                await this.updateIncident(this.currentIncident.id, formData);
                this.uiUtilities.showToast('Auto-saved', 'info', 1500);
            } else {
                // Save as draft incident
                await this.saveIncidentAsDraft();
            }

        } catch (error) {
            console.error('Auto-save failed:', error);
            // Don't show error to user for auto-save failures
        }
    }

    async saveIncidentAsDraft() {
        try {
            const formData = this.collectIncidentFormData();

            if (!formData || Object.keys(formData).length === 0) {
                return; // Nothing to save
            }

            const userContext = this.getCurrentUserContext();

            // Prepare incident data with draft status
            const incidentData = {
                ...formData,
                status: 'draft',
                ...this.addCreateMetadata()
            };

            // Ensure required fields have default values for drafts
            if (!incidentData.location) {
                incidentData.location = 'Draft - Location TBD';
            }
            if (!incidentData.description) {
                incidentData.description = 'Draft - Description TBD';
            }

            let savedIncident;

            // Update existing draft if we have one, otherwise create new
            if (this.currentIncident && this.currentIncident.status === 'draft') {
                savedIncident = await this.data.update('incidents', this.currentIncident.id, incidentData);
                console.log('Draft updated:', this.currentIncident.id);
            } else {
                savedIncident = await this.data.insert('incidents', incidentData);
                this.currentIncident = savedIncident;
                console.log('New draft created:', savedIncident.id);
            }

            this.uiUtilities.showToast('Draft saved', 'info', 2000);
            return savedIncident;

        } catch (error) {
            console.error('Error saving draft:', error);
            throw error;
        }
    }

    // Placeholder methods that would be implemented in other managers
    async setupIncidentAddressSearch() {
        console.log('Setting up incident address search...');
        // This would be handled by the address manager
    }

    initializePeopleManagement() {
        console.log('Initializing people management...');
        // Delegate to the people manager if available
        if (this.peopleManager) {
            this.peopleManager.initializePeopleManagement();
        } else {
            console.warn('People manager not available - people search may not work');
        }
    }

    setupFileUpload() {
        console.log('Setting up file upload...');
        // This would be handled by the file manager
    }

    // Legacy methods - now simplified
    async saveDraftIncident() {
        return await this.saveIncidentAsDraft();
    }

    async clearDraftIncident() {
        // For simplified draft system, just reset the current incident if it's a draft
        if (this.currentIncident && this.currentIncident.status === 'draft') {
            this.currentIncident = null;
            console.log('Draft cleared');
        }
    }

    async uploadIncidentFiles(incidentId) {
        console.log('Uploading incident files...');
        // This would be handled by the file manager
    }

    async loadIncidentAttachments(incidentId) {
        console.log('Loading incident attachments...');
        // This would be handled by the file manager
    }

    collectInvolvedPeopleData() {
        // This would be handled by the people manager
        return [];
    }

    async saveIncidentPeopleRelationships(incidentId) {
        console.log('Saving incident people relationships...');
        // This would be handled by the people manager
    }

    async updateIncidentPeopleRelationships(incidentId) {
        console.log('Updating incident people relationships...');
        // This would be handled by the people manager
    }

    async loadExistingIncidentPeople() {
        console.log('Loading existing incident people...');
        // Delegate to the people manager if available
        if (this.peopleManager && this.currentIncident) {
            await this.peopleManager.loadExistingIncidentPeople(this.currentIncident.id);
        } else {
            console.warn('People manager not available or no current incident');
        }
    }

    initializeIncidentMap() {
        console.log('Initializing incident map...');
        // This would be handled by the address manager
    }

    setInitialEditFormState() {
        // For edit forms, show the date/time fields by default since we have existing data
        const datetimeGroup = document.getElementById('datetime-group');
        const datetimeGroupTime = document.getElementById('datetime-group-time');
        const useCurrentTimeCheckbox = document.querySelector('input[name="use_current_time"]');
        
        if (datetimeGroup) datetimeGroup.style.display = 'block';
        if (datetimeGroupTime) datetimeGroupTime.style.display = 'block';
        
        // Uncheck the "use current time" checkbox for edit forms (we want to show the existing time)
        if (useCurrentTimeCheckbox) {
            useCurrentTimeCheckbox.checked = false;
        }
        
        console.log('Set initial edit form state - datetime fields visible');
    }

    cleanup() {
        console.log('Cleaning up incident form manager...');
        this.stopAutoSave();
    }
}