// Outreach Transaction Detail View Templates
// Created for future transaction detail and history views

export const outreachDetailTemplates = {
    // Transaction detail view template (for split-view right panel)
    createTransactionDetailView: (transaction) => `
        <div class="outreach-detail-view">
            <div class="detail-header">
                <h3>📦 Transaction Details</h3>
                <div class="detail-id">#${String(transaction.id).substring(0, 8)}</div>
            </div>
            
            <div class="detail-tabs">
                <div class="detail-tab active" data-detail-tab="overview">Overview</div>
                <div class="detail-tab" data-detail-tab="items">Items</div>
                <div class="detail-tab" data-detail-tab="person">Person</div>
                <div class="detail-tab" data-detail-tab="location">Location</div>
            </div>
            
            <div class="detail-content">
                <div class="detail-tab-content active" id="overview-content">
                    <div class="detail-section">
                        <div class="section-title">📅 Transaction Overview</div>
                        <div class="detail-grid">
                            <div class="detail-row">
                                <span class="detail-label">Date & Time:</span>
                                <span class="detail-value">${transaction.formatted_time || transaction.created_at}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Staff Member:</span>
                                <span class="detail-value">${transaction.staff_member || 'Unknown'}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Total Items:</span>
                                <span class="detail-value">${transaction.total_items || (Array.isArray(transaction.items) ? transaction.items.length : 0)}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value status-completed">Completed</span>
                            </div>
                        </div>
                    </div>
                    
                    ${transaction.notes ? `
                    <div class="detail-section">
                        <div class="section-title">📝 Notes</div>
                        <div class="notes-content">
                            ${transaction.notes}
                        </div>
                    </div>
                    ` : ''}
                </div>
                
                <div class="detail-tab-content" id="items-content">
                    <div class="detail-section">
                        <div class="section-title">📦 Items Distributed</div>
                        <div class="items-list">
                            ${(transaction.items && Array.isArray(transaction.items) && transaction.items.length > 0) ? transaction.items.map(item => `
                                <div class="item-row">
                                    <div class="item-info">
                                        <div class="item-name">${item.name || item.item_name}</div>
                                        <div class="item-category">${item.category}</div>
                                    </div>
                                    <div class="item-quantity">
                                        <span class="quantity">${item.quantity}</span>
                                        <span class="unit">${item.unit_type}</span>
                                    </div>
                                </div>
                            `).join('') : '<div class="no-items">No items recorded</div>'}
                        </div>
                    </div>
                </div>
                
                <div class="detail-tab-content" id="person-content">
                    <div class="detail-section">
                        <div class="section-title">👤 Person Information</div>
                        <div class="person-info-detail">
                            <div class="person-name-large">${transaction.person_name}</div>
                            <div class="person-id">ID: ${transaction.person_id ? String(transaction.person_id).substring(0, 8) : 'N/A'}</div>
                            <div class="person-actions">
                                <button class="action-link" onclick="app.viewPersonDetails('${transaction.person_id}')">
                                    View Full Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="detail-tab-content" id="location-content">
                    <div class="detail-section">
                        <div class="section-title">📍 Location Information</div>
                        <div class="location-info">
                            <div class="location-address">
                                ${transaction.location || 'Location not specified'}
                            </div>
                            ${transaction.coordinates ? `
                            <div class="location-coordinates">
                                Coordinates: ${transaction.coordinates.lat}, ${transaction.coordinates.lng}
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="detail-actions">
                <button class="action-button secondary" onclick="app.duplicateOutreachTransaction('${transaction.id}')">
                    📋 Duplicate
                </button>
                <button class="action-button" onclick="app.exportTransactionReport('${transaction.id}')">
                    📊 Export
                </button>
                <button class="action-button danger" data-action="delete-outreach-transaction" data-transaction-id="${transaction.id}">
                    🗑️ Delete
                </button>
            </div>
        </div>
    `,

    // Transaction summary card template (for lists/reports)
    createTransactionSummaryCard: (transaction) => `
        <div class="transaction-summary-card" onclick="app.viewTransactionDetail('${transaction.id}')">
            <div class="summary-header">
                <div class="summary-date">${transaction.date}</div>
                <div class="summary-id">#{String(transaction.id).substring(0, 8)}</div>
            </div>
            <div class="summary-content">
                <div class="summary-person">${transaction.person_name}</div>
                <div class="summary-items">${transaction.items_count} item(s) provided</div>
                <div class="summary-location">${transaction.location || 'Location not specified'}</div>
            </div>
            <div class="summary-footer">
                <span class="summary-staff">${transaction.staff_member}</span>
            </div>
        </div>
    `
};