/**
 * Address Management Module - Main Entry Point
 * Handles address management and location tracking functionality
 */

import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { AddressManager } from './address-manager.js';
import { Logger } from '../../shared/logger.js';

export class AddressManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities = null, modalManagement = null) {
        super('AddressManagement', '1.0.0', [], ['addresses']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.logger = Logger.forModule('AddressManagement');

        // Initialize managers
        this.addressManager = new AddressManager(dataManager, authManager, uiManager);
    }

    /**
     * Initialize the address management module
     * @returns {Promise<void>}
     */
    async initialize() {
        this.logger.info('Initializing Address Management module');
        this.initialized = true;
    }

    /**
     * Cleanup module resources
     * @returns {Promise<void>}
     */
    async cleanup() {
        this.logger.info('Cleaning up Address Management module');
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // TODO: Implement address management commands
        return new Map();
    }

    /**
     * Get module status
     * @returns {Object} Module status
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            initialized: this.initialized,
            uptime: Date.now() - this.startTime
        };
    }

    /**
     * Get workflows provided by this module
     * @returns {Object} Available workflows
     */
    getWorkflows() {
        return {
            loadAddressesManagement: () => this.addressManager.loadAddressesManagementContent.bind(this.addressManager),
            searchAddresses: () => this.addressManager.search.bind(this.addressManager),
            getAddressById: () => this.addressManager.getById.bind(this.addressManager)
        };
    }

    /**
     * Get templates provided by this module
     * @returns {Object} Available templates
     */
    getTemplates() {
        return {
            // TODO: Add address templates when available
        };
    }

    /**
     * Get API endpoints provided by this module
     * @returns {Object} Available API endpoints
     */
    getApiEndpoints() {
        return {
            // Address management uses standard CRUD endpoints
        };
    }

    /**
     * Get statistics for this module
     * @returns {Promise<Object>} Module statistics
     */
    async getStatistics() {
        try {
            const totalAddresses = await this.addressManager.getAll();
            return {
                totalAddresses: totalAddresses.length,
                // TODO: Add more detailed statistics
            };
        } catch (error) {
            this.logger.error('Failed to get address statistics', error);
            return {
                totalAddresses: 0,
                error: error.message
            };
        }
    }

    // Expose manager methods for backward compatibility
    async loadAddressesManagementContent() {
        return await this.addressManager.loadAddressesManagementContent();
    }

    async search(searchTerm) {
        return await this.addressManager.search(searchTerm);
    }

    async getById(id) {
        return await this.addressManager.getById(id);
    }

    async getAll() {
        return await this.addressManager.getAll();
    }

    async create(addressData) {
        return await this.addressManager.create(addressData);
    }

    async update(id, addressData) {
        return await this.addressManager.update(id, addressData);
    }

    async delete(id) {
        return await this.addressManager.delete(id);
    }
}

// Export both for compatibility
export { AddressManager } from './address-manager.js';
