# S.T.E.V.I Retro - Claude Memory

## Organization Background
**I.H.A.R.C** (Integrated Homelessness & Addictions Response Centre) is a non-profit organization located in Ontario, Canada that provides services to people experiencing homelessness and/or facing addiction issues. This application supports their operations and field work.

**THE APP IS STILL IN BETA -- <PERSON><PERSON><PERSON>WARDS COMPATABILITY IS NOT AN ISSUE AND SHOULD NOT BE CONSIDERED WHEN MAKING CODE CHANGES**

## Project Overview
S.T.E.V.I Retro is a comprehensive field management system designed specifically for IHARC staff working with vulnerable populations. The application emphasizes reliability, offline capability, and ease of use in challenging field conditions.

**Current Version**: 1.3.0

## Technology Stack

### Core Technologies
- **Electron** (v37.2.3) - Cross-platform desktop application
- **Vanilla JavaScript** - No frontend frameworks, pure HTML/CSS/JS with ES modules
- **Supabase** (v2.50.5) - Backend-as-a-Service (PostgreSQL database, auth, real-time)
- **SQLite** (better-sqlite3 v12.2.0) - Local caching layer for offline capabilities
- **Azure Blob Storage** - File storage for incident attachments and media

### Key Dependencies
- **uuid** (v11.1.0) - Unique identifier generation
- **semver** (v7.7.2) - Version management for auto-updates
- **electron-builder** (v26.0.12) - Application packaging
- **serialport** (v12.0.0) - GPS hardware integration
- **fs-extra** (v11.3.0) - Enhanced file system operations

## Architecture

### Modular Architecture (Enhanced 2025-08-01)
The application has been completely reorganized into a comprehensive modular architecture with feature-based modules and a centralized shared utilities system.

#### Shared Module System (`modules/shared/`)
**Centralized Utilities and Infrastructure** - All shared functionality consolidated into a single location:
- **Base Classes**: `BaseManager`, `BaseCrudManager`, `BaseCommand` - Standardized foundation classes
- **Utilities**: `IdGenerator`, `SearchUtility`, `StatsGenerator` - Common functionality across modules
- **Logging System**: `Logger`, `createLogger` - Centralized logging with module context
- **Module System**: `ModuleInterface`, `FeatureModuleInterface`, `ServiceModuleInterface` - Module lifecycle contracts
- **Registry**: `ModuleRegistry`, `moduleRegistry` - Centralized module management and lifecycle
- **Unified Exports**: Single import point (`modules/shared/index.js`) for all shared functionality

#### Top-Level Feature Modules (`modules/`)
- **Activity Management** (`modules/activity-management/`): Activity tracking and search functionality
- **Address Management** (`modules/address-management/`): Address CRUD, search, and form templates
- **AI Integration** (`modules/ai/`): Gemini AI service integration
- **Bike Management** (`modules/bike-management/`): Bike registration and tracking
- **Dashboard** (`modules/dashboard/`): Main dashboard functionality
- **Encampment Management** (`modules/encampment-management/`): Encampment tracking and templates
- **Incident Management** (`modules/incident-management/`): Complete incident workflow with 9 specialized sub-managers
- **Justice System** (`modules/justice/`): Comprehensive criminal justice case management with court dates, bail conditions, and legal contacts
- **Narrative Management** (`modules/narrative-management/`): Professional incident narrative creation and rendering
- **Organizations Management** (`modules/organizations-management/`): Service provider and partner agency management
- **Outreach Management** (`modules/outreach-management/`): Outreach transaction handling and templates
- **People Management** (`modules/people-management/`): Complete people management with 8 specialized sub-managers implementing `FeatureModuleInterface`
- **Property Management** (`modules/property-management/`): Found property tracking and return processes
- **Records** (`modules/records/`): Central records management
- **Reports Management** (`modules/reports-management/`): PDF report generation and templates
- **System Settings** (`modules/system-settings/`): Application configuration and settings
- **Vehicle Management** (`modules/vehicle-management/`): Comprehensive vehicle tracking with 6 specialized sub-managers

#### Legacy Renderer Modules (`renderer/js/modules/`)
- **Authentication** (`renderer/js/modules/authentication/`): Login and role verification flows
- **Modal Management** (`renderer/js/modules/modal-management/`): Unified modal and dialog management
- **UI Utilities** (`renderer/js/modules/ui-utilities/`): Terminal formatting, notifications, date/time utilities, network monitoring
- **Outreach Management** (`renderer/js/modules/outreach-management/`): Legacy outreach handling
- **Records Management** (`renderer/js/modules/records-management/`): Legacy records handling

#### Module Architecture Standards
Each module follows a standardized structure with interface compliance:
- **JavaScript** (`js/`): Core functionality and managers extending base classes
- **Templates** (`templates/`): HTML template generation functions
- **Styles** (`styles/`): Module-specific CSS (where applicable)
- **Index** (`index.js`): Module entry point implementing `ModuleInterface` or `FeatureModuleInterface`
- **Unified Imports**: All modules import shared utilities from `../../shared/` path

### Database Architecture
- **Primary Backend**: Supabase (PostgreSQL) with real-time subscriptions
- **Local Cache**: SQLite database for offline operations and performance
- **Data Flow**: Online-first approach - writes go to Supabase first, then cached locally
- **Sync Strategy**: Real-time synchronization with last-write-wins conflict resolution

### Database Schemas
- **Core Schema** (`core.*`): people, pets, medical_issues, organizations, addresses, items, bikes, encampments, incarceration_status, bail_conditions, court_dates, legal_contacts
- **Case Management** (`case_mgmt.*`): incidents, property_records, license_plates
- **Audit Schema** (`audit.*`): activity_logs, recovery_logs

### Authentication & Security  
- **Supabase Auth** with role-based access control
- **User Roles**: `iharc_staff`, `iharc_admin`, `iharc_supervisor`, `iharc_volunteer`
- **Row Level Security (RLS)** - Database-enforced access control
- **Secure credential management** via Supabase Vault

## Key Features

### Core Functionality
1. **Person Management** - Comprehensive tracking with housing status, medical issues, pets, criminal justice records
2. **Incident Reporting** - Professional incident forms with file attachments and narrative management
3. **Property Management** - Found property tracking, bike registration, theft reports
4. **Organization Database** - Service provider and partner agency management
5. **Activity Logging** - Ranger activities, outreach transactions, supply distribution
6. **Report Generation** - PDF resource/services reports with client search and filtering
7. **Real-time Weather** - Google Weather API integration for field operations
8. **Offline Capability** - Full functionality with automatic sync when reconnected
9. **Criminal Justice Tracking** - Incarceration status, bail conditions, court dates, legal contacts
10. **Vehicle Management** - Comprehensive vehicle tracking and activities
11. **AI Integration** - Gemini AI service for enhanced functionality

### User Interface
- **Retro DOS Styling** - Red-on-black terminal interface with ASCII art
- **Tab Navigation**: Dashboard, Incidents, Records, Property, Encampments, Reports, System, Admin
- **Optimized for Field Use** - Designed for 1024x768 Panasonic Toughbook screens

## Main Application Files

### Core System
- `electron/main.js` - Electron main process, window management, IPC, GPS service integration
- `renderer/js/app.js` - Primary application controller (significantly reduced through modularization)
- `renderer/js/data.js` - Data management layer with caching and schema synchronization
- `renderer/js/auth.js` - Authentication and user management
- `renderer/js/commands.js` - Command system with modular command registration

### Enhanced Base Architecture (2025-08-01)
- **Shared Module System** (`modules/shared/`): Centralized utilities, base classes, logging, and module management
- **CRUD Standardization**: All CRUD managers now extend `BaseCrudManager` for consistent operations and ~30% code reduction
- **Centralized Logging**: `Logger` system with module-specific contexts and monitoring integration readiness
- **Module Interfaces**: Standardized `ModuleInterface` and `FeatureModuleInterface` for consistent module lifecycle
- **Module Registry**: `ModuleRegistry` for centralized module management, initialization, and cleanup
- **Schema Management** (`renderer/js/schema.js`, `renderer/js/schema-sync-manager.js`): Dynamic schema management and synchronization
- **SQLite Integration** (`renderer/js/sqlite-manager.js`): Local caching with automatic schema updates

## Modular Architecture System

### Architecture Overview
The application implements a comprehensive modular architecture with three core pillars:

#### 1. Module Interface Contracts (`modules/shared/module-interface.js`)
**Purpose**: Standardized contracts ensuring consistent module behavior across the application.

**Interface Types**:
- **`ModuleInterface`** - Base contract for all modules
  - Required methods: `initialize()`, `cleanup()`, `getCommands()`, `getStatus()`
  - Provides: Dependency checking, configuration validation, metadata management
- **`FeatureModuleInterface`** - Extended interface for feature modules
  - Additional methods: `getWorkflows()`, `getTemplates()`, `getApiEndpoints()`, `getStatistics()`
  - Used by: People, Incidents, Vehicles, Properties, etc.
- **`ServiceModuleInterface`** - Specialized interface for utility services
  - Additional methods: `getHealthStatus()`
  - Used by: AI, Authentication, Weather services

#### 2. Module Registry (`modules/shared/module-registry.js`)
**Purpose**: Central registry managing module lifecycle, dependencies, and inter-module communication.

**Core Functions**:
- **Registration**: `register(name, moduleClass, config)` - Register modules with configuration
- **Lifecycle Management**: `initializeModule()`, `cleanupModule()`, `initializeAll()`, `cleanupAll()`
- **Dependency Resolution**: Automatic dependency checking and initialization ordering
- **Health Monitoring**: `getSystemStatus()` - Track module health and uptime
- **Command Aggregation**: `getAllCommands()` - Collect commands from all modules
- **Event System**: Module lifecycle events with listener support

#### 3. Standardized Base Classes (`modules/shared/`)

**BaseCrudManager** - Eliminates ~30% code duplication:
```javascript
class YourCrudManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager) {
        super(dataManager, authManager, 'your_table', 'your_entity', uiManager);
        // Inherits: create(), getById(), getAll(), update(), delete(), search()
    }
}
```

**BaseManager** - Common functionality foundation:
- User context management
- Error handling with retry logic
- Metadata generation
- Activity logging
- Data cleaning and validation

**Logger** - Centralized logging system:
```javascript
const logger = Logger.forModule('YourModule');
logger.info('Operation started', { context });
logger.error('Operation failed', error, { context });
logger.data('create', 'entity', id, data);
logger.userAction('action_name', userEmail, { metadata });
```

### Module Implementation Standards

#### Module Structure (MANDATORY)
```
modules/your-module/
├── js/
│   ├── index.js                 # Module entry point (implements ModuleInterface)
│   ├── your-crud-manager.js     # Extends BaseCrudManager
│   ├── your-commands.js         # Command factory pattern
│   └── your-workflow-manager.js # Business logic
├── templates/
│   └── your-templates.js        # Template functions
└── styles/
    └── your-styles.css          # Module-specific styles
```

#### Module Entry Point Implementation (REQUIRED)
```javascript
// modules/your-module/js/index.js
import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { Logger } from '../../shared/logger.js';

export class YourModule extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super('YourModule', '1.0.0', [], ['your_entity']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.logger = Logger.forModule('YourModule');
        
        // Initialize managers
        this.crudManager = new YourCrudManager(dataManager, authManager, uiManager);
        this.workflowManager = new YourWorkflowManager(this.crudManager);
    }
    
    // REQUIRED: Initialize module resources
    async initialize() {
        this.logger.info('Initializing Your Module');
        // Setup code here
    }
    
    // REQUIRED: Cleanup module resources
    async cleanup() {
        this.logger.info('Cleaning up Your Module');
        // Cleanup code here
    }
    
    // REQUIRED: Provide commands for command system
    getCommands(commandManager) {
        return new YourCommandsFactory(commandManager, this).createCommands();
    }
    
    // REQUIRED for FeatureModuleInterface: Provide workflows
    getWorkflows() {
        return {
            createEntity: () => this.workflowManager.createEntityWorkflow(),
            manageEntities: () => this.workflowManager.manageEntitiesWorkflow()
        };
    }
}
```

#### CRUD Manager Implementation (REQUIRED)
```javascript
// modules/your-module/js/your-crud-manager.js
import { BaseCrudManager } from '../../shared/base-crud-manager.js';

export class YourCrudManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager) {
        // CRITICAL: Use correct parameters (dataManager, authManager, tableName, entityType, uiManager)
        super(dataManager, authManager, 'your_table', 'your_entity', uiManager);
    }
    
    // Override only when custom logic needed
    async create(entityData, options = {}) {
        // Custom validation or processing
        return super.create(entityData, options);
    }
}
```

#### Command Factory Implementation (REQUIRED)
```javascript
// modules/your-module/js/your-commands.js
import { BaseCommand } from '../../shared/base-command.js';

export class YourCommandsFactory {
    constructor(commandManager, yourModule) {
        this.commands = commandManager;
        this.module = yourModule;
    }
    
    createCommands() {
        return new Map([
            ['your-action', new YourActionCommand(this.commands, this.module)]
        ]);
    }
}

class YourActionCommand extends BaseCommand {
    constructor(commandManager, yourModule) {
        super(commandManager);
        this.module = yourModule;
    }
    
    async execute(action, data) {
        // Delegate to module manager, not direct implementation
        return await this.module.workflowManager.handleAction(action, data);
    }
}
```

### Module Registration and Integration

#### App.js Integration (AUTOMATIC)
The Module Registry automatically handles:
1. Module registration during app startup
2. Dependency injection of core services
3. Command collection and registration
4. Lifecycle management during app shutdown

#### Command System Integration (AUTOMATIC)
Commands are automatically collected from all modules via `moduleRegistry.getAllCommands()`

### Best Practices and Requirements

#### MANDATORY Requirements
1. **Interface Compliance**: All modules MUST implement `ModuleInterface` or `FeatureModuleInterface`
2. **CRUD Standardization**: All data managers MUST extend `BaseCrudManager`
3. **Centralized Logging**: MUST use `Logger.forModule('ModuleName')` for all logging
4. **Shared Imports**: MUST import shared utilities from `../../shared/` path
5. **Command Delegation**: Commands MUST delegate to module managers, not implement logic directly
6. **Error Handling**: MUST use standardized error handling from base classes

#### Architecture Principles
1. **Single Responsibility**: Each module manages one feature or service
2. **Dependency Injection**: Dependencies injected via constructor, not global access
3. **Separation of Concerns**: Commands ≠ Business Logic ≠ Templates
4. **Consistent Patterns**: Follow established patterns for maintainability
5. **Interface Contracts**: Modules expose capabilities through defined interfaces

#### Common Anti-Patterns to Avoid
- ❌ Implementing business logic in command classes
- ❌ Direct database access bypassing CRUD managers
- ❌ Using console.log instead of centralized Logger
- ❌ Creating custom CRUD operations without extending BaseCrudManager
- ❌ Importing templates in commands.js
- ❌ Global variable access instead of dependency injection

### Module Development Workflow
1. **Create module structure** following standardized directory layout
2. **Implement interface contract** (ModuleInterface/FeatureModuleInterface)
3. **Extend CRUD manager** from BaseCrudManager for data operations
4. **Use centralized logging** via Logger.forModule()
5. **Create command factory** following delegation pattern
6. **Register module** in app.js initialization
7. **Test module lifecycle** (initialize, commands, cleanup)

### Security & Configuration
- `renderer/js/secure-config.js` - Secure credential handling
- `renderer/js/vault-manager.js` - API key storage using Supabase Vault
- `renderer/js/security-monitor.js` - Security monitoring and error handling
- `electron/supabase-config.js` - Supabase configuration management

## Recent Changes
- **2025-08-01**: **Phase 1 Modular Architecture Enhancements** - Completed shared utilities consolidation, CRUD standardization, centralized logging, and module interface system
- **2025-08-01**: **Shared Module System** - Moved all utilities to `modules/shared/`, updated 35+ import paths, implemented `Logger` with module-specific contexts
- **2025-08-01**: **CRUD Standardization** - Refactored People, Incident, and Vehicle CRUD managers to extend `BaseCrudManager` (~30% code reduction)
- **2025-08-01**: **Module Interface System** - Implemented `ModuleInterface`, `FeatureModuleInterface`, and `ModuleRegistry` for standardized module lifecycle
- **2025-07-31**: Completed major directory reorganization - moved all feature modules to top-level `modules/` directory
- **2025-07-31**: Added address_id foreign key to people_activities table for location tracking
- **2025-07-29**: Completed major modularization effort - extracted incident management (~6,000 lines), UI utilities, and modal management from app.js
- **2025-07-28**: Refactored all managers to use standardized base classes, eliminating 60-70% code duplication
- **2025-07-20**: Added comprehensive criminal justice tables (incarceration_status, bail_conditions, court_dates, legal_contacts)

## Known Issues & Technical Debt
- **Google Maps API Deprecation**: Using deprecated `AutocompleteService` and `PlacesService` APIs (functional until 2026)
- **Azure Upload Error Handling**: Enhanced error logging implemented
- **Module Migration**: Some legacy modules remain in `renderer/js/modules/` and need consolidation

## Schema Management Notes
- **Critical**: SQLite cache schemas must match Supabase exactly for real-time sync
- **Recent Migrations**: Added criminal justice tables, address linking for activities, and performance optimizations
- **Column Migration**: Dynamic ALTER TABLE mechanism in `sqlite-manager.js` handles schema updates
- **Migration Files**: Located in `supabase/migrations/` with timestamped filenames

## Development Notes
- **Main branch**: main
- **Architecture Pattern**: Online-first with offline fallback, comprehensive modular architecture with centralized shared utilities
- **UI Pattern**: Schema-driven dynamic form generation with template-based rendering
- **Caching**: Multi-level (Memory → SQLite → Supabase)
- **Target Hardware**: Panasonic Toughbook devices for field operations
- **Database Management**: Direct Supabase access via MCP tools for schema modifications, queries, and migrations
- **Event System**: Global event delegation using `data-action` attributes with automatic command routing
- **Command System**: Modular command factories with lightweight delegation to manager methods
- **Module System**: Standardized module interfaces (`ModuleInterface`, `FeatureModuleInterface`) with centralized registry
- **Logging System**: Centralized `Logger` with module-specific contexts - use `Logger.forModule('ModuleName')` for consistent logging
- **CRUD Operations**: All CRUD managers extend `BaseCrudManager` for standardized operations and reduced code duplication
- **Shared Utilities**: All shared functionality consolidated in `modules/shared/` - import from `../../shared/` in modules
- **Module Integration**: Each module provides commands via factory pattern and implements required interface methods
- **Always ensure that any changes to data management systems (i.e new fields, dropped fields, etc) are reflected in the SQLite cache and in supabase.**

## Commands
- Build: `npm run build` (electron-builder)
- Dev: `npm run dev` (development mode)
- Package: Platform-specific builds via electron-builder
- Lint: Check code quality and formatting
- Typecheck: Validate TypeScript types (if applicable)

## Code Implementation Guidelines

### **CRITICAL**: Follow Modular Architecture System
All development MUST follow the **Modular Architecture System** documented above. This is not optional - it ensures:
- Consistent code structure across the application
- Reduced code duplication (~30% elimination)
- Standardized error handling and logging
- Proper dependency management
- Interface compliance for maintainability

**Before implementing any feature or modification**:
1. ✅ Read the "Modular Architecture System" section above
2. ✅ Follow the module implementation standards
3. ✅ Use the provided code templates
4. ✅ Extend BaseCrudManager for data operations
5. ✅ Implement required interface contracts
6. ✅ Use centralized Logger system

### Quick Reference - Essential Patterns

#### Commands vs Module Separation
- **Commands.js**: Lightweight command classes that delegate to module managers
- **Module Managers**: Handle business logic, templates, complex UI operations, and DOM manipulation
- **Module Index**: Public API entry point implementing `ModuleInterface` or `FeatureModuleInterface`
- **Command Factory**: Each module provides commands via factory pattern for registration

### Critical Development Rules (MANDATORY)
**⚠️ See "Modular Architecture System" section above for complete implementation details**

1. **Interface Compliance**: All modules MUST implement `ModuleInterface` or `FeatureModuleInterface`
2. **CRUD Standardization**: All data managers MUST extend `BaseCrudManager` (not `BaseManager`)
3. **Centralized Logging**: MUST use `Logger.forModule('ModuleName')` for all logging
4. **Command Delegation**: Commands MUST delegate to module managers, not implement logic directly
5. **Shared Imports**: MUST import shared utilities from `../../shared/` path
6. **Template Separation**: Never import templates in commands.js
7. **Event Handling**: Use `data-action` attributes for button handling
8. **Module Factory**: Use module factory pattern for command registration
9. **Error Handling**: Use standardized error handling from base classes
10. **Dependency Injection**: Dependencies via constructor, not global access

**📚 For implementation examples, see the "Modular Architecture System" section above which contains:**
- Complete module entry point implementation
- CRUD manager implementation  
- Command factory implementation
- Centralized logging usage patterns
- Module registration and integration examples

### Architecture Compliance Checklist
Before committing any module changes, verify:

**Module Structure**:
- ✅ Module extends `ModuleInterface` or `FeatureModuleInterface`
- ✅ CRUD manager extends `BaseCrudManager` (not `BaseManager`)
- ✅ Commands use factory pattern and delegate to managers
- ✅ All imports from shared utilities use `../../shared/` path
- ✅ Module implements required interface methods (`initialize`, `cleanup`, `getCommands`)

**Code Quality**:
- ✅ All logging uses `Logger.forModule('ModuleName')`
- ✅ No direct database access (use CRUD managers)
- ✅ No business logic in command classes
- ✅ No template imports in commands.js
- ✅ Error handling uses standardized base class methods

**Integration**:
- ✅ Module registered in app.js initialization
- ✅ Commands properly collected by module registry
- ✅ Dependencies injected via constructor
- ✅ Module lifecycle methods work correctly (init/cleanup)