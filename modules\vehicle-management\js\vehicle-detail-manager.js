/**
 * Vehicle Detail Manager
 * Handles vehicle detail view with tabs for basic info and activities
 */

import { BaseManager } from '../../shared/base-manager.js';
import { vehicleDetailTemplates } from '../templates/vehicle-detail-templates.js';

export class VehicleDetailManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.activitiesManager = null; // Will be set by parent module
        this.currentVehicle = null;
    }

    /**
     * Show vehicle detail using main application layout (like person details)
     */
    async showVehicleDetail(vehicleId) {
        try {
            // Load vehicle data
            const vehicle = await this.loadVehicleDetail(vehicleId);
            if (!vehicle) {
                throw new Error('Vehicle not found');
            }

            this.currentVehicle = vehicle;

            // Use the app's tab loading system like person details
            if (window.app) {
                window.app.selectedVehicle = vehicle;
                await window.app.loadTabContent('records', 'vehicle-detail');

                // Set up tab navigation for vehicle detail
                setTimeout(() => {
                    this.setupVehicleDetailTabs();
                    this.loadVehicleActivities(vehicle.id);
                }, 100);
            }

        } catch (error) {
            console.error('Error showing vehicle detail:', error);
            this.ui.showDialog('Error', `Failed to show vehicle details: ${error.message}`, 'error');
        }
    }

    /**
     * Get vehicle detail content for the main app layout
     */
    async getVehicleDetailContent(vehicleId) {
        try {
            const vehicle = await this.loadVehicleDetail(vehicleId);
            if (!vehicle) {
                return '<div class="error">Vehicle not found</div>';
            }

            this.currentVehicle = vehicle;
            return vehicleDetailTemplates.vehicleDetailContent(vehicle);

        } catch (error) {
            console.error('Error loading vehicle detail content:', error);
            return `<div class="error">Failed to load vehicle details: ${error.message}</div>`;
        }
    }

    /**
     * Load vehicle detail data
     */
    async loadVehicleDetail(vehicleId) {
        try {
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: vehicle, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .select('*')
                .eq('id', vehicleId)
                .single();

            if (error) {
                throw error;
            }

            return vehicle;
        } catch (error) {
            console.error('Error loading vehicle detail:', error);
            throw error;
        }
    }

    /**
     * Set up tab navigation for vehicle detail (like person detail tabs)
     */
    setupVehicleDetailTabs() {
        // Tab switching
        const tabs = document.querySelectorAll('.vehicle-detail-tab');
        const tabContents = document.querySelectorAll('.vehicle-detail-tab-content .tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.getAttribute('data-tab');
                
                // Update tab states
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(tc => tc.classList.remove('active'));
                
                tab.classList.add('active');
                const targetContent = document.querySelector(`.tab-content[data-tab="${tabName}"]`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }

                // Load tab-specific content
                if (tabName === 'activities') {
                    this.loadVehicleActivities(this.currentVehicle.id);
                }
            });
        });

        // Set up button handlers using data-action attributes
        this.setupVehicleActionHandlers();
    }

    /**
     * Set up action handlers for vehicle detail buttons
     */
    setupVehicleActionHandlers() {
        // These will be handled by the global command system via data-action attributes
        // No need for individual event listeners since the app handles data-action delegation
    }

    /**
     * Load vehicle activities
     */
    async loadVehicleActivities(vehicleId) {
        try {
            const activitiesList = document.getElementById('activities-list');
            if (!activitiesList) return;

            activitiesList.innerHTML = '<div class="loading">Loading activities...</div>';

            // Use the activities manager if available
            if (this.activitiesManager) {
                const activities = await this.activitiesManager.loadVehicleActivities(vehicleId);
                this.activitiesManager.displayActivities(activities, activitiesList);
            } else {
                // Fallback to direct data access - use core schema
                const supabase = await this.data.getSupabaseClient();
                if (!supabase) {
                    throw new Error('Supabase client not available');
                }

                const { data: activities, error } = await supabase
                    .schema('core')
                    .from('vehicle_activities')
                    .select('*')
                    .eq('vehicle_id', vehicleId)
                    .order('created_at', { ascending: false });

                if (error) {
                    throw error;
                }

                this.displayActivities(activities || [], activitiesList);
            }

        } catch (error) {
            console.error('Error loading vehicle activities:', error);
            const activitiesList = document.getElementById('activities-list');
            if (activitiesList) {
                activitiesList.innerHTML = '<div class="error">Error loading activities</div>';
            }
        }
    }

    /**
     * Display activities in the activities list
     */
    displayActivities(activities, container) {
        if (!activities || activities.length === 0) {
            container.innerHTML = vehicleDetailTemplates.noActivitiesMessage();
            return;
        }

        // Sort activities by date (newest first)
        const sortedActivities = activities.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        );

        let activitiesHTML = '';
        sortedActivities.forEach(activity => {
            activitiesHTML += vehicleDetailTemplates.activityItem(activity);
        });

        container.innerHTML = activitiesHTML;
    }

    /**
     * Show edit vehicle form (delegates to CRUD manager)
     */
    async showEditVehicleForm(vehicleId) {
        try {
            // Emit event to show edit form
            const event = new CustomEvent('editVehicle', {
                detail: { vehicleId: vehicleId }
            });
            window.dispatchEvent(event);
        } catch (error) {
            console.error('Error showing edit form:', error);
            this.ui.showDialog('Error', `Failed to show edit form: ${error.message}`, 'error');
        }
    }

    /**
     * Confirm and delete vehicle (delegates to CRUD manager)
     */
    async confirmDeleteVehicle(vehicleId) {
        try {
            const confirmed = await this.ui.showConfirmDialog(
                'Delete Vehicle',
                'Are you sure you want to delete this vehicle record? This action cannot be undone.',
                'Delete',
                'Cancel'
            );

            if (confirmed) {
                // Emit event to delete vehicle
                const event = new CustomEvent('deleteVehicle', {
                    detail: { vehicleId: vehicleId }
                });
                window.dispatchEvent(event);

                // Close detail modal
                const modal = document.querySelector('.vehicle-detail-overlay');
                if (modal) {
                    document.body.removeChild(modal);
                }
            }
        } catch (error) {
            console.error('Error deleting vehicle:', error);
            this.ui.showDialog('Error', `Failed to delete vehicle: ${error.message}`, 'error');
        }
    }

    /**
     * Show add activity form (delegates to activities manager)
     */
    async showAddActivityForm(vehicleId) {
        try {
            if (this.activitiesManager) {
                await this.activitiesManager.showAddActivityForm(vehicleId);
            } else {
                // Emit event as fallback
                const event = new CustomEvent('addVehicleActivity', {
                    detail: { vehicleId: vehicleId }
                });
                window.dispatchEvent(event);
            }
        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show add activity form: ${error.message}`, 'error');
        }
    }

    /**
     * Refresh the current vehicle detail view
     */
    async refreshDetail() {
        if (this.currentVehicle) {
            await this.showVehicleDetail(this.currentVehicle.id);
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.currentVehicle = null;
    }
}
