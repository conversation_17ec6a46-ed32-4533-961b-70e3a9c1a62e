// Stats Generator Utility - Eliminates duplication of statistics logic
// Provides configurable statistics generation for any entity type

export class StatsGenerator {
    /**
     * Generate basic count statistics for a dataset
     * @param {Array} data - Array of objects to analyze
     * @param {Object} config - Configuration for stats generation
     * @returns {Object} Statistics object
     */
    static generateBasicStats(data, config = {}) {
        if (!data || !Array.isArray(data)) {
            return { total: 0 };
        }

        const stats = {
            total: data.length
        };

        // Generate count statistics by field values
        if (config.countByFields) {
            config.countByFields.forEach(fieldConfig => {
                const { field, name, values } = fieldConfig;
                
                if (values) {
                    // Count specific values
                    values.forEach(value => {
                        const count = data.filter(item => 
                            this.getFieldValue(item, field) === value
                        ).length;
                        stats[`${name || field}_${value}`] = count;
                    });
                } else {
                    // Count all unique values
                    const valueCounts = this.countUniqueValues(data, field);
                    Object.entries(valueCounts).forEach(([value, count]) => {
                        stats[`${name || field}_${value}`] = count;
                    });
                }
            });
        }

        // Generate boolean field statistics
        if (config.booleanFields) {
            config.booleanFields.forEach(fieldConfig => {
                const { field, name } = fieldConfig;
                const trueCount = data.filter(item => 
                    this.getFieldValue(item, field) === true
                ).length;
                const falseCount = data.length - trueCount;
                
                stats[`${name || field}_true`] = trueCount;
                stats[`${name || field}_false`] = falseCount;
            });
        }

        return stats;
    }

    /**
     * Generate time-based statistics
     * @param {Array} data - Array of objects with date fields
     * @param {Object} config - Time statistics configuration
     * @returns {Object} Time-based statistics
     */
    static generateTimeStats(data, config = {}) {
        if (!data || !Array.isArray(data)) {
            return {};
        }

        const stats = {};
        const dateField = config.dateField || 'created_at';
        const groupBy = config.groupBy || 'month'; // day, week, month, year

        // Filter out items without valid dates
        const validData = data.filter(item => {
            const date = this.getFieldValue(item, dateField);
            return date && !isNaN(new Date(date).getTime());
        });

        if (validData.length === 0) {
            return stats;
        }

        // Group by time periods
        const grouped = validData.reduce((acc, item) => {
            const date = new Date(this.getFieldValue(item, dateField));
            let key;

            switch (groupBy) {
                case 'day':
                    key = date.toISOString().split('T')[0]; // YYYY-MM-DD
                    break;
                case 'week':
                    const weekStart = new Date(date);
                    weekStart.setDate(date.getDate() - date.getDay());
                    key = weekStart.toISOString().split('T')[0];
                    break;
                case 'month':
                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                    break;
                case 'year':
                    key = String(date.getFullYear());
                    break;
                default:
                    key = 'all';
            }

            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(item);
            return acc;
        }, {});

        // Generate statistics for each time period
        Object.entries(grouped).forEach(([period, items]) => {
            stats[`${groupBy}_${period}`] = items.length;
            
            // Additional stats if requested
            if (config.includeSubStats) {
                stats[`${groupBy}_${period}_details`] = this.generateBasicStats(items, config.subStatsConfig);
            }
        });

        // Recent activity stats
        if (config.includeRecentActivity) {
            const now = new Date();
            const periods = {
                last_24h: 24 * 60 * 60 * 1000,
                last_7d: 7 * 24 * 60 * 60 * 1000,
                last_30d: 30 * 24 * 60 * 60 * 1000
            };

            Object.entries(periods).forEach(([period, milliseconds]) => {
                const cutoff = new Date(now.getTime() - milliseconds);
                const recentCount = validData.filter(item => {
                    const itemDate = new Date(this.getFieldValue(item, dateField));
                    return itemDate >= cutoff;
                }).length;
                stats[period] = recentCount;
            });
        }

        return stats;
    }

    /**
     * Generate status/workflow statistics
     * @param {Array} data - Array of objects with status fields
     * @param {Object} config - Status statistics configuration
     * @returns {Object} Status-based statistics
     */
    static generateStatusStats(data, config = {}) {
        if (!data || !Array.isArray(data)) {
            return {};
        }

        const statusField = config.statusField || 'status';
        const statusCounts = this.countUniqueValues(data, statusField);
        
        const stats = {
            total: data.length,
            ...statusCounts
        };

        // Calculate percentages if requested
        if (config.includePercentages && data.length > 0) {
            Object.entries(statusCounts).forEach(([status, count]) => {
                stats[`${status}_percentage`] = Math.round((count / data.length) * 100);
            });
        }

        // Track status transitions if history is available
        if (config.trackTransitions && config.historyField) {
            stats.transitions = this.calculateStatusTransitions(data, config);
        }

        return stats;
    }

    /**
     * Count unique values in a field
     * @param {Array} data - Data array
     * @param {string} field - Field to count
     * @returns {Object} Value counts
     */
    static countUniqueValues(data, field) {
        return data.reduce((acc, item) => {
            const value = this.getFieldValue(item, field);
            const key = value !== null && value !== undefined ? String(value) : 'null';
            acc[key] = (acc[key] || 0) + 1;
            return acc;
        }, {});
    }

    /**
     * Get field value with dot notation support
     * @param {Object} obj - Object to get value from
     * @param {string} field - Field path
     * @returns {*} Field value
     */
    static getFieldValue(obj, field) {
        return field.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }

    /**
     * Calculate status transitions (if transition history is available)
     * @param {Array} data - Data with status history
     * @param {Object} config - Transition configuration
     * @returns {Object} Transition statistics
     */
    static calculateStatusTransitions(data, config) {
        const transitions = {};
        const historyField = config.historyField;

        data.forEach(item => {
            const history = this.getFieldValue(item, historyField);
            if (!Array.isArray(history) || history.length < 2) return;

            for (let i = 1; i < history.length; i++) {
                const from = history[i - 1].status;
                const to = history[i].status;
                const transition = `${from}_to_${to}`;
                transitions[transition] = (transitions[transition] || 0) + 1;
            }
        });

        return transitions;
    }

    /**
     * Predefined statistics configurations for common entity types
     */
    static getEntityStatsConfig(entityType) {
        const configs = {
            bikes: {
                countByFields: [
                    { field: 'is_stolen', name: 'stolen_status', values: [true, false] },
                    { field: 'status', name: 'status' }
                ],
                booleanFields: [
                    { field: 'is_stolen', name: 'stolen' }
                ],
                includeRecentActivity: true
            },
            
            properties: {
                countByFields: [
                    { field: 'status', name: 'status' },
                    { field: 'item_type', name: 'type' }
                ],
                includeRecentActivity: true,
                dateField: 'found_date'
            },
            
            encampments: {
                countByFields: [
                    { field: 'status', name: 'status' },
                    { field: 'size_category', name: 'size' }
                ],
                booleanFields: [
                    { field: 'has_structures', name: 'structures' },
                    { field: 'needs_cleanup', name: 'cleanup_needed' }
                ],
                includeRecentActivity: true
            },
            
            people: {
                countByFields: [
                    { field: 'housing_status', name: 'housing' },
                    { field: 'primary_language', name: 'language' }
                ],
                booleanFields: [
                    { field: 'has_pets', name: 'pets' },
                    { field: 'has_medical_issues', name: 'medical' }
                ],
                includeRecentActivity: true
            },
            
            incidents: {
                countByFields: [
                    { field: 'incident_type', name: 'type' },
                    { field: 'priority', name: 'priority' },
                    { field: 'status', name: 'status' }
                ],
                includeRecentActivity: true,
                statusField: 'status',
                includePercentages: true
            }
        };

        return configs[entityType] || {
            countByFields: [],
            booleanFields: [],
            includeRecentActivity: false
        };
    }

    /**
     * Generate comprehensive statistics for an entity
     * @param {Array} data - Entity data
     * @param {string} entityType - Type of entity
     * @param {Object} customConfig - Custom configuration
     * @returns {Object} Comprehensive statistics
     */
    static generateEntityStats(data, entityType, customConfig = {}) {
        const defaultConfig = this.getEntityStatsConfig(entityType);
        const config = { ...defaultConfig, ...customConfig };

        const stats = {
            basic: this.generateBasicStats(data, config),
            time: this.generateTimeStats(data, config),
            status: this.generateStatusStats(data, config)
        };

        // Add meta information
        stats.meta = {
            entityType,
            generatedAt: new Date().toISOString(),
            dataCount: data ? data.length : 0
        };

        return stats;
    }
}