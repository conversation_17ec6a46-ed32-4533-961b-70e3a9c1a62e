# S.T.E.V.I Retro Installer Creation Script
# PowerShell script for creating installer on Windows

Write-Host "🚀 Creating S.T.E.V.I Retro Installer..." -ForegroundColor Green

try {
    # Step 1: Build the application
    Write-Host "📦 Building application..." -ForegroundColor Yellow
    & node build-custom.cjs
    if ($LASTEXITCODE -ne 0) { throw "Build failed" }
    
    # Step 2: Create LICENSE file if needed
    if (!(Test-Path "LICENSE")) {
        Write-Host "📄 Creating LICENSE file..." -ForegroundColor Yellow
        @"
MIT License

Copyright (c) 2024 I.H.A.R.C

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
"@ | Out-File -FilePath "LICENSE" -Encoding UTF8
    }
    
    # Step 3: Check for NSIS
    Write-Host "🔍 Checking for NSIS..." -ForegroundColor Yellow
    $nsisPath = $null
    $possiblePaths = @(
        "C:\Program Files (x86)\NSIS\makensis.exe",
        "C:\Program Files\NSIS\makensis.exe"
    )
    
    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $nsisPath = $path
            break
        }
    }
    
    # Also check if NSIS is in PATH
    if (!$nsisPath) {
        try {
            $null = Get-Command "makensis.exe" -ErrorAction Stop
            $nsisPath = "makensis.exe"
        } catch {}
    }
    
    if ($nsisPath) {
        # Step 4: Create installer with NSIS
        Write-Host "🔨 Creating installer with NSIS..." -ForegroundColor Yellow
        & $nsisPath "build\installer.nsi"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Installer created successfully!" -ForegroundColor Green
            Write-Host "📍 Installer location: S.T.E.V.I-Retro-Installer.exe" -ForegroundColor Cyan
        } else {
            throw "NSIS compilation failed"
        }
    } else {
        Write-Host "⚠️  NSIS not found. Creating portable ZIP instead..." -ForegroundColor Yellow
        
        # Create portable version
        $portableDir = "dist\S.T.E.V.I-Retro-Portable"
        if (Test-Path $portableDir) { Remove-Item $portableDir -Recurse -Force }
        New-Item -ItemType Directory -Path $portableDir -Force | Out-Null
        
        # Copy built application
        Copy-Item -Path "dist\win-unpacked\*" -Destination $portableDir -Recurse
        
        # Create README for portable version
        @"
S.T.E.V.I Retro - Portable Version
======================================

This is a portable version of S.T.E.V.I Retro.

To run the application:
1. Double-click "S.T.E.V.I Retro.exe"
2. The application will create its data folders in your user profile

To uninstall:
1. Simply delete this folder
2. Optionally, delete the data folder at: %APPDATA%\steviretro

Version: 1.3.0
Copyright © 2024 I.H.A.R.C. All rights reserved.
"@ | Out-File -FilePath "$portableDir\README.txt" -Encoding UTF8
        
        # Create ZIP if possible
        try {
            Write-Host "🗜️  Creating ZIP archive..." -ForegroundColor Yellow
            $zipPath = "S.T.E.V.I-Retro-Portable.zip"
            if (Test-Path $zipPath) { Remove-Item $zipPath }
            
            # Use .NET compression
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            [System.IO.Compression.ZipFile]::CreateFromDirectory($portableDir, $zipPath)
            
            Write-Host "✅ Portable ZIP created: S.T.E.V.I-Retro-Portable.zip" -ForegroundColor Green
            
        } catch {
            Write-Host "⚠️  Could not create ZIP. Portable folder available at: $portableDir" -ForegroundColor Yellow
        }
    }
    
    Write-Host "🎉 Build process completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Build process failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}