/**
 * People Commands Factory
 * Creates modular people commands following the established patterns
 */

import { BaseCommand } from '../../shared/base-command.js';

export class PeopleCommandsFactory {
    constructor(commandManager, peopleManagement) {
        this.commandManager = commandManager;
        this.peopleManagement = peopleManagement;
    }

    createCommands() {
        return new Map([
            // Main people management commands
            ['add-person', new AddPersonCommand(this.commandManager, this.peopleManagement)],
            ['edit-person', new EditPersonCommand(this.commandManager, this.peopleManagement)],
            ['delete-person', new DeletePersonCommand(this.commandManager, this.peopleManagement)],
            ['view-person-detail', new ViewPersonDetailCommand(this.commandManager, this.peopleManagement)],
            ['manage-people', new ManagePeopleCommand(this.commandManager, this.peopleManagement)],
            ['search-people', new SearchPeopleCommand(this.commandManager, this.peopleManagement)],

            // Pet management commands
            ['add-pet', new AddPetCommand(this.commandManager, this.peopleManagement)],
            ['edit-pet', new EditPetCommand(this.commandManager, this.peopleManagement)],
            ['delete-pet', new DeletePetCommand(this.commandManager, this.peopleManagement)],

            // Medical management commands
            ['add-medical-issue', new AddMedicalIssueCommand(this.commandManager, this.peopleManagement)],
            ['edit-medical-issue', new EditMedicalIssueCommand(this.commandManager, this.peopleManagement)],
            ['delete-medical-issue', new DeleteMedicalIssueCommand(this.commandManager, this.peopleManagement)],

            // Activity management commands
            ['add-person-activity', new AddPersonActivityCommand(this.commandManager, this.peopleManagement)],
            ['edit-person-activity', new EditPersonActivityCommand(this.commandManager, this.peopleManagement)],
            ['delete-person-activity', new DeletePersonActivityCommand(this.commandManager, this.peopleManagement)],
            ['view-person-activities', new ViewPersonActivitiesCommand(this.commandManager, this.peopleManagement)],

            // Legacy support commands
            ['show-medical-info-dialog', new ShowMedicalInfoDialogCommand(this.commandManager, this.peopleManagement)],
            ['save-medical-info', new SaveMedicalInfoCommand(this.commandManager, this.peopleManagement)],

            // Alias management commands (if needed)
            ['manage-aliases', new ManageAliasesCommand(this.commandManager, this.peopleManagement)]
        ]);
    }
}

// === MAIN PEOPLE MANAGEMENT COMMANDS ===

class AddPersonCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            return await this.peopleManagement.createPersonWorkflow();
        } catch (error) {
            console.error('Error in add person command:', error);
            throw error;
        }
    }
}

class EditPersonCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.editPerson(personId);
        } catch (error) {
            console.error('Error in edit person command:', error);
            throw error;
        }
    }
}

class DeletePersonCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.deletePerson(personId);
        } catch (error) {
            console.error('Error in delete person command:', error);
            throw error;
        }
    }
}

class ViewPersonDetailCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.viewPersonWorkflow(personId);
        } catch (error) {
            console.error('Error in view person detail command:', error);
            throw error;
        }
    }
}

class ManagePeopleCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            return await this.peopleManagement.managePeopleWorkflow();
        } catch (error) {
            console.error('Error in manage people command:', error);
            throw error;
        }
    }
}

class SearchPeopleCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            return await this.peopleManagement.searchPersonWorkflow();
        } catch (error) {
            console.error('Error in search people command:', error);
            throw error;
        }
    }
}

// === PET MANAGEMENT COMMANDS ===

class AddPetCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.addPet(personId);
        } catch (error) {
            console.error('Error in add pet command:', error);
            throw error;
        }
    }
}

class EditPetCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const petId = args?.petId || args?.id || args[0];
            if (!petId) {
                throw new Error('Pet ID is required');
            }
            return await this.peopleManagement.editPet(petId);
        } catch (error) {
            console.error('Error in edit pet command:', error);
            throw error;
        }
    }
}

class DeletePetCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const petId = args?.petId || args?.id || args[0];
            if (!petId) {
                throw new Error('Pet ID is required');
            }
            return await this.peopleManagement.deletePet(petId);
        } catch (error) {
            console.error('Error in delete pet command:', error);
            throw error;
        }
    }
}

// === MEDICAL MANAGEMENT COMMANDS ===

class AddMedicalIssueCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.addMedicalIssue(personId);
        } catch (error) {
            console.error('Error in add medical issue command:', error);
            throw error;
        }
    }
}

class EditMedicalIssueCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const issueId = args?.issueId || args?.id || args[0];
            if (!issueId) {
                throw new Error('Medical issue ID is required');
            }
            return await this.peopleManagement.medicalManager.editMedicalIssue(issueId);
        } catch (error) {
            console.error('Error in edit medical issue command:', error);
            throw error;
        }
    }
}

class DeleteMedicalIssueCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const issueId = args?.issueId || args?.id || args[0];
            if (!issueId) {
                throw new Error('Medical issue ID is required');
            }
            return await this.peopleManagement.medicalManager.deleteMedicalIssue(issueId);
        } catch (error) {
            console.error('Error in delete medical issue command:', error);
            throw error;
        }
    }
}

// === LEGACY SUPPORT COMMANDS ===

class ShowMedicalInfoDialogCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personIndex = args?.personIndex || args?.index || args[0];
            return await this.peopleManagement.showMedicalInfoDialog(personIndex);
        } catch (error) {
            console.error('Error in show medical info dialog command:', error);
            throw error;
        }
    }
}

class SaveMedicalInfoCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personIndex = args?.personIndex || args?.index || args[0];
            return await this.peopleManagement.saveMedicalInfo(personIndex);
        } catch (error) {
            console.error('Error in save medical info command:', error);
            throw error;
        }
    }
}

// === ALIAS MANAGEMENT COMMANDS ===

class ManageAliasesCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            
            // Get person record
            const person = await this.peopleManagement.data.get('people', personId);
            if (!person) {
                throw new Error('Person not found');
            }

            return await this.peopleManagement.detailManager.showAliasManagementModal(person);
        } catch (error) {
            console.error('Error in manage aliases command:', error);
            throw error;
        }
    }
}

// === ACTIVITY MANAGEMENT COMMANDS ===

class AddPersonActivityCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.addPersonActivity(personId);
        } catch (error) {
            console.error('Error in add person activity command:', error);
            throw error;
        }
    }
}

class EditPersonActivityCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const activityId = args?.activityId || args?.id || args[0];
            const updateData = args?.updateData || args[1];

            if (!activityId) {
                throw new Error('Activity ID is required');
            }
            if (!updateData) {
                throw new Error('Update data is required');
            }

            return await this.peopleManagement.updatePersonActivity(activityId, updateData);
        } catch (error) {
            console.error('Error in edit person activity command:', error);
            throw error;
        }
    }
}

class DeletePersonActivityCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const activityId = args?.activityId || args?.id || args[0];
            if (!activityId) {
                throw new Error('Activity ID is required');
            }
            return await this.peopleManagement.deletePersonActivity(activityId);
        } catch (error) {
            console.error('Error in delete person activity command:', error);
            throw error;
        }
    }
}

class ViewPersonActivitiesCommand extends BaseCommand {
    constructor(commandManager, peopleManagement) {
        super(commandManager);
        this.peopleManagement = peopleManagement;
    }

    async execute(args) {
        try {
            const personId = args?.personId || args?.id || args[0];
            if (!personId) {
                throw new Error('Person ID is required');
            }
            return await this.peopleManagement.loadPersonActivities(personId);
        } catch (error) {
            console.error('Error in view person activities command:', error);
            throw error;
        }
    }
}