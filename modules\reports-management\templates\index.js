export const reportTemplates = {
    reportMainView() {
        return `
            <div class="content-section reports-main">
                <h2>> REPORTS & PDF GENERATION</h2>
                
                <div class="reports-menu">
                    <div class="terminal-section">
                        <div class="section-header">> AVAILABLE REPORTS</div>
                        
                        <div class="report-options">
                            <div class="report-item" data-action="generate-resource-services-report">
                                <div class="report-icon">📋</div>
                                <div class="report-details">
                                    <div class="report-title">Resource/Services Printout</div>
                                    <div class="report-description">Generate printable resource and service information for clients</div>
                                </div>
                                <div class="report-action">
                                    <button class="terminal-button" data-action="generate-resource-services-report">[GENERATE]</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="terminal-section">
                        <div class="section-header">> REPORT HISTORY</div>
                        <div class="report-history">
                            <div class="history-placeholder">Recent reports will appear here...</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    resourceServicesModal() {
        return `
            <div class="modal-overlay" id="resource-services-modal">
                <div class="modal-content report-modal">
                    <div class="modal-header">
                        <h3>> RESOURCE/SERVICES REPORT GENERATOR</h3>
                        <button class="close-modal" id="close-resource-modal">[X]</button>
                    </div>
                    
                    <div class="modal-body">
                        <form id="resource-services-form" class="report-form">
                            <div class="form-section">
                                <div class="section-header">> SELECT CLIENT</div>
                                <div class="person-search-section">
                                    <div class="search-bar">
                                        <input type="text" id="person-search-input" 
                                               placeholder="Search for person by name..." 
                                               class="search-input" autocomplete="off">
                                        <button type="button" class="search-button" id="search-person-btn">🔍</button>
                                    </div>
                                    <div id="person-search-results" class="search-results"></div>
                                    <div id="selected-person" class="selected-person" style="display: none;">
                                        <div class="person-info">
                                            <strong>Selected: </strong><span id="selected-person-name"></span>
                                            <input type="hidden" id="selected-person-id" name="personId">
                                            <button type="button" class="change-person-btn">[CHANGE]</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <div class="section-header">> SELECT SERVICE CATEGORIES</div>
                                <div id="service-categories" class="checkbox-grid">
                                    <!-- Categories will be populated here -->
                                </div>
                            </div>
                            
                            <div class="form-section">
                                <div class="section-header">> SELECT LOCATION</div>
                                <div class="location-options">
                                    <select id="location-select" name="location" class="terminal-select">
                                        <option value="All">All Locations</option>
                                        <option value="Port Hope">Port Hope</option>
                                        <option value="Cobourg">Cobourg</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="terminal-button primary">[GENERATE REPORT]</button>
                                <button type="button" class="terminal-button" id="cancel-resource-report">[CANCEL]</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    },

    generateServiceCategoriesHTML(categories) {
        return categories.map(category => `
            <div class="checkbox-item">
                <input type="checkbox" id="cat_${category.value}" name="categories" value="${category.value}">
                <label for="cat_${category.value}">${category.label}</label>
            </div>
        `).join('');
    },

    personSearchResultsHTML(people) {
        if (people.length === 0) {
            return '<div class="no-results">No people found matching your search.</div>';
        }
        
        return people.map(person => `
            <div class="person-result" data-person-id="${person.id}">
                <div class="person-name">${person.first_name} ${person.last_name}</div>
                <div class="person-details">DOB: ${person.date_of_birth || 'N/A'} | ID: ${person.id}</div>
            </div>
        `).join('');
    },

    generatePDFContentHTML(reportData) {
        const { person, organizations, categories, location, staffName, generatedAt } = reportData;
        
        return `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Resource & Services Information</title>
                <style>
                    body {
                        font-family: 'Courier New', monospace;
                        background: black;
                        color: #ff0000;
                        margin: 20px;
                        line-height: 1.4;
                    }
                    .header {
                        text-align: center;
                        border-bottom: 2px solid #ff0000;
                        padding-bottom: 10px;
                        margin-bottom: 20px;
                    }
                    .client-info {
                        border: 1px solid #ff0000;
                        padding: 10px;
                        margin-bottom: 20px;
                    }
                    .organization {
                        border: 1px solid #ff0000;
                        margin-bottom: 15px;
                        padding: 10px;
                    }
                    .org-header {
                        font-weight: bold;
                        color: #ffff00;
                        margin-bottom: 5px;
                    }
                    .org-detail {
                        margin-bottom: 3px;
                    }
                    .footer {
                        border-top: 2px solid #ff0000;
                        padding-top: 10px;
                        margin-top: 20px;
                        text-align: center;
                    }
                    .print-section {
                        margin-top: 20px;
                        text-align: center;
                    }
                    .print-button {
                        background: #ff0000;
                        color: black;
                        padding: 10px 20px;
                        border: none;
                        font-family: 'Courier New', monospace;
                        font-weight: bold;
                        cursor: pointer;
                        margin: 5px;
                    }
                    @media print {
                        .print-section { display: none; }
                        body { background: white; color: black; }
                        .header, .client-info, .organization, .footer { border-color: black; }
                        .org-header { color: black; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>I.H.A.R.C RESOURCE & SERVICES INFORMATION</h1>
                    <div>Generated: ${generatedAt.toLocaleDateString()} ${generatedAt.toLocaleTimeString()}</div>
                </div>
                
                <div class="client-info">
                    <div><strong>CLIENT:</strong> ${person.first_name} ${person.last_name}</div>
                    <div><strong>CLIENT ID:</strong> ${person.id}</div>
                    <div><strong>PREPARED BY:</strong> ${staffName}</div>
                    <div><strong>LOCATION FILTER:</strong> ${location}</div>
                    <div><strong>SERVICE CATEGORIES:</strong> ${categories.join(', ')}</div>
                </div>
                
                <div class="organizations-section">
                    <h2>AVAILABLE SERVICES & ORGANIZATIONS (${organizations.length} found)</h2>
                    ${organizations.map(org => `
                        <div class="organization">
                            <div class="org-header">${org.name}</div>
                            <div class="org-detail"><strong>Type:</strong> ${org.organization_type || 'N/A'}</div>
                            <div class="org-detail"><strong>Services:</strong> ${org.services_provided || 'N/A'}</div>
                            <div class="org-detail"><strong>Address:</strong> ${org.address || ''} ${org.city || ''} ${org.province || ''} ${org.postal_code || ''}</div>
                            <div class="org-detail"><strong>Phone:</strong> ${org.phone || 'N/A'}</div>
                            <div class="org-detail"><strong>Hours:</strong> ${org.operating_hours || 'N/A'}</div>
                            <div class="org-detail"><strong>Contact:</strong> ${org.contact_person || 'N/A'} ${org.contact_title ? `(${org.contact_title})` : ''}</div>
                            <div class="org-detail"><strong>Special Requirements:</strong> ${org.special_requirements || 'None noted'}</div>
                            ${org.notes ? `<div class="org-detail"><strong>Notes:</strong> ${org.notes}</div>` : ''}
                        </div>
                    `).join('')}
                </div>
                
                <div class="footer">
                    <div>This information was compiled by I.H.A.R.C staff for client reference.</div>
                    <div>For updates or additional information, please contact I.H.A.R.C directly.</div>
                </div>
                
                <div class="print-section">
                    <button class="print-button" onclick="window.print()">[PRINT REPORT]</button>
                    <button class="print-button" onclick="window.close()">[CLOSE]</button>
                </div>
            </body>
            </html>
        `;
    }
};