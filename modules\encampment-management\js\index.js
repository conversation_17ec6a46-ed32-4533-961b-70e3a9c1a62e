/**
 * Encampment Management Module - Main Entry Point
 * Modular encampment management system following the established patterns
 * Handles encampments and visit tracking
 */

import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { EncampmentManager } from './encampment-manager.js';
import { EncampmentCommandsFactory } from './encampment-commands.js';
import { Logger } from '../../shared/logger.js';

export class EncampmentManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement, app = null) {
        super('EncampmentManagement', '1.0.0', [], ['encampment', 'encampment_visit']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.app = app;
        this.logger = Logger.forModule('EncampmentManagement');

        // Initialize encampment manager
        this.encampmentManager = new EncampmentManager(dataManager, authManager);
        
        this.logger.info('Encampment Management module initialized');
    }

    // Required interface methods
    async initialize() {
        this.logger.info('Initializing Encampment Management module');
    }

    async cleanup() {
        this.logger.info('Cleaning up Encampment Management module');
    }

    getCommands(commandManager) {
        const factory = new EncampmentCommandsFactory(commandManager, this);
        return factory.createCommands();
    }

    getWorkflows() {
        return {
            createEncampment: () => this.showAddEncampmentForm(),
            manageEncampments: () => this.showEncampmentList(),
            recordVisit: (encampmentId) => this.showAddVisitForm(encampmentId)
        };
    }

    getStatistics() {
        return this.encampmentManager.getEncampmentStats();
    }

    // Public API methods that delegate to the encampmentManager
    async showEncampmentList() {
        try {
            const encampments = await this.encampmentManager.getAllEncampments();
            const { encampmentListTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentListTemplates.encampmentList(encampments);
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'encampment-list';
        } catch (error) {
            this.logger.error('Error showing encampment list', error);
            this.ui.showErrorMessage('Failed to load encampments');
        }
    }

    async showAddEncampmentForm() {
        try {
            const { encampmentFormTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentFormTemplates.addEncampmentForm();
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'add-encampment';
        } catch (error) {
            this.logger.error('Error showing add encampment form', error);
            this.ui.showErrorMessage('Failed to show form');
        }
    }

    async showEditEncampmentForm(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                this.ui.showErrorMessage('Encampment not found');
                return;
            }

            const { encampmentFormTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentFormTemplates.editEncampmentForm(encampment);
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'edit-encampment';
            this.app.selectedEncampment = encampment;
        } catch (error) {
            this.logger.error('Error showing edit encampment form', error);
            this.ui.showErrorMessage('Failed to show edit form');
        }
    }

    async showEncampmentDetail(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                this.ui.showErrorMessage('Encampment not found');
                return;
            }

            const visits = await this.encampmentManager.getEncampmentVisits(encampmentId);
            const { encampmentDetailTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentDetailTemplates.encampmentDetail(encampment, visits);
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'encampment-detail';
            this.app.selectedEncampment = encampment;
        } catch (error) {
            this.logger.error('Error showing encampment detail', error);
            this.ui.showErrorMessage('Failed to show encampment detail');
        }
    }

    async showSearchForm() {
        try {
            const { encampmentSearchTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentSearchTemplates.searchForm();
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'search-encampments';
        } catch (error) {
            this.logger.error('Error showing search form', error);
            this.ui.showErrorMessage('Failed to show search form');
        }
    }

    async performSearch(query) {
        try {
            const results = await this.encampmentManager.searchEncampments(query);
            const { encampmentListTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentListTemplates.searchResults(results, query);
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'search-results';
        } catch (error) {
            this.logger.error('Error performing search', error);
            this.ui.showErrorMessage('Failed to perform search');
        }
    }

    async showAddVisitForm(encampmentId) {
        try {
            const encampment = await this.encampmentManager.getEncampmentById(encampmentId);
            if (!encampment) {
                this.ui.showErrorMessage('Encampment not found');
                return;
            }

            const { encampmentVisitTemplates } = await import('../templates/encampment-templates.js');
            
            const content = encampmentVisitTemplates.addVisitForm(encampment);
            this.ui.updateTabContent('encampments', content);
            this.app.currentScreen = 'add-visit';
        } catch (error) {
            this.logger.error('Error showing add visit form', error);
            this.ui.showErrorMessage('Failed to show visit form');
        }
    }

    async saveEncampment(formData) {
        try {
            if (formData.id) {
                // Update existing encampment
                await this.encampmentManager.updateEncampment(formData.id, formData);
                this.ui.showSuccessMessage('Encampment updated successfully');
            } else {
                // Create new encampment
                await this.encampmentManager.createEncampment(formData);
                this.ui.showSuccessMessage('Encampment created successfully');
            }
            
            // Return to list view
            await this.showEncampmentList();
        } catch (error) {
            this.logger.error('Error saving encampment', error);
            this.ui.showErrorMessage('Failed to save encampment');
        }
    }

    async deleteEncampment(encampmentId) {
        try {
            const confirmed = await this.modalManagement.showConfirmDialog(
                'Delete Encampment',
                'Are you sure you want to delete this encampment? This action cannot be undone.'
            );

            if (confirmed) {
                await this.encampmentManager.deleteEncampment(encampmentId);
                this.ui.showSuccessMessage('Encampment deleted successfully');
                await this.showEncampmentList();
            }
        } catch (error) {
            this.logger.error('Error deleting encampment', error);
            this.ui.showErrorMessage('Failed to delete encampment');
        }
    }

    async cancelForm() {
        await this.showEncampmentList();
    }
}

// Export the main class and the original manager for backward compatibility
export { EncampmentManager };
