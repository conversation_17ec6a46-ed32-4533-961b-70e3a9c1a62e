/**
 * Vehicle Search Manager
 * Handles vehicle search functionality with proper field mapping
 */

import { BaseManager } from '../../shared/base-manager.js';

export class VehicleSearchManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    /**
     * Show vehicle search interface
     */
    async showVehicleSearchInterface() {
        return new Promise((resolve) => {
            // Create search modal
            const searchModal = document.createElement('div');
            searchModal.className = 'modal-overlay';
            searchModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>🔍 Search Vehicles</h3>
                        <button type="button" class="close-btn" id="cancel-vehicle-search">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="search-form">
                            <div class="form-field">
                                <label for="vehicle-search-input">Search vehicles:</label>
                                <input type="text" id="vehicle-search-input" placeholder="Enter license plate, make, model, owner name, or province..." class="text-input">
                            </div>
                            <div class="form-actions">
                                <button type="button" id="perform-vehicle-search" class="action-btn primary">Search</button>
                                <button type="button" id="cancel-vehicle-search" class="action-btn secondary">Cancel</button>
                            </div>
                        </div>
                        <div id="vehicle-search-results" class="search-results"></div>
                    </div>
                </div>
            `;

            document.body.appendChild(searchModal);

            const searchInput = searchModal.querySelector('#vehicle-search-input');
            const searchBtn = searchModal.querySelector('#perform-vehicle-search');
            const resultsDiv = searchModal.querySelector('#vehicle-search-results');

            const performSearch = async () => {
                const query = searchInput.value.trim();
                if (!query) {
                    resultsDiv.innerHTML = '<div class="error">Please enter a search term.</div>';
                    return;
                }

                try {
                    resultsDiv.innerHTML = '<div class="loading">Searching vehicles...</div>';
                    
                    const vehicles = await this.searchVehicles(query);

                    if (vehicles.length === 0) {
                        resultsDiv.innerHTML = '<div class="no-results">No vehicles found matching your search.</div>';
                        return;
                    }

                    this.displaySearchResults(vehicles, resultsDiv);

                } catch (error) {
                    resultsDiv.innerHTML = `<div class="error">Error searching vehicles: ${error.message}</div>`;
                }
            };

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Handle cancel
            const cancelBtns = searchModal.querySelectorAll('#cancel-vehicle-search');
            cancelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    document.body.removeChild(searchModal);
                    resolve(null);
                });
            });

            // Close on overlay click
            searchModal.addEventListener('click', (e) => {
                if (e.target === searchModal) {
                    document.body.removeChild(searchModal);
                    resolve(null);
                }
            });

            // Focus search input
            searchInput.focus();
        });
    }

    /**
     * Search vehicles with correct field mapping
     */
    async searchVehicles(query) {
        try {
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            // If no query, return all vehicles
            if (!query || query.trim() === '') {
                const { data: vehicles, error } = await supabase
                    .schema('case_mgmt')
                    .from('license_plates')
                    .select('*')
                    .order('created_at', { ascending: false });

                if (error) throw error;
                return vehicles || [];
            }

            // Search across multiple fields
            const searchTerm = `%${query.toLowerCase()}%`;
            const { data: vehicles, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .select('*')
                .or(`plate_number.ilike.${searchTerm},vehicle_make.ilike.${searchTerm},vehicle_model.ilike.${searchTerm},owner_name.ilike.${searchTerm},province.ilike.${searchTerm}`)
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            return vehicles || [];
        } catch (error) {
            console.error('Error searching vehicles:', error);
            throw error;
        }
    }

    /**
     * Display search results with proper field mapping
     */
    displaySearchResults(vehicles, resultsDiv) {
        let resultsHTML = '<div class="search-results-list">';
        
        vehicles.forEach(vehicle => {
            resultsHTML += `
                <div class="search-result-item vehicle-result" data-vehicle-id="${vehicle.id}" style="cursor: pointer;">
                    <div class="result-header">
                        <strong>🚗 ${vehicle.plate_number || 'Unknown Plate'}</strong>
                        <span class="result-date">${new Date(vehicle.created_at).toLocaleDateString()}</span>
                    </div>
                    <div class="result-details">
                        <p><strong>Make/Model:</strong> ${vehicle.vehicle_make || 'Unknown'} ${vehicle.vehicle_model || ''}</p>
                        <p><strong>Year:</strong> ${vehicle.vehicle_year || 'Unknown'}</p>
                        <p><strong>Color:</strong> ${vehicle.vehicle_color || 'Unknown'}</p>
                        <p><strong>Type:</strong> ${vehicle.vehicle_type || 'Unknown'}</p>
                        <p><strong>Province:</strong> ${vehicle.province || 'Unknown'}</p>
                        <p><strong>Owner:</strong> ${vehicle.owner_name || 'Unknown'}</p>
                        ${vehicle.notes ? `<p><strong>Notes:</strong> ${vehicle.notes}</p>` : ''}
                    </div>
                    <div class="result-actions">
                        <button class="action-btn view-vehicle-detail" data-vehicle-id="${vehicle.id}">View Details</button>
                    </div>
                </div>
            `;
        });
        
        resultsHTML += '</div>';
        resultsDiv.innerHTML = resultsHTML;

        // Add click handlers for vehicle detail buttons
        resultsDiv.querySelectorAll('.view-vehicle-detail').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const vehicleId = btn.getAttribute('data-vehicle-id');
                this.showVehicleDetail(vehicleId);
            });
        });

        // Add click handlers for result items
        resultsDiv.querySelectorAll('.vehicle-result').forEach(item => {
            item.addEventListener('click', () => {
                const vehicleId = item.getAttribute('data-vehicle-id');
                this.showVehicleDetail(vehicleId);
            });
        });
    }

    /**
     * Show vehicle detail (delegates to detail manager)
     */
    async showVehicleDetail(vehicleId) {
        try {
            // Close search modal
            const searchModal = document.querySelector('.modal-overlay');
            if (searchModal) {
                document.body.removeChild(searchModal);
            }

            // Emit event to show vehicle detail
            const event = new CustomEvent('showVehicleDetail', {
                detail: { vehicleId: vehicleId }
            });
            window.dispatchEvent(event);

        } catch (error) {
            console.error('Error showing vehicle detail:', error);
            this.ui.showDialog('Error', `Failed to show vehicle details: ${error.message}`, 'error');
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners or intervals if needed
    }
}
