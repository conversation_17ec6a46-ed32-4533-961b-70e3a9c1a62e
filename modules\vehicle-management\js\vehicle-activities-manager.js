/**
 * Vehicle Activities Manager
 * Handles vehicle activities using the core.vehicle_activities table
 */

import { BaseManager } from '../../shared/base-manager.js';

export class VehicleActivitiesManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.onActivitiesChanged = null; // Callback for when activities change
    }

    /**
     * Load vehicle activities
     */
    async loadVehicleActivities(vehicleId) {
        try {
            // Access vehicle_activities from core schema
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: activities, error } = await supabase
                .schema('core')
                .from('vehicle_activities')
                .select('*')
                .eq('vehicle_id', String(vehicleId))
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            return activities || [];
        } catch (error) {
            console.error('Error loading vehicle activities:', error);
            throw error;
        }
    }

    /**
     * Add vehicle activity
     */
    async addVehicleActivity(vehicleId, activityData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            
            const activity = {
                vehicle_id: String(vehicleId), // Core table uses TEXT for vehicle_id
                activity_type: activityData.activity_type,
                title: activityData.title || activityData.activity_type,
                description: activityData.description || null,
                location: activityData.location || null,
                activity_date: activityData.activity_date || new Date().toISOString().split('T')[0],
                activity_time: activityData.activity_time || null,
                staff_member: activityData.staff_member || currentUser?.name || currentUser?.email || 'Unknown Staff',
                officer_badge: activityData.officer_badge || null,
                citation_number: activityData.citation_number || null,
                outcome: activityData.outcome || null,
                follow_up_required: activityData.follow_up_required || false,
                follow_up_date: activityData.follow_up_date || null,
                priority: activityData.priority || null,
                tags: activityData.tags || null,
                attachments: activityData.attachments || null,
                created_by: currentUser?.email || 'unknown'
            };

            // Insert into core.vehicle_activities
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: result, error } = await supabase
                .schema('core')
                .from('vehicle_activities')
                .insert(activity)
                .select()
                .single();

            if (error) {
                throw error;
            }

            // Trigger callback if set
            if (this.onActivitiesChanged) {
                this.onActivitiesChanged();
            }

            return result;
        } catch (error) {
            console.error('Error adding vehicle activity:', error);
            throw error;
        }
    }

    /**
     * Show add activity form
     */
    async showAddActivityForm(vehicleId) {
        try {
            // Get vehicle info for context
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: vehicle, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .select('*')
                .eq('id', vehicleId)
                .single();

            if (error || !vehicle) {
                throw new Error('Vehicle not found');
            }

            const currentUser = this.auth.getCurrentUser();

            // Define form fields based on core.vehicle_activities schema
            const fields = [
                {
                    name: 'activity_type',
                    label: 'Activity Type',
                    type: 'select',
                    required: true,
                    options: [
                        'traffic_stop',
                        'citation_issued',
                        'warning_issued',
                        'vehicle_search',
                        'impound',
                        'release',
                        'inspection',
                        'accident_report',
                        'theft_report',
                        'recovery',
                        'maintenance',
                        'other'
                    ]
                },
                {
                    name: 'title',
                    label: 'Title',
                    type: 'text',
                    required: true,
                    placeholder: 'Brief title for this activity'
                },
                {
                    name: 'description',
                    label: 'Description',
                    type: 'textarea',
                    placeholder: 'Detailed description of the activity'
                },
                {
                    name: 'location',
                    label: 'Location',
                    type: 'text',
                    placeholder: 'Where did this activity occur?'
                },
                {
                    name: 'activity_date',
                    label: 'Activity Date',
                    type: 'date',
                    required: true,
                    value: new Date().toISOString().split('T')[0]
                },
                {
                    name: 'activity_time',
                    label: 'Activity Time',
                    type: 'time'
                },
                {
                    name: 'staff_member',
                    label: 'Staff Member',
                    type: 'text',
                    required: true,
                    value: currentUser?.name || currentUser?.email || ''
                },
                {
                    name: 'officer_badge',
                    label: 'Officer Badge Number',
                    type: 'text',
                    placeholder: 'Badge number if applicable'
                },
                {
                    name: 'citation_number',
                    label: 'Citation Number',
                    type: 'text',
                    placeholder: 'Citation or ticket number if applicable'
                },
                {
                    name: 'outcome',
                    label: 'Outcome',
                    type: 'select',
                    options: [
                        '',
                        'citation_issued',
                        'warning_issued',
                        'no_action',
                        'arrest_made',
                        'vehicle_impounded',
                        'vehicle_released',
                        'case_closed',
                        'follow_up_required'
                    ]
                },
                {
                    name: 'follow_up_required',
                    label: 'Follow-up Required',
                    type: 'checkbox'
                },
                {
                    name: 'follow_up_date',
                    label: 'Follow-up Date',
                    type: 'date'
                },
                {
                    name: 'priority',
                    label: 'Priority',
                    type: 'select',
                    options: [
                        '',
                        'low',
                        'medium',
                        'high',
                        'urgent'
                    ]
                }
            ];

            // Show form
            this.ui.showFullScreenForm(
                `Add Activity - ${vehicle.plate_number}`,
                fields,
                async (formData) => {
                    try {
                        await this.addVehicleActivity(vehicleId, formData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        // Refresh activities if detail view is open
                        const activitiesList = document.getElementById('activities-list');
                        if (activitiesList) {
                            const activities = await this.loadVehicleActivities(vehicleId);
                            this.displayActivities(activities, activitiesList);
                        }

                        return true; // Success
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false; // Stay on form
                    }
                }
            );

        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show add activity form: ${error.message}`, 'error');
        }
    }

    /**
     * Display activities (helper method for detail manager)
     */
    displayActivities(activities, container) {
        if (!activities || activities.length === 0) {
            container.innerHTML = `
                <div class="no-activities">
                    <div class="no-activities-icon">📝</div>
                    <div class="no-activities-text">No activities recorded yet.</div>
                    <div class="no-activities-subtext">Click "Add Activity" to record the first activity for this vehicle.</div>
                </div>
            `;
            return;
        }

        // Sort activities by date (newest first)
        const sortedActivities = activities.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        );

        let activitiesHTML = '';
        sortedActivities.forEach(activity => {
            activitiesHTML += this.renderActivityItem(activity);
        });

        container.innerHTML = activitiesHTML;
    }

    /**
     * Render individual activity item
     */
    renderActivityItem(activity) {
        return `
            <div class="activity-item" data-activity-id="${activity.id}">
                <div class="activity-header">
                    <div class="activity-title">
                        <strong>${activity.title || activity.activity_type}</strong>
                        <span class="activity-type-badge">${activity.activity_type}</span>
                    </div>
                    <div class="activity-date">
                        ${activity.activity_date ? new Date(activity.activity_date).toLocaleDateString() : 'No date'}
                        ${activity.activity_time ? ` at ${activity.activity_time}` : ''}
                    </div>
                </div>
                <div class="activity-content">
                    ${activity.description ? `<p class="activity-description">${activity.description}</p>` : ''}
                    ${activity.location ? `<p class="activity-location"><strong>Location:</strong> ${activity.location}</p>` : ''}
                    ${activity.staff_member ? `<p class="activity-staff"><strong>Staff:</strong> ${activity.staff_member}</p>` : ''}
                    ${activity.officer_badge ? `<p class="activity-badge"><strong>Badge:</strong> ${activity.officer_badge}</p>` : ''}
                    ${activity.citation_number ? `<p class="activity-citation"><strong>Citation:</strong> ${activity.citation_number}</p>` : ''}
                    ${activity.outcome ? `<p class="activity-outcome"><strong>Outcome:</strong> ${activity.outcome}</p>` : ''}
                    ${activity.follow_up_required ? `<p class="activity-followup"><strong>Follow-up Required:</strong> ${activity.follow_up_date ? `by ${new Date(activity.follow_up_date).toLocaleDateString()}` : 'Yes'}</p>` : ''}
                    ${activity.priority ? `<p class="activity-priority"><strong>Priority:</strong> <span class="priority-${activity.priority}">${activity.priority.toUpperCase()}</span></p>` : ''}
                </div>
                <div class="activity-meta">
                    <span class="activity-created">Created: ${new Date(activity.created_at).toLocaleString()}</span>
                    ${activity.created_by ? `<span class="activity-created-by">by ${activity.created_by}</span>` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.onActivitiesChanged = null;
    }
}
