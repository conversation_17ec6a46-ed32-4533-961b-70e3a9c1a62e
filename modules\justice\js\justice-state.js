// Justice State Management
// In-memory store and helper functions for managing justice data

export class JusticeState {
    constructor() {
        this.currentPerson = null;
        this.currentEpisode = null;
        this.episodes = [];
        this.events = [];
        this.charges = [];
        this.conditions = [];
        this.contacts = [];
        this.facilities = [];
        this.conditionPacks = [];
        this.conditionPackItems = new Map(); // packId -> items
        
        // UI state
        this.activeView = 'episodes'; // episodes, timeline, charges, conditions, contacts
        this.selectedEventType = null;
        this.isOffline = false;
    }
    
    // Person management
    setCurrentPerson(person) {
        this.currentPerson = person;
    }
    
    getCurrentPerson() {
        return this.currentPerson;
    }
    
    // Episode management
    setCurrentEpisode(episode) {
        this.currentEpisode = episode;
    }
    
    getCurrentEpisode() {
        return this.currentEpisode;
    }
    
    setEpisodes(episodes) {
        this.episodes = episodes || [];
    }
    
    getEpisodes() {
        return this.episodes;
    }
    
    addEpisode(episode) {
        this.episodes.unshift(episode); // Add to beginning (most recent first)
    }
    
    updateEpisode(episodeId, updates) {
        const index = this.episodes.findIndex(ep => ep.id === episodeId);
        if (index !== -1) {
            this.episodes[index] = { ...this.episodes[index], ...updates };
        }
        
        if (this.currentEpisode && this.currentEpisode.id === episodeId) {
            this.currentEpisode = { ...this.currentEpisode, ...updates };
        }
    }
    
    // Event management
    setEvents(events) {
        this.events = events || [];
    }
    
    getEvents() {
        return this.events;
    }
    
    addEvent(event) {
        this.events.push(event);
        this.events.sort((a, b) => new Date(a.event_dt) - new Date(b.event_dt));
    }
    
    // Charge management
    setCharges(charges) {
        this.charges = charges || [];
    }
    
    getCharges() {
        return this.charges;
    }
    
    addCharge(charge) {
        this.charges.push(charge);
    }
    
    updateCharge(chargeId, updates) {
        const index = this.charges.findIndex(ch => ch.id === chargeId);
        if (index !== -1) {
            this.charges[index] = { ...this.charges[index], ...updates };
        }
    }
    
    // Condition management
    setConditions(conditions) {
        this.conditions = conditions || [];
    }
    
    getConditions() {
        return this.conditions;
    }
    
    getActiveConditions() {
        return this.conditions.filter(cond => !cond.end_dt);
    }
    
    addCondition(condition) {
        this.conditions.push(condition);
    }
    
    endCondition(conditionId, endDt) {
        const index = this.conditions.findIndex(cond => cond.id === conditionId);
        if (index !== -1) {
            this.conditions[index].end_dt = endDt;
        }
    }
    
    // Contact management
    setContacts(contacts) {
        this.contacts = contacts || [];
    }
    
    getContacts() {
        return this.contacts;
    }
    
    addContact(contact) {
        this.contacts.push(contact);
    }
    
    updateContact(contactId, updates) {
        const index = this.contacts.findIndex(cont => cont.id === contactId);
        if (index !== -1) {
            this.contacts[index] = { ...this.contacts[index], ...updates };
        }
    }
    
    // Reference data management
    setFacilities(facilities) {
        this.facilities = facilities || [];
    }
    
    getFacilities() {
        return this.facilities;
    }
    
    getFacilityById(facilityId) {
        return this.facilities.find(f => f.id === facilityId);
    }
    
    setConditionPacks(packs) {
        this.conditionPacks = packs || [];
    }
    
    getConditionPacks() {
        return this.conditionPacks;
    }
    
    getConditionPackById(packId) {
        return this.conditionPacks.find(p => p.id === packId);
    }
    
    setConditionPackItems(packId, items) {
        this.conditionPackItems.set(packId, items || []);
    }
    
    getConditionPackItems(packId) {
        return this.conditionPackItems.get(packId) || [];
    }
    
    // UI state management
    setActiveView(view) {
        this.activeView = view;
    }
    
    getActiveView() {
        return this.activeView;
    }
    
    setSelectedEventType(eventType) {
        this.selectedEventType = eventType;
    }
    
    getSelectedEventType() {
        return this.selectedEventType;
    }
    
    setOfflineStatus(isOffline) {
        this.isOffline = isOffline;
    }
    
    isOfflineMode() {
        return this.isOffline;
    }
    
    // Helper methods
    getEpisodeById(episodeId) {
        return this.episodes.find(ep => ep.id === episodeId);
    }
    
    getEventById(eventId) {
        return this.events.find(ev => ev.id === eventId);
    }
    
    getChargeById(chargeId) {
        return this.charges.find(ch => ch.id === chargeId);
    }
    
    getConditionById(conditionId) {
        return this.conditions.find(cond => cond.id === conditionId);
    }
    
    getContactById(contactId) {
        return this.contacts.find(cont => cont.id === contactId);
    }
    
    // Status helpers
    hasActiveEpisode() {
        return this.episodes.some(ep => ep.current_state !== 'COMPLETED');
    }
    
    getActiveEpisode() {
        return this.episodes.find(ep => ep.current_state !== 'COMPLETED');
    }
    
    hasOutstandingWarrants() {
        return this.episodes.some(ep => ep.has_warrant);
    }
    
    getCurrentCustodyStatus() {
        const activeEpisode = this.getActiveEpisode();
        if (!activeEpisode) return null;
        
        return {
            inCustody: activeEpisode.current_state === 'IN_CUSTODY',
            facility: activeEpisode.current_facility,
            facilityType: activeEpisode.current_facility_type
        };
    }
    
    getNextCourtDate() {
        const activeEpisode = this.getActiveEpisode();
        return activeEpisode?.next_court_dt || null;
    }
    
    getActiveConditionsSummary() {
        const activeEpisode = this.getActiveEpisode();
        return activeEpisode?.active_conditions_summary || null;
    }
    
    // Data validation helpers
    validateEpisodeData(episodeData) {
        const errors = [];
        
        if (!episodeData.person_id) {
            errors.push('Person ID is required');
        }
        
        if (!episodeData.origin) {
            errors.push('Origin is required');
        }
        
        if (!episodeData.origin_dt) {
            errors.push('Origin date/time is required');
        }
        
        return errors;
    }
    
    validateEventData(eventData) {
        const errors = [];
        
        if (!eventData.je_id) {
            errors.push('Episode ID is required');
        }
        
        if (!eventData.event_type) {
            errors.push('Event type is required');
        }
        
        if (!eventData.event_dt) {
            errors.push('Event date/time is required');
        }
        
        return errors;
    }
    
    // Clear state
    clearAll() {
        this.currentPerson = null;
        this.currentEpisode = null;
        this.episodes = [];
        this.events = [];
        this.charges = [];
        this.conditions = [];
        this.contacts = [];
        this.activeView = 'episodes';
        this.selectedEventType = null;
    }
    
    clearEpisodeData() {
        this.currentEpisode = null;
        this.events = [];
        this.charges = [];
        this.conditions = [];
        this.contacts = [];
    }
}
