// Configuration Manager for S.T.E.V.I DOS Electron App
import { VaultManager } from './vault-manager.js';
import { supabaseConfig } from './supabase-config.js';

export class ConfigManager {
    constructor() {
        this.config = {
            supabase: {
                url: supabaseConfig.getUrl(),
                anonKey: supabaseConfig.getAnonKey()
            },
            google: {
                // API key now stored securely in Supabase Vault
                apiKey: null // No fallback - must use vault
            },
            cache: {
                ttl: 3600, // 1 hour
                maxSize: 1000
            },
            app: {
                name: 'S.T.E.V.I DOS',
                version: '1.0.0',
                debug: false, // Set to false for production, can be overridden via user config
                disableRealtime: false // Set to true to disable WebSocket connections if having issues
            },
            paths: {
                data: this.getDataPath(),
                templates: this.getTemplatesPath(),
                reports: this.getReportsPath(),
                media: this.getMediaPath()
            }
        };

        // VaultManager will be initialized when Supabase client is available
        this.vaultManager = null;
        this.supabaseClient = null;

        this.loadUserConfig();
    }

    /**
     * Initialize VaultManager with Supabase client
     * @param {Object} supabaseClient - The Supabase client instance
     */
    initializeVault(supabaseClient) {
        this.supabaseClient = supabaseClient;
        this.vaultManager = new VaultManager(supabaseClient);
        // VaultManager initialized
    }

    getDataPath() {
        // Fallback for renderer process - use localStorage or default
        return localStorage.getItem('stevidos_data_path') || './data';
    }

    getTemplatesPath() {
        return `${this.getDataPath()}/templates`;
    }

    getReportsPath() {
        return `${this.getDataPath()}/reports`;
    }

    getMediaPath() {
        return `${this.getDataPath()}/media`;
    }

    loadUserConfig() {
        try {
            const userConfig = localStorage.getItem('stevidos_config');
            if (userConfig) {
                const parsed = JSON.parse(userConfig);
                this.config = { ...this.config, ...parsed };
            }
        } catch (error) {
            console.warn('Could not load user config:', error);
        }
    }

    saveUserConfig() {
        try {
            localStorage.setItem('stevidos_config', JSON.stringify(this.config));
        } catch (error) {
            console.warn('Could not save user config:', error);
        }
    }

    get(key) {
        const keys = key.split('.');
        let value = this.config;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return undefined;
            }
        }

        return value;
    }

    set(key, value) {
        const keys = key.split('.');
        let target = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in target) || typeof target[k] !== 'object') {
                target[k] = {};
            }
            target = target[k];
        }

        target[keys[keys.length - 1]] = value;
        this.saveUserConfig();
    }

    getSupabaseUrl() {
        return this.get('supabase.url');
    }

    getSupabaseAnonKey() {
        return this.get('supabase.anonKey');
    }

    /**
     * Get Google API key from Vault
     * @returns {Promise<string|null>} The API key or null if not found
     */
    async getGoogleApiKey() {
        // Ensure VaultManager is initialized
        if (!this.vaultManager) {
            console.warn('ConfigManager: VaultManager not initialized. Cannot retrieve Google API key.');
            return null;
        }

        // Check if Supabase client is available
        if (!this.supabaseClient) {
            console.warn('ConfigManager: Supabase client not available. Cannot retrieve Google API key.');
            return null;
        }

        try {
            console.log('ConfigManager: Attempting to retrieve Google API key from Vault...');

            // Retrieving Google API key from Vault
            const apiKey = await this.vaultManager.getSecret('google_api_key');

            if (apiKey) {
                console.log('ConfigManager: Successfully retrieved Google API key from Vault');
                return apiKey;
            } else {
                console.warn('ConfigManager: Google API key not found in Vault');
                return null;
            }
        } catch (error) {
            console.error('ConfigManager: Error retrieving Google API key from Vault:', error);
            return null;
        }
    }

    /**
     * Get Gemini API key from Vault
     * @returns {Promise<string|null>} The API key or null if not found
     */
    async getGeminiApiKey() {
        // Ensure VaultManager is initialized
        if (!this.vaultManager) {
            console.warn('ConfigManager: VaultManager not initialized. Cannot retrieve Gemini API key.');
            return null;
        }

        // Check if Supabase client is available
        if (!this.supabaseClient) {
            console.warn('ConfigManager: Supabase client not available. Cannot retrieve Gemini API key.');
            return null;
        }

        try {
            console.log('ConfigManager: Attempting to retrieve Gemini API key from Vault...');

            // Retrieving Gemini API key from Vault
            const apiKey = await this.vaultManager.getSecret('gemini_api_key');

            if (apiKey) {
                console.log('ConfigManager: Successfully retrieved Gemini API key from Vault');
                return apiKey;
            } else {
                console.warn('ConfigManager: Gemini API key not found in Vault');
                return null;
            }
        } catch (error) {
            console.error('ConfigManager: Error retrieving Gemini API key from Vault:', error);
            return null;
        }
    }

    /**
     * Get Google API key synchronously (fallback only)
     * @returns {string} The fallback Google API key
     */
    getGoogleApiKeySync() {
        return this.get('google.apiKey');
    }

    getCacheTTL() {
        return this.get('cache.ttl');
    }

    isDebugMode() {
        return this.get('app.debug');
    }

    getAppVersion() {
        return this.get('app.version');
    }

    // Environment-specific settings
    isDevelopment() {
        // Check for debug flag in config or fallback to false
        return this.get('app.debug') === true;
    }

    isProduction() {
        // Production is the default when not in development
        return !this.isDevelopment();
    }

    // Test mode for development
    isTestMode() {
        // FORCE PRODUCTION MODE - disable test mode completely
        // Test mode: production mode
        return false;
    }
}