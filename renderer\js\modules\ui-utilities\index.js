/**
 * UI Utilities Module
 * 
 * Centralized utility functions for UI operations, terminal formatting,
 * notifications, and system status management.
 * 
 * Extracted from app.js as part of Phase 3 modularization.
 */

import { TerminalFormatter } from './terminal-formatter.js';
import { NotificationManager } from './notification-manager.js';
import { DateTimeUtils } from './datetime-utils.js';
import { NetworkMonitor } from './network-monitor.js';

export class UIUtilities {
    constructor() {
        this.terminalFormatter = new TerminalFormatter();
        this.notificationManager = new NotificationManager();
        this.dateTimeUtils = new DateTimeUtils();
        this.networkMonitor = new NetworkMonitor();
    }

    // Terminal formatting functions
    formatDate(dateString) {
        return this.dateTimeUtils.formatDate(dateString);
    }

    formatDateTime(dateString) {
        return this.dateTimeUtils.formatDateTime(dateString);
    }

    formatTime(timeString) {
        return this.dateTimeUtils.formatTime(timeString);
    }

    formatPriorityTerminal(priority) {
        return this.terminalFormatter.formatPriorityTerminal(priority);
    }

    formatTextForTerminal(text, maxWidth = 49) {
        return this.terminalFormatter.formatTextForTerminal(text, maxWidth);
    }

    // Status and notification functions
    updateTerminalStatus() {
        const lastUpdate = document.getElementById('last-update');
        if (lastUpdate) {
            const now = new Date();
            lastUpdate.textContent = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }
    }

    showNotification(message, type = 'info') {
        return this.notificationManager.showNotification(message, type);
    }

    showToast(message, type = 'info') {
        return this.notificationManager.showToast(message, type);
    }

    updateDateTime() {
        this.dateTimeUtils.updateDateTime();
    }

    // Network monitoring functions
    async checkNetworkConnection() {
        return await this.networkMonitor.checkNetworkConnection();
    }

    setupNetworkMonitoring() {
        return this.networkMonitor.setupNetworkMonitoring();
    }

    updateNetworkStatus() {
        return this.networkMonitor.updateNetworkStatus();
    }

    // Static utility methods for direct access
    static formatDate(dateString) {
        return DateTimeUtils.formatDate(dateString);
    }

    static formatTime(timeString) {
        return DateTimeUtils.formatTime(timeString);
    }

    static formatPriorityTerminal(priority) {
        return TerminalFormatter.formatPriorityTerminal(priority);
    }

    static formatTextForTerminal(text, maxWidth = 49) {
        return TerminalFormatter.formatTextForTerminal(text, maxWidth);
    }
}

// Export individual components for specific use cases
export { TerminalFormatter, NotificationManager, DateTimeUtils, NetworkMonitor };