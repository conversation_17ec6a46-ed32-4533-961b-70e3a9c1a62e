export const vehicleManagementTemplates = {
    vehicleListView: (vehicles) => {
        return `
            <style>
                .vehicle-list-container {
                    background-color: #000000;
                    color: #ff0000;
                    min-height: 100vh;
                    padding: 1rem;
                }
                .vehicle-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 1.5rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #ff0000;
                }
                .vehicle-title {
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.4rem;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                }
                .vehicle-controls {
                    display: flex;
                    gap: 1rem;
                }
                .vehicle-list {
                    background-color: rgba(255, 0, 0, 0.05);
                    border: 1px solid #ff0000;
                    margin-bottom: 2rem;
                }
                .vehicle-list-header {
                    display: grid;
                    grid-template-columns: 2fr 2fr 2fr 1fr 1.5fr;
                    gap: 1rem;
                    padding: 1rem;
                    background-color: rgba(255, 0, 0, 0.1);
                    border-bottom: 1px solid #ff0000;
                    font-weight: bold;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }
                .vehicle-list-item {
                    display: grid;
                    grid-template-columns: 2fr 2fr 2fr 1fr 1.5fr;
                    gap: 1rem;
                    padding: 1rem;
                    border-bottom: 1px dashed rgba(255, 0, 0, 0.3);
                    transition: background-color 0.2s;
                }
                .vehicle-list-item:hover {
                    background-color: rgba(255, 0, 0, 0.1);
                }
                .vehicle-plate {
                    font-weight: bold;
                    color: #ff0000;
                }
                .vehicle-make-model {
                    color: #cccccc;
                }
                .vehicle-owner {
                    color: #cccccc;
                }
                .vehicle-province {
                    color: #999999;
                    text-align: center;
                }
                .vehicle-actions {
                    display: flex;
                    gap: 0.5rem;
                }
                .search-container {
                    margin-bottom: 1.5rem;
                }
                .search-input {
                    width: 100%;
                    padding: 0.75rem;
                    background-color: #000000;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1rem;
                }
                .search-input::placeholder {
                    color: rgba(255, 0, 0, 0.5);
                }
                .no-vehicles {
                    text-align: center;
                    padding: 3rem;
                    color: #ff0000;
                    font-family: 'Courier New', monospace;
                    font-size: 1.2rem;
                    line-height: 1.6;
                }
                .secondary-button {
                    background-color: transparent;
                    border: 1px solid #ff0000;
                    color: #ff0000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .secondary-button:hover {
                    background-color: #ff0000;
                    color: #000000;
                }
                .action-button {
                    background-color: #ff0000;
                    border: 1px solid #ff0000;
                    color: #000000;
                    padding: 0.5rem 1rem;
                    font-family: 'Courier New', monospace;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .action-button:hover {
                    background-color: transparent;
                    color: #ff0000;
                }
                .action-button.primary {
                    font-weight: bold;
                }
            </style>
            
            <div class="vehicle-list-container">
                <div class="vehicle-header">
                    <div class="vehicle-title">VEHICLE DIRECTORY</div>
                    <div class="vehicle-controls">
                        <button class="secondary-button" data-action="back-to-records">← Back to Records</button>
                        <button class="action-button primary" data-action="add-vehicle">+ Add Vehicle</button>
                    </div>
                </div>
                
                <div class="search-container">
                    <input type="text" 
                           class="search-input" 
                           placeholder="Search vehicles by plate, make, model, or owner..."
                           data-action="search-vehicles-input">
                </div>
                
                <div class="vehicle-list" id="vehicle-list">
                    ${vehicles && vehicles.length > 0 
                        ? `<div class="vehicle-list-header">
                            <div>License Plate</div>
                            <div>Make/Model</div>
                            <div>Owner</div>
                            <div>Province</div>
                            <div>Actions</div>
                           </div>
                           ${vehicles.map(vehicle => vehicleManagementTemplates.vehicleListItem(vehicle)).join('')}`
                        : '<div class="no-vehicles">NO VEHICLES FOUND<br><br>Click "Add Vehicle" to create the first entry.</div>'
                    }
                </div>
            </div>
        `;
    },

    vehicleListItem: (vehicle) => {
        const makeModel = [
            vehicle.vehicle_make || '',
            vehicle.vehicle_model || ''
        ].filter(Boolean).join(' ') || 'Unknown Make/Model';

        return `
            <div class="vehicle-list-item" data-vehicle-id="${vehicle.id}">
                <div class="vehicle-plate">${vehicle.plate_number || 'UNKNOWN PLATE'}</div>
                <div class="vehicle-make-model">${makeModel}</div>
                <div class="vehicle-owner">${vehicle.owner_name || 'Unknown Owner'}</div>
                <div class="vehicle-province">${vehicle.province || 'N/A'}</div>
                <div class="vehicle-actions">
                    <button class="secondary-button" data-action="view-vehicle-detail" data-vehicle-id="${vehicle.id}">View</button>
                    <button class="action-button" data-action="edit-vehicle" data-vehicle-id="${vehicle.id}">Edit</button>
                </div>
            </div>
        `;
    },

};
