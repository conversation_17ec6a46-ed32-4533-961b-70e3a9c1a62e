/**
 * Vehicle Commands Factory
 * Creates modular vehicle commands following the incident management pattern
 */

import { BaseCommand } from '../../shared/base-commands.js';

export class VehicleCommandsFactory {
    constructor(commandManager, vehicleManagement) {
        this.commandManager = commandManager;
        this.vehicleManagement = vehicleManagement;
    }

    createCommands() {
        return new Map([
            ['add-vehicle', new AddVehicleCommand(this.commandManager, this.vehicleManagement)],
            ['search-vehicles', new SearchVehiclesCommand(this.commandManager, this.vehicleManagement)],
            ['list-vehicles', new ListVehiclesCommand(this.commandManager, this.vehicleManagement)],
            ['manage-vehicles', new ManageVehiclesCommand(this.commandManager, this.vehicleManagement)],
            ['view-vehicle-detail', new ViewVehicleDetailCommand(this.commandManager, this.vehicleManagement)],
            ['back-to-vehicle-list', new BackToVehicleListCommand(this.commandManager, this.vehicleManagement)],
            ['edit-vehicle', new EditVehicleCommand(this.commandManager, this.vehicleManagement)],
            ['add-vehicle-activity', new AddVehicleActivityCommand(this.commandManager, this.vehicleManagement)]
        ]);
    }
}

class AddVehicleCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            return await this.vehicleManagement.createVehicleWorkflow();
        } catch (error) {
            console.error('Error in add vehicle command:', error);
            throw error;
        }
    }
}

class SearchVehiclesCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            return await this.vehicleManagement.searchVehicleWorkflow();
        } catch (error) {
            console.error('Error in search vehicles command:', error);
            throw error;
        }
    }
}

class ListVehiclesCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            return await this.vehicleManagement.listVehicleWorkflow();
        } catch (error) {
            console.error('Error in list vehicles command:', error);
            throw error;
        }
    }
}

class ManageVehiclesCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            return await this.vehicleManagement.manageVehiclesWorkflow();
        } catch (error) {
            console.error('Error in manage vehicles command:', error);
            throw error;
        }
    }
}

class ViewVehicleDetailCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            const vehicleId = args?.vehicleId || args?.id;
            if (!vehicleId) {
                throw new Error('Vehicle ID is required');
            }
            return await this.vehicleManagement.viewVehicleWorkflow(vehicleId);
        } catch (error) {
            console.error('Error in view vehicle detail command:', error);
            throw error;
        }
    }
}

class BackToVehicleListCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            return await this.vehicleManagement.manageVehiclesWorkflow();
        } catch (error) {
            console.error('Error in back to vehicle list command:', error);
            throw error;
        }
    }
}

class EditVehicleCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            const vehicleId = args?.vehicleId || args?.id;
            if (!vehicleId) {
                throw new Error('Vehicle ID is required');
            }
            return await this.vehicleManagement.editVehicleWorkflow(vehicleId);
        } catch (error) {
            console.error('Error in edit vehicle command:', error);
            throw error;
        }
    }
}

class AddVehicleActivityCommand extends BaseCommand {
    constructor(commandManager, vehicleManagement) {
        super(commandManager);
        this.vehicleManagement = vehicleManagement;
    }

    async execute(args) {
        try {
            const vehicleId = args?.vehicleId || args?.id;
            if (!vehicleId) {
                throw new Error('Vehicle ID is required');
            }
            return await this.vehicleManagement.addVehicleActivityWorkflow(vehicleId);
        } catch (error) {
            console.error('Error in add vehicle activity command:', error);
            throw error;
        }
    }
}
