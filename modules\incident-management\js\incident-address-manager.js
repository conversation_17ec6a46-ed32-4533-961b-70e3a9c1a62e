/**
 * Incident Address Manager
 * Handles incident address search, geolocation, and Google Maps integration
 * Extracted from app.js - manages ~6 address/geolocation functions
 */

import { BaseManager } from '../../shared/base-manager.js';

export class IncidentAddressManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.googleMapsLoaded = false;
        this.autocompleteService = null;
        this.placesService = null;
        this.geocoder = null;
        this.currentMap = null;
        this.currentMarker = null;
    }

    async setupIncidentAddressSearch() {
        console.log('Setting up incident address search...');
        
        try {
            // Initialize Google Maps if not already loaded
            await this.initializeGoogleMaps();
            
            // Set up address search input
            this.setupAddressSearchInput();
            
            // Set up manual coordinates input
            this.setupManualCoordinatesInput();
            
            // Set up map functionality
            this.setupMapFunctionality();
            
            console.log('Incident address search setup complete');
            
        } catch (error) {
            console.error('Error setting up incident address search:', error);
            // Gracefully degrade if Google Maps fails to load
            this.setupFallbackAddressInput();
        }
    }

    async initializeGoogleMaps() {
        if (this.googleMapsLoaded) {
            return;
        }

        // Get Google API key from config/vault
        const apiKey = await this.getGoogleApiKey();
        if (!apiKey) {
            console.warn('No Google API key available, falling back to manual address input');
            this.setupFallbackAddressInput();
            return;
        }

        return new Promise((resolve, reject) => {
            // Check if Google Maps is already loaded
            if (window.google && window.google.maps) {
                this.googleMapsLoaded = true;
                this.initializeGoogleMapsServices();
                resolve();
                return;
            }

            // Define callback function
            window.initGoogleMaps = () => {
                this.googleMapsLoaded = true;
                this.initializeGoogleMapsServices();
                resolve();
            };

            // Load Google Maps API
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMaps`;
            script.onerror = () => reject(new Error('Failed to load Google Maps API'));
            document.head.appendChild(script);
        });
    }

    async getGoogleApiKey() {
        try {
            // Try to get from config if available
            if (window.app && window.app.config && window.app.config.getGoogleApiKey) {
                const apiKey = await window.app.config.getGoogleApiKey();
                if (apiKey) {
                    return apiKey;
                }
            }

            // Fallback: try to access vault manager directly
            if (window.app && window.app.vaultManager) {
                const apiKey = await window.app.vaultManager.getSecret('google_api_key');
                if (apiKey) {
                    return apiKey;
                }
            }

            console.warn('Unable to retrieve Google API key from vault or config');
            return null;
        } catch (error) {
            console.error('Error getting Google API key:', error);
            return null;
        }
    }

    initializeGoogleMapsServices() {
        if (window.google && window.google.maps) {
            this.autocompleteService = new google.maps.places.AutocompleteService();
            this.geocoder = new google.maps.Geocoder();
            
            // Create a dummy element for PlacesService
            const dummyElement = document.createElement('div');
            this.placesService = new google.maps.places.PlacesService(dummyElement);
            
            console.log('Google Maps services initialized');
        }
    }

    setupAddressSearchInput() {
        const addressInput = document.getElementById('incident-location');
        const suggestionsContainer = document.getElementById('address-suggestions');
        
        if (!addressInput) return;

        let searchTimeout;
        
        addressInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            const query = addressInput.value.trim();
            
            if (query.length < 3) {
                this.hideSuggestions();
                return;
            }
            
            searchTimeout = setTimeout(() => {
                this.searchAddressSuggestions(query);
            }, 300);
        });

        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!addressInput.contains(e.target) && !suggestionsContainer?.contains(e.target)) {
                this.hideSuggestions();
            }
        });

        // Handle keyboard navigation
        addressInput.addEventListener('keydown', (e) => {
            this.handleAddressInputKeyboard(e);
        });
    }

    async searchAddressSuggestions(query) {
        if (!this.autocompleteService) {
            console.log('AutocompleteService not available, using fallback');
            return;
        }

        try {
            const request = {
                input: query,
                componentRestrictions: { country: 'ca' }, // Restrict to Canada
                types: ['geocode'] // Use geocode which includes addresses and points of interest
            };

            this.autocompleteService.getPlacePredictions(request, (predictions, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
                    this.displayAddressSuggestions(predictions);
                } else {
                    this.hideSuggestions();
                }
            });

        } catch (error) {
            console.error('Error searching address suggestions:', error);
            this.hideSuggestions();
        }
    }

    displayAddressSuggestions(predictions) {
        const suggestionsContainer = document.getElementById('address-suggestions');
        if (!suggestionsContainer) return;

        const suggestionsHTML = predictions.map(prediction => `
            <div class="address-suggestion" data-place-id="${prediction.place_id}">
                <div class="suggestion-main">${prediction.structured_formatting.main_text}</div>
                <div class="suggestion-secondary">${prediction.structured_formatting.secondary_text}</div>
            </div>
        `).join('');

        suggestionsContainer.innerHTML = suggestionsHTML;
        suggestionsContainer.style.display = 'block';

        // Add click handlers
        suggestionsContainer.querySelectorAll('.address-suggestion').forEach(suggestion => {
            suggestion.addEventListener('click', () => {
                const placeId = suggestion.dataset.placeId;
                this.selectAddressSuggestion(placeId);
            });
        });
    }

    async selectAddressSuggestion(placeId) {
        if (!this.placesService) {
            console.error('PlacesService not available');
            return;
        }

        try {
            const request = { placeId: placeId };
            
            this.placesService.getDetails(request, (place, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK) {
                    this.handleIncidentAddressSelection(place);
                } else {
                    console.error('Failed to get place details:', status);
                }
            });

        } catch (error) {
            console.error('Error selecting address suggestion:', error);
        }
    }

    handleIncidentAddressSelection(place) {
        try {
            console.log('Handling incident address selection:', place);

            // Extract address components
            const addressData = this.extractAddressComponents(place);
            
            // Update form fields
            this.updateAddressFields(addressData);
            
            // Update map if available
            if (place.geometry && place.geometry.location) {
                this.updateIncidentMap(place.geometry.location, addressData.formatted_address);
            }
            
            // Hide suggestions
            this.hideSuggestions();
            
            console.log('Address selection handled successfully');

        } catch (error) {
            console.error('Error handling address selection:', error);
        }
    }

    extractAddressComponents(place) {
        const addressData = {
            formatted_address: place.formatted_address || '',
            street_number: '',
            route: '',
            locality: '',
            administrative_area_level_1: '',
            postal_code: '',
            country: '',
            coordinates: null
        };

        // Extract components from Google Maps result
        if (place.address_components) {
            place.address_components.forEach(component => {
                const types = component.types;
                
                if (types.includes('street_number')) {
                    addressData.street_number = component.long_name;
                } else if (types.includes('route')) {
                    addressData.route = component.long_name;
                } else if (types.includes('locality')) {
                    addressData.locality = component.long_name;
                } else if (types.includes('administrative_area_level_1')) {
                    addressData.administrative_area_level_1 = component.short_name;
                } else if (types.includes('postal_code')) {
                    addressData.postal_code = component.long_name;
                } else if (types.includes('country')) {
                    addressData.country = component.long_name;
                }
            });
        }

        // Extract coordinates
        if (place.geometry && place.geometry.location) {
            addressData.coordinates = {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng()
            };
        }

        return addressData;
    }

    updateAddressFields(addressData) {
        // Update main location field
        const locationInput = document.getElementById('incident-location');
        if (locationInput) {
            locationInput.value = addressData.formatted_address;
        }

        // Update detailed address fields
        const streetAddressInput = document.getElementById('street_address');
        if (streetAddressInput) {
            const streetAddress = `${addressData.street_number} ${addressData.route}`.trim();
            streetAddressInput.value = streetAddress;
        }

        const cityInput = document.getElementById('city');
        if (cityInput) {
            cityInput.value = addressData.locality;
        }

        const provinceInput = document.getElementById('province');
        if (provinceInput) {
            provinceInput.value = addressData.administrative_area_level_1;
        }

        const postalCodeInput = document.getElementById('postal_code');
        if (postalCodeInput) {
            postalCodeInput.value = addressData.postal_code;
        }

        // Update coordinates fields
        if (addressData.coordinates) {
            const coordinatesInput = document.getElementById('coordinates');
            if (coordinatesInput) {
                coordinatesInput.value = `${addressData.coordinates.lat}, ${addressData.coordinates.lng}`;
            }

            const latInput = document.getElementById('latitude');
            const lngInput = document.getElementById('longitude');
            if (latInput) latInput.value = addressData.coordinates.lat;
            if (lngInput) lngInput.value = addressData.coordinates.lng;
        }
    }

    setupManualCoordinatesInput() {
        const coordinatesInput = document.getElementById('coordinates');
        const geocodeBtn = document.getElementById('geocode-address');
        
        if (coordinatesInput) {
            coordinatesInput.addEventListener('blur', () => {
                this.validateAndUpdateCoordinates();
            });
        }

        if (geocodeBtn) {
            geocodeBtn.addEventListener('click', () => {
                this.geocodeIncidentAddress();
            });
        }
    }

    async geocodeIncidentAddress() {
        const locationInput = document.getElementById('incident-location');
        if (!locationInput || !locationInput.value.trim()) {
            this.ui.showDialog('Error', 'Please enter an address to geocode.', 'error');
            return;
        }

        if (!this.geocoder) {
            this.ui.showDialog('Error', 'Geocoding service not available.', 'error');
            return;
        }

        try {
            const address = locationInput.value.trim();
            
            // Show loading state
            const geocodeBtn = document.getElementById('geocode-address');
            if (geocodeBtn) {
                geocodeBtn.textContent = 'Geocoding...';
                geocodeBtn.disabled = true;
            }

            this.geocoder.geocode({ address: address }, (results, status) => {
                // Reset button
                if (geocodeBtn) {
                    geocodeBtn.textContent = 'Geocode Address';
                    geocodeBtn.disabled = false;
                }

                if (status === google.maps.GeocoderStatus.OK && results[0]) {
                    const place = results[0];
                    this.handleIncidentAddressSelection(place);
                    this.uiUtilities.showToast('Address geocoded successfully', 'success');
                } else {
                    console.error('Geocoding failed:', status);
                    this.ui.showDialog('Error', 'Failed to geocode address. Please check the address and try again.', 'error');
                }
            });

        } catch (error) {
            console.error('Error geocoding address:', error);
            this.ui.showDialog('Error', `Geocoding failed: ${error.message}`, 'error');
        }
    }

    validateAndUpdateCoordinates() {
        const coordinatesInput = document.getElementById('coordinates');
        if (!coordinatesInput) return;

        const coordinatesText = coordinatesInput.value.trim();
        if (!coordinatesText) return;

        // Parse coordinates (support multiple formats)
        const coordsMatch = coordinatesText.match(/(-?\d+\.?\d*),\s*(-?\d+\.?\d*)/);
        
        if (coordsMatch) {
            const lat = parseFloat(coordsMatch[1]);
            const lng = parseFloat(coordsMatch[2]);
            
            // Validate coordinate ranges
            if (lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                // Update individual lat/lng fields
                const latInput = document.getElementById('latitude');
                const lngInput = document.getElementById('longitude');
                if (latInput) latInput.value = lat;
                if (lngInput) lngInput.value = lng;
                
                // Update map
                this.updateIncidentMapFromCoordinates(lat, lng);
                
                coordinatesInput.classList.remove('invalid');
            } else {
                coordinatesInput.classList.add('invalid');
                this.ui.showDialog('Error', 'Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180.', 'error');
            }
        } else {
            coordinatesInput.classList.add('invalid');
            this.ui.showDialog('Error', 'Invalid coordinate format. Please use "latitude, longitude" format (e.g., "43.6532, -79.3832").', 'error');
        }
    }

    setupMapFunctionality() {
        // Set up map container click handler
        const mapContainer = document.getElementById('incident-map');
        if (mapContainer) {
            // Initialize map when the map tab is activated
            const mapTab = document.querySelector('[data-tab="location"]');
            if (mapTab) {
                mapTab.addEventListener('click', () => {
                    setTimeout(() => {
                        this.initializeIncidentMap();
                    }, 100);
                });
            }
        }
    }

    initializeIncidentMap() {
        const mapContainer = document.getElementById('incident-map');
        if (!mapContainer || !this.googleMapsLoaded) {
            console.log('Map container not found or Google Maps not loaded');
            return;
        }

        try {
            // Default to a central location (e.g., Toronto)
            const defaultLocation = { lat: 43.6532, lng: -79.3832 };
            
            // Check if we have coordinates from the form
            const latInput = document.getElementById('latitude');
            const lngInput = document.getElementById('longitude');
            
            let mapCenter = defaultLocation;
            if (latInput && lngInput && latInput.value && lngInput.value) {
                mapCenter = {
                    lat: parseFloat(latInput.value),
                    lng: parseFloat(lngInput.value)
                };
            }

            // Create map
            this.currentMap = new google.maps.Map(mapContainer, {
                zoom: 15,
                center: mapCenter,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });

            // Add marker if we have coordinates
            if (mapCenter !== defaultLocation) {
                this.addMapMarker(mapCenter);
            }

            // Add click handler to map
            this.currentMap.addListener('click', (event) => {
                this.handleMapClick(event);
            });

            console.log('Incident map initialized');

        } catch (error) {
            console.error('Error initializing incident map:', error);
        }
    }

    updateIncidentMap(location, address) {
        if (!this.currentMap) {
            this.initializeIncidentMap();
            if (!this.currentMap) return;
        }

        const latLng = {
            lat: typeof location.lat === 'function' ? location.lat() : location.lat,
            lng: typeof location.lng === 'function' ? location.lng() : location.lng
        };

        // Center map on location
        this.currentMap.setCenter(latLng);
        this.currentMap.setZoom(16);

        // Add or update marker
        this.addMapMarker(latLng, address);
    }

    updateIncidentMapFromCoordinates(lat, lng) {
        if (!this.currentMap) {
            this.initializeIncidentMap();
            if (!this.currentMap) return;
        }

        const latLng = { lat: lat, lng: lng };

        // Center map on coordinates
        this.currentMap.setCenter(latLng);
        this.currentMap.setZoom(16);

        // Add or update marker
        this.addMapMarker(latLng);
    }

    addMapMarker(location, title = null) {
        // Remove existing marker
        if (this.currentMarker) {
            this.currentMarker.setMap(null);
        }

        // Create new marker
        this.currentMarker = new google.maps.Marker({
            position: location,
            map: this.currentMap,
            title: title || 'Incident Location',
            draggable: true
        });

        // Add drag handler
        this.currentMarker.addListener('dragend', (event) => {
            this.handleMarkerDrag(event);
        });
    }

    handleMapClick(event) {
        const latLng = event.latLng;
        
        // Update coordinates in form
        const coordinatesInput = document.getElementById('coordinates');
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');
        
        if (coordinatesInput) {
            coordinatesInput.value = `${latLng.lat()}, ${latLng.lng()}`;
        }
        if (latInput) latInput.value = latLng.lat();
        if (lngInput) lngInput.value = latLng.lng();

        // Add marker at clicked location
        this.addMapMarker(latLng);

        // Try to reverse geocode the location
        this.reverseGeocodeLocation(latLng);
    }

    handleMarkerDrag(event) {
        const latLng = event.latLng;
        
        // Update coordinates in form
        const coordinatesInput = document.getElementById('coordinates');
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');
        
        if (coordinatesInput) {
            coordinatesInput.value = `${latLng.lat()}, ${latLng.lng()}`;
        }
        if (latInput) latInput.value = latLng.lat();
        if (lngInput) lngInput.value = latLng.lng();

        // Try to reverse geocode the new location
        this.reverseGeocodeLocation(latLng);
    }

    async reverseGeocodeLocation(latLng) {
        if (!this.geocoder) return;

        try {
            this.geocoder.geocode({ location: latLng }, (results, status) => {
                if (status === google.maps.GeocoderStatus.OK && results[0]) {
                    const place = results[0];
                    
                    // Update location field with formatted address
                    const locationInput = document.getElementById('incident-location');
                    if (locationInput) {
                        locationInput.value = place.formatted_address;
                    }
                    
                    // Extract and update address components
                    const addressData = this.extractAddressComponents(place);
                    this.updateAddressFields(addressData);
                }
            });

        } catch (error) {
            console.error('Error reverse geocoding location:', error);
        }
    }

    hideSuggestions() {
        const suggestionsContainer = document.getElementById('address-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    handleAddressInputKeyboard(e) {
        const suggestionsContainer = document.getElementById('address-suggestions');
        if (!suggestionsContainer || suggestionsContainer.style.display === 'none') return;

        const suggestions = suggestionsContainer.querySelectorAll('.address-suggestion');
        let currentSelected = suggestionsContainer.querySelector('.address-suggestion.selected');
        let selectedIndex = currentSelected ? Array.from(suggestions).indexOf(currentSelected) : -1;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, suggestions.length - 1);
                this.updateSelectedSuggestion(suggestions, selectedIndex);
                break;

            case 'ArrowUp':
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, 0);
                this.updateSelectedSuggestion(suggestions, selectedIndex);
                break;

            case 'Enter':
                e.preventDefault();
                if (currentSelected) {
                    const placeId = currentSelected.dataset.placeId;
                    this.selectAddressSuggestion(placeId);
                }
                break;

            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }

    updateSelectedSuggestion(suggestions, selectedIndex) {
        suggestions.forEach((suggestion, index) => {
            suggestion.classList.toggle('selected', index === selectedIndex);
        });
    }

    setupFallbackAddressInput() {
        console.log('Setting up fallback address input (Google Maps not available)');
        
        // Hide Google Maps specific elements
        const mapContainer = document.getElementById('incident-map');
        const geocodeBtn = document.getElementById('geocode-address');
        const suggestionsContainer = document.getElementById('address-suggestions');
        
        if (mapContainer) mapContainer.style.display = 'none';
        if (geocodeBtn) geocodeBtn.style.display = 'none';
        if (suggestionsContainer) suggestionsContainer.style.display = 'none';
        
        // Show manual address entry message
        const locationInput = document.getElementById('incident-location');
        if (locationInput) {
            locationInput.placeholder = 'Enter incident location (address or description)';
            locationInput.title = 'Google Maps integration unavailable - please enter address manually';
        }
    }

    // Method to get current location data
    getCurrentLocationData() {
        const locationInput = document.getElementById('incident-location');
        const streetAddressInput = document.getElementById('street_address');
        const cityInput = document.getElementById('city');
        const provinceInput = document.getElementById('province');
        const postalCodeInput = document.getElementById('postal_code');
        const coordinatesInput = document.getElementById('coordinates');
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');

        return {
            location: locationInput?.value || '',
            street_address: streetAddressInput?.value || '',
            city: cityInput?.value || '',
            province: provinceInput?.value || '',
            postal_code: postalCodeInput?.value || '',
            coordinates: coordinatesInput?.value || '',
            latitude: latInput?.value || '',
            longitude: lngInput?.value || ''
        };
    }

    // Method to populate location fields (for edit forms)
    populateLocationFields(locationData) {
        const locationInput = document.getElementById('incident-location');
        const streetAddressInput = document.getElementById('street_address');
        const cityInput = document.getElementById('city');
        const provinceInput = document.getElementById('province');
        const postalCodeInput = document.getElementById('postal_code');
        const coordinatesInput = document.getElementById('coordinates');
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');

        if (locationInput) locationInput.value = locationData.location || '';
        if (streetAddressInput) streetAddressInput.value = locationData.street_address || '';
        if (cityInput) cityInput.value = locationData.city || '';
        if (provinceInput) provinceInput.value = locationData.province || '';
        if (postalCodeInput) postalCodeInput.value = locationData.postal_code || '';
        if (coordinatesInput) coordinatesInput.value = locationData.coordinates || '';
        if (latInput) latInput.value = locationData.latitude || '';
        if (lngInput) lngInput.value = locationData.longitude || '';

        // Update map if coordinates are available
        if (locationData.latitude && locationData.longitude) {
            const lat = parseFloat(locationData.latitude);
            const lng = parseFloat(locationData.longitude);
            if (!isNaN(lat) && !isNaN(lng)) {
                this.updateIncidentMapFromCoordinates(lat, lng);
            }
        }
    }
}