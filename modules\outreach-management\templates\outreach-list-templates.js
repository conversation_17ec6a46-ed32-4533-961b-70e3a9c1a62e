// Outreach Transaction List and Split-View Templates
// Split-view layout similar to incidents (list left, details right)

export const outreachListTemplates = {
    // Main outreach dashboard with split-view layout
    outreachDashboard: () => `
        <div class="outreach-container">
            <!-- Header -->
            <div class="outreach-header">
                <h2>📦 OUTREACH MANAGEMENT</h2>
                <div class="outreach-status">
                    <span id="outreach-count-display">0</span> Recent Transactions |
                    Last Updated: <span id="outreach-last-update">--:--:--</span>
                </div>
            </div>

            <!-- Main Content - Split View Layout -->
            <div class="outreach-main">
                <!-- LEFT SIDE: Transaction List -->
                <div class="outreach-list-section">
                    <div class="outreach-list-header">
                        <div class="list-filters">
                            <select id="outreach-date-filter">
                                <option value="">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                            </select>
                            <select id="outreach-staff-filter">
                                <option value="">All Staff</option>
                                <!-- Populated dynamically -->
                            </select>
                            <input type="text" id="outreach-search-input" placeholder="Search by person name, location...">
                        </div>
                    </div>
                    <div class="outreach-list" id="outreach-list">
                        <div class="loading">Loading outreach transactions...</div>
                    </div>
                </div>

                <!-- RIGHT SIDE: Transaction Details -->
                <div class="outreach-details-section" id="outreach-details">
                    <div class="no-selection">
                        <h3>Select a Transaction</h3>
                        <p>Choose a transaction from the list to view details.</p>
                        <div class="outreach-quick-stats">
                            <div class="stat-card">
                                <div class="stat-number" id="today-transactions">0</div>
                                <div class="stat-label">Today</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="week-transactions">0</div>
                                <div class="stat-label">This Week</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="total-items-distributed">0</div>
                                <div class="stat-label">Items Distributed</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="outreach-actions">
                <button class="action-button primary" onclick="app.showOutreachTransactionForm()">
                    📦 New Transaction
                </button>
                <button class="action-button" id="refresh-outreach" onclick="app.refreshOutreachList()">
                    🔄 Refresh
                </button>
                <button class="action-button" id="export-outreach" onclick="app.exportOutreachData()">
                    📊 Export
                </button>
                <button class="action-button" id="clear-outreach-search" onclick="app.clearOutreachSearch()">
                    🧹 Clear Search
                </button>
            </div>
        </div>
    `,

    // Individual transaction list item template
    createTransactionListItem: (transaction) => `
        <div class="outreach-item ${transaction.id === window.selectedOutreachId ? 'selected' : ''}" 
             onclick="app.selectOutreachTransaction('${transaction.id}')">
            <div class="outreach-item-header">
                <div class="transaction-time">${transaction.formatted_time || transaction.created_at}</div>
                <div class="transaction-id">#${String(transaction.id).substring(0, 8)}</div>
            </div>
            <div class="outreach-item-content">
                <div class="person-info">
                    <span class="person-name">👤 ${transaction.person_name}</span>
                    <span class="person-id">${transaction.person_id ? String(transaction.person_id).substring(0, 8) : 'N/A'}</span>
                </div>
                <div class="transaction-summary">
                    <span class="items-count">📦 ${transaction.total_items || transaction.items?.length || 0} item(s)</span>
                    <span class="location">📍 ${transaction.location || 'No location'}</span>
                </div>
                <div class="staff-info">
                    <span class="staff-member">👨‍💼 ${transaction.staff_member || 'Unknown'}</span>
                </div>
            </div>
            <div class="outreach-item-footer">
                <span class="transaction-status">Completed</span>
            </div>
        </div>
    `,

    // Empty state when no transactions found
    createNoTransactionsFound: () => `
        <div class="no-results">
            <div class="no-results-icon">📦</div>
            <h3>No Transactions Found</h3>
            <p>No outreach transactions match the current filters.</p>
            <button class="action-button primary" onclick="app.showOutreachTransactionForm()">
                Create First Transaction
            </button>
        </div>
    `,

    // Loading state for transaction list
    createOutreachListLoading: () => `
        <div class="loading">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading outreach transactions...</div>
        </div>
    `,

    // Error state for transaction list
    createOutreachListError: () => `
        <div class="error">
            <div class="error-icon">⚠️</div>
            <h3>Error Loading Transactions</h3>
            <p>Unable to load outreach transactions. Please try again.</p>
            <button class="action-button" onclick="app.refreshOutreachList()">
                Retry
            </button>
        </div>
    `,

    // Quick stats summary for right panel when no selection
    createQuickStats: (stats) => `
        <div class="outreach-quick-stats">
            <div class="stat-card">
                <div class="stat-number">${stats.today || 0}</div>
                <div class="stat-label">Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.week || 0}</div>
                <div class="stat-label">This Week</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.month || 0}</div>
                <div class="stat-label">This Month</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalItems || 0}</div>
                <div class="stat-label">Total Items</div>
            </div>
        </div>
    `
};