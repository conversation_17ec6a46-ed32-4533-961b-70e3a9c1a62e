// Timeline Templates
// Displays ordered events and provides Add Event actions

export const timelineTemplates = {
    timelineView(data) {
        const { episode, events } = data;
        
        return `
            <div class="justice-timeline">
                <div class="timeline-header">
                    <h4>Episode Timeline</h4>
                    <div class="timeline-actions">
                        <button class="secondary-button" data-action="refresh-timeline">
                            <span class="button-icon">🔄</span>
                            Refresh
                        </button>
                    </div>
                </div>
                
                <div class="add-event-section">
                    <h5>Add Event</h5>
                    <div class="add-event-buttons">
                        <button class="event-btn" data-action="je:add-bail-hearing" data-episode-id="${episode.id}">
                            <span class="event-icon">⚖️</span>
                            Bail Hearing
                        </button>
                        <button class="event-btn" data-action="je:add-transfer" data-episode-id="${episode.id}">
                            <span class="event-icon">🏢</span>
                            Transfer
                        </button>
                        <button class="event-btn" data-action="je:add-release-conditions" data-episode-id="${episode.id}">
                            <span class="event-icon">🚪</span>
                            Release/Conditions
                        </button>
                        <button class="event-btn" data-action="je:add-court-appearance" data-episode-id="${episode.id}">
                            <span class="event-icon">🏛️</span>
                            Court Appearance
                        </button>
                        <button class="event-btn" data-action="je:add-sentence" data-episode-id="${episode.id}">
                            <span class="event-icon">⚖️</span>
                            Sentence
                        </button>
                        <button class="event-btn" data-action="je:add-warrant" data-episode-id="${episode.id}">
                            <span class="event-icon">📜</span>
                            Warrant
                        </button>
                    </div>
                </div>
                
                <div class="timeline-events">
                    ${events.length > 0 ? timelineTemplates.renderEvents(events) : timelineTemplates.renderNoEvents()}
                </div>
            </div>
        `;
    },
    
    renderEvents(events) {
        return `
            <div class="events-list">
                ${events.map(event => timelineTemplates.renderEvent(event)).join('')}
            </div>
        `;
    },
    
    renderEvent(event) {
        return `
            <div class="timeline-event" data-event-id="${event.id}">
                <div class="event-header">
                    <div class="event-icon">
                        ${timelineTemplates.getEventIcon(event.event_type)}
                    </div>
                    <div class="event-info">
                        <div class="event-title">
                            ${timelineTemplates.getEventTypeLabel(event.event_type)}
                        </div>
                        <div class="event-datetime">
                            ${timelineTemplates.formatEventDateTime(event.event_dt)}
                        </div>
                    </div>
                    <div class="event-toggle">▼</div>
                </div>
                
                <div class="event-details">
                    ${timelineTemplates.renderEventDetails(event)}
                </div>
            </div>
        `;
    },
    
    renderEventDetails(event) {
        try {
            const payload = typeof event.payload === 'string' 
                ? JSON.parse(event.payload) 
                : event.payload;
            
            switch (event.event_type) {
                case 'BAIL_HEARING':
                    return timelineTemplates.renderBailHearingDetails(payload);
                case 'TRANSFER_TO_FACILITY':
                    return timelineTemplates.renderTransferDetails(payload);
                case 'RELEASE_ORDER':
                case 'CONDITIONS_SET':
                case 'PROBATION_ORDER':
                    return timelineTemplates.renderConditionsDetails(payload);
                case 'COURT_APPEARANCE':
                    return timelineTemplates.renderCourtAppearanceDetails(payload);
                case 'SENTENCE':
                    return timelineTemplates.renderSentenceDetails(payload);
                case 'WARRANT_ISSUED':
                case 'WARRANT_EXECUTED':
                    return timelineTemplates.renderWarrantDetails(payload, event.event_type);
                default:
                    return timelineTemplates.renderGenericDetails(payload);
            }
        } catch (error) {
            return '<div class="event-detail-error">Error displaying event details</div>';
        }
    },
    
    renderBailHearingDetails(payload) {
        return `
            <div class="event-details-content">
                <div class="detail-row">
                    <span class="detail-label">Outcome:</span>
                    <span class="detail-value outcome-${payload.outcome?.toLowerCase()}">${payload.outcome}</span>
                </div>
                ${payload.next_date ? `
                    <div class="detail-row">
                        <span class="detail-label">Next Date:</span>
                        <span class="detail-value">${timelineTemplates.formatDateTime(payload.next_date)}</span>
                    </div>
                ` : ''}
                ${payload.notes ? `
                    <div class="detail-row">
                        <span class="detail-label">Notes:</span>
                        <span class="detail-value">${payload.notes}</span>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderTransferDetails(payload) {
        return `
            <div class="event-details-content">
                <div class="detail-row">
                    <span class="detail-label">Facility:</span>
                    <span class="detail-value">${payload.facility_name || payload.facility_id}</span>
                </div>
                ${payload.reason ? `
                    <div class="detail-row">
                        <span class="detail-label">Reason:</span>
                        <span class="detail-value">${payload.reason}</span>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderConditionsDetails(payload) {
        return `
            <div class="event-details-content">
                ${payload.pack_id ? `
                    <div class="detail-row">
                        <span class="detail-label">Condition Pack:</span>
                        <span class="detail-value">${payload.pack_name || payload.pack_id}</span>
                    </div>
                ` : ''}
                ${payload.replace ? `
                    <div class="detail-row">
                        <span class="detail-label">Replace Previous:</span>
                        <span class="detail-value">Yes</span>
                    </div>
                ` : ''}
                ${payload.start_dt ? `
                    <div class="detail-row">
                        <span class="detail-label">Start Date:</span>
                        <span class="detail-value">${timelineTemplates.formatDateTime(payload.start_dt)}</span>
                    </div>
                ` : ''}
                ${payload.end_dt ? `
                    <div class="detail-row">
                        <span class="detail-label">End Date:</span>
                        <span class="detail-value">${timelineTemplates.formatDateTime(payload.end_dt)}</span>
                    </div>
                ` : ''}
                ${payload.conditions && payload.conditions.length > 0 ? `
                    <div class="detail-row">
                        <span class="detail-label">Conditions:</span>
                        <div class="detail-value">
                            <ul class="conditions-list">
                                ${payload.conditions.map(cond => `
                                    <li>${cond.label || cond.type}</li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderCourtAppearanceDetails(payload) {
        return `
            <div class="event-details-content">
                <div class="detail-row">
                    <span class="detail-label">Court:</span>
                    <span class="detail-value">${payload.court_name}</span>
                </div>
                ${payload.address ? `
                    <div class="detail-row">
                        <span class="detail-label">Address:</span>
                        <span class="detail-value">${payload.address}</span>
                    </div>
                ` : ''}
                <div class="detail-row">
                    <span class="detail-label">Type:</span>
                    <span class="detail-value">${payload.appearance_type}</span>
                </div>
                ${payload.outcome ? `
                    <div class="detail-row">
                        <span class="detail-label">Outcome:</span>
                        <span class="detail-value">${payload.outcome}</span>
                    </div>
                ` : ''}
                ${payload.next_date ? `
                    <div class="detail-row">
                        <span class="detail-label">Next Date:</span>
                        <span class="detail-value">${timelineTemplates.formatDateTime(payload.next_date)}</span>
                    </div>
                ` : ''}
                ${payload.notes ? `
                    <div class="detail-row">
                        <span class="detail-label">Notes:</span>
                        <span class="detail-value">${payload.notes}</span>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderSentenceDetails(payload) {
        return `
            <div class="event-details-content">
                <div class="detail-row">
                    <span class="detail-label">Mode:</span>
                    <span class="detail-value">${payload.mode}</span>
                </div>
                ${payload.length_days ? `
                    <div class="detail-row">
                        <span class="detail-label">Length:</span>
                        <span class="detail-value">${payload.length_days} days</span>
                    </div>
                ` : ''}
                ${payload.credit_days ? `
                    <div class="detail-row">
                        <span class="detail-label">Credit:</span>
                        <span class="detail-value">${payload.credit_days} days</span>
                    </div>
                ` : ''}
                ${payload.conditions ? `
                    <div class="detail-row">
                        <span class="detail-label">Conditions:</span>
                        <span class="detail-value">${payload.conditions}</span>
                    </div>
                ` : ''}
                ${payload.notes ? `
                    <div class="detail-row">
                        <span class="detail-label">Notes:</span>
                        <span class="detail-value">${payload.notes}</span>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderWarrantDetails(payload, eventType) {
        const isExecution = eventType === 'WARRANT_EXECUTED';
        
        return `
            <div class="event-details-content">
                <div class="detail-row">
                    <span class="detail-label">Type:</span>
                    <span class="detail-value">${payload.type}</span>
                </div>
                ${isExecution && payload.executing_officer ? `
                    <div class="detail-row">
                        <span class="detail-label">Executing Officer:</span>
                        <span class="detail-value">${payload.executing_officer}</span>
                    </div>
                ` : ''}
                ${isExecution && payload.location ? `
                    <div class="detail-row">
                        <span class="detail-label">Location:</span>
                        <span class="detail-value">${payload.location}</span>
                    </div>
                ` : ''}
                ${!isExecution && payload.issuing_court ? `
                    <div class="detail-row">
                        <span class="detail-label">Issuing Court:</span>
                        <span class="detail-value">${payload.issuing_court}</span>
                    </div>
                ` : ''}
                ${!isExecution && payload.reason ? `
                    <div class="detail-row">
                        <span class="detail-label">Reason:</span>
                        <span class="detail-value">${payload.reason}</span>
                    </div>
                ` : ''}
                ${payload.notes ? `
                    <div class="detail-row">
                        <span class="detail-label">Notes:</span>
                        <span class="detail-value">${payload.notes}</span>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    renderGenericDetails(payload) {
        const keys = Object.keys(payload);
        if (keys.length === 0) {
            return '<div class="event-details-content">No additional details</div>';
        }
        
        return `
            <div class="event-details-content">
                ${keys.map(key => `
                    <div class="detail-row">
                        <span class="detail-label">${timelineTemplates.formatKey(key)}:</span>
                        <span class="detail-value">${payload[key]}</span>
                    </div>
                `).join('')}
            </div>
        `;
    },
    
    renderNoEvents() {
        return `
            <div class="no-events">
                <p>No events recorded for this episode yet.</p>
                <p>Use the buttons above to add events to the timeline.</p>
            </div>
        `;
    },
    
    errorView(message) {
        return `
            <div class="timeline-error">
                <div class="error-message">
                    Failed to load timeline: ${message}
                </div>
                <button class="secondary-button" data-action="refresh-timeline">
                    Retry
                </button>
            </div>
        `;
    },
    
    getEventIcon(eventType) {
        const icons = {
            'ARREST': '👮',
            'SUMMONS': '📋',
            'BAIL_HEARING': '⚖️',
            'TRANSFER_TO_FACILITY': '🏢',
            'RELEASE_ORDER': '🚪',
            'CONDITIONS_SET': '📝',
            'PROBATION_ORDER': '📋',
            'COURT_APPEARANCE': '🏛️',
            'SENTENCE': '⚖️',
            'WARRANT_ISSUED': '📜',
            'WARRANT_EXECUTED': '👮'
        };
        
        return icons[eventType] || '📅';
    },
    
    getEventTypeLabel(eventType) {
        const labels = {
            'ARREST': 'Arrest',
            'SUMMONS': 'Summons',
            'BAIL_HEARING': 'Bail Hearing',
            'TRANSFER_TO_FACILITY': 'Transfer to Facility',
            'RELEASE_ORDER': 'Release Order',
            'CONDITIONS_SET': 'Conditions Set',
            'PROBATION_ORDER': 'Probation Order',
            'COURT_APPEARANCE': 'Court Appearance',
            'SENTENCE': 'Sentence',
            'WARRANT_ISSUED': 'Warrant Issued',
            'WARRANT_EXECUTED': 'Warrant Executed'
        };
        
        return labels[eventType] || eventType;
    },
    
    formatEventDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    formatKey(key) {
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
};
