// Justice API Layer
// Handles all Supabase operations for justice schema with schema-qualified table access

export class JusticeAPI {
    constructor(dataManager) {
        this.data = dataManager;
    }
    
    async getSupabaseClient() {
        return await this.data.getSupabaseClient();
    }
    
    /**
     * Create a new justice episode
     * @param {Object} episodeData - Episode creation data
     * @returns {Object} Created episode
     */
    async createEpisode(episodeData) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('justice_episode')
            .insert({
                id: episodeData.id || crypto.randomUUID(),
                person_id: episodeData.person_id,
                origin: episodeData.origin,
                origin_dt: episodeData.origin_dt,
                origin_agency: episodeData.origin_agency,
                jurisdiction: episodeData.jurisdiction,
                created_by: (await supabase.auth.getUser()).data.user?.id
            })
            .select()
            .single();
            
        if (error) throw error;
        
        // If charges are provided, create them
        if (episodeData.charges && episodeData.charges.length > 0) {
            await this.createCharges(data.id, episodeData.charges);
        }
        
        return data;
    }
    
    /**
     * Get a specific episode with status information
     * @param {string} episodeId - Episode ID
     * @returns {Object} Episode with status
     */
    async getEpisode(episodeId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('v_je_status')
            .select('*')
            .eq('je_id', episodeId)
            .single();
            
        if (error) throw error;
        return data;
    }
    
    /**
     * List episodes for a person with status information
     * @param {string} personId - Person ID
     * @returns {Array} Episodes with status
     */
    async listEpisodesByPerson(personId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('v_je_status')
            .select('*')
            .eq('person_id', personId)
            .order('origin_dt', { ascending: false });
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Add an event to a justice episode
     * @param {string} episodeId - Episode ID
     * @param {string} eventType - Event type
     * @param {string} eventDt - Event datetime (ISO string)
     * @param {Object} payload - Event payload
     * @returns {Object} Created event
     */
    async addEvent(episodeId, eventType, eventDt, payload = {}) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_event')
            .insert({
                id: crypto.randomUUID(),
                je_id: episodeId,
                event_type: eventType,
                event_dt: eventDt,
                payload: payload,
                entered_by: (await supabase.auth.getUser()).data.user?.id
            })
            .select()
            .single();
            
        if (error) throw error;
        return data;
    }
    
    /**
     * Get events for an episode
     * @param {string} episodeId - Episode ID
     * @returns {Array} Events ordered by datetime
     */
    async getEpisodeEvents(episodeId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_event')
            .select('*')
            .eq('je_id', episodeId)
            .order('event_dt', { ascending: true });
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Create charges for an episode
     * @param {string} episodeId - Episode ID
     * @param {Array} charges - Array of charge objects
     * @returns {Array} Created charges
     */
    async createCharges(episodeId, charges) {
        const supabase = await this.getSupabaseClient();
        
        const chargeData = charges.map(charge => ({
            id: crypto.randomUUID(),
            je_id: episodeId,
            code: charge.code,
            label: charge.label,
            severity: charge.severity || 'OTHER'
        }));
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_charge')
            .insert(chargeData)
            .select();
            
        if (error) throw error;
        return data;
    }
    
    /**
     * Get charges for an episode
     * @param {string} episodeId - Episode ID
     * @returns {Array} Charges
     */
    async getEpisodeCharges(episodeId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_charge')
            .select('*')
            .eq('je_id', episodeId)
            .order('created_at', { ascending: true });
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Update charge status
     * @param {string} chargeId - Charge ID
     * @param {string} status - New status
     * @returns {Object} Updated charge
     */
    async updateChargeStatus(chargeId, status) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_charge')
            .update({ status })
            .eq('id', chargeId)
            .select()
            .single();
            
        if (error) throw error;
        return data;
    }
    
    /**
     * List available condition packs
     * @returns {Array} Condition packs
     */
    async listConditionPacks() {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('condition_pack')
            .select('*')
            .eq('active', true)
            .order('name');
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Get condition pack items
     * @param {string} packId - Pack ID
     * @returns {Array} Pack items
     */
    async listPackItems(packId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('condition_pack_item')
            .select('*')
            .eq('pack_id', packId)
            .order('sort_order');
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * List available facilities
     * @returns {Array} Facilities
     */
    async listFacilities() {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('facility')
            .select('*')
            .eq('active', true)
            .order('name');
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Get active conditions for an episode
     * @param {string} episodeId - Episode ID
     * @returns {Array} Active conditions
     */
    async getActiveConditions(episodeId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_condition')
            .select('*')
            .eq('je_id', episodeId)
            .is('end_dt', null)
            .order('start_dt', { ascending: false });
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Get contacts for an episode
     * @param {string} episodeId - Episode ID
     * @returns {Array} Contacts
     */
    async getEpisodeContacts(episodeId) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_contact')
            .select('*')
            .eq('je_id', episodeId)
            .eq('active', true)
            .order('role', { ascending: true });
            
        if (error) throw error;
        return data || [];
    }
    
    /**
     * Add a contact to an episode
     * @param {Object} contactData - Contact data
     * @returns {Object} Created contact
     */
    async addContact(contactData) {
        const supabase = await this.getSupabaseClient();
        
        const { data, error } = await supabase
            .schema('justice')
            .from('je_contact')
            .insert({
                id: crypto.randomUUID(),
                ...contactData
            })
            .select()
            .single();
            
        if (error) throw error;
        return data;
    }
}
