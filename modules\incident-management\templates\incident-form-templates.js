// Incident Form Templates
// Extracted from app.js for better maintainability

export const incidentFormTemplates = {
    // Comprehensive tabbed incident creation form (extracted from commands.js)
    createIncidentForm: () => `
        <div class="incident-creation-container">
            <div class="incident-form-wrapper">
                <form id="comprehensive-incident-form" class="comprehensive-incident-form">
                    <div class="incident-form-tabs">
                        <button type="button" class="action-btn back-to-incidents incident-tab-back">[BACK]</button>
                        <span class="incident-form-title">&gt; CREATE NEW INCIDENT</span>
                        <button type="button" class="incident-form-tab active" data-tab="basic">BASIC INFO</button>
                        <button type="button" class="incident-form-tab" data-tab="location">LOCATION</button>
                        <button type="button" class="incident-form-tab" data-tab="people">PEOPLE</button>
                        <button type="button" class="incident-form-tab" data-tab="property">PROPERTY</button>
                        <button type="button" class="incident-form-tab" data-tab="services">SERVICES</button>
                        <button type="button" class="incident-form-tab" data-tab="agencies">AGENCIES</button>
                        <button type="button" class="incident-form-tab" data-tab="safety">SAFETY</button>
                        <button type="button" class="incident-form-tab" data-tab="narrative">EVENTS/NARRATIVE</button>
                        <button type="button" class="incident-form-tab" data-tab="followup">FOLLOW-UP</button>
                    </div>
                    
                    <div class="incident-form-content">
                        <div class="form-content-scroll">
                            ${incidentFormTemplates.generateBasicTab()}
                            ${incidentFormTemplates.generateLocationTab()}
                            ${incidentFormTemplates.generatePeopleTab()}
                            ${incidentFormTemplates.generatePropertyTab()}
                            ${incidentFormTemplates.generateServicesTab()}
                            ${incidentFormTemplates.generateAgenciesTab()}
                            ${incidentFormTemplates.generateSafetyTab()}
                            ${incidentFormTemplates.generateNarrativeTab()}
                            ${incidentFormTemplates.generateFollowupTab()}
                        </div>
                    </div>
                    
                    <div class="incident-form-actions">
                        <button type="submit" class="primary-button" id="submit-comprehensive-incident">CREATE INCIDENT</button>
                        <button type="button" class="action-btn cancel-incident-form">CANCEL</button>
                        <button type="button" id="save-draft" class="action-btn">SAVE DRAFT</button>
                    </div>
                </form>
            </div>
        </div>
    `,

    // Generate individual tab content (extracted from commands.js)
    generateBasicTab: () => `
        <div class="tab-content active" data-tab="basic">
            <div class="form-field">
                <label>
                    <input type="checkbox" name="use_current_time" checked> Use current date and time?
                </label>
            </div>
            <div class="form-field" id="datetime-group" style="display: none;">
                <label for="incident_date">Incident Date:</label>
                <input type="date" name="incident_date" id="incident_date">
            </div>
            <div class="form-field" id="datetime-group-time" style="display: none;">
                <label for="incident_time">Incident Time:</label>
                <input type="time" name="incident_time" id="incident_time">
            </div>
            <div class="form-field">
                <label for="incident_type">Incident Type *:</label>
                <select name="incident_type" id="incident_type" required>
                    <option value="">Select type...</option>
                    <option value="Assault">Assault</option>
                    <option value="Theft">Theft</option>
                    <option value="Vandalism">Vandalism</option>
                    <option value="Drug Activity">Drug Activity</option>
                    <option value="Mental Health Crisis">Mental Health Crisis</option>
                    <option value="Medical Emergency">Medical Emergency</option>
                    <option value="Disturbance">Disturbance</option>
                    <option value="Trespassing">Trespassing</option>
                    <option value="Welfare Check">Welfare Check</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            <div class="form-field">
                <label for="priority">Priority *:</label>
                <select name="priority" id="priority" required>
                    <option value="low">Low</option>
                    <option value="medium" selected>Medium</option>
                    <option value="high">High</option>
                </select>
            </div>
            <div class="form-field">
                <label for="assigned_ranger">Assigned Ranger:</label>
                <input type="text" name="assigned_ranger" id="assigned_ranger" placeholder="Ranger assigned to this incident">
            </div>
            <div class="form-field">
                <label for="description">Summary *:</label>
                <textarea name="description" id="description" required placeholder="Brief summary of the incident" rows="3"></textarea>
            </div>
        </div>
    `,

    generateLocationTab: () => `
        <div class="tab-content" data-tab="location">
            <div class="form-field">
                <label for="incident-location">Address *:</label>
                <div class="address-search-container">
                    <input type="text" name="location" id="incident-location" placeholder="Start typing an address..." autocomplete="off" required>
                    <div id="address-suggestions" class="address-suggestions" style="display: none;"></div>
                </div>
                <small class="field-help">Start typing to see address suggestions from Google Maps</small>
            </div>

            <div class="form-field">
                <label for="coordinates">GPS Coordinates:</label>
                <input type="text" name="coordinates" id="coordinates" placeholder="Latitude, Longitude (e.g., 49.2827, -123.1207)" readonly>
                <small class="field-help">Coordinates will be automatically filled when you select an address</small>
            </div>

            <div class="form-field">
                <label for="location_notes">Location Notes:</label>
                <textarea name="location_notes" id="location_notes" placeholder="Additional details about the location (apartment number, building details, etc.)" rows="3"></textarea>
            </div>

            <!-- Address component fields - hidden by default, shown if Google Maps fails -->
            <div class="address-components" style="display: none;">
                <div class="form-field">
                    <label for="street_address">Street Address:</label>
                    <input type="text" name="street_address" id="street_address" placeholder="123 Main Street">
                </div>
                <div class="form-row">
                    <div class="form-field half-width">
                        <label for="city">City:</label>
                        <input type="text" name="city" id="city" placeholder="City">
                    </div>
                    <div class="form-field quarter-width">
                        <label for="province">Province:</label>
                        <input type="text" name="province" id="province" value="Ontario" placeholder="ON">
                    </div>
                    <div class="form-field quarter-width">
                        <label for="postal_code">Postal Code:</label>
                        <input type="text" name="postal_code" id="postal_code" placeholder="K1A 0A6">
                    </div>
                </div>
            </div>

            <!-- Hidden fields for backend storage -->
            <input type="hidden" name="country" id="country" value="Canada">
            <input type="hidden" name="latitude" id="latitude">
            <input type="hidden" name="longitude" id="longitude">
        </div>
    `,

    generatePeopleTab: () => `
        <div class="tab-content" data-tab="people">
            <div class="people-management-section">
                <div class="form-section-header">
                    <h4>&gt; PEOPLE INVOLVED</h4>
                </div>
                
                <div class="people-search-container">
                    <div class="form-field">
                        <label for="person-search-input">Search for Person:</label>
                        <div class="search-input-group">
                            <input type="text" id="person-search-input" placeholder="Enter name to search existing records..." class="text-input">
                            <button type="button" id="person-search-btn" class="action-btn">[SEARCH]</button>
                            <button type="button" id="person-search-clear" class="action-btn secondary">[CLEAR]</button>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="add-unknown-person-btn" class="action-btn secondary">[ADD UNKNOWN PERSON]</button>
                    </div>
                </div>

                <div class="people-search-section" id="people-search-section">
                    <div class="people-search-results" id="person-search-results">
                        <div class="search-hint">Enter at least 2 characters to search</div>
                    </div>
                </div>
                
                <div class="involved-people-display" id="involved-people-list">
                    <div class="results-header">
                        <h5>&gt; PEOPLE ADDED TO INCIDENT</h5>
                    </div>
                    <div class="people-list-container" id="people-list-container">
                        <div class="no-people-message terminal-text">
                            [NO PEOPLE ADDED TO INCIDENT]<br>
                            Use search above to add known individuals or click "ADD UNKNOWN PERSON"
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    generatePropertyTab: () => `
        <div class="tab-content" data-tab="property">
            <div class="form-field">
                <label>
                    <input type="checkbox" name="property_recovered" id="property_recovered"> Property Recovered/Found?
                </label>
            </div>
            <div id="property-details" style="display: none;">
                <div class="form-field">
                    <label for="property_type">Property Type *:</label>
                    <select name="property_type" id="property_type">
                        <option value="">Select type...</option>
                        <option value="bicycle">Bicycle</option>
                        <option value="shopping_cart">Shopping Cart</option>
                        <option value="electronics">Electronics</option>
                        <option value="clothing">Clothing</option>
                        <option value="personal_items">Personal Items</option>
                        <option value="tools">Tools</option>
                        <option value="vehicle_parts">Vehicle Parts</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="property_description">Description *:</label>
                    <textarea name="property_description" id="property_description" placeholder="Detailed description of the property" rows="3"></textarea>
                </div>
                <div class="form-field">
                    <label for="recovery_method">How was it recovered?:</label>
                    <select name="recovery_method" id="recovery_method">
                        <option value="found_during_incident">Found during incident</option>
                        <option value="confiscated">Confiscated</option>
                        <option value="abandoned">Abandoned</option>
                        <option value="turned_in_by_citizen">Turned in by citizen</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="disposition_type">What was done with the property?:</label>
                    <select name="disposition_type" id="disposition_type">
                        <option value="">Select disposition...</option>
                        <option value="turned_over_to_police">Turned over to police</option>
                        <option value="left_where_found">Left where found</option>
                        <option value="returned_to_owner">Returned to owner</option>
                        <option value="stored_at_facility">Stored at facility</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="property_brand">Brand/Make:</label>
                    <input type="text" name="property_brand" id="property_brand" placeholder="Brand or manufacturer">
                </div>
                <div class="form-field">
                    <label for="property_model">Model:</label>
                    <input type="text" name="property_model" id="property_model" placeholder="Model or specific type">
                </div>
                <div class="form-field">
                    <label for="property_serial">Serial Number:</label>
                    <input type="text" name="property_serial" id="property_serial" placeholder="Serial number if available">
                </div>
                <div class="form-field">
                    <label for="property_value">Estimated Value:</label>
                    <input type="number" name="property_value" id="property_value" placeholder="Estimated value in CAD" step="0.01">
                </div>
                <div class="form-field">
                    <label for="property_condition">Condition:</label>
                    <select name="property_condition" id="property_condition">
                        <option value="excellent">Excellent</option>
                        <option value="good">Good</option>
                        <option value="fair">Fair</option>
                        <option value="poor">Poor</option>
                        <option value="damaged">Damaged</option>
                    </select>
                </div>
            </div>
        </div>
    `,

    generateServicesTab: () => `
        <div class="tab-content" data-tab="services">
            <div class="form-field">
                <label>Services Offered:</label>
                <div class="checkbox-grid">
                    <label><input type="checkbox" name="services_offered" value="Housing Support"> Housing Support</label>
                    <label><input type="checkbox" name="services_offered" value="Mental Health"> Mental Health</label>
                    <label><input type="checkbox" name="services_offered" value="Addiction Support"> Addiction Support</label>
                    <label><input type="checkbox" name="services_offered" value="Medical Care"> Medical Care</label>
                    <label><input type="checkbox" name="services_offered" value="Food Services"> Food Services</label>
                    <label><input type="checkbox" name="services_offered" value="Legal Aid"> Legal Aid</label>
                    <label><input type="checkbox" name="services_offered" value="Employment"> Employment</label>
                    <label><input type="checkbox" name="services_offered" value="Benefits"> Benefits</label>
                </div>
            </div>
            <div class="form-field">
                <label>Services Provided:</label>
                <div class="checkbox-grid">
                    <label><input type="checkbox" name="services_provided" value="Housing Support"> Housing Support</label>
                    <label><input type="checkbox" name="services_provided" value="Mental Health"> Mental Health</label>
                    <label><input type="checkbox" name="services_provided" value="Addiction Support"> Addiction Support</label>
                    <label><input type="checkbox" name="services_provided" value="Medical Care"> Medical Care</label>
                    <label><input type="checkbox" name="services_provided" value="Food Services"> Food Services</label>
                    <label><input type="checkbox" name="services_provided" value="Legal Aid"> Legal Aid</label>
                    <label><input type="checkbox" name="services_provided" value="Employment"> Employment</label>
                    <label><input type="checkbox" name="services_provided" value="Benefits"> Benefits</label>
                </div>
            </div>
            <div class="form-field">
                <label>Resources Distributed:</label>
                <div class="checkbox-grid">
                    <label><input type="checkbox" name="resources_distributed" value="Food"> Food</label>
                    <label><input type="checkbox" name="resources_distributed" value="Water"> Water</label>
                    <label><input type="checkbox" name="resources_distributed" value="Clothing"> Clothing</label>
                    <label><input type="checkbox" name="resources_distributed" value="Blankets"> Blankets</label>
                    <label><input type="checkbox" name="resources_distributed" value="Hygiene Items"> Hygiene Items</label>
                    <label><input type="checkbox" name="resources_distributed" value="Medical Supplies"> Medical Supplies</label>
                    <label><input type="checkbox" name="resources_distributed" value="Information"> Information</label>
                    <label><input type="checkbox" name="resources_distributed" value="Transportation"> Transportation</label>
                </div>
            </div>
            <div class="form-field">
                <label>Referrals Made:</label>
                <div class="checkbox-grid">
                    <label><input type="checkbox" name="referrals_made" value="Shelter"> Shelter</label>
                    <label><input type="checkbox" name="referrals_made" value="Hospital"> Hospital</label>
                    <label><input type="checkbox" name="referrals_made" value="Mental Health"> Mental Health</label>
                    <label><input type="checkbox" name="referrals_made" value="Addiction Services"> Addiction Services</label>
                    <label><input type="checkbox" name="referrals_made" value="Social Services"> Social Services</label>
                    <label><input type="checkbox" name="referrals_made" value="Legal Aid"> Legal Aid</label>
                    <label><input type="checkbox" name="referrals_made" value="Employment"> Employment</label>
                    <label><input type="checkbox" name="referrals_made" value="Benefits Office"> Benefits Office</label>
                </div>
            </div>
        </div>
    `,

    generateAgenciesTab: () => `
        <div class="tab-content" data-tab="agencies">
            <div class="agencies-section">
                <h4>EMERGENCY SERVICES RESPONSE</h4>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="police_notified"> Police Notified?
                    </label>
                </div>
                <div class="form-field" id="police-response-details" style="display: none;">
                    <label for="police_file_number">Police File Number:</label>
                    <input type="text" name="police_file_number" id="police_file_number" placeholder="Police report number">
                    <label for="officers_attending">Officers Attending:</label>
                    <textarea name="officers_attending" id="officers_attending" placeholder="Names and badge numbers" rows="2"></textarea>
                </div>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="fire_department_called"> Fire Department Called?
                    </label>
                </div>
                <div class="form-field" id="fire-response-details" style="display: none;">
                    <label for="fire_unit_number">Fire Unit Number:</label>
                    <input type="text" name="fire_unit_number" id="fire_unit_number" placeholder="e.g., Engine 23, Ladder 5">
                    <label for="fire_personnel">Fire Personnel:</label>
                    <input type="text" name="fire_personnel" id="fire_personnel" placeholder="Names of fire personnel if available">
                </div>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="paramedics_called"> Paramedics/EMS Called?
                    </label>
                </div>
                <div class="form-field" id="paramedic-response-details" style="display: none;">
                    <label for="ambulance_unit_number">Ambulance Unit Number:</label>
                    <input type="text" name="ambulance_unit_number" id="ambulance_unit_number" placeholder="e.g., Medic 15, Ambulance 23">
                    <label for="ems_personnel_names">EMS Personnel Names:</label>
                    <input type="text" name="ems_personnel_names" id="ems_personnel_names" placeholder="Names of paramedics/EMTs">
                    <label for="ems_response_time">Response Time:</label>
                    <input type="time" name="ems_response_time" id="ems_response_time">
                </div>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="bylaw_notified"> Bylaw Notified?
                    </label>
                </div>
                <div class="form-field" id="bylaw-response-details" style="display: none;">
                    <label for="bylaw_file_number">Bylaw File Number:</label>
                    <input type="text" name="bylaw_file_number" id="bylaw_file_number" placeholder="Bylaw report number">
                    <label for="bylaw_officers_attending">Bylaw Officers Attending:</label>
                    <textarea name="bylaw_officers_attending" id="bylaw_officers_attending" placeholder="Names and badge numbers" rows="2"></textarea>
                    <label for="bylaw_response_time">Response Time:</label>
                    <input type="time" name="bylaw_response_time" id="bylaw_response_time">
                    <label for="bylaw_enforcement_action">Enforcement Action Taken:</label>
                    <textarea name="bylaw_enforcement_action" id="bylaw_enforcement_action" placeholder="Describe any enforcement actions taken" rows="2"></textarea>
                    <label for="bylaw_notes">Bylaw Notes:</label>
                    <textarea name="bylaw_notes" id="bylaw_notes" placeholder="Additional bylaw-related notes" rows="2"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="agency_response_notes">Agency Response Notes:</label>
                    <textarea name="agency_response_notes" id="agency_response_notes" placeholder="Overall notes about emergency services response" rows="3"></textarea>
                </div>
            </div>
        </div>
    `,

    generateSafetyTab: () => `
        <div class="tab-content" data-tab="safety">
            <div class="form-field">
                <label for="scene_safety">Scene Safety Assessment:</label>
                <select name="scene_safety" id="scene_safety">
                    <option value="">Select...</option>
                    <option value="safe">Safe</option>
                    <option value="caution">Caution Required</option>
                    <option value="unsafe">Unsafe</option>
                    <option value="hazardous">Hazardous</option>
                </select>
            </div>
            <div class="form-field">
                <label for="safety_concerns">Safety Concerns:</label>
                <textarea name="safety_concerns" id="safety_concerns" placeholder="Describe any safety concerns" rows="3"></textarea>
            </div>
            <div class="form-field">
                <label>Substance Indicators:</label>
                <div class="checkbox-grid">
                    <label><input type="checkbox" name="substance_indicators" value="Alcohol"> Alcohol</label>
                    <label><input type="checkbox" name="substance_indicators" value="Drugs"> Drugs</label>
                    <label><input type="checkbox" name="substance_indicators" value="Paraphernalia"> Paraphernalia</label>
                    <label><input type="checkbox" name="substance_indicators" value="Intoxication"> Intoxication</label>
                    <label><input type="checkbox" name="substance_indicators" value="Withdrawal"> Withdrawal</label>
                    <label><input type="checkbox" name="substance_indicators" value="None Observed"> None Observed</label>
                </div>
            </div>
            <div class="form-field">
                <label>Environmental Factors:</label>
                <div class="checkbox-grid">
                    <label><input type="checkbox" name="environmental_factors" value="Weather"> Weather</label>
                    <label><input type="checkbox" name="environmental_factors" value="Traffic"> Traffic</label>
                    <label><input type="checkbox" name="environmental_factors" value="Crowds"> Crowds</label>
                    <label><input type="checkbox" name="environmental_factors" value="Noise"> Noise</label>
                    <label><input type="checkbox" name="environmental_factors" value="Poor Lighting"> Poor Lighting</label>
                    <label><input type="checkbox" name="environmental_factors" value="Hazardous Materials"> Hazardous Materials</label>
                </div>
            </div>
            <div class="form-field">
                <label for="weather_conditions">Weather Conditions:</label>
                <input type="text" name="weather_conditions" id="weather_conditions" placeholder="Current weather conditions">
            </div>
            <div class="form-field">
                <label for="scene_conditions">Scene Conditions:</label>
                <textarea name="scene_conditions" id="scene_conditions" placeholder="Overall scene conditions" rows="3"></textarea>
            </div>
        </div>
    `,

    generateNarrativeTab: () => `
        <div class="tab-content" data-tab="narrative">
            <div class="narrative-section">
                <h4>&gt; EVENTS & NARRATIVE</h4>
                <div class="narrative-container">
                    <div class="narrative-header">
                        <button type="button" class="action-btn add-narrative-btn" onclick="window.app.addNarrativeEntry()">
                            📝 Add Entry
                        </button>
                    </div>
                    <div class="narrative-entries" id="narrative-entries-form">
                        <div class="no-entries-message">
                            [NO NARRATIVE ENTRIES YET]<br>
                            Click "Add Entry" to start documenting events and observations.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    generateFollowupTab: () => `
        <div class="tab-content" data-tab="followup">
            <div class="form-field">
                <label>
                    <input type="checkbox" name="follow_up_required"> Follow-up Required?
                </label>
            </div>
            <div class="form-field" id="followup-details" style="display: none;">
                <label for="follow_up_date">Follow-up Date:</label>
                <input type="date" name="follow_up_date" id="follow_up_date">
                <label for="follow_up_notes">Follow-up Notes:</label>
                <textarea name="follow_up_notes" id="follow_up_notes" placeholder="Details about required follow-up" rows="4"></textarea>
            </div>
        </div>
    `,

    // Incident edit form template
    editIncidentForm: (incident) => `
        <div class="incident-creation-container">
            <div class="incident-form-wrapper">
                <form id="edit-incident-form" class="comprehensive-incident-form">
                    <input type="hidden" id="incident-id" value="${incident.id}">
                    <div class="incident-form-tabs">
                        <button type="button" class="action-btn back-to-incidents incident-tab-back" data-action="back-to-incident-details" data-incident-id="${incident.id}">[BACK]</button>
                        <span class="incident-form-title">&gt; EDIT INCIDENT ${incident.incident_number || `#${incident.id}`}</span>
                        <button type="button" class="incident-form-tab active" data-tab="basic">BASIC INFO</button>
                        <button type="button" class="incident-form-tab" data-tab="location">LOCATION</button>
                        <button type="button" class="incident-form-tab" data-tab="people">PEOPLE</button>
                        <button type="button" class="incident-form-tab" data-tab="property">PROPERTY</button>
                        <button type="button" class="incident-form-tab" data-tab="services">SERVICES</button>
                        <button type="button" class="incident-form-tab" data-tab="agencies">AGENCIES</button>
                        <button type="button" class="incident-form-tab" data-tab="safety">SAFETY</button>
                        <button type="button" class="incident-form-tab" data-tab="narrative">EVENTS/NARRATIVE</button>
                        <button type="button" class="incident-form-tab" data-tab="followup">FOLLOW-UP</button>
                    </div>
                    
                    <div class="incident-form-content">
                        <div class="form-content-scroll">
                            ${incidentFormTemplates.generateEditBasicTab(incident)}
                            ${incidentFormTemplates.generateEditLocationTab(incident)}
                            ${incidentFormTemplates.generateEditPeopleTab(incident)}
                            ${incidentFormTemplates.generateEditPropertyTab(incident)}
                            ${incidentFormTemplates.generateEditServicesTab(incident)}
                            ${incidentFormTemplates.generateEditAgenciesTab(incident)}
                            ${incidentFormTemplates.generateEditSafetyTab(incident)}
                            ${incidentFormTemplates.generateEditNarrativeTab(incident)}
                            ${incidentFormTemplates.generateEditFollowupTab(incident)}
                        </div>
                    </div>
                    
                    <div class="incident-form-actions">
                        <button type="submit" class="primary-button" id="update-comprehensive-incident">UPDATE INCIDENT</button>
                        <button type="button" class="action-btn cancel-incident-form" data-action="back-to-incident-details" data-incident-id="${incident.id}">CANCEL</button>
                        <button type="button" id="save-edit-draft" class="action-btn">SAVE DRAFT</button>
                    </div>
                </form>
            </div>
        </div>
    `,

    // Generate individual tab content for editing (populated with incident data)
    generateEditBasicTab: (incident) => `
        <div class="tab-content active" data-tab="basic">
            <div class="form-field">
                <label>
                    <input type="checkbox" name="use_current_time"> Use current date and time?
                </label>
            </div>
            <div class="form-field" id="datetime-group">
                <label for="incident_date">Incident Date:</label>
                <input type="date" name="incident_date" id="incident_date" value="${incident.incident_date ? incident.incident_date.split('T')[0] : ''}">
            </div>
            <div class="form-field" id="datetime-group-time">
                <label for="incident_time">Incident Time:</label>
                <input type="time" name="incident_time" id="incident_time" value="${incident.incident_time || ''}">
            </div>
            <div class="form-field">
                <label for="incident_type">Incident Type *:</label>
                <select name="incident_type" id="incident_type" required>
                    <option value="">Select type...</option>
                    <option value="Assault" ${incident.incident_type === 'Assault' ? 'selected' : ''}>Assault</option>
                    <option value="Theft" ${incident.incident_type === 'Theft' ? 'selected' : ''}>Theft</option>
                    <option value="Vandalism" ${incident.incident_type === 'Vandalism' ? 'selected' : ''}>Vandalism</option>
                    <option value="Drug Activity" ${incident.incident_type === 'Drug Activity' ? 'selected' : ''}>Drug Activity</option>
                    <option value="Mental Health Crisis" ${incident.incident_type === 'Mental Health Crisis' ? 'selected' : ''}>Mental Health Crisis</option>
                    <option value="Medical Emergency" ${incident.incident_type === 'Medical Emergency' ? 'selected' : ''}>Medical Emergency</option>
                    <option value="Disturbance" ${incident.incident_type === 'Disturbance' ? 'selected' : ''}>Disturbance</option>
                    <option value="Trespassing" ${incident.incident_type === 'Trespassing' ? 'selected' : ''}>Trespassing</option>
                    <option value="Welfare Check" ${incident.incident_type === 'Welfare Check' ? 'selected' : ''}>Welfare Check</option>
                    <option value="Other" ${incident.incident_type === 'Other' ? 'selected' : ''}>Other</option>
                </select>
            </div>
            <div class="form-field">
                <label for="priority">Priority *:</label>
                <select name="priority" id="priority" required>
                    <option value="low" ${incident.priority === 'low' ? 'selected' : ''}>Low</option>
                    <option value="medium" ${incident.priority === 'medium' ? 'selected' : ''}>Medium</option>
                    <option value="high" ${incident.priority === 'high' ? 'selected' : ''}>High</option>
                </select>
            </div>
            <div class="form-field">
                <label for="assigned_ranger">Assigned Ranger:</label>
                <input type="text" name="assigned_ranger" id="assigned_ranger" placeholder="Ranger assigned to this incident" value="${incident.assigned_ranger || ''}">
            </div>
            <div class="form-field">
                <label for="description">Summary *:</label>
                <textarea name="description" id="description" required placeholder="Brief summary of the incident" rows="3">${incident.description || ''}</textarea>
            </div>
            <div class="form-field">
                <label for="status">Status:</label>
                <select name="status" id="status">
                    <option value="open" ${incident.status === 'open' ? 'selected' : ''}>Open</option>
                    <option value="in_progress" ${incident.status === 'in_progress' ? 'selected' : ''}>In Progress</option>
                    <option value="resolved" ${incident.status === 'resolved' ? 'selected' : ''}>Resolved</option>
                    <option value="closed" ${incident.status === 'closed' ? 'selected' : ''}>Closed</option>
                    <option value="draft" ${incident.status === 'draft' ? 'selected' : ''}>Draft</option>
                </select>
            </div>
        </div>
    `,

    generateEditLocationTab: (incident) => `
        <div class="tab-content" data-tab="location">
            <div class="form-field">
                <label for="address_search">Address Search:</label>
                <input type="text" name="address_search" id="address_search" placeholder="Start typing an address..." value="${incident.location || ''}">
                <div id="location-suggestions" class="location-suggestions"></div>
            </div>
            <div class="form-field">
                <label for="street_address">Street Address:</label>
                <input type="text" name="street_address" id="street_address" placeholder="123 Main Street" value="${incident.street_address || ''}">
            </div>
            <div class="form-field">
                <label for="city">City:</label>
                <input type="text" name="city" id="city" placeholder="Ottawa" value="${incident.city || ''}">
            </div>
            <div class="form-field">
                <label for="province">Province:</label>
                <input type="text" name="province" id="province" placeholder="ON" value="${incident.province || 'ON'}">
            </div>
            <div class="form-field">
                <label for="postal_code">Postal Code:</label>
                <input type="text" name="postal_code" id="postal_code" placeholder="K1A 0A6" value="${incident.postal_code || ''}">
            </div>
            <div class="form-field">
                <label for="location_notes">Location Notes:</label>
                <textarea name="location_notes" id="location_notes" placeholder="Additional location details" rows="3">${incident.location_notes || ''}</textarea>
            </div>
            <div class="form-field">
                <label for="coordinates">GPS Coordinates:</label>
                <input type="text" name="coordinates" id="coordinates" placeholder="45.4215, -75.6972" value="${incident.coordinates || ''}">
            </div>
        </div>
    `,

    generateEditPeopleTab: (incident) => `
        <div class="tab-content" data-tab="people">
            <div class="people-management-section">
                <div class="form-section-header">
                    <h4>&gt; PEOPLE INVOLVED</h4>
                </div>
                
                <div class="people-search-container">
                    <div class="form-field">
                        <label for="person-search-input">Search for Person:</label>
                        <div class="search-input-group">
                            <input type="text" id="person-search-input" placeholder="Enter name to search existing records..." class="text-input">
                            <button type="button" id="person-search-btn" class="action-btn">[SEARCH]</button>
                            <button type="button" id="person-search-clear" class="action-btn secondary">[CLEAR]</button>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="add-unknown-person-btn" class="action-btn secondary">[ADD UNKNOWN PERSON]</button>
                    </div>
                </div>

                <div class="people-search-section" id="people-search-section">
                    <div class="people-search-results" id="person-search-results">
                        <div class="search-hint">Enter at least 2 characters to search</div>
                    </div>
                </div>
                
                <div class="involved-people-display" id="involved-people-list">
                    <div class="results-header">
                        <h5>&gt; PEOPLE ADDED TO INCIDENT</h5>
                    </div>
                    <div class="people-list-container" id="people-list-container">
                        <div class="no-people-message terminal-text">
                            [NO PEOPLE ADDED TO INCIDENT]<br>
                            Use search above to add known individuals or click "ADD UNKNOWN PERSON"
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    generateEditPropertyTab: (incident) => `
        <div class="tab-content" data-tab="property">
            <div class="form-field">
                <label>
                    <input type="checkbox" name="property_involved" id="property_involved" ${incident.property_involved ? 'checked' : ''}> Property Involved
                </label>
            </div>
            <div id="property-details" class="conditional-section" style="${incident.property_involved ? 'display: block;' : 'display: none;'}">
                <div class="form-field">
                    <label for="property_type">Property Type:</label>
                    <select name="property_type" id="property_type">
                        <option value="">Select type...</option>
                        <option value="Personal Belongings" ${incident.property_type === 'Personal Belongings' ? 'selected' : ''}>Personal Belongings</option>
                        <option value="Bicycle" ${incident.property_type === 'Bicycle' ? 'selected' : ''}>Bicycle</option>
                        <option value="Vehicle" ${incident.property_type === 'Vehicle' ? 'selected' : ''}>Vehicle</option>
                        <option value="Electronics" ${incident.property_type === 'Electronics' ? 'selected' : ''}>Electronics</option>
                        <option value="Weapons" ${incident.property_type === 'Weapons' ? 'selected' : ''}>Weapons</option>
                        <option value="Drugs/Paraphernalia" ${incident.property_type === 'Drugs/Paraphernalia' ? 'selected' : ''}>Drugs/Paraphernalia</option>
                        <option value="Other" ${incident.property_type === 'Other' ? 'selected' : ''}>Other</option>
                    </select>
                </div>
                <div class="form-field">
                    <label for="property_description">Property Description:</label>
                    <textarea name="property_description" id="property_description" placeholder="Detailed description of property" rows="3">${incident.property_description || ''}</textarea>
                </div>
                <div class="form-field">
                    <label for="property_condition">Property Condition:</label>
                    <select name="property_condition" id="property_condition">
                        <option value="">Select condition...</option>
                        <option value="Excellent" ${incident.property_condition === 'Excellent' ? 'selected' : ''}>Excellent</option>
                        <option value="Good" ${incident.property_condition === 'Good' ? 'selected' : ''}>Good</option>
                        <option value="Fair" ${incident.property_condition === 'Fair' ? 'selected' : ''}>Fair</option>
                        <option value="Poor" ${incident.property_condition === 'Poor' ? 'selected' : ''}>Poor</option>
                        <option value="Damaged" ${incident.property_condition === 'Damaged' ? 'selected' : ''}>Damaged</option>
                    </select>
                </div>
            </div>
        </div>
    `,

    generateEditServicesTab: (incident) => `
        <div class="tab-content" data-tab="services">
            <div class="form-field">
                <label for="services_provided">Services Provided:</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" name="services_provided" value="Food/Water" ${incident.services_provided?.includes('Food/Water') ? 'checked' : ''}> Food/Water</label>
                    <label><input type="checkbox" name="services_provided" value="Clothing" ${incident.services_provided?.includes('Clothing') ? 'checked' : ''}> Clothing</label>
                    <label><input type="checkbox" name="services_provided" value="Medical Assistance" ${incident.services_provided?.includes('Medical Assistance') ? 'checked' : ''}> Medical Assistance</label>
                    <label><input type="checkbox" name="services_provided" value="Mental Health Support" ${incident.services_provided?.includes('Mental Health Support') ? 'checked' : ''}> Mental Health Support</label>
                    <label><input type="checkbox" name="services_provided" value="Housing Information" ${incident.services_provided?.includes('Housing Information') ? 'checked' : ''}> Housing Information</label>
                    <label><input type="checkbox" name="services_provided" value="Transportation" ${incident.services_provided?.includes('Transportation') ? 'checked' : ''}> Transportation</label>
                    <label><input type="checkbox" name="services_provided" value="Legal Aid Information" ${incident.services_provided?.includes('Legal Aid Information') ? 'checked' : ''}> Legal Aid Information</label>
                    <label><input type="checkbox" name="services_provided" value="Other" ${incident.services_provided?.includes('Other') ? 'checked' : ''}> Other</label>
                </div>
            </div>
            <div class="form-field">
                <label for="services_notes">Services Notes:</label>
                <textarea name="services_notes" id="services_notes" placeholder="Additional details about services provided" rows="4">${incident.services_notes || ''}</textarea>
            </div>
        </div>
    `,

    generateEditAgenciesTab: (incident) => `
        <div class="tab-content" data-tab="agencies">
            <div class="agencies-section">
                <h4>EMERGENCY SERVICES RESPONSE</h4>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="police_notified" ${incident.police_notified ? 'checked' : ''}> Police Notified?
                    </label>
                </div>
                <div class="form-field" id="police-response-details" style="display: ${incident.police_notified ? 'block' : 'none'};">
                    <label for="police_file_number">Police File Number:</label>
                    <input type="text" name="police_file_number" id="police_file_number" placeholder="Police report number" value="${incident.police_file_number || ''}">
                    <label for="officers_attending">Officers Attending:</label>
                    <textarea name="officers_attending" id="officers_attending" placeholder="Names and badge numbers" rows="2">${incident.officers_attending || ''}</textarea>
                </div>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="fire_department_called" ${incident.fire_department_called ? 'checked' : ''}> Fire Department Called?
                    </label>
                </div>
                <div class="form-field" id="fire-response-details" style="display: ${incident.fire_department_called ? 'block' : 'none'};">
                    <label for="fire_unit_number">Fire Unit Number:</label>
                    <input type="text" name="fire_unit_number" id="fire_unit_number" placeholder="e.g., Engine 23, Ladder 5" value="${incident.fire_unit_number || ''}">
                    <label for="fire_personnel">Fire Personnel:</label>
                    <input type="text" name="fire_personnel" id="fire_personnel" placeholder="Names of fire personnel if available" value="${incident.fire_personnel || ''}">
                </div>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="paramedics_called" ${incident.paramedics_called ? 'checked' : ''}> Paramedics/EMS Called?
                    </label>
                </div>
                <div class="form-field" id="paramedic-response-details" style="display: ${incident.paramedics_called ? 'block' : 'none'};">
                    <label for="ambulance_unit_number">Ambulance Unit Number:</label>
                    <input type="text" name="ambulance_unit_number" id="ambulance_unit_number" placeholder="e.g., Medic 15, Ambulance 23" value="${incident.ambulance_unit_number || ''}">
                    <label for="ems_personnel_names">EMS Personnel Names:</label>
                    <input type="text" name="ems_personnel_names" id="ems_personnel_names" placeholder="Names of paramedics/EMTs" value="${incident.ems_personnel_names || ''}">
                    <label for="ems_response_time">Response Time:</label>
                    <input type="time" name="ems_response_time" id="ems_response_time" value="${incident.ems_response_time || ''}">
                </div>
                
                <div class="form-field">
                    <label>
                        <input type="checkbox" name="bylaw_notified" ${incident.bylaw_notified ? 'checked' : ''}> Bylaw Notified?
                    </label>
                </div>
                <div class="form-field" id="bylaw-response-details" style="display: ${incident.bylaw_notified ? 'block' : 'none'};">
                    <label for="bylaw_file_number">Bylaw File Number:</label>
                    <input type="text" name="bylaw_file_number" id="bylaw_file_number" placeholder="Bylaw report number" value="${incident.bylaw_file_number || ''}">
                    <label for="bylaw_officers_attending">Bylaw Officers Attending:</label>
                    <textarea name="bylaw_officers_attending" id="bylaw_officers_attending" placeholder="Names and badge numbers" rows="2">${incident.bylaw_officers_attending || ''}</textarea>
                    <label for="bylaw_response_time">Response Time:</label>
                    <input type="time" name="bylaw_response_time" id="bylaw_response_time" value="${incident.bylaw_response_time || ''}">
                    <label for="bylaw_enforcement_action">Enforcement Action Taken:</label>
                    <textarea name="bylaw_enforcement_action" id="bylaw_enforcement_action" placeholder="Describe any enforcement actions taken" rows="2">${incident.bylaw_enforcement_action || ''}</textarea>
                    <label for="bylaw_notes">Bylaw Notes:</label>
                    <textarea name="bylaw_notes" id="bylaw_notes" placeholder="Additional bylaw-related notes" rows="2">${incident.bylaw_notes || ''}</textarea>
                </div>
                
                <div class="form-field">
                    <label for="agency_response_notes">Agency Response Notes:</label>
                    <textarea name="agency_response_notes" id="agency_response_notes" placeholder="Overall notes about emergency services response" rows="3">${incident.agency_response_notes || ''}</textarea>
                </div>
            </div>
        </div>
    `,

    generateEditSafetyTab: (incident) => `
        <div class="tab-content" data-tab="safety">
            <div class="form-field">
                <label for="scene_safety">Scene Safety Assessment:</label>
                <select name="scene_safety" id="scene_safety">
                    <option value="">Select...</option>
                    <option value="safe" ${incident.scene_safety === 'safe' ? 'selected' : ''}>Safe</option>
                    <option value="caution" ${incident.scene_safety === 'caution' ? 'selected' : ''}>Caution Required</option>
                    <option value="unsafe" ${incident.scene_safety === 'unsafe' ? 'selected' : ''}>Unsafe</option>
                    <option value="hazardous" ${incident.scene_safety === 'hazardous' ? 'selected' : ''}>Hazardous</option>
                </select>
            </div>
            <div class="form-field">
                <label for="safety_concerns">Safety Concerns:</label>
                <textarea name="safety_concerns" id="safety_concerns" placeholder="Describe any safety concerns" rows="3">${incident.safety_concerns || ''}</textarea>
            </div>
            <div class="form-field">
                <label>Substance Indicators:</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" name="substance_indicators" value="Alcohol" ${incident.substance_indicators?.includes('Alcohol') ? 'checked' : ''}> Alcohol</label>
                    <label><input type="checkbox" name="substance_indicators" value="Cannabis" ${incident.substance_indicators?.includes('Cannabis') ? 'checked' : ''}> Cannabis</label>
                    <label><input type="checkbox" name="substance_indicators" value="Hard Drugs" ${incident.substance_indicators?.includes('Hard Drugs') ? 'checked' : ''}> Hard Drugs</label>
                    <label><input type="checkbox" name="substance_indicators" value="Needles/Paraphernalia" ${incident.substance_indicators?.includes('Needles/Paraphernalia') ? 'checked' : ''}> Needles/Paraphernalia</label>
                    <label><input type="checkbox" name="substance_indicators" value="Prescription Meds" ${incident.substance_indicators?.includes('Prescription Meds') ? 'checked' : ''}> Prescription Meds</label>
                </div>
            </div>
            <div class="form-field">
                <label>Environmental Factors:</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" name="environmental_factors" value="Weather Hazards" ${incident.environmental_factors?.includes('Weather Hazards') ? 'checked' : ''}> Weather Hazards</label>
                    <label><input type="checkbox" name="environmental_factors" value="Traffic/Road" ${incident.environmental_factors?.includes('Traffic/Road') ? 'checked' : ''}> Traffic/Road</label>
                    <label><input type="checkbox" name="environmental_factors" value="Wildlife" ${incident.environmental_factors?.includes('Wildlife') ? 'checked' : ''}> Wildlife</label>
                    <label><input type="checkbox" name="environmental_factors" value="Contamination" ${incident.environmental_factors?.includes('Contamination') ? 'checked' : ''}> Contamination</label>
                    <label><input type="checkbox" name="environmental_factors" value="Structural Damage" ${incident.environmental_factors?.includes('Structural Damage') ? 'checked' : ''}> Structural Damage</label>
                </div>
            </div>
            <div class="form-field">
                <label>
                    <input type="checkbox" name="police_notified" id="police_notified" ${incident.police_notified ? 'checked' : ''}> Police Response Required
                </label>
            </div>
            <div id="police-details" class="conditional-section" style="${incident.police_notified ? 'display: block;' : 'display: none;'}">
                <div class="form-field">
                    <label for="police_file_number">Police File Number:</label>
                    <input type="text" name="police_file_number" id="police_file_number" placeholder="Police report number" value="${incident.police_file_number || ''}">
                </div>
                <div class="form-field">
                    <label for="officers_attending">Officers Attending:</label>
                    <textarea name="officers_attending" id="officers_attending" placeholder="Names and badge numbers" rows="2">${incident.officers_attending || ''}</textarea>
                </div>
            </div>
        </div>
    `,

    generateEditFollowupTab: (incident) => `
        <div class="tab-content" data-tab="followup">
            <div class="form-field">
                <label>
                    <input type="checkbox" name="follow_up_required" id="follow_up_required" ${incident.follow_up_required ? 'checked' : ''}> Follow-up Required
                </label>
            </div>
            <div id="followup-details" class="conditional-section" style="${incident.follow_up_required ? 'display: block;' : 'display: none;'}">
                <div class="form-field">
                    <label for="follow_up_date">Follow-up Date:</label>
                    <input type="date" name="follow_up_date" id="follow_up_date" value="${incident.follow_up_date || ''}">
                </div>
                <div class="form-field">
                    <label for="follow_up_notes">Follow-up Notes:</label>
                    <textarea name="follow_up_notes" id="follow_up_notes" placeholder="Details about required follow-up actions" rows="4">${incident.follow_up_notes || ''}</textarea>
                </div>
            </div>
            <div class="form-field">
                <label for="outcome">Outcome:</label>
                <textarea name="outcome" id="outcome" placeholder="Final outcome or resolution" rows="4">${incident.outcome || ''}</textarea>
            </div>
            <div class="form-field">
                <label for="recommendations">Recommendations:</label>
                <textarea name="recommendations" id="recommendations" placeholder="Recommendations for future incidents" rows="3">${incident.recommendations || ''}</textarea>
            </div>
        </div>
    `,

    generateEditNarrativeTab: (incident) => `
        <div class="tab-content" data-tab="narrative">
            <div class="narrative-section">
                <h4>&gt; EVENTS & NARRATIVE</h4>
                <div class="narrative-container">
                    <div class="narrative-header">
                        <button type="button" class="action-btn add-narrative-btn" onclick="window.app.addNarrativeEntry('${incident.id}')">
                            📝 Add Entry
                        </button>
                    </div>
                    <div class="narrative-entries" id="narrative-entries-edit-${incident.id}">
                        <!-- Narrative entries will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    `
};