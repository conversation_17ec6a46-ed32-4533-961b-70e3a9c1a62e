// Google Maps Address Search Component for S.T.E.V.I Retro
import { ConfigManager } from './config.js';

export class GoogleMapsAddressSearch {
    constructor(configManager = null) {
        this.config = configManager || new ConfigManager();
        this.apiKey = null;
        this.autocompleteService = null;
        this.placesService = null;
        this.geocoder = null;
        this.isInitialized = false;
        this.currentInput = null;
        this.currentCallback = null;
        this.searchTimeout = null;
    }

    /**
     * Initialize Google Maps API
     * @returns {Promise<boolean>} True if successful, false otherwise
     */
    async initialize() {
        if (this.isInitialized) {
            return true;
        }

        try {
            // Get API key from vault
            this.apiKey = await this.config.getGoogleApiKey();
            if (!this.apiKey) {
                console.error('Google Maps API key not available');
                return false;
            }

            // Load Google Maps API if not already loaded
            if (!window.google || !window.google.maps) {
                await this.loadGoogleMapsAPI();
            }

            // Initialize services
            // NOTE: AutocompleteService is deprecated as of March 2025 but still functional
            // TODO: Migrate to AutocompleteSuggestion API when stable
            this.autocompleteService = new google.maps.places.AutocompleteService();
            this.geocoder = new google.maps.Geocoder();
            
            // Create a dummy div for PlacesService (required by API)
            // NOTE: PlacesService is deprecated as of March 2025 but still functional  
            // TODO: Migrate to Place API when stable
            const dummyDiv = document.createElement('div');
            this.placesService = new google.maps.places.PlacesService(dummyDiv);

            this.isInitialized = true;
            console.log('Google Maps Address Search initialized successfully');
            return true;

        } catch (error) {
            console.error('Error initializing Google Maps Address Search:', error);
            return false;
        }
    }

    /**
     * Load Google Maps API dynamically
     * @returns {Promise<void>}
     */
    async loadGoogleMapsAPI() {
        return new Promise((resolve, reject) => {
            if (window.google && window.google.maps) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${this.apiKey}&libraries=places&callback=initGoogleMaps`;
            script.async = true;
            script.defer = true;

            // Set up callback
            window.initGoogleMaps = () => {
                delete window.initGoogleMaps;
                resolve();
            };

            script.onerror = () => {
                reject(new Error('Failed to load Google Maps API'));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Create an address search input field with autocomplete
     * @param {HTMLElement} container - Container element to append the search field
     * @param {Object} options - Configuration options
     * @param {Function} onAddressSelected - Callback when address is selected
     * @returns {HTMLElement} The created input element
     */
    createAddressSearchField(container, options = {}, onAddressSelected = null) {
        const {
            placeholder = 'Start typing an address...',
            className = 'address-search-input',
            id = 'address-search',
            required = false
        } = options;

        // Create input wrapper
        const wrapper = document.createElement('div');
        wrapper.className = 'address-search-wrapper';

        // Create input field
        const input = document.createElement('input');
        input.type = 'text';
        input.id = id;
        input.className = className;
        input.placeholder = placeholder;
        input.autocomplete = 'off';
        if (required) input.required = true;

        // Create suggestions dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'address-suggestions-dropdown';
        dropdown.style.display = 'none';

        wrapper.appendChild(input);
        wrapper.appendChild(dropdown);
        container.appendChild(wrapper);

        // Set up event listeners
        this.setupAddressSearchListeners(input, dropdown, onAddressSelected);

        return input;
    }

    /**
     * Set up event listeners for address search
     * @param {HTMLElement} input - Input element
     * @param {HTMLElement} dropdown - Dropdown element
     * @param {Function} onAddressSelected - Callback function
     */
    setupAddressSearchListeners(input, dropdown, onAddressSelected) {
        this.currentInput = input;
        this.currentCallback = onAddressSelected;

        // Input event for autocomplete
        input.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            // Clear previous timeout
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            if (query.length < 3) {
                this.hideDropdown(dropdown);
                return;
            }

            // Debounce search
            this.searchTimeout = setTimeout(() => {
                this.searchAddresses(query, dropdown);
            }, 300);
        });

        // Handle keyboard navigation
        input.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e, dropdown);
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                this.hideDropdown(dropdown);
            }
        });
    }

    /**
     * Search for addresses using Google Places API
     * @param {string} query - Search query
     * @param {HTMLElement} dropdown - Dropdown element to show results
     */
    async searchAddresses(query, dropdown) {
        if (!this.isInitialized) {
            const initialized = await this.initialize();
            if (!initialized) {
                console.error('Cannot search addresses: Google Maps not initialized');
                return;
            }
        }

        try {
            const request = {
                input: query,
                componentRestrictions: { country: 'ca' }, // Restrict to Canada
                types: ['address']
            };

            this.autocompleteService.getPlacePredictions(request, (predictions, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
                    this.showSuggestions(predictions, dropdown);
                } else {
                    this.hideDropdown(dropdown);
                }
            });

        } catch (error) {
            console.error('Error searching addresses:', error);
            this.hideDropdown(dropdown);
        }
    }

    /**
     * Show address suggestions in dropdown
     * @param {Array} predictions - Google Places predictions
     * @param {HTMLElement} dropdown - Dropdown element
     */
    showSuggestions(predictions, dropdown) {
        dropdown.innerHTML = '';
        
        predictions.forEach((prediction, index) => {
            const item = document.createElement('div');
            item.className = 'address-suggestion-item';
            item.dataset.placeId = prediction.place_id;
            item.dataset.index = index;
            
            // Format the suggestion
            const mainText = prediction.structured_formatting.main_text;
            const secondaryText = prediction.structured_formatting.secondary_text;
            
            item.innerHTML = `
                <div class="suggestion-main">${mainText}</div>
                <div class="suggestion-secondary">${secondaryText}</div>
            `;

            item.addEventListener('click', () => {
                this.selectAddress(prediction.place_id, prediction.description);
                this.hideDropdown(dropdown);
            });

            dropdown.appendChild(item);
        });

        dropdown.style.display = 'block';
    }

    /**
     * Hide the suggestions dropdown
     * @param {HTMLElement} dropdown - Dropdown element
     */
    hideDropdown(dropdown) {
        dropdown.style.display = 'none';
        dropdown.innerHTML = '';
    }

    /**
     * Handle keyboard navigation in dropdown
     * @param {KeyboardEvent} e - Keyboard event
     * @param {HTMLElement} dropdown - Dropdown element
     */
    handleKeyboardNavigation(e, dropdown) {
        const items = dropdown.querySelectorAll('.address-suggestion-item');
        const currentActive = dropdown.querySelector('.address-suggestion-item.active');
        let activeIndex = currentActive ? parseInt(currentActive.dataset.index) : -1;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                activeIndex = Math.min(activeIndex + 1, items.length - 1);
                this.setActiveItem(items, activeIndex);
                break;
            case 'ArrowUp':
                e.preventDefault();
                activeIndex = Math.max(activeIndex - 1, 0);
                this.setActiveItem(items, activeIndex);
                break;
            case 'Enter':
                e.preventDefault();
                if (currentActive) {
                    const placeId = currentActive.dataset.placeId;
                    const description = currentActive.textContent.trim();
                    this.selectAddress(placeId, description);
                    this.hideDropdown(dropdown);
                }
                break;
            case 'Escape':
                this.hideDropdown(dropdown);
                break;
        }
    }

    /**
     * Set active item in dropdown
     * @param {NodeList} items - List of suggestion items
     * @param {number} activeIndex - Index of active item
     */
    setActiveItem(items, activeIndex) {
        items.forEach((item, index) => {
            if (index === activeIndex) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Select an address and get detailed information
     * @param {string} placeId - Google Places ID
     * @param {string} description - Address description
     */
    async selectAddress(placeId, description) {
        if (!this.isInitialized) {
            console.error('Cannot select address: Google Maps not initialized');
            return;
        }

        try {
            // Update input field
            if (this.currentInput) {
                this.currentInput.value = description;
            }

            // Get detailed place information
            const request = {
                placeId: placeId,
                fields: ['address_components', 'formatted_address', 'geometry', 'name']
            };

            this.placesService.getDetails(request, (place, status) => {
                if (status === google.maps.places.PlacesServiceStatus.OK) {
                    const addressData = this.parseAddressComponents(place);
                    
                    if (this.currentCallback) {
                        this.currentCallback(addressData);
                    }
                } else {
                    console.error('Error getting place details:', status);
                }
            });

        } catch (error) {
            console.error('Error selecting address:', error);
        }
    }

    /**
     * Parse Google Places address components into structured data
     * @param {Object} place - Google Places result
     * @returns {Object} Parsed address data
     */
    parseAddressComponents(place) {
        const components = place.address_components || [];
        const addressData = {
            formatted_address: place.formatted_address || '',
            street_number: '',
            route: '',
            street_address: '',
            city: '',
            province: '',
            postal_code: '',
            country: '',
            coordinates: null
        };

        // Extract coordinates
        if (place.geometry && place.geometry.location) {
            addressData.coordinates = {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng()
            };
        }

        // Parse address components
        components.forEach(component => {
            const types = component.types;
            const value = component.long_name;

            if (types.includes('street_number')) {
                addressData.street_number = value;
            } else if (types.includes('route')) {
                addressData.route = value;
            } else if (types.includes('locality')) {
                addressData.city = value;
            } else if (types.includes('administrative_area_level_1')) {
                addressData.province = value;
            } else if (types.includes('postal_code')) {
                addressData.postal_code = value;
            } else if (types.includes('country')) {
                addressData.country = value;
            }
        });

        // Combine street number and route for full street address
        addressData.street_address = [addressData.street_number, addressData.route]
            .filter(part => part)
            .join(' ');

        return addressData;
    }


}
