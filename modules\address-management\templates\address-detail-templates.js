// Address Detail Templates
// Comprehensive address detail screen with tabs for basic info, activities, and actions

export const addressDetailTemplates = {
    // Main address detail content (like person/vehicle detail - not a modal)
    addressDetailContent: (address) => {
        const fullAddress = [
            address.street_address,
            address.city,
            address.province,
            address.postal_code
        ].filter(Boolean).join(', ');

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>ADDRESS DETAILS</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="edit-address" data-address-id="${address.id}">
                            <span class="button-icon">✏️</span>
                            Edit Address
                        </button>
                        <button class="secondary-button" data-action="back-to-address-list">
                            <span class="button-icon">←</span>
                            Back to Addresses
                        </button>
                    </div>
                </div>

                <div class="address-detail-container">
                    <div class="address-detail-header">
                        <div class="address-avatar-large">
                            <div class="avatar-placeholder-large address-avatar">
                                🏠
                            </div>
                        </div>
                        <div class="address-title-info">
                            <h3 class="address-identifier">
                                ${address.street_address || 'Unknown Address'}
                            </h3>
                            <div class="address-description">
                                ${fullAddress}
                            </div>
                            <div class="address-type-badge">
                                ${address.address_type || 'Address'}
                            </div>
                            <div class="address-id">ID: ${address.id}</div>
                            <div class="address-created">Created: ${new Date(address.created_at).toLocaleString()}</div>
                        </div>
                    </div>

                    ${addressDetailTemplates.addressDetailTabs(address)}
                </div>
            </div>
        `;
    },

    // Address detail tabs (matching person detail page structure)
    addressDetailTabs: (address) => {
        return `
            <div class="person-detail-tabs">
                <button class="person-detail-tab active" data-tab="basic">Basic Info</button>
                <button class="person-detail-tab" data-tab="activities">Activities</button>
            </div>

            <div class="person-detail-content">
                <div class="tab-content active" id="basic-tab">
                    ${addressDetailTemplates.basicInfoTab(address)}
                </div>
                <div class="tab-content" id="activities-tab">
                    ${addressDetailTemplates.activitiesTab(address)}
                </div>
            </div>
        `;
    },

    // Basic info tab content (following person/vehicle detail style)
    basicInfoTab: (address) => {
        return `
            <div class="detail-sections">
                <div class="detail-section">
                    <h4>Address Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Type:</label>
                            <span>${address.address_type || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Street Address:</label>
                            <span>${address.street_address || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Unit Number:</label>
                            <span>${address.unit_number || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>City:</label>
                            <span>${address.city || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Province/State:</label>
                            <span>${address.province || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Postal Code:</label>
                            <span>${address.postal_code || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Country:</label>
                            <span>${address.country || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Record Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Created:</label>
                            <span>${new Date(address.created_at).toLocaleString()}</span>
                        </div>
                        <div class="detail-item">
                            <label>Created By:</label>
                            <span>${address.created_by || 'Unknown'}</span>
                        </div>
                        ${address.updated_at ? `
                        <div class="detail-item">
                            <label>Last Updated:</label>
                            <span>${new Date(address.updated_at).toLocaleString()}</span>
                        </div>
                        ` : ''}
                        ${address.updated_by ? `
                        <div class="detail-item">
                            <label>Updated By:</label>
                            <span>${address.updated_by}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>

                ${address.notes ? `
                <div class="detail-section">
                    <h4>Notes</h4>
                    <div class="notes-content">
                        ${address.notes}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    },

    // Activities tab content (following person/vehicle detail style)
    activitiesTab: (address) => {
        return `
            <div class="detail-sections">
                <div class="detail-section">
                    <div class="section-header">
                        <h4>Address Activities</h4>
                        <button class="primary-button" data-action="add-address-activity" data-address-id="${address.id}">
                            <span class="button-icon">+</span>
                            Add Activity
                        </button>
                    </div>
                    <div class="records-list activities-list" id="activities-list">
                        <div class="loading">Loading activities...</div>
                    </div>
                </div>
            </div>
        `;
    },

    // Activity item template (matching main records list styling)
    activityItem: (activity) => {
        return `
            <div class="record-item activity-item" data-activity-id="${activity.id}">
                <div class="record-header">
                    ${activity.title || activity.activity_type}
                    <span class="activity-type-badge">${activity.activity_type}</span>
                </div>
                <div class="record-details">
                    ${activity.description ? `<span class="activity-description">${activity.description}</span>` : ''}
                    ${activity.activity_date ? `<span class="activity-date">Date: ${new Date(activity.activity_date).toLocaleDateString()}</span>` : ''}
                    ${activity.activity_time ? `<span class="activity-time">Time: ${activity.activity_time}</span>` : ''}
                    ${activity.staff_member ? `<span class="activity-staff">Staff: ${activity.staff_member}</span>` : ''}
                    ${activity.location_notes ? `<span class="activity-location">Location: ${activity.location_notes}</span>` : ''}
                </div>
                <div class="record-meta">
                    ID: ${activity.id} | Created: ${new Date(activity.created_at).toLocaleDateString()}
                    ${activity.created_by ? ` by ${activity.created_by}` : ''}
                </div>
                <div class="record-actions">
                    <button class="action-button edit-button" data-action="edit-address-activity" data-activity-id="${activity.id}" data-address-id="${activity.address_id}" title="Edit Activity">
                        Edit
                    </button>
                    <button class="action-button delete-button" data-action="delete-address-activity" data-activity-id="${activity.id}" data-address-id="${activity.address_id}" title="Delete Activity">
                        Delete
                    </button>
                </div>
            </div>
        `;
    },

    // No activities message
    noActivitiesMessage: () => {
        return `
            <div class="no-activities">
                <div class="no-activities-icon">📝</div>
                <div class="no-activities-text">No activities recorded yet.</div>
                <div class="no-activities-subtext">Click "Add Activity" to record the first activity for this address.</div>
            </div>
        `;
    }
};