/**
 * Narrative Render Manager
 * 
 * Handles all rendering operations for narrative entries including
 * container detection, entry formatting, and DOM updates.
 */

import { BaseManager } from '../../shared/base-manager.js';

export class NarrativeRenderManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, configManager) {
        super(dataManager, authManager, uiManager, configManager);
        
        this.narrativeManager = null;
    }

    // Set manager references
    setNarrativeManager(narrativeManager) {
        this.narrativeManager = narrativeManager;
    }

    // Main render method
    render(entries, currentIncidentId) {
        // Check if we're in a modal context first
        const inModal = document.querySelector('.modal-overlay');
        if (inModal) {
            return;
        }

        // Try multiple container IDs based on context
        const possibleContainerIds = [
            currentIncidentId ? `narrative-entries-${currentIncidentId}` : null,
            currentIncidentId ? `narrative-entries-edit-${currentIncidentId}` : null,
            'narrative-entries-form'
        ].filter(Boolean);

        let container = null;
        for (const containerId of possibleContainerIds) {
            container = document.getElementById(containerId);
            if (container) break;
        }

        if (!container) {
            console.warn('Narrative entries container not found. Tried:', possibleContainerIds);
            console.warn('Current incident ID:', currentIncidentId);
            console.warn('Available containers:', Array.from(document.querySelectorAll('[id*="narrative-entries"]')).map(el => ({ id: el.id, visible: el.offsetParent !== null })));
            
            const fallbackContainer = document.querySelector('[id*="narrative-entries"]');
            if (fallbackContainer) {
                console.info('Using fallback container:', fallbackContainer.id);
                container = fallbackContainer;
            } else {
                return;
            }
        }

        if (!entries || entries.length === 0) {
            container.innerHTML = `
                <div class="no-entries-message">
                    [NO NARRATIVE ENTRIES YET]<br>
                    Click "Add Entry" to start documenting events and observations.
                </div>
            `;
            return;
        }

        // Sort entries by event_time (chronological order - oldest first)
        const sortedEntries = [...entries].sort((a, b) => {
            const timeA = new Date(a.event_time || a.timestamp);
            const timeB = new Date(b.event_time || b.timestamp);
            return timeA - timeB;
        });

        container.innerHTML = sortedEntries.map(entry => this.renderEntry(entry)).join('');
    }

    // Render a single narrative entry
    renderEntry(entry) {
        const eventTime = new Date(entry.event_time || entry.timestamp).toLocaleString();
        const createdTime = entry.timestamp && entry.event_time !== entry.timestamp ? 
            ` (logged: ${new Date(entry.timestamp).toLocaleString()})` : '';
        
        const entryTypeIcons = {
            'initial_observation': '👁️',
            'action_taken': '✅',
            'status_update': '🔄',
            'follow_up': '⏰',
            'contact_made': '📞',
            'note': '📝',
            'other': '📌'
        };
        
        const icon = entryTypeIcons[entry.entry_type] || '📝';
        const typeClass = `narrative-type-${entry.entry_type}`;
        
        return `
            <div class="narrative-entry" data-entry-id="${entry.id}">
                <div class="narrative-header">
                    <div class="narrative-meta">
                        <span class="narrative-type ${typeClass}">${icon} ${entry.entry_type.toUpperCase().replace('_', ' ')}</span>
                        <span class="narrative-timestamp">🕐 ${eventTime}${createdTime}</span>
                        <span class="narrative-user">by ${entry.user}</span>
                    </div>
                    <div class="narrative-actions">
                        <button class="action-btn small edit-entry-btn" 
                                onclick="window.app.narrativeManagement.editEntry('${entry.id}')"
                                title="Edit this narrative entry">
                            ✏️ Edit
                        </button>
                        <button class="action-btn small delete-entry-btn" 
                                onclick="window.app.narrativeManagement.deleteEntry('${entry.id}')"
                                title="Delete this narrative entry">
                            🗑️ Delete
                        </button>
                    </div>
                </div>
                <div class="narrative-content">
                    ${this.formatContent(entry.content)}
                </div>
            </div>
        `;
    }

    // Format content with basic HTML support
    formatContent(content) {
        if (!content) return '';
        
        // Escape HTML first to prevent XSS
        let formatted = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;');
        
        // Apply formatting transformations
        formatted = formatted
            // Convert line breaks to HTML
            .replace(/\n/g, '<br>')
            // Bold formatting (**text**)
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // Underline formatting (__text__)
            .replace(/__(.*?)__/g, '<u>$1</u>')
            // Italic formatting (*text*) - but not **bold**
            .replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em>$1</em>')
            // Bullet lists (• text)
            .replace(/^• (.+)$/gm, '<div class="bullet-item">• $1</div>')
            // Numbered lists (1. text)
            .replace(/^(\d+)\. (.+)$/gm, '<div class="number-item">$1. $2</div>');
        
        return formatted;
    }

    // Force re-render
    forceRender(entries, currentIncidentId) {
        // Clear any existing modals to ensure clean render
        const existingModals = document.querySelectorAll('.modal-overlay');
        existingModals.forEach(modal => {
            if (modal.id && modal.id.includes('narrative')) {
                modal.remove();
            }
        });

        // Wait a tick then render
        setTimeout(() => {
            this.render(entries, currentIncidentId);
        }, 10);
    }

    // Update specific entry in DOM
    updateEntry(entry) {
        const entryElement = document.querySelector(`[data-entry-id="${entry.id}"]`);
        if (entryElement) {
            entryElement.outerHTML = this.renderEntry(entry);
        }
    }

    // Remove entry from DOM
    removeEntry(entryId) {
        const entryElement = document.querySelector(`[data-entry-id="${entryId}"]`);
        if (entryElement) {
            entryElement.remove();
        }
    }

    // Check if container exists
    hasContainer(currentIncidentId) {
        const possibleContainerIds = [
            currentIncidentId ? `narrative-entries-${currentIncidentId}` : null,
            currentIncidentId ? `narrative-entries-edit-${currentIncidentId}` : null,
            'narrative-entries-form'
        ].filter(Boolean);

        for (const containerId of possibleContainerIds) {
            if (document.getElementById(containerId)) {
                return true;
            }
        }

        return false;
    }

    // Get active container
    getActiveContainer(currentIncidentId) {
        const possibleContainerIds = [
            currentIncidentId ? `narrative-entries-${currentIncidentId}` : null,
            currentIncidentId ? `narrative-entries-edit-${currentIncidentId}` : null,
            'narrative-entries-form'
        ].filter(Boolean);

        for (const containerId of possibleContainerIds) {
            const container = document.getElementById(containerId);
            if (container) {
                return container;
            }
        }

        return null;
    }
}