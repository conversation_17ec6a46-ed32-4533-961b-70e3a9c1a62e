// ID Generator Utility - Eliminates duplication of ID generation logic
// Provides consistent ID generation patterns across all managers

export class IdGenerator {
    /**
     * Generate date-based ID with prefix (eliminates duplication from BikeManager/PropertyManager)
     * @param {string} prefix - Prefix for the ID (e.g., 'BIKE', 'PROP', 'ENC')
     * @param {Object} options - Generation options
     * @returns {string} Generated ID
     */
    static generateDateBasedId(prefix, options = {}) {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        
        // Different suffix strategies
        let suffix;
        if (options.timeBasedSuffix) {
            // Use last 4 digits of timestamp (BikeManager/PropertyManager pattern)
            suffix = now.getTime().toString().slice(-4);
        } else if (options.randomSuffix) {
            // Use random suffix
            suffix = Math.random().toString(36).substr(2, 4).toUpperCase();
        } else {
            // Use time components (hour + minute + second)
            const hour = String(now.getHours()).padStart(2, '0');
            const minute = String(now.getMinutes()).padStart(2, '0');
            const second = String(now.getSeconds()).padStart(2, '0');
            suffix = `${hour}${minute}${second}`;
        }
        
        return `${prefix}-${year}${month}${day}-${suffix}`;
    }

    /**
     * Generate base36 ID (EncampmentManager pattern)
     * @param {string} prefix - Optional prefix
     * @returns {string} Generated ID
     */
    static generateBase36Id(prefix = '') {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2);
        const id = timestamp + random;
        
        return prefix ? `${prefix}_${id}` : id;
    }

    /**
     * Generate UUID-style ID
     * @param {string} prefix - Optional prefix
     * @returns {string} Generated ID
     */
    static generateUuidStyleId(prefix = '') {
        const chars = '0123456789abcdef';
        let result = '';
        
        for (let i = 0; i < 32; i++) {
            if (i === 8 || i === 12 || i === 16 || i === 20) {
                result += '-';
            }
            result += chars[Math.floor(Math.random() * 16)];
        }
        
        return prefix ? `${prefix}_${result}` : result;
    }

    /**
     * Generate sequential ID with prefix (useful for incident numbers, etc.)
     * @param {string} prefix - Prefix for the ID
     * @param {number} lastNumber - Last used number (for increment)
     * @param {Object} options - Generation options
     * @returns {string} Generated ID
     */
    static generateSequentialId(prefix, lastNumber = 0, options = {}) {
        const nextNumber = lastNumber + 1;
        const paddedNumber = String(nextNumber).padStart(options.padLength || 4, '0');
        
        if (options.includeDate) {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            return `${prefix}-${year}${month}-${paddedNumber}`;
        }
        
        return `${prefix}-${paddedNumber}`;
    }

    /**
     * Generate ID for specific entity types (convenience methods)
     */
    static generateBikeId() {
        return this.generateDateBasedId('BIKE', { timeBasedSuffix: true });
    }

    static generatePropertyId() {
        return this.generateDateBasedId('PROP', { timeBasedSuffix: true });
    }

    static generateEncampmentId() {
        return this.generateBase36Id();
    }

    static generateIncidentId(lastIncidentNumber = 0) {
        return this.generateSequentialId('INC', lastIncidentNumber, { 
            includeDate: true, 
            padLength: 4 
        });
    }

    static generateActivityId() {
        return this.generateDateBasedId('ACT', { randomSuffix: true });
    }

    static generateReportId() {
        return this.generateDateBasedId('RPT', { timeBasedSuffix: true });
    }
}