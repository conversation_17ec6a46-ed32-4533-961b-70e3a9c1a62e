{"version": "1.3.0", "name": "steviretro", "dependencies": {"@azure/identity": {"version": "4.10.2", "resolved": "https://registry.npmjs.org/@azure/identity/-/identity-4.10.2.tgz", "overridden": false}, "@azure/storage-blob": {"version": "12.27.0", "resolved": "https://registry.npmjs.org/@azure/storage-blob/-/storage-blob-12.27.0.tgz", "overridden": false}, "@supabase/supabase-js": {"version": "2.52.0", "resolved": "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.52.0.tgz", "overridden": false}, "better-sqlite3": {"version": "12.2.0", "resolved": "https://registry.npmjs.org/better-sqlite3/-/better-sqlite3-12.2.0.tgz", "overridden": false}, "electron-builder": {"version": "26.0.12", "resolved": "https://registry.npmjs.org/electron-builder/-/electron-builder-26.0.12.tgz", "overridden": false}, "electron-reload": {"version": "2.0.0-alpha.1", "resolved": "https://registry.npmjs.org/electron-reload/-/electron-reload-2.0.0-alpha.1.tgz", "overridden": false}, "electron": {"version": "37.2.3", "resolved": "https://registry.npmjs.org/electron/-/electron-37.2.3.tgz", "overridden": false}, "fs-extra": {"version": "11.3.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-11.3.0.tgz", "overridden": false}, "node-fetch": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "overridden": false}, "semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "overridden": false}, "uuid": {"version": "11.1.0", "resolved": "https://registry.npmjs.org/uuid/-/uuid-11.1.0.tgz", "overridden": false}}}