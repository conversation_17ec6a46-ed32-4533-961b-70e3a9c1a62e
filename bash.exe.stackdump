Stack trace:
Frame         Function      Args
0007FFFF7F40  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF6E40) msys-2.0.dll+0x2118E
0007FFFF7F40  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF8218) msys-2.0.dll+0x69BA
0007FFFF7F40  0002100469F2 (00021028DF99, 0007FFFF7DF8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF7F40  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF7F40  00021006A545 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF8220  00021006B9A5 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF889020000 ntdll.dll
7FF887030000 KERNEL32.DLL
7FF886920000 KERNELBASE.dll
7FF887660000 USER32.dll
7FF886230000 win32u.dll
000210040000 msys-2.0.dll
7FF888B80000 GDI32.dll
7FF886260000 gdi32full.dll
7FF886D10000 msvcp_win.dll
7FF8867D0000 ucrtbase.dll
7FF887110000 advapi32.dll
7FF887E20000 msvcrt.dll
7FF887830000 sechost.dll
7FF887980000 RPCRT4.dll
7FF885770000 CRYPTBASE.DLL
7FF8863A0000 bcryptPrimitives.dll
7FF887AA0000 IMM32.DLL
