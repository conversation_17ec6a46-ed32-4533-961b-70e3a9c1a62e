# S.T.E.V.I Retro - Development Setup Guide

## Quick Start

1. **Use the launch script**: Simply run `launch.bat` - it will handle dependency installation automatically
2. **For clean setup**: Run `clean-install.bat` if you encounter dependency issues

## Common Issues and Solutions

### Issue: "Cannot find package semver" or similar dependency errors

**Cause**: Incomplete or corrupted node_modules directory, often due to file locking during npm install

**Solutions**:
1. Run `clean-install.bat` for a complete clean installation
2. Run `install-missing-deps.bat` to install specific missing packages
3. Manually install globally and copy: `npm install -g semver` then copy to local node_modules

### Issue: "EBUSY: resource busy or locked" during npm install

**Cause**: Electron processes holding file locks on node_modules

**Solutions**:
1. Close all Electron applications
2. Run `taskkill /f /im electron.exe` to force-close Electron processes
3. Wait a few seconds and retry installation
4. Use the `clean-install.bat` script which handles this automatically

### Issue: Package-lock.json conflicts between machines

**Solution**: The .gitignore now excludes package-lock.json to prevent cross-machine conflicts

## Development Environment Best Practices

### 1. Prevent File Locking
- Always close the application properly before running npm commands
- Use the provided scripts that handle process cleanup
- Avoid manual npm install when the app is running

### 2. Dependency Management
- Use `npm install --no-package-lock --no-save` for temporary installations
- The project excludes package-lock.json from git to prevent version conflicts
- Use global installations with local copying for problematic packages

### 3. Clean Development Setup
```bash
# Full clean setup (recommended for new machines)
clean-install.bat

# Quick launch (handles missing deps automatically)
launch.bat

# Install specific missing dependencies
install-missing-deps.bat
```

### 4. Troubleshooting Steps
1. Check if Node.js is installed: `node --version`
2. Check if npm is working: `npm --version`
3. Check if Electron is available: `npx electron --version`
4. If issues persist, run `clean-install.bat`

## File Structure for Dependencies

```
node_modules/
├── electron/          # Main Electron runtime
├── semver/            # Version comparison utility
├── @supabase/         # Supabase client library
├── better-sqlite3/    # SQLite database driver
├── fs-extra/          # Enhanced file system operations
├── node-fetch/        # HTTP client for Node.js
└── uuid/              # UUID generation utility
```

## Scripts Overview

- `launch.bat` - Main application launcher with dependency checking
- `clean-install.bat` - Complete clean installation with retry logic
- `install-missing-deps.bat` - Install specific missing packages
- `npm start` - Standard npm start (now uses npx electron)

## Notes for Team Development

1. **Never commit node_modules** - It's in .gitignore
2. **Never commit package-lock.json** - It's in .gitignore to prevent conflicts
3. **Use the provided scripts** - They handle edge cases and file locking
4. **Report new dependency issues** - Update this guide if you encounter new problems

## Emergency Recovery

If all else fails:
1. Delete the entire `node_modules` directory manually
2. Delete `package-lock.json` if it exists
3. Run `npm cache clean --force`
4. Run `clean-install.bat`
