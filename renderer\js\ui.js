// UI Manager for S.T.E.V.I DOS Electron App
export class UIManager {
    constructor() {
        this.currentScreen = null;
        this.bootMessages = [
            'Initializing system...',
            'Loading IHARC protocols...',
            'Establishing secure connection...',
            'Checking authentication systems...',
            'Loading user interface...',
            'System ready.'
        ];
    }

    showScreen(screenName) {
        // Hide all screens
        const screens = document.querySelectorAll('.screen');
        screens.forEach(screen => screen.classList.remove('active'));

        // Show target screen
        const targetScreen = document.getElementById(`${screenName}-screen`);
        if (targetScreen) {
            targetScreen.classList.add('active');
            this.currentScreen = screenName;
        }
    }

    showFullScreenForm(title, fields, onSubmit, onCancel = null) {
        // Store the previous screen for navigation back
        this.previousScreen = this.currentScreen;

        // Show the form screen
        this.showScreen('form');

        // Update form title
        const formTitle = document.getElementById('form-title');
        if (formTitle) {
            formTitle.textContent = title;
        }

        // Update user info and datetime in form header
        this.updateFormHeader();

        // Generate form fields
        this.generateFormFields(fields);

        // Handle form submission
        const form = document.getElementById('fullscreen-form');
        const submitBtn = document.getElementById('form-submit');
        const cancelBtn = document.getElementById('form-cancel');

        // Clear any existing event listeners
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);

        const newSubmitBtn = document.getElementById('form-submit');
        const newCancelBtn = document.getElementById('form-cancel');

        const handleSubmit = (e) => {
            e.preventDefault();

            const formData = {};
            fields.forEach(field => {
                if (field.type === 'multi-select') {
                    // Handle multi-select checkboxes
                    const checkboxes = newForm.querySelectorAll(`[name="${field.name}"]:checked`);
                    formData[field.name] = Array.from(checkboxes).map(cb => cb.value);
                } else {
                    const element = newForm.querySelector(`[name="${field.name}"]`);
                    if (element) {
                        if (element.type === 'checkbox') {
                            formData[field.name] = element.checked;
                        } else {
                            formData[field.name] = element.value;
                        }
                    }
                }
            });

            if (onSubmit) {
                const result = onSubmit(formData);
                // If onSubmit returns a promise, handle it
                if (result && typeof result.then === 'function') {
                    result.then(() => {
                        this.returnToPreviousScreen();
                    }).catch((error) => {
                        console.error('Form submission error:', error);
                        // Stay on form screen if there's an error
                    });
                } else {
                    this.returnToPreviousScreen();
                }
            } else {
                this.returnToPreviousScreen();
            }
        };

        const handleCancel = () => {
            if (onCancel) {
                onCancel();
            }
            this.returnToPreviousScreen();
        };

        // Add event listeners
        newForm.addEventListener('submit', handleSubmit);
        newSubmitBtn.addEventListener('click', handleSubmit);
        newCancelBtn.addEventListener('click', handleCancel);

        // Handle ESC key for cancel
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                handleCancel();
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);

        // Initialize map fields after form is populated
        this.initializeMapFields(fields, document.getElementById('form-screen'));

        // Focus first input
        const firstInput = newForm.querySelector('input, select, textarea');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }

    returnToPreviousScreen() {
        if (this.previousScreen) {
            this.showScreen(this.previousScreen);
            this.previousScreen = null;
        } else {
            this.showScreen('main');
        }
    }

    updateFormHeader() {
        // Update user info in form header
        const userInfo = document.getElementById('user-info');
        const formUserInfo = document.getElementById('form-user-info');
        if (userInfo && formUserInfo) {
            formUserInfo.textContent = userInfo.textContent;
        }

        // Update datetime in form header
        const datetime = document.getElementById('datetime');
        const formDatetime = document.getElementById('form-datetime');
        if (datetime && formDatetime) {
            formDatetime.textContent = datetime.textContent;
        }

        // Update network status in form
        const networkIndicator = document.getElementById('network-indicator');
        const formNetworkIndicator = document.getElementById('form-network-indicator');
        const networkText = document.getElementById('network-text');
        const formNetworkText = document.getElementById('form-network-text');

        if (networkIndicator && formNetworkIndicator) {
            formNetworkIndicator.className = networkIndicator.className;
        }
        if (networkText && formNetworkText) {
            formNetworkText.textContent = networkText.textContent;
        }
    }

    generateFormFields(fields) {
        const container = document.getElementById('form-fields-container');
        if (!container) return;

        container.innerHTML = '';

        fields.forEach(field => {
            const fieldType = field.type || 'text';
            const required = field.required ? 'required' : '';
            // Use defaultValue if value is not set, and handle both field.value and field.defaultValue
            const value = field.value || field.defaultValue || '';

            let inputHTML = '';

            if (fieldType === 'select') {
                const options = field.options || [];
                const optionsHTML = options.map(option => {
                    const optionValue = typeof option === 'object' ? option.value : option;
                    const optionLabel = typeof option === 'object' ? option.label : option;
                    const isDisabled = typeof option === 'object' && option.disabled ? 'disabled' : '';
                    const isHeader = typeof option === 'object' && option.isHeader;

                    // Check for selected state: either from field value/defaultValue or option.selected property
                    let selected = '';
                    if (value === optionValue) {
                        selected = 'selected';
                    } else if (typeof option === 'object' && option.selected && !value) {
                        selected = 'selected';
                    }

                    const optionClass = isHeader ? 'class="option-header"' : '';
                    return `<option value="${optionValue}" ${selected} ${isDisabled} ${optionClass}>${optionLabel}</option>`;
                }).join('');

                inputHTML = `<select name="${field.name}" ${required}>${optionsHTML}</select>`;
            } else if (fieldType === 'textarea') {
                inputHTML = `<textarea name="${field.name}" ${required} placeholder="${field.placeholder || ''}">${value}</textarea>`;
            } else if (fieldType === 'checkbox') {
                const checked = value ? 'checked' : '';
                inputHTML = `
                    <div class="checkbox-group">
                        <input type="checkbox" name="${field.name}" ${checked} ${required}>
                        <label>${field.label}</label>
                    </div>
                `;
            } else if (fieldType === 'multi-select') {
                const options = field.options || [];
                const selectedValues = Array.isArray(value) ? value : [];

                const optionsHTML = options.map(option => {
                    const optionValue = typeof option === 'object' ? option.value : option;
                    const optionLabel = typeof option === 'object' ? option.label : option;
                    const checked = selectedValues.includes(optionValue) ? 'checked' : '';

                    return `
                        <div class="multi-select-option">
                            <input type="checkbox" name="${field.name}" value="${optionValue}" ${checked}>
                            <label>${optionLabel}</label>
                        </div>
                    `;
                }).join('');

                inputHTML = `<div class="multi-select-group">${optionsHTML}</div>`;
            } else {
                // Default to text input for other types
                inputHTML = `<input type="${fieldType}" name="${field.name}" value="${value}" ${required} placeholder="${field.placeholder || ''}">`;
            }

            const fieldHTML = `
                <div class="form-group">
                    <label ${required ? 'class="required"' : ''}>${field.label}</label>
                    ${inputHTML}
                </div>
            `;

            container.insertAdjacentHTML('beforeend', fieldHTML);
        });
    }

    async showInitialBoot() {
        // Show boot screen initially
        this.showScreen('boot');

        // Update version display dynamically
        try {
            const { ipcRenderer } = window.require ? window.require('electron') : {};
            if (ipcRenderer) {
                const currentVersion = await ipcRenderer.invoke('app-version');
                const versionDisplay = document.getElementById('version-display');
                if (versionDisplay) {
                    versionDisplay.textContent = `IHARC Field Staff Terminal v${currentVersion}`;
                }
            }
        } catch (error) {
            console.error('Error getting app version:', error);
        }

        const progressBar = document.getElementById('boot-progress');
        const messagesDiv = document.getElementById('boot-messages');
        
        // Clear existing messages
        messagesDiv.innerHTML = '';

        // Simple initial boot messages
        const initialMessages = [
            'System initializing...',
            'Loading authentication systems...',
            'Preparing login interface...'
        ];

        for (let i = 0; i < initialMessages.length; i++) {
            // Add system message
            const messageDiv = document.createElement('div');
            messageDiv.textContent = initialMessages[i];
            messagesDiv.appendChild(messageDiv);

            // Update progress bar
            if (progressBar) {
                const progress = ((i + 1) / initialMessages.length) * 100;
                progressBar.style.width = `${progress}%`;
            }

            // Wait between messages
            await new Promise(resolve => setTimeout(resolve, 800));
        }

        // Short delay before transitioning to login
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    async showBootSequence() {
        this.showScreen('boot');

        // Update version display dynamically
        try {
            const { ipcRenderer } = window.require ? window.require('electron') : {};
            if (ipcRenderer) {
                const currentVersion = await ipcRenderer.invoke('app-version');
                const versionDisplay = document.getElementById('version-display');
                if (versionDisplay) {
                    versionDisplay.textContent = `IHARC Field Staff Terminal v${currentVersion}`;
                }
            }
        } catch (error) {
            console.error('Error getting app version:', error);
        }

        const progressBar = document.getElementById('boot-progress');
        const messagesDiv = document.getElementById('boot-messages');
        const journeyScene = document.getElementById('journey-scene');
        const journeyText = document.getElementById('journey-text');

        // Clear existing messages
        messagesDiv.innerHTML = '';

        // Journey stages with corresponding messages
        const journeyStages = [
            { stage: 'stage-1', text: 'Starting on the streets...', message: 'Loading user data...' },
            { stage: 'stage-2', text: 'Finding temporary shelter...', message: 'Synchronizing records...' },
            { stage: 'stage-3', text: 'Moving to transitional housing...', message: 'Preparing incident forms...' },
            { stage: 'stage-4', text: 'Securing permanent housing...', message: 'Loading field protocols...' },
            { stage: 'stage-5', text: 'Journey complete - housed and stable!', message: 'Activating terminal interface...' }
        ];

        const totalSteps = journeyStages.length;

        for (let i = 0; i < totalSteps; i++) {
            const currentStage = journeyStages[i];

            // Update journey animation
            if (journeyScene) {
                journeyScene.className = `journey-scene ${currentStage.stage}`;
            }
            if (journeyText) {
                journeyText.textContent = currentStage.text;
            }

            // Add system message
            const messageDiv = document.createElement('div');
            messageDiv.textContent = currentStage.message;
            messagesDiv.appendChild(messageDiv);

            // Update progress bar
            const progress = ((i + 1) / totalSteps) * 100;
            progressBar.style.width = `${progress}%`;

            // Wait for animation to complete
            await this.delay(1500);
        }

        // Final celebration
        if (journeyText) {
            journeyText.textContent = '🎉 Welcome to S.T.E.V.I Retro - Supporting every step of the journey! 🎉';
        }
        await this.delay(2000);
    }

    async showBootSequenceWithDataLoading() {
        this.showScreen('boot');

        // Update version display dynamically
        try {
            const { ipcRenderer } = window.require ? window.require('electron') : {};
            if (ipcRenderer) {
                const currentVersion = await ipcRenderer.invoke('app-version');
                const versionDisplay = document.getElementById('version-display');
                if (versionDisplay) {
                    versionDisplay.textContent = `IHARC Field Staff Terminal v${currentVersion}`;
                }
            }
        } catch (error) {
            console.error('Error getting app version:', error);
        }

        const progressBar = document.getElementById('boot-progress');
        const messagesDiv = document.getElementById('boot-messages');
        const journeyScene = document.getElementById('journey-scene');
        const journeyText = document.getElementById('journey-text');

        // Clear existing messages
        messagesDiv.innerHTML = '';

        // Journey stages with data loading messages
        const journeyStages = [
            { stage: 'stage-1', text: 'Starting on the streets...', message: 'Connecting to IHARC database...' },
            { stage: 'stage-2', text: 'Finding temporary shelter...', message: 'Loading client records...' },
            { stage: 'stage-3', text: 'Moving to transitional housing...', message: 'Synchronizing incident data...' },
            { stage: 'stage-4', text: 'Securing permanent housing...', message: 'Preparing field interface...' },
            { stage: 'stage-5', text: 'Journey complete - housed and stable!', message: 'System ready for field work...' }
        ];

        const totalSteps = journeyStages.length;

        for (let i = 0; i < totalSteps; i++) {
            const currentStage = journeyStages[i];

            // Update journey animation
            if (journeyScene) {
                journeyScene.className = `journey-scene ${currentStage.stage}`;
            }
            if (journeyText) {
                journeyText.textContent = currentStage.text;
            }

            // Add system message
            const messageDiv = document.createElement('div');
            messageDiv.textContent = currentStage.message;
            messagesDiv.appendChild(messageDiv);

            // Update progress bar
            const progress = ((i + 1) / totalSteps) * 100;
            progressBar.style.width = `${progress}%`;

            // Wait for animation to complete (shorter delays since data is loading)
            await this.delay(1200);
        }

        // Final message
        if (journeyText) {
            journeyText.textContent = '🎉 Welcome to S.T.E.V.I Retro - Ready to support every step of the journey! 🎉';
        }
        await this.delay(1500);
    }

    setStatus(message, type = 'info') {
        try {
            const statusElement = document.getElementById('status-message');
            if (statusElement) {
                statusElement.textContent = message;

                // Remove existing type classes
                statusElement.classList.remove('status-error', 'status-warning', 'status-success');

                // Add type class
                if (type !== 'info') {
                    statusElement.classList.add(`status-${type}`);
                }
            } else {
                console.log(`Status: ${message} (${type})`);
            }
        } catch (error) {
            console.error('Error setting status:', error);
            console.log(`Status: ${message} (${type})`);
        }
    }

    showStatus(message, type = 'info') {
        // For login screen status
        const loginStatus = document.getElementById('login-status');
        if (loginStatus && this.currentScreen === 'login') {
            loginStatus.textContent = message;
            loginStatus.className = `login-status ${type}`;
        } else {
            this.setStatus(message, type);
        }
    }

    showDialog(title, message, type = 'info') {
        // Create modal dialog
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay';

        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (dialog.parentNode) {
                dialog.remove();
            }
        }, 5000);
    }

    showConfirmDialog(title, message, onConfirm, onCancel = null) {
        // Support both callback and Promise patterns
        if (typeof onConfirm === 'function') {
            // Callback pattern (existing usage)
            return this._showConfirmDialogCallback(title, message, onConfirm, onCancel);
        } else {
            // Promise pattern (new admin usage)
            return this._showConfirmDialogPromise(title, message, onConfirm, onCancel);
        }
    }

    _showConfirmDialogCallback(title, message, onConfirm, onCancel = null) {
        // Create confirmation dialog
        const dialog = document.createElement('div');
        dialog.className = 'modal-overlay';

        dialog.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" id="confirm-cancel">Cancel</button>
                    <button class="primary-button" id="confirm-ok">Confirm</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);

        // Handle button clicks
        const cancelBtn = dialog.querySelector('#confirm-cancel');
        const confirmBtn = dialog.querySelector('#confirm-ok');

        cancelBtn.addEventListener('click', () => {
            dialog.remove();
            if (onCancel) onCancel();
        });

        confirmBtn.addEventListener('click', () => {
            dialog.remove();
            if (onConfirm) onConfirm();
        });

        // Focus the confirm button
        confirmBtn.focus();
    }

    _showConfirmDialogPromise(title, message, confirmText = 'Confirm', cancelText = 'Cancel') {
        return new Promise((resolve) => {
            // Create confirmation dialog
            const dialog = document.createElement('div');
            dialog.className = 'modal-overlay';

            dialog.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button class="secondary-button" id="confirm-cancel">${cancelText}</button>
                        <button class="primary-button" id="confirm-ok">${confirmText}</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            // Handle button clicks
            const cancelBtn = dialog.querySelector('#confirm-cancel');
            const confirmBtn = dialog.querySelector('#confirm-ok');

            cancelBtn.addEventListener('click', () => {
                dialog.remove();
                resolve(false);
            });

            confirmBtn.addEventListener('click', () => {
                dialog.remove();
                resolve(true);
            });

            // Focus the confirm button
            confirmBtn.focus();
        });
    }

    showForm(title, fields, onSubmit) {
        // Create form modal
        const formModal = document.createElement('div');
        formModal.className = 'modal-overlay';
        
        let fieldsHTML = '';
        fields.forEach(field => {
            const fieldType = field.type || 'text';
            const required = field.required ? 'required' : '';
            // Use defaultValue if value is not set, and handle both field.value and field.defaultValue
            const value = field.value || field.defaultValue || '';

            let inputHTML = '';

            if (fieldType === 'textarea') {
                inputHTML = `<textarea id="${field.name}" name="${field.name}" ${required}>${value}</textarea>`;
            } else if (fieldType === 'select') {
                const options = field.options || [];
                let optionsHTML = '<option value="">-- Select --</option>';
                options.forEach(option => {
                    const optionValue = typeof option === 'object' ? option.value : option;
                    const optionLabel = typeof option === 'object' ? option.label : option;
                    const isDisabled = typeof option === 'object' && option.disabled ? 'disabled' : '';
                    const isHeader = typeof option === 'object' && option.isHeader;

                    // Check for selected state: either from field value/defaultValue or option.selected property
                    let selected = '';
                    if (value === optionValue) {
                        selected = 'selected';
                    } else if (typeof option === 'object' && option.selected && !value) {
                        selected = 'selected';
                    }

                    const optionClass = isHeader ? 'class="option-header"' : '';
                    optionsHTML += `<option value="${optionValue}" ${selected} ${isDisabled} ${optionClass}>${optionLabel}</option>`;
                });
                inputHTML = `<select id="${field.name}" name="${field.name}" ${required}>${optionsHTML}</select>`;
            } else if (fieldType === 'checkbox') {
                const checked = value === true || value === 'true' || value === 'on' ? 'checked' : '';
                inputHTML = `<input type="checkbox" id="${field.name}" name="${field.name}" ${checked} ${required}>`;
            } else if (fieldType === 'multi-select') {
                // Multi-select checkboxes for arrays like services_tags
                const options = field.options || [];
                const selectedValues = Array.isArray(value) ? value : (value ? [value] : []);
                let checkboxesHTML = '';
                options.forEach(option => {
                    const optionValue = typeof option === 'object' ? option.value : option;
                    const optionLabel = typeof option === 'object' ? option.label : option;
                    const checked = selectedValues.includes(optionValue) ? 'checked' : '';
                    checkboxesHTML += `
                        <div class="checkbox-option">
                            <input type="checkbox" id="${field.name}_${optionValue}" name="${field.name}" value="${optionValue}" ${checked}>
                            <label for="${field.name}_${optionValue}">${optionLabel}</label>
                        </div>
                    `;
                });
                inputHTML = `<div class="multi-select-container">${checkboxesHTML}</div>`;
            } else if (fieldType === 'date') {
                // Custom date input with proper validation and navigation
                const dateValue = value ? this.formatDateForInput(value) : '';
                inputHTML = this.createCustomDateInput(field.name, dateValue, required);
            } else if (fieldType === 'map') {
                // Interactive map for coordinate selection
                const mapId = `map-${field.name}-${Date.now()}`;
                inputHTML = `
                    <div class="map-field-container">
                        <input type="text" id="${field.name}" name="${field.name}" value="${value}" placeholder="${field.placeholder || 'Click map to set coordinates'}" readonly>
                        <div class="map-widget" id="${mapId}" style="width: 100%; height: 300px; margin-top: 10px; border: 1px solid #ccc;">
                            <div class="map-loading">Click to load map...</div>
                        </div>
                        <div class="map-instructions">
                            <small>Click on the map to set coordinates. Current: <span id="${field.name}-display">${value || 'None set'}</span></small>
                        </div>
                    </div>
                `;
            } else {
                inputHTML = `<input type="${fieldType}" id="${field.name}" name="${field.name}" value="${value}" ${required}>`;
            }

            fieldsHTML += `
                <div class="form-field">
                    <label for="${field.name}">${field.label}:</label>
                    ${inputHTML}
                </div>
            `;
        });
        
        formModal.innerHTML = `
            <div class="modal-dialog form-modal">
                <div class="modal-header">
                    <h3>${title}</h3>
                </div>
                <div class="modal-body">
                    <form id="dynamic-form">
                        ${fieldsHTML}
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button type="submit" class="primary-button" id="form-submit">Submit</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(formModal);

        // Initialize map fields after modal is added to DOM
        this.initializeMapFields(fields, formModal);

        // Handle form submission
        const form = formModal.querySelector('#dynamic-form');
        const submitBtn = formModal.querySelector('#form-submit');
        
        const handleSubmit = (e) => {
            e.preventDefault();
            
            const formData = {};
            fields.forEach(field => {
                if (field.type === 'multi-select') {
                    // Handle multi-select checkboxes
                    const checkboxes = form.querySelectorAll(`[name="${field.name}"]:checked`);
                    formData[field.name] = Array.from(checkboxes).map(cb => cb.value);
                } else {
                    const element = form.querySelector(`[name="${field.name}"]`);
                    if (element) {
                        if (element.type === 'checkbox') {
                            formData[field.name] = element.checked;
                        } else {
                            formData[field.name] = element.value;
                        }
                    }
                }
            });
            
            if (onSubmit) {
                onSubmit(formData);
            }
            
            formModal.remove();
        };
        
        form.addEventListener('submit', handleSubmit);
        submitBtn.addEventListener('click', handleSubmit);

        // Set up custom date inputs
        this.setupCustomDateInputs(form);

        // Focus first input
        const firstInput = form.querySelector('input, textarea, .date-year');
        if (firstInput) {
            firstInput.focus();
        }
    }

    async initializeMapFields(fields, formModal) {
        // Find all map fields and initialize them
        const mapFields = fields.filter(field => field.type === 'map');

        for (const field of mapFields) {
            // Find the map widget by looking for elements with IDs starting with "map-fieldname-"
            const mapWidgets = formModal.querySelectorAll(`[id^="map-${field.name}-"]`);
            if (mapWidgets.length > 0) {
                await this.initializeMapWidget(mapWidgets[0], field, formModal);
            }
        }
    }

    async initializeMapWidget(mapWidget, field, formModal) {
        try {
            // Add click handler to load map
            mapWidget.addEventListener('click', async () => {
                if (mapWidget.classList.contains('map-loaded')) return;

                mapWidget.innerHTML = '<div class="map-loading">Loading Google Maps...</div>';

                // Load Google Maps API if not already loaded
                if (!window.google || !window.google.maps) {
                    await this.loadGoogleMapsAPI();
                }

                // Get current coordinates if any
                const coordInput = formModal.querySelector(`#${field.name}`);
                let initialLat = 43.9589; // Default to Cobourg
                let initialLng = -78.1648;

                if (coordInput.value) {
                    const coords = coordInput.value.split(',');
                    if (coords.length === 2) {
                        const lat = parseFloat(coords[0].trim());
                        const lng = parseFloat(coords[1].trim());
                        if (!isNaN(lat) && !isNaN(lng)) {
                            initialLat = lat;
                            initialLng = lng;
                        }
                    }
                }

                // Create map
                const map = new google.maps.Map(mapWidget, {
                    center: { lat: initialLat, lng: initialLng },
                    zoom: 15,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    styles: [
                        {
                            "featureType": "all",
                            "elementType": "all",
                            "stylers": [
                                { "invert_lightness": true },
                                { "saturation": -100 },
                                { "lightness": 33 },
                                { "gamma": 0.5 },
                                { "hue": "#ff0000" }
                            ]
                        }
                    ]
                });

                // Create draggable marker
                const marker = new google.maps.Marker({
                    position: { lat: initialLat, lng: initialLng },
                    map: map,
                    draggable: true,
                    title: 'Drag to set location',
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#ff6b35"/>
                                <circle cx="12" cy="9" r="2.5" fill="#ffffff"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(24, 24)
                    }
                });

                // Update coordinates when marker is moved
                const updateCoordinates = (position) => {
                    const lat = position.lat();
                    const lng = position.lng();
                    const coordString = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

                    coordInput.value = coordString;

                    const displayElement = formModal.querySelector(`#${field.name}-display`);
                    if (displayElement) {
                        displayElement.textContent = coordString;
                    }
                };

                // Add event listeners
                marker.addListener('dragend', () => {
                    updateCoordinates(marker.getPosition());
                });

                map.addListener('click', (event) => {
                    marker.setPosition(event.latLng);
                    updateCoordinates(event.latLng);
                });

                // Update initial coordinates display
                updateCoordinates(marker.getPosition());

                mapWidget.classList.add('map-loaded');

            }, { once: true }); // Only add the click handler once

        } catch (error) {
            console.error('Error initializing map widget:', error);
            mapWidget.innerHTML = `<div class="map-error">Failed to load map: ${error.message}</div>`;
        }
    }

    async loadGoogleMapsAPI() {
        // Check if Google Maps API is already loaded
        if (window.google && window.google.maps) {
            return true;
        }

        // Check if app instance exists and has the loadGoogleMapsAPI method
        if (window.app && typeof window.app.loadGoogleMapsAPI === 'function') {
            return await window.app.loadGoogleMapsAPI();
        }

        // Fallback: load directly (this should match the app's implementation)
        return new Promise(async (resolve, reject) => {
            if (window.google && window.google.maps) {
                resolve(true);
                return;
            }

            const script = document.createElement('script');
            script.onload = () => resolve(true);
            script.onerror = () => reject(new Error('Failed to load Google Maps API'));

            // Get API key from vault or config
            let apiKey = null;
            if (window.app && window.app.config && window.app.config.vaultManager) {
                try {
                    apiKey = await window.app.config.vaultManager.getSecret('google_api_key');
                } catch (error) {
                    console.warn('Could not get Google API key from vault:', error);
                }
            }

            if (!apiKey) {
                console.error('Google API key not available - Maps functionality will be limited');
                reject(new Error('Google API key not available'));
                return;
            }

            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;

            document.head.appendChild(script);
        });
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Utility methods for retro effects
    addScanlines() {
        if (!document.querySelector('.scanlines')) {
            const scanlines = document.createElement('div');
            scanlines.className = 'scanlines';
            document.body.appendChild(scanlines);
        }
    }

    removeScanlines() {
        const scanlines = document.querySelector('.scanlines');
        if (scanlines) {
            scanlines.remove();
        }
    }

    addCRTEffect() {
        document.body.classList.add('crt-effect');
    }

    removeCRTEffect() {
        document.body.classList.remove('crt-effect');
    }

    formatDateForInput(dateString) {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        } catch {
            return '';
        }
    }

    createCustomDateInput(fieldName, value, required) {
        const parts = value ? value.split('-') : ['', '', ''];
        const year = parts[0] || '';
        const month = parts[1] || '';
        const day = parts[2] || '';

        return `
            <div class="custom-date-input" data-field="${fieldName}">
                <input type="text"
                       class="date-year"
                       placeholder="YYYY"
                       maxlength="4"
                       value="${year}"
                       data-part="year">
                <span class="date-separator">-</span>
                <input type="text"
                       class="date-month"
                       placeholder="MM"
                       maxlength="2"
                       value="${month}"
                       data-part="month">
                <span class="date-separator">-</span>
                <input type="text"
                       class="date-day"
                       placeholder="DD"
                       maxlength="2"
                       value="${day}"
                       data-part="day">
                <input type="hidden"
                       id="${fieldName}"
                       name="${fieldName}"
                       value="${value}"
                       ${required}>
            </div>
        `;
    }

    setupCustomDateInputs(formElement) {
        const dateInputs = formElement.querySelectorAll('.custom-date-input');

        dateInputs.forEach(dateInput => {
            const fieldName = dateInput.dataset.field;
            const hiddenInput = dateInput.querySelector(`#${fieldName}`);
            const yearInput = dateInput.querySelector('.date-year');
            const monthInput = dateInput.querySelector('.date-month');
            const dayInput = dateInput.querySelector('.date-day');

            // Set up event handlers for each part
            [yearInput, monthInput, dayInput].forEach(input => {
                this.setupDatePartInput(input, dateInput, hiddenInput);
            });

            // Initial validation
            this.updateDateValue(dateInput, hiddenInput);
        });
    }

    setupDatePartInput(input, container, hiddenInput) {
        const part = input.dataset.part;

        // Handle input validation and formatting
        input.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, ''); // Only digits

            // Apply specific validation based on part
            if (part === 'year') {
                if (value.length > 4) {
                    value = value.substring(0, 4);
                }
                // Validate year range (1900-2100)
                if (value.length === 4) {
                    const year = parseInt(value);
                    if (year < 1900 || year > 2100) {
                        input.classList.add('invalid');
                    } else {
                        input.classList.remove('invalid');
                    }
                }
            } else if (part === 'month') {
                if (value.length > 2) {
                    value = value.substring(0, 2);
                }
                // Validate month (01-12)
                if (value.length === 2) {
                    const month = parseInt(value);
                    if (month < 1 || month > 12) {
                        value = '12';
                    }
                }
                if (value.length === 1 && parseInt(value) > 1) {
                    value = '0' + value;
                }
            } else if (part === 'day') {
                if (value.length > 2) {
                    value = value.substring(0, 2);
                }
                // Validate day (01-31)
                if (value.length === 2) {
                    const day = parseInt(value);
                    if (day < 1 || day > 31) {
                        value = '31';
                    }
                }
                if (value.length === 1 && parseInt(value) > 3) {
                    value = '0' + value;
                }
            }

            e.target.value = value;
            this.updateDateValue(container, hiddenInput);
        });

        // Handle navigation between fields
        input.addEventListener('keydown', (e) => {
            const currentValue = e.target.value;

            if (e.key === 'Backspace' && currentValue === '') {
                // Move to previous field when backspacing on empty field
                const prevInput = this.getPreviousDateInput(input, container);
                if (prevInput) {
                    e.preventDefault();
                    prevInput.focus();
                    prevInput.setSelectionRange(prevInput.value.length, prevInput.value.length);
                }
            } else if (e.key === 'ArrowLeft' && e.target.selectionStart === 0) {
                // Move to previous field with left arrow at start
                const prevInput = this.getPreviousDateInput(input, container);
                if (prevInput) {
                    e.preventDefault();
                    prevInput.focus();
                    prevInput.setSelectionRange(prevInput.value.length, prevInput.value.length);
                }
            } else if (e.key === 'ArrowRight' && e.target.selectionStart === currentValue.length) {
                // Move to next field with right arrow at end
                const nextInput = this.getNextDateInput(input, container);
                if (nextInput) {
                    e.preventDefault();
                    nextInput.focus();
                    nextInput.setSelectionRange(0, 0);
                }
            }
        });

        // Auto-advance to next field when max length reached
        input.addEventListener('input', (e) => {
            const maxLength = parseInt(e.target.maxLength);
            if (e.target.value.length === maxLength) {
                const nextInput = this.getNextDateInput(input, container);
                if (nextInput) {
                    nextInput.focus();
                    nextInput.select();
                }
            }
        });
    }

    getPreviousDateInput(currentInput, container) {
        const part = currentInput.dataset.part;
        if (part === 'month') return container.querySelector('.date-year');
        if (part === 'day') return container.querySelector('.date-month');
        return null;
    }

    getNextDateInput(currentInput, container) {
        const part = currentInput.dataset.part;
        if (part === 'year') return container.querySelector('.date-month');
        if (part === 'month') return container.querySelector('.date-day');
        return null;
    }

    updateDateValue(container, hiddenInput) {
        const yearInput = container.querySelector('.date-year');
        const monthInput = container.querySelector('.date-month');
        const dayInput = container.querySelector('.date-day');

        const year = yearInput.value.padStart(4, '0');
        const month = monthInput.value.padStart(2, '0');
        const day = dayInput.value.padStart(2, '0');

        // Only set value if all parts are filled
        if (year.length === 4 && month.length === 2 && day.length === 2) {
            const dateValue = `${year}-${month}-${day}`;

            // Validate the date
            const date = new Date(dateValue);
            if (date.getFullYear() == year &&
                (date.getMonth() + 1) == month &&
                date.getDate() == day) {
                hiddenInput.value = dateValue;
                container.classList.remove('invalid');
            } else {
                hiddenInput.value = '';
                container.classList.add('invalid');
            }
        } else {
            hiddenInput.value = '';
            container.classList.remove('invalid');
        }
    }
}
