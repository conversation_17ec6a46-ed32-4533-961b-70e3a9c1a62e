// Start Episode Wizard Templates
// 60-second wizard for creating new justice episodes

export const startEpisodeTemplates = {
    wizardForm(formData) {
        return `
            ${startEpisodeTemplates.inlineChargeFormStyles()}
            <form id="episode-wizard-form" class="episode-wizard">
                <div class="wizard-step active" data-step="1">
                    <h4>Episode Origin</h4>
                    
                    <div class="form-row">
                        <label for="origin">Origin Type:</label>
                        <select name="origin" id="origin" required>
                            <option value="ARREST" ${formData.origin === 'ARREST' ? 'selected' : ''}>Arrest</option>
                            <option value="SUMMONS" ${formData.origin === 'SUMMONS' ? 'selected' : ''}>Summons</option>
                            <option value="WARRANT" ${formData.origin === 'WARRANT' ? 'selected' : ''}>Warrant Execution</option>
                            <option value="VIOLATION" ${formData.origin === 'VIOLATION' ? 'selected' : ''}>Condition Violation</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <label for="origin_dt">Date & Time:</label>
                        <input type="datetime-local" name="origin_dt" id="origin_dt" 
                               value="${formData.origin_dt}" required>
                    </div>
                    
                    <div class="form-row">
                        <label for="origin_agency">Agency:</label>
                        <input type="text" name="origin_agency" id="origin_agency" 
                               value="${formData.origin_agency}" 
                               placeholder="e.g., Cobourg Police, OPP">
                    </div>
                    
                    <div class="form-row">
                        <label for="jurisdiction">Jurisdiction:</label>
                        <input type="text" name="jurisdiction" id="jurisdiction" 
                               value="${formData.jurisdiction}" 
                               placeholder="e.g., Cobourg, Northumberland County">
                    </div>
                    
                    <div id="held-for-bail-section" class="form-row" style="display: ${formData.origin === 'ARREST' ? 'block' : 'none'}">
                        <label>
                            <input type="checkbox" name="held_for_bail" id="held_for_bail" 
                                   ${formData.held_for_bail ? 'checked' : ''}>
                            Held for bail hearing
                        </label>
                    </div>
                </div>
                
                <div class="wizard-step" data-step="2">
                    <h4>Initial Charges</h4>
                    <p>Add any known charges at this time. You can add more charges later.</p>
                    
                    <div id="charges-container">
                        <!-- Charges will be populated here -->
                    </div>
                    
                    <!-- Inline Add Charge Form -->
                    <div id="add-charge-form" class="add-charge-form collapsed">
                        <div class="form-row">
                            <label for="charge-code">Charge Code:</label>
                            <input type="text" id="charge-code" placeholder="e.g., CC 266" class="charge-input">
                        </div>
                        
                        <div class="form-row">
                            <label for="charge-label">Description:</label>
                            <input type="text" id="charge-label" placeholder="e.g., Assault" required class="charge-input">
                        </div>
                        
                        <div class="form-row">
                            <label for="charge-severity">Severity:</label>
                            <select id="charge-severity" class="charge-input">
                                <option value="SUMM">Summary</option>
                                <option value="IND">Indictable</option>
                                <option value="HYBRID">Hybrid</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" id="cancel-charge-btn" class="secondary-button">
                                Cancel
                            </button>
                            <button type="button" id="save-charge-btn" class="primary-button">
                                <span class="button-icon">✓</span>
                                Save Charge
                            </button>
                        </div>
                    </div>
                    
                    <button type="button" id="add-charge-btn" class="secondary-button">
                        <span class="button-icon">+</span>
                        Add Charge
                    </button>
                </div>
                
                <div class="wizard-actions">
                    <button type="button" id="cancel-wizard-btn" class="secondary-button">
                        Cancel
                    </button>
                    <button type="button" id="create-episode-btn" class="primary-button">
                        <span class="button-icon">⚖️</span>
                        Create Episode
                    </button>
                </div>
            </form>
        `;
    },
    
    chargesList(charges) {
        if (!charges || charges.length === 0) {
            return '<div class="no-charges">No charges added yet.</div>';
        }
        
        return `
            <div class="charges-list">
                ${charges.map((charge, index) => `
                    <div class="charge-item">
                        <div class="charge-details">
                            ${charge.code ? `<div class="charge-code">${charge.code}</div>` : ''}
                            <div class="charge-label">${charge.label || 'Unlabeled charge'}</div>
                            <div class="charge-severity severity-${charge.severity?.toLowerCase() || 'other'}">
                                ${startEpisodeTemplates.formatSeverity(charge.severity)}
                            </div>
                        </div>
                        <button type="button" class="remove-charge-btn" data-index="${index}">
                            ×
                        </button>
                    </div>
                `).join('')}
            </div>
        `;
    },
    
    inlineChargeFormStyles() {
        return `
            <style>
                .add-charge-form {
                    margin: 1rem 0;
                    border: 1px solid #444;
                    border-radius: 4px;
                    background: #1a1a1a;
                    transition: all 0.3s ease;
                    overflow: hidden;
                }
                
                .add-charge-form.collapsed {
                    max-height: 0;
                    opacity: 0;
                    margin: 0;
                    border-width: 0;
                }
                
                .add-charge-form.expanded {
                    max-height: 400px;
                    opacity: 1;
                    padding: 1rem;
                }
                
                .add-charge-form .form-row {
                    margin-bottom: 1rem;
                }
                
                .add-charge-form .charge-input {
                    width: 100%;
                    padding: 0.5rem;
                    background: #2a2a2a;
                    border: 1px solid #555;
                    color: #e0e0e0;
                    border-radius: 3px;
                }
                
                .add-charge-form .form-actions {
                    display: flex;
                    gap: 0.5rem;
                    justify-content: flex-end;
                    margin-top: 1rem;
                    padding-top: 1rem;
                    border-top: 1px solid #444;
                }
            </style>
        `;
    },
    
    formatSeverity(severity) {
        const severities = {
            'SUMM': 'Summary',
            'IND': 'Indictable',
            'HYBRID': 'Hybrid',
            'OTHER': 'Other'
        };
        
        return severities[severity] || 'Other';
    }
};
