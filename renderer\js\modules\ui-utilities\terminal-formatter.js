/**
 * Terminal Formatter
 * 
 * Handles all terminal-style text formatting for the retro DOS interface.
 * Extracted from app.js formatPriorityTerminal() and formatTextForTerminal() methods.
 */

export class TerminalFormatter {
    
    /**
     * Format priority for terminal display
     * @param {string} priority - Priority level (high, medium, low)
     * @returns {string} Terminal-formatted priority string
     */
    static formatPriorityTerminal(priority) {
        switch(priority?.toLowerCase()) {
            case 'high': return '[H]';
            case 'medium': return '[M]';
            case 'low': return '[L]';
            default: return '[M]';
        }
    }

    /**
     * Format text for terminal display with word wrapping
     * @param {string} text - Text to format
     * @param {number} maxWidth - Maximum width per line (default: 49)
     * @returns {string} Terminal-formatted text with proper line breaks
     */
    static formatTextForTerminal(text, maxWidth = 49) {
        if (!text) return '│                                                   │<br>';

        const words = text.split(' ');
        const lines = [];
        let currentLine = '';

        words.forEach(word => {
            if ((currentLine + word).length <= maxWidth) {
                currentLine += (currentLine ? ' ' : '') + word;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    // Word is too long, truncate it
                    lines.push(word.substring(0, maxWidth));
                    currentLine = '';
                }
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        // Format each line with terminal borders and padding
        return lines.map(line => {
            const padding = ' '.repeat(maxWidth - line.length);
            return `│ ${line}${padding} │`;
        }).join('<br>') + '<br>';
    }

    /**
     * Instance methods for non-static usage
     */
    formatPriorityTerminal(priority) {
        return TerminalFormatter.formatPriorityTerminal(priority);
    }

    formatTextForTerminal(text, maxWidth = 49) {
        return TerminalFormatter.formatTextForTerminal(text, maxWidth);
    }
}