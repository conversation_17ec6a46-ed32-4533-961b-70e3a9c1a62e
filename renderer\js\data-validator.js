// Data Validator - Ensures data integrity before database operations
export class DataValidator {
    constructor() {
        this.validationRules = new Map();
        this.setupValidationRules();
    }

    setupValidationRules() {
        // People validation rules
        this.validationRules.set('people', {
            required: [],
            custom: [
                {
                    name: 'name_required',
                    message: 'Either first name or last name must be provided',
                    validator: (data) => {
                        // Check both possible field name formats (schema vs database)
                        const hasFirstName = (data.first_name && data.first_name.trim() !== '') || 
                                           (data['First Name'] && data['First Name'].trim() !== '');
                        const hasLastName = (data.last_name && data.last_name.trim() !== '') || 
                                          (data['Last Name'] && data['Last Name'].trim() !== '');
                        return hasFirstName || hasLastName;
                    }
                },
                {
                    name: 'email_format',
                    message: 'Email must be in valid format',
                    validator: (data) => {
                        if (!data.email) return true; // Email is optional
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        return emailRegex.test(data.email);
                    }
                },
                {
                    name: 'age_calculation',
                    message: 'Age must be calculated from date_of_birth if provided',
                    validator: (data) => {
                        if (!data.date_of_birth) return true;
                        const birthDate = new Date(data.date_of_birth);
                        const today = new Date();
                        const calculatedAge = Math.floor((today - birthDate) / (365.25 * 24 * 60 * 60 * 1000));
                        
                        // If age is provided, it should match calculated age
                        if (data.age !== undefined && data.age !== null) {
                            return Math.abs(data.age - calculatedAge) <= 1; // Allow 1 year difference
                        }
                        return true;
                    }
                }
            ]
        });

        // People Activities validation rules
        this.validationRules.set('people_activities', {
            required: ['person_id', 'activity_type', 'title', 'activity_date', 'staff_member', 'created_by'],
            custom: [
                {
                    name: 'person_id_numeric',
                    message: 'person_id must be a valid number',
                    validator: (data) => {
                        if (!data.person_id) return false;
                        const personId = parseInt(data.person_id);
                        return !isNaN(personId) && personId > 0;
                    }
                },
                {
                    name: 'activity_date_valid',
                    message: 'activity_date must be a valid date',
                    validator: (data) => {
                        if (!data.activity_date) return false;
                        const date = new Date(data.activity_date);
                        return !isNaN(date.getTime());
                    }
                },
                {
                    name: 'activity_date_not_future',
                    message: 'activity_date cannot be in the future',
                    validator: (data) => {
                        if (!data.activity_date) return true;
                        const activityDate = new Date(data.activity_date);
                        const today = new Date();
                        today.setHours(23, 59, 59, 999); // End of today
                        return activityDate <= today;
                    }
                }
            ]
        });

        // Supply Provisions validation rules
        this.validationRules.set('supply_provisions', {
            required: ['activity_id', 'item_id', 'quantity_provided'],
            custom: [
                {
                    name: 'quantity_positive',
                    message: 'quantity_provided must be greater than 0',
                    validator: (data) => {
                        const quantity = parseInt(data.quantity_provided);
                        return !isNaN(quantity) && quantity > 0;
                    }
                },
                {
                    name: 'uuid_format',
                    message: 'activity_id and item_id must be valid UUIDs',
                    validator: (data) => {
                        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

                        if (data.activity_id && !uuidRegex.test(data.activity_id)) return false;
                        if (data.item_id && !uuidRegex.test(data.item_id)) return false;
                        return true;
                    }
                }
            ]
        });

        // License Plates validation rules
        this.validationRules.set('license_plates', {
            required: ['plate_number'],
            custom: [
                {
                    name: 'plate_number_format',
                    message: 'Plate number must not be empty and should contain valid characters',
                    validator: (data) => {
                        if (!data.plate_number || typeof data.plate_number !== 'string') return false;
                        const plateNumber = data.plate_number.trim();
                        return plateNumber.length > 0 && plateNumber.length <= 20;
                    }
                },
                {
                    name: 'province_format',
                    message: 'Province must be a valid 2-letter code if provided',
                    validator: (data) => {
                        if (!data.province) return true; // Province is optional
                        const validProvinces = [
                            'AB', 'BC', 'MB', 'NB', 'NL', 'NS', 'ON', 'PE', 'QC', 'SK', 'NT', 'NU', 'YT', // Canada
                            'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY', 'DC' // USA
                        ];
                        return validProvinces.includes(data.province);
                    }
                },
                {
                    name: 'vehicle_year_range',
                    message: 'Vehicle year must be between 1900 and current year + 1 if provided',
                    validator: (data) => {
                        if (!data.vehicle_year) return true; // Year is optional
                        const year = parseInt(data.vehicle_year);
                        const currentYear = new Date().getFullYear();
                        return !isNaN(year) && year >= 1900 && year <= (currentYear + 1);
                    }
                }
            ]
        });

        // Incidents validation rules
        this.validationRules.set('incidents', {
            required: ['description'],
            custom: [
                {
                    name: 'incident_date_valid',
                    message: 'incident_date must be a valid date if provided',
                    validator: (data) => {
                        if (!data.incident_date) return true;
                        const date = new Date(data.incident_date);
                        return !isNaN(date.getTime());
                    }
                },
                {
                    name: 'priority_valid',
                    message: 'priority must be low, medium, or high',
                    validator: (data) => {
                        if (!data.priority) return true;
                        return ['low', 'medium', 'high'].includes(data.priority.toLowerCase());
                    }
                },
                {
                    name: 'scene_safety_valid',
                    message: 'scene_safety must be safe, caution, unsafe, or hazardous if provided',
                    validator: (data) => {
                        if (!data.scene_safety || data.scene_safety === '') return true;
                        return ['safe', 'caution', 'unsafe', 'hazardous'].includes(data.scene_safety);
                    }
                }
            ]
        });

        // Activity logs validation rules
        this.validationRules.set('activity_logs', {
            required: ['activity_type', 'description'],
            custom: [
                {
                    name: 'activity_type_valid',
                    message: 'activity_type must be a valid activity type',
                    validator: (data) => {
                        if (!data.activity_type) return false;
                        const validTypes = [
                            'incident_created',
                            'incident_updated',
                            'status_changed',
                            'assignment_changed',
                            'note_added'
                        ];
                        return validTypes.includes(data.activity_type);
                    }
                }
            ]
        });

    }

    // Main validation method
    async validate(tableName, data, operation = 'insert') {
        const errors = [];
        const warnings = [];

        const rules = this.validationRules.get(tableName);
        if (!rules) {
            warnings.push(`No validation rules defined for table: ${tableName}`);
            return { isValid: true, errors, warnings };
        }

        // Check required fields
        for (const field of rules.required) {
            if (data[field] === undefined || data[field] === null || data[field] === '') {
                errors.push(`Required field missing: ${field}`);
            }
        }

        // Run custom validation rules
        for (const rule of rules.custom) {
            try {
                if (!rule.validator(data)) {
                    errors.push(rule.message);
                }
            } catch (error) {
                errors.push(`Validation error in rule '${rule.name}': ${error.message}`);
            }
        }

        // Additional operation-specific validation
        if (operation === 'update' && !data.id) {
            errors.push('ID is required for update operations');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    // Validate and sanitize data before database operations
    async validateAndSanitize(tableName, data, operation = 'insert') {
        const validation = await this.validate(tableName, data, operation);
        
        if (!validation.isValid) {
            throw new Error(`Validation failed for ${tableName}: ${validation.errors.join(', ')}`);
        }

        // Log warnings if any
        if (validation.warnings.length > 0) {
            console.warn(`Validation warnings for ${tableName}:`, validation.warnings);
        }

        // Sanitize data
        const sanitizedData = this.sanitizeData(tableName, data);

        return {
            data: sanitizedData,
            warnings: validation.warnings
        };
    }

    // Sanitize data based on table schema
    sanitizeData(tableName, data) {
        const sanitized = { ...data };

        // Common sanitization
        Object.keys(sanitized).forEach(key => {
            const value = sanitized[key];
            
            // Trim string values
            if (typeof value === 'string') {
                sanitized[key] = value.trim();
                
                // Convert empty strings to null for optional fields
                if (sanitized[key] === '') {
                    sanitized[key] = null;
                }
            }
            
            // Convert string numbers to actual numbers where appropriate
            if (key.includes('_id') && typeof value === 'string' && !isNaN(value)) {
                sanitized[key] = parseInt(value);
            }
        });

        // Table-specific sanitization
        switch (tableName) {
            case 'people':
                // Ensure age is calculated from date_of_birth if provided
                if (sanitized.date_of_birth && !sanitized.age) {
                    const birthDate = new Date(sanitized.date_of_birth);
                    const today = new Date();
                    sanitized.age = Math.floor((today - birthDate) / (365.25 * 24 * 60 * 60 * 1000));
                }
                break;

            case 'people_activities':
                // Ensure person_id is a number
                if (sanitized.person_id) {
                    sanitized.person_id = parseInt(sanitized.person_id);
                }
                break;

            case 'supply_provisions':
                // Ensure quantity is a number
                if (sanitized.quantity_provided) {
                    sanitized.quantity_provided = parseInt(sanitized.quantity_provided);
                }
                break;
        }

        return sanitized;
    }

    // Check referential integrity before operations
    async checkReferentialIntegrity(tableName, data, dataManager) {
        const errors = [];

        switch (tableName) {
            case 'people_activities':
                if (data.person_id) {
                    try {
                        const person = await dataManager.get('people', data.person_id);
                        if (!person) {
                            errors.push(`Referenced person with ID ${data.person_id} does not exist`);
                        }
                    } catch (error) {
                        errors.push(`Could not verify person reference: ${error.message}`);
                    }
                }
                break;

            case 'supply_provisions':
                if (data.activity_id) {
                    try {
                        const activity = await dataManager.get('people_activities', data.activity_id);
                        if (!activity) {
                            errors.push(`Referenced activity with ID ${data.activity_id} does not exist`);
                        }
                    } catch (error) {
                        errors.push(`Could not verify activity reference: ${error.message}`);
                    }
                }
                
                if (data.item_id) {
                    try {
                        const item = await dataManager.get('items', data.item_id);
                        if (!item) {
                            errors.push(`Referenced item with ID ${data.item_id} does not exist`);
                        }
                    } catch (error) {
                        errors.push(`Could not verify item reference: ${error.message}`);
                    }
                }
                break;
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}
