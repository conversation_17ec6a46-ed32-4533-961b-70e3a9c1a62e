// Shared Module Index - Centralized exports for all shared utility classes
// Provides single import point for utility functions across all modules

// Base Classes
export { BaseManager } from './base-manager.js';
export { BaseCrudManager } from './base-crud-manager.js';
export { BaseCommand } from './base-commands.js';

// Utilities
export { IdGenerator } from './id-generator.js';
export { SearchUtility } from './search-utility.js';
export { StatsGenerator } from './stats-generator.js';

// Logging
export { Logger, createLogger } from './logger.js';

// Module System
export { ModuleInterface, FeatureModuleInterface, ServiceModuleInterface } from './module-interface.js';
export { ModuleRegistry, moduleRegistry } from './module-registry.js';