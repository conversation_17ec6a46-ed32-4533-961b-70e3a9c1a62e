/**
 * Bike Management Module - Main Entry Point
 * Handles bike registration and tracking functionality
 */

import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { BikeManager } from './bike-manager.js';
import { Logger } from '../../shared/logger.js';

export class BikeManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities = null, modalManagement = null) {
        super('BikeManagement', '1.0.0', [], ['bikes']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.logger = Logger.forModule('BikeManagement');

        // Initialize managers
        this.bikeManager = new BikeManager(dataManager, authManager, uiManager);
    }

    /**
     * Initialize the bike management module
     * @returns {Promise<void>}
     */
    async initialize() {
        this.logger.info('Initializing Bike Management module');
        this.initialized = true;
    }

    /**
     * Cleanup module resources
     * @returns {Promise<void>}
     */
    async cleanup() {
        this.logger.info('Cleaning up Bike Management module');
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // TODO: Implement bike management commands
        return new Map();
    }

    /**
     * Get module status
     * @returns {Object} Module status
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            initialized: this.initialized,
            uptime: Date.now() - this.startTime
        };
    }

    /**
     * Get workflows provided by this module
     * @returns {Object} Available workflows
     */
    getWorkflows() {
        return {
            registerBike: () => this.bikeManager.registerBike.bind(this.bikeManager),
            searchBikes: () => this.bikeManager.search.bind(this.bikeManager),
            getBikeById: () => this.bikeManager.getById.bind(this.bikeManager)
        };
    }

    /**
     * Get templates provided by this module
     * @returns {Object} Available templates
     */
    getTemplates() {
        return {
            // TODO: Add bike templates when available
        };
    }

    /**
     * Get API endpoints provided by this module
     * @returns {Object} Available API endpoints
     */
    getApiEndpoints() {
        return {
            // Bike management uses standard CRUD endpoints
        };
    }

    /**
     * Get statistics for this module
     * @returns {Promise<Object>} Module statistics
     */
    async getStatistics() {
        try {
            const totalBikes = await this.bikeManager.getAll();
            return {
                totalBikes: totalBikes.length,
                // TODO: Add more detailed statistics
            };
        } catch (error) {
            this.logger.error('Failed to get bike statistics', error);
            return {
                totalBikes: 0,
                error: error.message
            };
        }
    }

    // Expose manager methods for backward compatibility
    async registerBike(bikeData) {
        return await this.bikeManager.registerBike(bikeData);
    }

    async search(searchTerm) {
        return await this.bikeManager.search(searchTerm);
    }

    async getById(id) {
        return await this.bikeManager.getById(id);
    }

    async getAll() {
        return await this.bikeManager.getAll();
    }
}

// Export both for compatibility
export { BikeManager } from './bike-manager.js';
