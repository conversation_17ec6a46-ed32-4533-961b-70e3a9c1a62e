/**
 * Dialog Manager
 * 
 * Handles simple dialog and confirmation dialog creation.
 * Provides a consistent interface for user interactions.
 */

export class DialogManager {
    constructor(uiManager = null) {
        this.uiManager = uiManager;
    }

    /**
     * Show a simple dialog
     * @param {string} title - Dialog title
     * @param {string} content - Dialog content
     * @param {string} type - Dialog type (info, error, warning, success)
     * @returns {Promise} Promise that resolves when dialog is closed
     */
    async showDialog(title, content, type = 'info') {
        // Delegate to UIManager if available
        if (this.uiManager?.showDialog) {
            return this.uiManager.showDialog(title, content, type);
        }

        // Fallback implementation
        return new Promise((resolve) => {
            const dialogHTML = `
                <div class="dialog-container ${type}">
                    <div class="dialog-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="dialog-content">
                        <p>${content}</p>
                    </div>
                    <div class="dialog-actions">
                        <button id="dialog-ok" class="primary-button">OK</button>
                    </div>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'modal-overlay dialog-modal';
            modal.innerHTML = `<div class="modal-content">${dialogHTML}</div>`;

            document.body.appendChild(modal);

            const okButton = modal.querySelector('#dialog-ok');
            const closeDialog = () => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                resolve();
            };

            okButton.addEventListener('click', closeDialog);

            // Close on escape or outside click
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    document.removeEventListener('keydown', handleKeyDown);
                    closeDialog();
                }
            };

            document.addEventListener('keydown', handleKeyDown);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.removeEventListener('keydown', handleKeyDown);
                    closeDialog();
                }
            });

            // Focus the OK button
            setTimeout(() => okButton.focus(), 100);
        });
    }

    /**
     * Show a confirmation dialog
     * @param {string} message - Confirmation message
     * @param {string} title - Dialog title
     * @returns {Promise<boolean>} Promise that resolves to true if confirmed, false if cancelled
     */
    async showConfirmDialog(message, title = 'Confirm') {
        // Delegate to UIManager if available
        if (this.uiManager?.showConfirmDialog) {
            return this.uiManager.showConfirmDialog(message, title);
        }

        // Fallback implementation
        return new Promise((resolve) => {
            const dialogHTML = `
                <div class="dialog-container confirm">
                    <div class="dialog-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="dialog-content">
                        <p>${message}</p>
                    </div>
                    <div class="dialog-actions">
                        <button id="dialog-confirm" class="primary-button">Confirm</button>
                        <button id="dialog-cancel" class="secondary-button">Cancel</button>
                    </div>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'modal-overlay confirm-modal';
            modal.innerHTML = `<div class="modal-content">${dialogHTML}</div>`;

            document.body.appendChild(modal);

            const confirmButton = modal.querySelector('#dialog-confirm');
            const cancelButton = modal.querySelector('#dialog-cancel');

            const closeDialog = (result) => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                document.removeEventListener('keydown', handleKeyDown);
                resolve(result);
            };

            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    closeDialog(false);
                } else if (e.key === 'Enter') {
                    closeDialog(true);
                }
            };

            confirmButton.addEventListener('click', () => closeDialog(true));
            cancelButton.addEventListener('click', () => closeDialog(false));

            document.addEventListener('keydown', handleKeyDown);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeDialog(false);
                }
            });

            // Focus the confirm button
            setTimeout(() => confirmButton.focus(), 100);
        });
    }

    /**
     * Set the UI manager for delegation
     * @param {Object} uiManager - UI manager instance
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
    }

    /**
     * Show a custom dialog with multiple buttons
     * @param {Object} config - Dialog configuration
     * @param {string} config.title - Dialog title
     * @param {string} config.content - Dialog content
     * @param {Array} config.buttons - Array of button configurations
     * @param {string} config.type - Dialog type
     * @returns {Promise} Promise that resolves with the clicked button value
     */
    async showCustomDialog(config) {
        const { title, content, buttons = [], type = 'info' } = config;

        return new Promise((resolve) => {
            const buttonHTML = buttons.map((button, index) => 
                `<button id="dialog-btn-${index}" class="${button.className || 'secondary-button'}">${button.text}</button>`
            ).join('');

            const dialogHTML = `
                <div class="dialog-container ${type}">
                    <div class="dialog-header">
                        <h3>${title}</h3>
                    </div>
                    <div class="dialog-content">
                        ${content}
                    </div>
                    <div class="dialog-actions">
                        ${buttonHTML}
                    </div>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'modal-overlay custom-dialog-modal';
            modal.innerHTML = `<div class="modal-content">${dialogHTML}</div>`;

            document.body.appendChild(modal);

            const closeDialog = (result) => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
                document.removeEventListener('keydown', handleKeyDown);
                resolve(result);
            };

            // Setup button handlers
            buttons.forEach((button, index) => {
                const buttonElement = modal.querySelector(`#dialog-btn-${index}`);
                buttonElement.addEventListener('click', () => {
                    closeDialog(button.value || button.text);
                });
            });

            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    closeDialog(null);
                }
            };

            document.addEventListener('keydown', handleKeyDown);

            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeDialog(null);
                }
            });

            // Focus the first button
            const firstButton = modal.querySelector('.dialog-actions button');
            if (firstButton) {
                setTimeout(() => firstButton.focus(), 100);
            }
        });
    }
}