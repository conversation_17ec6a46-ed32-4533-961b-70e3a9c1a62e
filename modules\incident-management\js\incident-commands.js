/**
 * Incident Commands Module
 * Contains all incident-related command classes extracted from commands.js
 * Integrates with the new IncidentManagement module
 */

import { BaseManager } from '../../shared/base-manager.js';

// Base Command Class for incident commands
// Note: This extends the pattern from commands.js but doesn't inherit from it
// to avoid circular dependencies. The command system expects execute() method.
class BaseIncidentCommand {
    constructor(manager, incidentManagement) {
        this.manager = manager;
        this.auth = manager.auth;
        this.data = manager.data;
        this.ui = manager.ui;
        this.app = manager.app;
        this.schema = manager.app.schema;
        this.incidentManagement = incidentManagement;
    }

    async execute(args) {
        throw new Error('Command must implement execute method');
    }
}

/**
 * Search Incidents Command
 * Provides advanced incident search functionality
 */
export class SearchIncidentsCommand extends BaseIncidentCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showIncidentSearchInterface(resolve);
        });
    }

    showIncidentSearchInterface(resolve) {
        // Create search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>🔍 Search Incidents</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="incident-search-field">Search Field:</label>
                                <select id="incident-search-field" class="form-control">
                                    <option value="all">All Fields</option>
                                    <option value="incident_number">Incident Number</option>
                                    <option value="description">Description</option>
                                    <option value="location">Location</option>
                                    <option value="incident_type">Incident Type</option>
                                    <option value="people_involved">People Involved</option>
                                    <option value="status">Status</option>
                                    <option value="reported_by">Reported By</option>
                                    <option value="police_file_number">Police File Number</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="incident-search-query">Search Term:</label>
                                <input type="text" id="incident-search-query" class="form-control" placeholder="Enter search term...">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="incident-status-filter">Status Filter:</label>
                                <select id="incident-status-filter" class="form-control">
                                    <option value="all">All Statuses</option>
                                    <option value="open">Open</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="resolved">Resolved</option>
                                    <option value="closed">Closed</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="incident-date-from">Date From:</label>
                                <input type="date" id="incident-date-from" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="incident-date-to">Date To:</label>
                                <input type="date" id="incident-date-to" class="form-control">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="incident-search-submit" class="primary-button">🔍 Search Incidents</button>
                            <button id="incident-search-clear" class="secondary-button">Clear</button>
                        </div>
                    </div>
                    <div id="incident-search-results" class="search-results" style="display: none;">
                        <div class="results-header">
                            <h4>Search Results</h4>
                            <div class="results-count"></div>
                        </div>
                        <div class="results-container"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        // Get form elements
        const searchField = document.getElementById('incident-search-field');
        const searchQuery = document.getElementById('incident-search-query');
        const statusFilter = document.getElementById('incident-status-filter');
        const dateFrom = document.getElementById('incident-date-from');
        const dateTo = document.getElementById('incident-date-to');
        const searchSubmit = document.getElementById('incident-search-submit');
        const searchClear = document.getElementById('incident-search-clear');
        const searchResults = document.getElementById('incident-search-results');
        const resultsContainer = searchResults.querySelector('.results-container');
        const resultsCount = searchResults.querySelector('.results-count');

        // Handle search submission
        const performSearch = async () => {
            const field = searchField.value;
            const query = searchQuery.value.trim();
            const status = statusFilter.value;
            const fromDate = dateFrom.value;
            const toDate = dateTo.value;

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = '🔍 Searching...';

                // Use incident management module for search
                const results = await this.incidentManagement.searchManager.performIncidentSearch();
                this.displayIncidentResults(results, resultsContainer, resultsCount);
                searchResults.style.display = 'block';

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = '🔍 Search Incidents';
            }
        };

        // Handle clear
        const clearSearch = () => {
            searchQuery.value = '';
            statusFilter.value = 'all';
            dateFrom.value = '';
            dateTo.value = '';
            searchField.value = 'all';
            searchResults.style.display = 'none';
        };

        // Event listeners
        searchSubmit.addEventListener('click', performSearch);
        searchClear.addEventListener('click', clearSearch);

        // Enter key to search
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Auto-focus search input
        setTimeout(() => searchQuery.focus(), 100);

        // Handle modal close
        searchModal.addEventListener('click', (e) => {
            if (e.target === searchModal) {
                searchModal.remove();
                resolve(null);
            }
        });
    }

    displayIncidentResults(incidents, container, countElement) {
        if (!incidents || incidents.length === 0) {
            container.innerHTML = '<div class="no-results">No incidents found matching your search criteria.</div>';
            countElement.textContent = '0 incidents found';
            return;
        }

        countElement.textContent = `${incidents.length} incident${incidents.length === 1 ? '' : 's'} found`;

        const resultsHTML = incidents.map(incident => {
            const statusClass = this.getStatusClass(incident.status);
            const priorityClass = this.getPriorityClass(incident.priority);

            return `
                <div class="incident-result-item" data-incident-id="${incident.id}">
                    <div class="incident-result-header">
                        <div class="incident-number">${incident.incident_number || 'N/A'}</div>
                        <div class="incident-badges">
                            <span class="status-badge ${statusClass}">${incident.status || 'Open'}</span>
                            ${incident.priority ? `<span class="priority-badge ${priorityClass}">${incident.priority}</span>` : ''}
                        </div>
                    </div>
                    <div class="incident-result-content">
                        <div class="incident-description">
                            <strong>Description:</strong> ${incident.description || 'No description'}
                        </div>
                        <div class="incident-details">
                            <div class="detail-item">
                                <span class="detail-label">📅 Date:</span>
                                <span class="detail-value">${this.formatDate(incident.incident_date)} ${incident.incident_time || ''}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">📍 Location:</span>
                                <span class="detail-value">${incident.location || 'Not specified'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">🏷️ Type:</span>
                                <span class="detail-value">${incident.incident_type || 'Not specified'}</span>
                            </div>
                            ${incident.people_involved ? `
                                <div class="detail-item">
                                    <span class="detail-label">👥 People:</span>
                                    <span class="detail-value">${incident.people_involved}</span>
                                </div>
                            ` : ''}
                            ${incident.police_file_number ? `
                                <div class="detail-item">
                                    <span class="detail-label">🚔 Police File:</span>
                                    <span class="detail-value">${incident.police_file_number}</span>
                                </div>
                            ` : ''}
                            <div class="detail-item">
                                <span class="detail-label">👤 Reported By:</span>
                                <span class="detail-value">${incident.reported_by || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="incident-result-actions">
                        <button class="action-button view-incident" data-incident-id="${incident.id}">👁️ View Details</button>
                        <button class="action-button edit-incident" data-incident-id="${incident.id}">✏️ Edit</button>
                        ${incident.status !== 'closed' ? `<button class="action-button close-incident" data-incident-id="${incident.id}">✅ Close</button>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = resultsHTML;

        // Add event listeners for action buttons
        this.attachIncidentActionListeners(container);
    }

    attachIncidentActionListeners(container) {
        // View incident details
        container.querySelectorAll('.view-incident').forEach(button => {
            button.addEventListener('click', async (e) => {
                const incidentId = e.target.dataset.incidentId;
                await this.viewIncidentDetails(incidentId);
            });
        });

        // Edit incident
        container.querySelectorAll('.edit-incident').forEach(button => {
            button.addEventListener('click', async (e) => {
                const incidentId = e.target.dataset.incidentId;
                await this.editIncident(incidentId);
            });
        });

        // Close incident
        container.querySelectorAll('.close-incident').forEach(button => {
            button.addEventListener('click', async (e) => {
                const incidentId = e.target.dataset.incidentId;
                await this.closeIncident(incidentId);
            });
        });
    }

    async viewIncidentDetails(incidentId) {
        try {
            // Navigate to incidents tab if not already there
            const incidentsTab = document.querySelector('[data-tab="incidents"]');
            if (incidentsTab && !incidentsTab.classList.contains('active')) {
                window.app.loadTabContent('incidents');
                // Small delay to ensure tab loads before selecting incident
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Use the incident management module to select incident
            await this.incidentManagement.selectIncident(incidentId);

        } catch (error) {
            this.ui.showDialog('Error', `Failed to load incident details: ${error.message}`, 'error');
        }
    }

    async editIncident(incidentId) {
        try {
            // Use incident management module for editing workflow
            await this.incidentManagement.editIncidentWorkflow(incidentId);

        } catch (error) {
            this.ui.showDialog('Error', `Failed to load incident for editing: ${error.message}`, 'error');
        }
    }

    async closeIncident(incidentId) {
        try {
            // Use incident management module to close incident
            await this.incidentManagement.closeIncident(incidentId);

            // Remove the incident from the current view
            const incidentElement = document.querySelector(`[data-incident-id="${incidentId}"]`);
            if (incidentElement) {
                incidentElement.remove();
            }

        } catch (error) {
            this.ui.showDialog('Error', `Failed to close incident: ${error.message}`, 'error');
        }
    }

    getStatusClass(status) {
        const statusMap = {
            'open': 'status-open',
            'in_progress': 'status-progress',
            'resolved': 'status-resolved',
            'closed': 'status-closed'
        };
        return statusMap[status?.toLowerCase()] || 'status-open';
    }

    getPriorityClass(priority) {
        const priorityMap = {
            'low': 'priority-low',
            'medium': 'priority-medium',
            'high': 'priority-high',
            'urgent': 'priority-urgent'
        };
        return priorityMap[priority?.toLowerCase()] || 'priority-medium';
    }

    formatDate(dateString) {
        if (!dateString) return 'Not specified';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }
}

/**
 * Show Update Status Modal Command
 * Shows the incident status update modal
 */
export class ShowUpdateStatusModalCommand extends BaseIncidentCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const incidentId = args.incidentId || args[0];
                if (!incidentId) {
                    this.ui.showDialog('Error', 'Incident ID is required', 'error');
                    resolve(null);
                    return;
                }
                
                // Use incident management module
                await this.incidentManagement.showUpdateStatusModal(incidentId);
                resolve(true);
            } catch (error) {
                console.error('Error showing update status modal:', error);
                this.ui.showDialog('Error', `Failed to show update status modal: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

/**
 * Print Incident Report Command
 * Generates printable incident report
 */
export class PrintIncidentReportCommand extends BaseIncidentCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const incidentId = args.incidentId || args[0];
                if (!incidentId) {
                    this.ui.showDialog('Error', 'Incident ID is required for printing.', 'error');
                    resolve(null);
                    return;
                }
                
                const incident = await this.data.get('incidents', incidentId);
                if (!incident) {
                    this.ui.showDialog('Error', 'Incident not found.', 'error');
                    resolve(null);
                    return;
                }
                
                // For now, delegate to app method until we extract print functionality
                await this.app.showIncidentPrintPreview(incident);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load print preview: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

/**
 * Create Test Incident Command
 * Creates test incidents for development and testing
 */
export class CreateTestIncidentCommand extends BaseIncidentCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Creating test incident with map coordinates...', 'info');

            // Create test incident with Cobourg coordinates
            const testIncident = {
                incident_number: `TEST-${Date.now()}`,
                incident_type: 'Test Incident',
                location: 'Cobourg Police Station, 107 King St W, Cobourg, ON',
                coordinates: '43.9589, -78.1648', // Cobourg coordinates
                description: 'Test incident created for map functionality testing',
                status: 'active',
                priority: 'medium',
                reported_by: 'System Test',
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const result = await this.data.insert('incidents', testIncident);

            this.ui.showDialog(
                'Test Incident Created',
                `Test incident created successfully!\n\n` +
                `Incident Number: ${testIncident.incident_number}\n` +
                `Location: ${testIncident.location}\n` +
                `Coordinates: ${testIncident.coordinates}\n\n` +
                `To test the map:\n` +
                `1. Go to Dispatch screen\n` +
                `2. Click on the test incident\n` +
                `3. Click the "Map" tab\n` +
                `4. Verify the map loads with a marker\n\n` +
                `The incident will appear in the dispatch list.`,
                'success'
            );

            this.ui.setStatus('Test incident created successfully', 'success');

        } catch (error) {
            console.error('Error creating test incident:', error);
            this.ui.showDialog(
                'Error',
                `Failed to create test incident: ${error.message}\n\n` +
                `This might be due to database connection issues.`,
                'error'
            );
            this.ui.setStatus('Failed to create test incident', 'error');
        }
    }
}

/**
 * Edit Incident Command
 * Handles incident editing workflow
 */
export class EditIncidentCommand extends BaseIncidentCommand {
    async execute(args) {
        const incidentId = args.incidentId;
        if (!incidentId) {
            console.error('No incident ID provided for editing');
            return;
        }

        try {
            // Use the comprehensive editIncidentWorkflow method from the incident management module
            // Pass true to display the form in the current tab
            await this.incidentManagement.editIncidentWorkflow(incidentId, true);
        } catch (error) {
            console.error('Error starting incident edit:', error);
        }
    }
}

/**
 * Back to Incident Details Command
 * Handles navigation back to incident details from edit form
 */
export class BackToIncidentDetailsCommand extends BaseIncidentCommand {
    async execute(args) {
        const incidentId = args.incidentId;
        if (!incidentId) {
            console.error('No incident ID provided for navigation');
            return;
        }

        try {
            // Navigate back to incidents tab and select the incident
            if (window.app && window.app.loadTabContent && window.app.selectIncident) {
                await window.app.loadTabContent('incidents');
                // Small delay to ensure tab loads before selecting incident
                setTimeout(() => {
                    window.app.selectIncident(incidentId);
                }, 100);
            } else {
                console.error('App navigation methods not available');
            }
        } catch (error) {
            console.error('Error navigating back to incident details:', error);
        }
    }
}

/**
 * Incident Commands Factory
 * Creates incident command instances for the command system
 */
export class IncidentCommandsFactory {
    constructor(manager, incidentManagement) {
        this.manager = manager;
        this.incidentManagement = incidentManagement;
    }

    createCommands() {
        return new Map([
            ['search-incidents', new SearchIncidentsCommand(this.manager, this.incidentManagement)],
            ['show-update-status-modal', new ShowUpdateStatusModalCommand(this.manager, this.incidentManagement)],
            ['print-incident-report', new PrintIncidentReportCommand(this.manager, this.incidentManagement)],
            ['create-test-incident', new CreateTestIncidentCommand(this.manager, this.incidentManagement)],
            ['edit-incident', new EditIncidentCommand(this.manager, this.incidentManagement)],
            ['back-to-incident-details', new BackToIncidentDetailsCommand(this.manager, this.incidentManagement)]
        ]);
    }
}