/**
 * Person Activity Templates
 * Templates for displaying and managing person activities with address integration
 */

export const personActivityTemplates = {
    /**
     * Activity item template for displaying in lists
     */
    activityItem(activity) {
        const activityDate = activity.activity_date ? new Date(activity.activity_date).toLocaleDateString() : 'No date';
        const activityTime = activity.activity_time || '';
        const dateTimeDisplay = activityTime ? `${activityDate} at ${activityTime}` : activityDate;
        
        // Format address information if available
        const addressInfo = activity.address_id ? 
            `<div class="activity-address">
                <i class="fas fa-map-marker-alt"></i>
                <span>Address ID: ${activity.address_id}</span>
            </div>` : '';

        // Format location information
        const locationInfo = activity.location ? 
            `<div class="activity-location">
                <i class="fas fa-location-dot"></i>
                <span>${activity.location}</span>
            </div>` : '';

        // Format follow-up information
        const followUpInfo = activity.follow_up_required ? 
            `<div class="activity-follow-up">
                <i class="fas fa-calendar-check"></i>
                <span>Follow-up required${activity.follow_up_date ? ` by ${new Date(activity.follow_up_date).toLocaleDateString()}` : ''}</span>
            </div>` : '';

        // Format priority badge
        const priorityBadge = activity.priority ? 
            `<span class="priority-badge priority-${activity.priority.toLowerCase()}">${activity.priority}</span>` : '';

        // Format tags
        const tagsDisplay = activity.tags && activity.tags.length > 0 ? 
            `<div class="activity-tags">
                ${activity.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>` : '';

        return `
            <div class="activity-item" data-activity-id="${activity.id}">
                <div class="activity-header">
                    <div class="activity-title-section">
                        <h4 class="activity-title">${activity.title || activity.activity_type}</h4>
                        ${priorityBadge}
                    </div>
                    <div class="activity-meta">
                        <span class="activity-date">${dateTimeDisplay}</span>
                        <span class="activity-staff">by ${activity.staff_member}</span>
                    </div>
                </div>
                
                <div class="activity-content">
                    <div class="activity-type">
                        <strong>Type:</strong> ${activity.activity_type}
                    </div>
                    
                    ${activity.description ? `
                        <div class="activity-description">
                            <strong>Description:</strong> ${activity.description}
                        </div>
                    ` : ''}
                    
                    ${addressInfo}
                    ${locationInfo}
                    
                    ${activity.outcome ? `
                        <div class="activity-outcome">
                            <strong>Outcome:</strong> ${activity.outcome}
                        </div>
                    ` : ''}
                    
                    ${followUpInfo}
                    ${tagsDisplay}
                </div>
                
                <div class="activity-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editPersonActivity('${activity.id}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deletePersonActivity('${activity.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    },

    /**
     * No activities message
     */
    noActivitiesMessage() {
        return `
            <div class="no-activities-message">
                <div class="no-activities-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3>No Activities Found</h3>
                <p>No activities have been recorded for this person yet.</p>
                <button class="btn btn-primary" onclick="addPersonActivity()">
                    <i class="fas fa-plus"></i> Add First Activity
                </button>
            </div>
        `;
    },

    /**
     * Activities list container
     */
    activitiesListContainer(activities) {
        if (!activities || activities.length === 0) {
            return this.noActivitiesMessage();
        }

        return `
            <div class="activities-list">
                <div class="activities-header">
                    <h3>Activities (${activities.length})</h3>
                    <button class="btn btn-primary" onclick="addPersonActivity()">
                        <i class="fas fa-plus"></i> Add Activity
                    </button>
                </div>
                <div class="activities-items">
                    ${activities.map(activity => this.activityItem(activity)).join('')}
                </div>
            </div>
        `;
    },

    /**
     * Activity summary for person detail view
     */
    activitySummary(activities) {
        if (!activities || activities.length === 0) {
            return `
                <div class="activity-summary">
                    <h4>Recent Activities</h4>
                    <p class="text-muted">No activities recorded</p>
                </div>
            `;
        }

        const recentActivities = activities.slice(0, 3);
        const totalCount = activities.length;

        return `
            <div class="activity-summary">
                <h4>Recent Activities (${totalCount} total)</h4>
                <div class="recent-activities">
                    ${recentActivities.map(activity => `
                        <div class="recent-activity-item">
                            <div class="activity-info">
                                <strong>${activity.title || activity.activity_type}</strong>
                                <span class="activity-date">${new Date(activity.activity_date).toLocaleDateString()}</span>
                            </div>
                            ${activity.description ? `<div class="activity-brief">${activity.description.substring(0, 100)}${activity.description.length > 100 ? '...' : ''}</div>` : ''}
                        </div>
                    `).join('')}
                </div>
                ${totalCount > 3 ? `
                    <button class="btn btn-sm btn-outline-primary" onclick="viewAllPersonActivities()">
                        View All ${totalCount} Activities
                    </button>
                ` : ''}
            </div>
        `;
    },

    /**
     * Activity form template (if needed for custom forms)
     */
    activityForm(personId, activity = null) {
        const isEdit = !!activity;
        const formTitle = isEdit ? 'Edit Activity' : 'Add Activity';
        
        return `
            <div class="activity-form-container">
                <h3>${formTitle}</h3>
                <form id="person-activity-form" data-person-id="${personId}" ${isEdit ? `data-activity-id="${activity.id}"` : ''}>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="activity_type">Activity Type *</label>
                            <select class="form-control" id="activity_type" name="activity_type" required>
                                <option value="">Select activity type...</option>
                                <option value="Check-in" ${activity?.activity_type === 'Check-in' ? 'selected' : ''}>Check-in</option>
                                <option value="Outreach" ${activity?.activity_type === 'Outreach' ? 'selected' : ''}>Outreach</option>
                                <option value="Service Referral" ${activity?.activity_type === 'Service Referral' ? 'selected' : ''}>Service Referral</option>
                                <option value="Case Management" ${activity?.activity_type === 'Case Management' ? 'selected' : ''}>Case Management</option>
                                <option value="Follow-up" ${activity?.activity_type === 'Follow-up' ? 'selected' : ''}>Follow-up</option>
                                <option value="Other" ${activity?.activity_type === 'Other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="activity_date">Activity Date *</label>
                            <input type="date" class="form-control" id="activity_date" name="activity_date" 
                                   value="${activity?.activity_date || new Date().toISOString().split('T')[0]}" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="activity_time">Activity Time</label>
                            <input type="time" class="form-control" id="activity_time" name="activity_time" 
                                   value="${activity?.activity_time || ''}">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="address_id">Address Location</label>
                            <select class="form-control" id="address_id" name="address_id">
                                <option value="">No address selected</option>
                                <option value="CREATE_NEW">+ Create New Address</option>
                                <!-- Address options will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="Describe the activity...">${activity?.description || ''}</textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Location Notes</label>
                        <input type="text" class="form-control" id="location" name="location" 
                               value="${activity?.location || ''}" placeholder="Specific location details...">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="outcome">Outcome</label>
                            <textarea class="form-control" id="outcome" name="outcome" rows="2" 
                                      placeholder="What was the result?">${activity?.outcome || ''}</textarea>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="priority">Priority</label>
                            <select class="form-control" id="priority" name="priority">
                                <option value="">No priority set</option>
                                <option value="Low" ${activity?.priority === 'Low' ? 'selected' : ''}>Low</option>
                                <option value="Medium" ${activity?.priority === 'Medium' ? 'selected' : ''}>Medium</option>
                                <option value="High" ${activity?.priority === 'High' ? 'selected' : ''}>High</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="follow_up_required" name="follow_up_required" 
                                   ${activity?.follow_up_required ? 'checked' : ''}>
                            <label class="form-check-label" for="follow_up_required">
                                Follow-up required
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-group" id="follow_up_date_group" style="display: ${activity?.follow_up_required ? 'block' : 'none'};">
                        <label for="follow_up_date">Follow-up Date</label>
                        <input type="date" class="form-control" id="follow_up_date" name="follow_up_date" 
                               value="${activity?.follow_up_date || ''}">
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> ${isEdit ? 'Update' : 'Save'} Activity
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="cancelActivityForm()">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        `;
    }
};

// CSS styles for person activities (to be included in the main CSS)
export const personActivityStyles = `
    .activity-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 1rem;
        padding: 1rem;
        background: white;
        transition: box-shadow 0.2s;
    }
    
    .activity-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.75rem;
    }
    
    .activity-title-section {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .activity-title {
        margin: 0;
        color: #2c3e50;
        font-size: 1.1rem;
    }
    
    .activity-meta {
        text-align: right;
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .activity-content {
        margin-bottom: 1rem;
    }
    
    .activity-content > div {
        margin-bottom: 0.5rem;
    }
    
    .activity-address, .activity-location, .activity-follow-up {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #495057;
    }
    
    .activity-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        margin-top: 0.5rem;
    }
    
    .tag {
        background: #e9ecef;
        color: #495057;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
    }
    
    .priority-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .priority-low { background: #d4edda; color: #155724; }
    .priority-medium { background: #fff3cd; color: #856404; }
    .priority-high { background: #f8d7da; color: #721c24; }
    
    .activity-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
    }
    
    .no-activities-message {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }
    
    .no-activities-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .activities-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .activity-summary {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .recent-activity-item {
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .recent-activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.25rem;
    }
    
    .activity-brief {
        font-size: 0.875rem;
        color: #6c757d;
    }
`;
