// Vault Diagnostics Utility for S.T.E.V.I Retro
// This utility helps diagnose vault manager issues

export class VaultDiagnostics {
    constructor(app) {
        this.app = app;
    }

    /**
     * Run comprehensive vault diagnostics
     */
    async runDiagnostics() {
        console.log('🔍 Starting Vault Diagnostics...');
        
        const results = {
            timestamp: new Date().toISOString(),
            tests: [],
            summary: {
                passed: 0,
                failed: 0,
                warnings: 0
            }
        };

        // Test 1: Check if app is initialized
        results.tests.push(await this.testAppInitialization());
        
        // Test 2: Check Supabase client
        results.tests.push(await this.testSupabaseClient());
        
        // Test 3: Check vault manager initialization
        results.tests.push(await this.testVaultManagerInitialization());
        
        // Test 4: Check authentication status
        results.tests.push(await this.testAuthenticationStatus());
        
        // Test 5: Test RPC connectivity
        results.tests.push(await this.testRPCConnectivity());
        
        // Test 6: Test API key retrieval
        results.tests.push(await this.testAPIKeyRetrieval());
        
        // Calculate summary
        results.tests.forEach(test => {
            if (test.status === 'PASS') results.summary.passed++;
            else if (test.status === 'FAIL') results.summary.failed++;
            else if (test.status === 'WARN') results.summary.warnings++;
        });

        console.log('🔍 Vault Diagnostics Complete:', results);
        return results;
    }

    async testAppInitialization() {
        const test = {
            name: 'App Initialization',
            status: 'PASS',
            message: '',
            details: {}
        };

        try {
            test.details.appExists = !!this.app;
            test.details.configExists = !!this.app?.config;
            test.details.authExists = !!this.app?.auth;
            test.details.weatherExists = !!this.app?.weather;

            if (!this.app) {
                test.status = 'FAIL';
                test.message = 'App instance not available';
            } else if (!this.app.config) {
                test.status = 'FAIL';
                test.message = 'Config manager not available';
            } else {
                test.message = 'App components initialized successfully';
            }
        } catch (error) {
            test.status = 'FAIL';
            test.message = `App initialization test failed: ${error.message}`;
        }

        return test;
    }

    async testSupabaseClient() {
        const test = {
            name: 'Supabase Client',
            status: 'PASS',
            message: '',
            details: {}
        };

        try {
            const supabaseClient = this.app?.config?.supabaseClient || this.app?.auth?.supabase;
            
            test.details.clientExists = !!supabaseClient;
            test.details.rpcMethodExists = !!supabaseClient?.rpc;
            test.details.clientType = typeof supabaseClient;

            if (!supabaseClient) {
                test.status = 'FAIL';
                test.message = 'Supabase client not available';
            } else if (!supabaseClient.rpc) {
                test.status = 'FAIL';
                test.message = 'Supabase RPC method not available';
            } else {
                test.message = 'Supabase client available with RPC support';
            }
        } catch (error) {
            test.status = 'FAIL';
            test.message = `Supabase client test failed: ${error.message}`;
        }

        return test;
    }

    async testVaultManagerInitialization() {
        const test = {
            name: 'Vault Manager Initialization',
            status: 'PASS',
            message: '',
            details: {}
        };

        try {
            const vaultManager = this.app?.config?.vaultManager;
            
            test.details.vaultManagerExists = !!vaultManager;
            test.details.vaultManagerType = typeof vaultManager;
            test.details.hasSupabaseClient = !!vaultManager?.supabase;

            if (!vaultManager) {
                test.status = 'FAIL';
                test.message = 'VaultManager not initialized';
            } else if (!vaultManager.supabase) {
                test.status = 'FAIL';
                test.message = 'VaultManager missing Supabase client';
            } else {
                test.message = 'VaultManager initialized successfully';
            }
        } catch (error) {
            test.status = 'FAIL';
            test.message = `Vault manager test failed: ${error.message}`;
        }

        return test;
    }

    async testAuthenticationStatus() {
        const test = {
            name: 'Authentication Status',
            status: 'PASS',
            message: '',
            details: {}
        };

        try {
            const currentUser = this.app?.auth?.getCurrentUser();
            const isAuthenticated = !!currentUser;
            
            test.details.isAuthenticated = isAuthenticated;
            test.details.userEmail = currentUser?.email || 'Not available';
            test.details.authInitialized = !!this.app?.auth?.initialized;

            if (!isAuthenticated) {
                test.status = 'WARN';
                test.message = 'User not authenticated - vault access may be limited';
            } else {
                test.message = `User authenticated as ${currentUser.email}`;
            }
        } catch (error) {
            test.status = 'FAIL';
            test.message = `Authentication test failed: ${error.message}`;
        }

        return test;
    }

    async testRPCConnectivity() {
        const test = {
            name: 'RPC Connectivity',
            status: 'PASS',
            message: '',
            details: {}
        };

        try {
            const vaultManager = this.app?.config?.vaultManager;
            
            if (!vaultManager) {
                test.status = 'FAIL';
                test.message = 'VaultManager not available for RPC test';
                return test;
            }

            // Try a simple RPC call to test connectivity
            const { data, error } = await vaultManager.supabase.rpc('get_secret_by_name', { 
                secret_name: 'test_connectivity' 
            });

            test.details.rpcCallSuccessful = !error;
            test.details.rpcError = error?.message || null;
            test.details.rpcData = data;

            if (error) {
                if (error.code === 'PGRST301' || error.message?.includes('JWT') || error.message?.includes('not authenticated')) {
                    test.status = 'WARN';
                    test.message = 'RPC call failed due to authentication - this is expected if not logged in';
                } else {
                    test.status = 'FAIL';
                    test.message = `RPC call failed: ${error.message}`;
                }
            } else {
                test.message = 'RPC connectivity successful';
            }
        } catch (error) {
            test.status = 'FAIL';
            test.message = `RPC connectivity test failed: ${error.message}`;
        }

        return test;
    }

    async testAPIKeyRetrieval() {
        const test = {
            name: 'API Key Retrieval',
            status: 'PASS',
            message: '',
            details: {}
        };

        try {
            const vaultManager = this.app?.config?.vaultManager;
            
            if (!vaultManager) {
                test.status = 'FAIL';
                test.message = 'VaultManager not available for API key test';
                return test;
            }

            // Test Google API key retrieval
            const googleKey = await vaultManager.getSecret('google_api_key');
            test.details.googleKeyAvailable = !!googleKey;
            test.details.googleKeyLength = googleKey ? googleKey.length : 0;

            // Test Gemini API key retrieval
            const geminiKey = await vaultManager.getSecret('gemini_api_key');
            test.details.geminiKeyAvailable = !!geminiKey;
            test.details.geminiKeyLength = geminiKey ? geminiKey.length : 0;

            if (!googleKey && !geminiKey) {
                test.status = 'FAIL';
                test.message = 'No API keys found in vault';
            } else if (!googleKey) {
                test.status = 'WARN';
                test.message = 'Google API key not found, Gemini key available';
            } else if (!geminiKey) {
                test.status = 'WARN';
                test.message = 'Gemini API key not found, Google key available';
            } else {
                test.message = 'Both API keys retrieved successfully';
            }
        } catch (error) {
            test.status = 'FAIL';
            test.message = `API key retrieval test failed: ${error.message}`;
        }

        return test;
    }

    /**
     * Generate a diagnostic report
     */
    generateReport(results) {
        let report = `
=== VAULT DIAGNOSTICS REPORT ===
Generated: ${results.timestamp}

SUMMARY:
✅ Passed: ${results.summary.passed}
⚠️  Warnings: ${results.summary.warnings}
❌ Failed: ${results.summary.failed}

DETAILED RESULTS:
`;

        results.tests.forEach(test => {
            const icon = test.status === 'PASS' ? '✅' : test.status === 'WARN' ? '⚠️' : '❌';
            report += `\n${icon} ${test.name}: ${test.message}\n`;
            
            if (Object.keys(test.details).length > 0) {
                report += '   Details:\n';
                Object.entries(test.details).forEach(([key, value]) => {
                    report += `   - ${key}: ${value}\n`;
                });
            }
        });

        return report;
    }
}

// Global function to run diagnostics from console
window.runVaultDiagnostics = async function() {
    if (!window.app) {
        console.error('App not available');
        return;
    }
    
    const diagnostics = new VaultDiagnostics(window.app);
    const results = await diagnostics.runDiagnostics();
    const report = diagnostics.generateReport(results);
    
    console.log(report);
    return results;
};
