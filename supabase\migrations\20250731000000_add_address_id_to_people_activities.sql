-- Migration: Add address_id foreign key to people_activities table
-- This allows person activities to be associated with specific addresses
-- for tracking activities that occur at specific locations

-- Add address_id column to people_activities table
ALTER TABLE core.people_activities 
ADD COLUMN address_id BIGINT;

-- Add foreign key constraint to ensure referential integrity
ALTER TABLE core.people_activities 
ADD CONSTRAINT fk_people_activities_address_id 
FOREIGN KEY (address_id) REFERENCES core.addresses(id)
ON DELETE SET NULL;

-- Add index for performance when querying activities by address
CREATE INDEX idx_people_activities_address_id 
ON core.people_activities(address_id);

-- Add comment to document the purpose of this field
COMMENT ON COLUMN core.people_activities.address_id IS 'Foreign key to addresses table - links person activity to a specific address location';
