<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S.T.E.V.I Retro - I.H.A.R.C Field Staff Terminal</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="../modules/incident-management/styles/incidents.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="terminal-container">
        <!-- Boot sequence -->
        <div id="boot-screen" class="screen active">
            <div class="boot-content">
                <div class="boot-header">
                    <div class="iharc-branding">
                        <div class="iharc-logo">I.H.A.R.C</div>
                        <div class="iharc-name">Integrated Homelessness & Addictions Response Centre</div>
                    </div>
                    <div class="ascii-art">
███████╗████████╗███████╗██╗   ██╗██╗    ██████╗ ███████╗████████╗██████╗  ██████╗
██╔════╝╚══██╔══╝██╔════╝██║   ██║██║    ██╔══██╗██╔════╝╚══██╔══╝██╔══██╗██╔═══██╗
███████╗   ██║   █████╗  ██║   ██║██║    ██████╔╝█████╗     ██║   ██████╔╝██║   ██║
╚════██║   ██║   ██╔══╝  ╚██╗ ██╔╝██║    ██╔══██╗██╔══╝     ██║   ██╔══██╗██║   ██║
███████║   ██║   ███████╗ ╚████╔╝ ██║    ██║  ██║███████╗   ██║   ██║  ██║╚██████╔╝
╚══════╝   ╚═╝   ╚══════╝  ╚═══╝  ╚═╝    ╚═╝  ╚═╝╚══════╝   ╚═╝   ╚═╝  ╚═╝ ╚═════╝
                    </div>
                    <div class="version-info">
                        <div>Supportive Technology to Enable Vulnerable Individuals - Retro Interface</div>
                        <div id="version-display">IHARC Field Staff Terminal v1.0</div>
                        <div>Copyright (C) 2024 IHARC. All rights reserved.</div>
                    </div>
                </div>
                <div class="boot-progress">
                    <div class="journey-animation" id="journey-animation">
                        <div class="journey-scene" id="journey-scene">
                            <div class="scene-background"></div>
                            <div class="person" id="person">
                                <div class="stick-figure">
                                    <div class="head"></div>
                                    <div class="body"></div>
                                    <div class="arm arm-left"></div>
                                    <div class="arm arm-right"></div>
                                    <div class="leg leg-left"></div>
                                    <div class="leg leg-right"></div>
                                </div>
                            </div>
                            <div class="shelter" id="shelter"></div>
                            <div class="progress-path"></div>
                        </div>
                        <div class="journey-text" id="journey-text">Starting journey...</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="boot-progress"></div>
                    </div>
                    <div class="boot-messages" id="boot-messages">
                        <div>Initializing system...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login screen -->
        <div id="login-screen" class="screen">
            <div class="login-content">
                <div class="login-header">
                    <div class="iharc-brand-login">I.H.A.R.C</div>
                    <div class="system-name">S.T.E.V.I Retro</div>
                    <div class="login-prompt">AUTHORIZED PERSONNEL ONLY</div>
                </div>
                <div class="login-form">
                    <div class="form-field">
                        <label>Email Address:</label>
                        <input type="email" id="login-email" autocomplete="username">
                    </div>
                    <div class="form-field">
                        <label>Password:</label>
                        <input type="password" id="login-password" autocomplete="current-password">
                    </div>
                    <div class="form-field">
                        <label>
                            <input type="checkbox" id="save-username" style="margin-right: 8px;">
                            Remember username for next session
                        </label>
                    </div>
                    <div class="form-actions">
                        <button id="login-submit" class="primary-button">LOGIN</button>
                    </div>
                </div>
                <div class="login-status" id="login-status"></div>
            </div>
        </div>

        <!-- Main interface -->
        <div id="main-screen" class="screen">
            <div class="main-header">
                <div class="header-left">
                    <span class="system-name">S.T.E.V.I DOS</span>
                    <span class="user-info" id="user-info"></span>
                </div>
                <div class="header-right">
                    <span class="datetime" id="datetime"></span>
                </div>
            </div>

            <div class="main-content">
                <!-- Tab navigation -->
                <div class="tab-bar" id="tab-bar">
                    <div class="tab active" data-tab="dashboard">DASHBOARD</div>
                    <div class="tab" data-tab="incidents">INCIDENTS</div>
                    <div class="tab" data-tab="records">RECORDS</div>
                    <div class="tab" data-tab="property">PROPERTY</div>
                    <div class="tab" data-tab="encampments">ENCAMPMENTS</div>
                    <div class="tab" data-tab="outreach">OUTREACH</div>
                    <div class="tab" data-tab="reports">REPORTS</div>
                    <div class="tab" data-tab="system">SYSTEM</div>
                    <div class="tab admin-tab" data-tab="admin" style="display: none;">ADMIN</div>
                    <div class="tab logout-tab" data-tab="logout">LOGOUT</div>
                </div>

                <!-- Content area -->
                <div class="content-area" id="content-area">
                    <!-- Content will be dynamically loaded here -->
                </div>
            </div>

            <div class="status-bar">
                <div class="status-left">
                    <span class="iharc-brand">I.H.A.R.C</span>
                    <span class="status-separator">|</span>
                    <span id="status-message">Ready</span>
                </div>
                <div class="status-center">
                    <span id="network-status" class="network-status">
                        <span class="network-indicator" id="network-indicator">●</span>
                        <span id="network-text">ONLINE</span>
                    </span>
                </div>
                <div class="status-right">
                    <span>F1 Help | F10 Menu | ESC Exit</span>
                </div>
            </div>
        </div>

        <!-- Full-screen form interface -->
        <div id="form-screen" class="screen">
            <div class="main-header">
                <div class="header-left">
                    <span class="system-name">S.T.E.V.I DOS</span>
                    <span class="user-info" id="form-user-info"></span>
                </div>
                <div class="header-right">
                    <span class="datetime" id="form-datetime"></span>
                </div>
            </div>

            <div class="form-content">
                <div class="form-header">
                    <h2 id="form-title">Add Record</h2>
                    <div class="form-actions">
                        <button type="button" class="secondary-button" id="form-cancel">Cancel</button>
                        <button type="submit" class="primary-button" id="form-submit">Submit</button>
                    </div>
                </div>

                <div class="form-body">
                    <form id="fullscreen-form" class="fullscreen-form">
                        <div id="form-fields-container">
                            <!-- Form fields will be dynamically generated here -->
                        </div>
                    </form>
                </div>
            </div>

            <div class="status-bar">
                <div class="status-left">
                    <span class="iharc-brand">I.H.A.R.C</span>
                    <span class="status-separator">|</span>
                    <span id="form-status-message">Ready</span>
                </div>
                <div class="status-center">
                    <span id="form-network-status" class="network-status">
                        <span class="network-indicator" id="form-network-indicator">●</span>
                        <span id="form-network-text">ONLINE</span>
                    </span>
                </div>
                <div class="status-right">
                    <span>ESC Cancel | F10 Submit</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Add error handling for module loading
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            document.body.innerHTML = `
                <div style="color: #ff0000; background: #000000; padding: 20px; font-family: monospace;">
                    <h1>S.T.E.V.I DOS - Loading Error</h1>
                    <p>Error: ${e.error?.message || e.message}</p>
                    <p>File: ${e.filename}</p>
                    <p>Line: ${e.lineno}</p>
                    <button onclick="location.reload()" style="background: #ff0000; color: #000000; padding: 10px; border: none; font-family: monospace;">Retry</button>
                </div>
            `;
        });
    </script>
    <script src="js/app.js" type="module"></script>
</body>
</html>
