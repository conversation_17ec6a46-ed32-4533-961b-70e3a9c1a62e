/**
 * Activity Management Module
 * Provides modular activity management functionality
 */

import { FeatureModuleInterface } from '../../shared/feature-module-interface.js';
import { ActivityManager } from './activity-manager.js';
import { ActivitySearchManager } from './activity-search-manager.js';

export class ActivityManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super();
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;

        // Initialize managers
        this.activityManager = new ActivityManager(dataManager, authManager, uiManager, uiUtilities);
        this.searchManager = new ActivitySearchManager(dataManager, authManager, uiManager, uiUtilities);
    }

    // === MAIN WORKFLOWS ===

    /**
     * Show activity type selection modal (main entry point)
     */
    async showActivityTypeSelection() {
        return await this.activityManager.showActivityTypeSelection();
    }

    /**
     * Show record search for specific activity type
     */
    async showRecordSearch(activityType) {
        return await this.searchManager.showRecordSearch(activityType);
    }

    /**
     * Show activity form for selected record
     */
    async showActivityForm(recordType, record) {
        return await this.activityManager.showActivityForm(recordType, record);
    }

    // === DELEGATED METHODS ===

    // Activity Manager methods
    async addActivity(recordType, recordId, activityData) {
        return await this.activityManager.addActivity(recordType, recordId, activityData);
    }

    async loadActivities(recordType, recordId) {
        return await this.activityManager.loadActivities(recordType, recordId);
    }

    // Search Manager methods
    async searchRecords(recordType, query) {
        return await this.searchManager.searchRecords(recordType, query);
    }

    async showPersonCreationForm() {
        return await this.searchManager.showPersonCreationForm();
    }

    // === UTILITY METHODS ===

    getActivityTableName(recordType) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities', 
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordType] || 'activities';
    }

    getForeignKeyName(recordType) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[recordType] || 'record_id';
    }

    /**
     * Cleanup method
     */
    cleanup() {
        if (this.activityManager?.cleanup) {
            this.activityManager.cleanup();
        }
        if (this.searchManager?.cleanup) {
            this.searchManager.cleanup();
        }
    }
}

export { ActivityManager, ActivitySearchManager };
