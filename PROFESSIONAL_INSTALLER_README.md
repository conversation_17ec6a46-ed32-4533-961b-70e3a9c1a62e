# S.T.E.V.I Retro Professional Windows Installer

## Overview

This professional installer system creates a comprehensive Windows installer (EXE) that can be deployed on fresh Windows 10/11 systems. The installer handles all dependencies, provides Program Files installation, and includes uninstall/repair functionality.

## Features

### ✅ Professional Installation
- **Program Files Installation**: Installs to `C:\Program Files\S.T.E.V.I Retro\`
- **Administrator Privileges**: <PERSON><PERSON>ly requests and handles elevation
- **Windows Integration**: Appears in Windows Programs list
- **Desktop & Start Menu**: Creates shortcuts automatically
- **Uninstall Support**: Full uninstall with data preservation options
- **Repair Functionality**: Can repair broken installations

### ✅ Dependency Management
- **Visual C++ Redistributable**: Automatically detects and installs if missing
- **System Requirements**: Validates Windows 10/11 and 64-bit architecture
- **Optional Components**: .NET Framework 4.8 (recommended but not required)
- **Fresh Windows Support**: Works on completely fresh Windows installations

### ✅ User Data Protection
- **Secure Directories**: Creates protected user data folders
- **Data Preservation**: Uninstall preserves user data by default
- **Backup Options**: Automatic settings backup system
- **Proper Permissions**: Sets correct file permissions for security

## Quick Start

### Method 1: One-Click Build (Recommended)
```batch
# Double-click this file:
create-professional-installer.bat
```

### Method 2: PowerShell (Advanced)
```powershell
# Run with dependency inclusion:
.\create-professional-installer.ps1 -IncludeDependencies

# Clean build:
.\create-professional-installer.ps1 -Clean -IncludeDependencies

# Skip dependency checks (CI/CD):
.\create-professional-installer.ps1 -SkipDependencyCheck
```

### Method 3: npm Scripts
```bash
# Standard build:
npm run build-win

# Clean and rebuild:
npm run clean && npm run build-win
```

## Output Files

After building, you'll find these files in the `dist/` directory:

```
dist/
├── S.T.E.V.I-Retro-Setup-1.3.0.exe    # Main installer (recommended)
├── S.T.E.V.I-Retro-Setup-1.3.0.exe.blockmap  # Update verification
└── win-unpacked/                       # Standalone application
    ├── S.T.E.V.I Retro.exe            # Main executable
    └── [other application files]
```

## Installation Process

### For End Users
1. **Download** `S.T.E.V.I-Retro-Setup-1.3.0.exe`
2. **Right-click** and select "Run as administrator" (recommended)
3. **Follow** the installation wizard:
   - Choose installation directory (default: Program Files)
   - Select components (VC++ Redistributable is required)
   - Create shortcuts
4. **Launch** the application after installation

### Installation Modes
The installer automatically detects and handles:
- **Fresh Installation**: First-time installation
- **Upgrade**: Updates existing installation while preserving data
- **Repair**: Fixes broken installation without data loss

## System Requirements

### Build Requirements (Development)
- Windows 10/11
- Node.js 18+ LTS
- npm 8+
- PowerShell 5.1+
- Administrator privileges (recommended)
- NSIS 3.0+ (optional, for custom installers)

### Runtime Requirements (End Users)
- Windows 10/11 (64-bit)
- 500MB free disk space
- 4GB RAM (minimum)
- Administrator privileges (for installation only)

## Dependency Handling

### Automatic Detection
The installer checks for:
- ✅ **Visual C++ Redistributable 2015-2022** (required)
- ✅ **Windows version compatibility**
- ✅ **System architecture (64-bit)**
- ⚠️ **.NET Framework 4.8** (optional, recommended)

### Installation Behavior
- **Missing Dependencies**: Automatically downloads and installs
- **Existing Dependencies**: Skips installation, shows status
- **Failed Downloads**: Provides manual download links
- **Silent Installation**: No user interaction required for dependencies

## Uninstall & Repair

### Uninstall Options
When uninstalling, users can choose:
1. **Keep Data**: Removes application but preserves user data and settings
2. **Remove Everything**: Complete removal including all user data
3. **Cancel**: Abort uninstall process

### Repair Functionality
- Accessible from Windows Programs list
- Restores missing or corrupted files
- Preserves user data and settings
- Re-registers Windows integration

## Troubleshooting

### Build Issues

**Error: "Node.js or npm not found"**
```bash
# Install Node.js LTS from https://nodejs.org/
# Verify installation:
node --version
npm --version
```

**Error: "package.json not found"**
```bash
# Ensure you're in the project root directory:
cd path\to\stevi_retro
```

**Error: "Dependency check failed"**
```bash
# Skip dependency checks:
npm run build-win --skip-deps
```

### Installation Issues

**Error: "Administrator privileges required"**
- Right-click installer and select "Run as administrator"

**Error: "Visual C++ Redistributable installation failed"**
- Download manually from: https://aka.ms/vs/17/release/vc_redist.x64.exe
- Install before running S.T.E.V.I Retro installer

**Error: "Unsupported Windows version"**
- Requires Windows 10 build 10240 or later
- Windows 11 is fully supported

## Advanced Configuration

### Custom Build Options
```powershell
# Include dependency installers in the setup:
.\create-professional-installer.ps1 -IncludeDependencies

# Verbose output for debugging:
.\create-professional-installer.ps1 -Verbose

# Custom output directory:
.\create-professional-installer.ps1 -OutputDir "custom-dist"
```

### Electron Builder Configuration
The installer is configured in `package.json`:
```json
{
  "build": {
    "nsis": {
      "perMachine": true,
      "allowElevation": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "allowToChangeInstallationDirectory": true
    }
  }
}
```

## Security Considerations

### Code Signing (Recommended)
For production distribution:
1. Obtain a code signing certificate
2. Configure electron-builder with certificate details
3. Signed installers avoid Windows security warnings

### User Data Protection
- User data stored in `%APPDATA%\S.T.E.V.I Retro\`
- Proper file permissions (user-only access)
- Encrypted settings storage
- Automatic backup system

## Support

### Documentation
- [Main README](README.md) - Application overview
- [Installation Guide](INSTALLER_README.md) - Detailed installation instructions
- [Build Guide](INSTALLER_CREATION_SUMMARY.md) - Build process documentation

### Getting Help
- **Issues**: Check error messages and logs
- **Support**: Contact I.H.A.R.C. support team
- **Updates**: Check for newer installer versions

---

**The professional installer provides a complete, enterprise-ready deployment solution for S.T.E.V.I Retro on Windows systems.**
