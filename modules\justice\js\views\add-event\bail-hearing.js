// Bail Hearing Event Modal
// Handles adding bail hearing events with outcome tracking

import { addEventTemplates } from '../../../templates/add-event-templates.js';

export class BailHearingModal {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.episodeId = null;
    }
    
    async show(episodeId) {
        this.episodeId = episodeId;
        
        try {
            // Create modal
            this.modal = this.justice.ui.createModal({
                id: 'add-bail-hearing-modal',
                title: 'Add Bail Hearing',
                size: 'medium'
            });
            
            // Render form
            this.render();
            
            // Show modal
            this.justice.ui.showModal(this.modal);
            
            // Setup event handlers
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to show bail hearing modal:', error);
            this.justice.ui.showDialog('Error', `Failed to open bail hearing form: ${error.message}`, 'error');
        }
    }
    
    render() {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = addEventTemplates.bailHearingForm({
            episodeId: this.episodeId,
            defaultDateTime: new Date().toISOString().slice(0, 16)
        });
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#bail-hearing-form');
        const outcomeSelect = this.modal.querySelector('#hearing-outcome');
        const nextDateSection = this.modal.querySelector('#next-date-section');
        
        // Show/hide next date based on outcome
        outcomeSelect.addEventListener('change', (e) => {
            const outcome = e.target.value;
            if (nextDateSection) {
                nextDateSection.style.display = outcome === 'RESERVED' ? 'block' : 'none';
            }
        });
        
        // Save button
        const saveBtn = this.modal.querySelector('#save-bail-hearing-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveBailHearing(form);
            });
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-bail-hearing-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }
    
    async saveBailHearing(form) {
        try {
            const formData = new FormData(form);
            
            const eventData = {
                event_dt: formData.get('hearing_dt'),
                payload: {
                    outcome: formData.get('outcome'),
                    notes: formData.get('notes'),
                    next_date: formData.get('next_date') || null
                }
            };
            
            // Validate required fields
            if (!eventData.event_dt) {
                this.justice.ui.showDialog('Error', 'Hearing date/time is required.', 'error');
                return;
            }
            
            if (!eventData.payload.outcome) {
                this.justice.ui.showDialog('Error', 'Hearing outcome is required.', 'error');
                return;
            }
            
            // Show loading state
            const saveBtn = this.modal.querySelector('#save-bail-hearing-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
            
            // Create the event
            const newEvent = await this.justice.api.addEvent(
                this.episodeId,
                'BAIL_HEARING',
                eventData.event_dt,
                eventData.payload
            );
            
            // Update state
            this.justice.state.addEvent(newEvent);
            
            // Close modal
            this.close();
            
            // Show success
            this.justice.ui.showDialog('Success', 'Bail hearing added successfully!', 'success');
            
            // Refresh timeline if visible
            if (this.justice.timelineView && this.justice.timelineView.container) {
                await this.justice.timelineView.refresh();
            }
            
        } catch (error) {
            console.error('Failed to save bail hearing:', error);
            this.justice.ui.showDialog('Error', `Failed to save bail hearing: ${error.message}`, 'error');
            
            // Reset button
            const saveBtn = this.modal.querySelector('#save-bail-hearing-btn');
            if (saveBtn) {
                saveBtn.textContent = 'Save Bail Hearing';
                saveBtn.disabled = false;
            }
        }
    }
    
    close() {
        if (this.modal) {
            this.justice.ui.closeModal(this.modal.id);
            this.modal = null;
        }
        this.episodeId = null;
    }
}
