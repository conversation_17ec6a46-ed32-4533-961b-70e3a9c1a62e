// Start Episode Wizard View
// 60-second wizard for creating new justice episodes

import { startEpisodeTemplates } from '../../templates/start-episode-templates.js';

export class StartEpisodeWizard {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.personId = null;
        this.formData = {};
    }
    
    async show(personId) {
        this.personId = personId;
        this.formData = {
            person_id: personId,
            origin: 'ARREST',
            origin_dt: new Date().toISOString().slice(0, 16), // YYYY-MM-DDTHH:MM
            origin_agency: '',
            jurisdiction: '',
            held_for_bail: false,
            charges: []
        };
        
        try {
            // Access app's modal management system
            const app = window.app;
            if (!app || !app.modalManagement) {
                throw new Error('App modal management system not available');
            }
            
            // Create modal using app's modal management
            this.modal = app.modalManagement.createModal({
                id: 'start-episode-wizard',
                className: 'modal-overlay',
                content: `
                    <div class="modal-content">
                        <div class="modal-header">  
                            <h3>Start New Justice Episode</h3>
                            <button class="modal-close" data-action="close-modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <!-- Content will be populated by render() -->
                        </div>
                    </div>
                `
            });
            
            // Render wizard content
            this.render();
            
            // Setup event handlers
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to show start episode wizard:', error);
            this.justice.ui.showDialog('Error', `Failed to open episode wizard: ${error.message}`, 'error');
        }
    }
    
    render() {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = startEpisodeTemplates.wizardForm(this.formData);
        
        // Set initial form values
        this.updateFormFields();
    }
    
    updateFormFields() {
        const form = this.modal.querySelector('#episode-wizard-form');
        if (!form) return;
        
        // Set form values from formData
        Object.keys(this.formData).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = this.formData[key];
                } else {
                    field.value = this.formData[key];
                }
            }
        });
        
        // Update charges display
        this.updateChargesDisplay();
    }
    
    updateChargesDisplay() {
        const chargesContainer = this.modal.querySelector('#charges-container');
        if (!chargesContainer) return;
        
        chargesContainer.innerHTML = startEpisodeTemplates.chargesList(this.formData.charges);
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#episode-wizard-form');
        
        // Form field changes
        form.addEventListener('change', (e) => {
            const field = e.target;
            const value = field.type === 'checkbox' ? field.checked : field.value;
            this.formData[field.name] = value;
            
            // Special handling for origin change
            if (field.name === 'origin') {
                this.handleOriginChange(value);
            }
        });
        
        // Add charge button
        const addChargeBtn = this.modal.querySelector('#add-charge-btn');
        if (addChargeBtn) {
            addChargeBtn.addEventListener('click', () => {
                this.showInlineChargeForm();
            });
        }
        
        // Remove charge buttons (delegated)
        this.modal.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-charge-btn')) {
                const index = parseInt(e.target.dataset.index);
                this.removeCharge(index);
            }
        });
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-wizard-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
        
        // Create episode button
        const createBtn = this.modal.querySelector('#create-episode-btn');
        if (createBtn) {
            createBtn.addEventListener('click', async () => {
                await this.createEpisode();
            });
        }
    }
    
    handleOriginChange(origin) {
        // Update UI based on origin type
        const heldForBailSection = this.modal.querySelector('#held-for-bail-section');
        if (heldForBailSection) {
            heldForBailSection.style.display = origin === 'ARREST' ? 'block' : 'none';
        }
    }
    
    showInlineChargeForm() {
        const chargeForm = this.modal.querySelector('#add-charge-form');
        const addChargeBtn = this.modal.querySelector('#add-charge-btn');
        
        if (!chargeForm) return;
        
        // Expand the form
        chargeForm.classList.remove('collapsed');
        chargeForm.classList.add('expanded');
        
        // Hide the add button while form is active
        addChargeBtn.style.display = 'none';
        
        // Clear any previous values
        chargeForm.querySelector('#charge-code').value = '';
        chargeForm.querySelector('#charge-label').value = '';
        chargeForm.querySelector('#charge-severity').value = 'SUMM';
        
        // Focus on the first input
        setTimeout(() => {
            chargeForm.querySelector('#charge-label').focus();
        }, 300); // Wait for animation to complete
        
        // Setup inline form handlers (only once)
        if (!chargeForm.hasAttribute('data-handlers-setup')) {
            this.setupInlineChargeFormHandlers(chargeForm, addChargeBtn);
            chargeForm.setAttribute('data-handlers-setup', 'true');
        }
    }
    
    setupInlineChargeFormHandlers(chargeForm, addChargeBtn) {
        const saveChargeBtn = chargeForm.querySelector('#save-charge-btn');
        const cancelChargeBtn = chargeForm.querySelector('#cancel-charge-btn');
        
        saveChargeBtn.addEventListener('click', () => {
            const code = chargeForm.querySelector('#charge-code').value.trim();
            const label = chargeForm.querySelector('#charge-label').value.trim();
            const severity = chargeForm.querySelector('#charge-severity').value;
            
            if (label) { // At minimum, require a label
                this.addCharge({ code, label, severity });
                this.hideInlineChargeForm(chargeForm, addChargeBtn);
            } else {
                // Show validation error
                chargeForm.querySelector('#charge-label').style.borderColor = '#ff4444';
                setTimeout(() => {
                    chargeForm.querySelector('#charge-label').style.borderColor = '#555';
                }, 2000);
            }
        });
        
        cancelChargeBtn.addEventListener('click', () => {
            this.hideInlineChargeForm(chargeForm, addChargeBtn);
        });
        
        // Allow Enter key to save
        chargeForm.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
                e.preventDefault();
                saveChargeBtn.click();
            } else if (e.key === 'Escape') {
                cancelChargeBtn.click();
            }
        });
    }
    
    hideInlineChargeForm(chargeForm, addChargeBtn) {
        // Collapse the form
        chargeForm.classList.remove('expanded');
        chargeForm.classList.add('collapsed');
        
        // Show the add button again
        setTimeout(() => {
            addChargeBtn.style.display = 'block';
        }, 300); // Wait for animation to complete
    }
    
    addCharge(charge) {
        this.formData.charges.push(charge);
        this.updateChargesDisplay();
    }
    
    removeCharge(index) {
        this.formData.charges.splice(index, 1);
        this.updateChargesDisplay();
    }
    
    async createEpisode() {
        try {
            // Validate form data
            const errors = this.validateFormData();
            if (errors.length > 0) {
                this.justice.ui.showDialog('Validation Error', errors.join('\n'), 'error');
                return;
            }
            
            // Show loading state
            const createBtn = this.modal.querySelector('#create-episode-btn');
            const originalText = createBtn.textContent;
            createBtn.textContent = 'Creating...';
            createBtn.disabled = true;
            
            // Create episode
            const episode = await this.justice.api.createEpisode(this.formData);
            
            // If held for bail, create initial custody event
            if (this.formData.held_for_bail) {
                await this.justice.api.addEvent(
                    episode.id,
                    'TRANSFER_TO_FACILITY',
                    this.formData.origin_dt,
                    {
                        facility_id: 'holding', // Default holding facility
                        reason: 'Initial arrest - held for bail'
                    }
                );
            }
            
            // Update state
            this.justice.state.addEpisode(episode);
            
            // Close wizard
            this.close();
            
            // Show success and navigate to episode detail
            this.justice.ui.showDialog('Success', 'Justice episode created successfully!', 'success');
            
            // Trigger episode detail view
            if (window.app && window.app.showEpisodeDetail) {
                await window.app.showEpisodeDetail(episode.id);
            }
            
        } catch (error) {
            console.error('Failed to create episode:', error);
            this.justice.ui.showDialog('Error', `Failed to create episode: ${error.message}`, 'error');
            
            // Reset button
            const createBtn = this.modal.querySelector('#create-episode-btn');
            createBtn.textContent = 'Create Episode';
            createBtn.disabled = false;
        }
    }
    
    validateFormData() {
        const errors = [];
        
        if (!this.formData.person_id) {
            errors.push('Person ID is required');
        }
        
        if (!this.formData.origin) {
            errors.push('Origin is required');
        }
        
        if (!this.formData.origin_dt) {
            errors.push('Origin date/time is required');
        }
        
        // Validate origin_dt is not in the future
        const originDate = new Date(this.formData.origin_dt);
        const now = new Date();
        if (originDate > now) {
            errors.push('Origin date/time cannot be in the future');
        }
        
        return errors;
    }
    
    close() {
        if (this.modal) {
            // Use app's modal management system
            const app = window.app;
            if (app && app.modalManagement) {
                app.modalManagement.closeModal('start-episode-wizard');
            } else {
                // Fallback - remove modal manually
                this.modal.remove();
            }
            this.modal = null;
        }
        this.personId = null;
        this.formData = {};
    }
}
