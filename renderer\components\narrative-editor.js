// Narrative Editor Component
// Rich text editor for incident narrative entries
import { GeminiService } from '../../modules/ai/js/index.js';

export class NarrativeEditor {
    constructor() {
        this.entries = [];
        this.currentIncidentId = null;
        this.isEditing = false;
        this.editingEntryId = null;
        
        // Initialize Gemini service for AI text enhancement
        // Will be properly initialized when app data manager is available
        this.geminiService = null;
        this.currentEditor = null;
    }

    // Initialize the narrative editor for an incident
    init(incidentId, existingEntries = []) {
        this.currentIncidentId = incidentId;
        // Only set entries if they're provided and not empty
        if (existingEntries && existingEntries.length > 0) {
            this.entries = existingEntries;
        }
        this.render();
    }

    // Render the narrative entries
    render() {
        // Check if we're in a modal context first
        const inModal = document.querySelector('.modal-overlay');
        if (inModal) {
            // Don't render in the background while modal is open
            return;
        }

        // Try multiple container IDs based on context
        const possibleContainerIds = [
            this.currentIncidentId ? `narrative-entries-${this.currentIncidentId}` : null,
            this.currentIncidentId ? `narrative-entries-edit-${this.currentIncidentId}` : null,
            'narrative-entries-form'
        ].filter(Boolean); // Remove null values

        let container = null;
        for (const containerId of possibleContainerIds) {
            container = document.getElementById(containerId);
            if (container) break;
        }

        if (!container) {
            // Enhanced debugging but don't error out - container might not be ready yet
            console.warn('Narrative entries container not found. Tried:', possibleContainerIds);
            console.warn('Current incident ID:', this.currentIncidentId);
            console.warn('Available containers:', Array.from(document.querySelectorAll('[id*="narrative-entries"]')).map(el => ({ id: el.id, visible: el.offsetParent !== null })));
            
            // Try to find any narrative container as fallback
            const fallbackContainer = document.querySelector('[id*="narrative-entries"]');
            if (fallbackContainer) {
                console.info('Using fallback container:', fallbackContainer.id);
                container = fallbackContainer;
            } else {
                // Container not ready yet, will be rendered later
                return;
            }
        }

        if (this.entries.length === 0) {
            container.innerHTML = `
                <div class="no-entries-message">
                    [NO NARRATIVE ENTRIES YET]<br>
                    Click "Add Entry" to start documenting events and observations.
                </div>
            `;
            return;
        }

        // Sort entries by event_time (chronological order - oldest first)
        const sortedEntries = [...this.entries].sort((a, b) => {
            const timeA = new Date(a.event_time || a.timestamp);
            const timeB = new Date(b.event_time || b.timestamp);
            return timeA - timeB;
        });

        container.innerHTML = sortedEntries.map(entry => this.renderEntry(entry)).join('');
    }

    // Render a single narrative entry
    renderEntry(entry) {
        const eventTime = new Date(entry.event_time || entry.timestamp).toLocaleString();
        const createdTime = entry.timestamp && entry.event_time !== entry.timestamp ? 
            ` (logged: ${new Date(entry.timestamp).toLocaleString()})` : '';
        
        const entryTypeIcons = {
            'initial_observation': '👁️',
            'action_taken': '✅',
            'status_update': '🔄',
            'follow_up': '⏰',
            'contact_made': '📞',
            'note': '📝',
            'other': '📌'
        };
        
        const icon = entryTypeIcons[entry.entry_type] || '📝';
        const typeClass = `narrative-type-${entry.entry_type}`;
        
        return `
            <div class="narrative-entry" data-entry-id="${entry.id}">
                <div class="narrative-header">
                    <div class="narrative-meta">
                        <span class="narrative-type ${typeClass}">${icon} ${entry.entry_type.toUpperCase().replace('_', ' ')}</span>
                        <span class="narrative-timestamp">🕐 ${eventTime}${createdTime}</span>
                        <span class="narrative-user">by ${entry.user}</span>
                    </div>
                    <div class="narrative-actions">
                        <button class="action-btn small edit-entry-btn" onclick="window.narrativeEditor.editEntry('${entry.id}')">
                            ✏️ Edit
                        </button>
                        <button class="action-btn small delete-entry-btn" onclick="window.narrativeEditor.deleteEntry('${entry.id}')">
                            🗑️ Delete
                        </button>
                    </div>
                </div>
                <div class="narrative-content">
                    ${this.formatContent(entry.content)}
                </div>
            </div>
        `;
    }

    // Format content with basic HTML support
    formatContent(content) {
        if (!content) return '';
        
        // Escape HTML first to prevent XSS
        let formatted = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;');
        
        // Apply formatting transformations
        formatted = formatted
            // Convert line breaks to HTML
            .replace(/\n/g, '<br>')
            // Bold formatting (**text**)
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            // Underline formatting (__text__)
            .replace(/__(.*?)__/g, '<u>$1</u>')
            // Italic formatting (*text*) - but not **bold**
            .replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em>$1</em>')
            // Bullet lists (• text)
            .replace(/^• (.+)$/gm, '<div class="bullet-item">• $1</div>')
            // Numbered lists (1. text)
            .replace(/^(\d+)\. (.+)$/gm, '<div class="number-item">$1. $2</div>');
        
        return formatted;
    }

    // Show the add entry form
    showAddEntryForm() {
        const modal = this.createEntryModal();
        document.body.appendChild(modal);
        
        // Focus on the content textarea
        setTimeout(() => {
            const textarea = modal.querySelector('#narrative-content');
            if (textarea) textarea.focus();
        }, 100);
    }

    // Show the edit entry form
    editEntry(entryId) {
        const entry = this.entries.find(e => e.id === entryId);
        if (!entry) {
            console.error('Entry not found:', entryId);
            return;
        }

        this.isEditing = true;
        this.editingEntryId = entryId;
        
        const modal = this.createEntryModal(entry);
        document.body.appendChild(modal);
        
        // Focus on the content editor
        setTimeout(() => {
            const editor = modal.querySelector('#narrative-content');
            if (editor) {
                editor.focus();
                // Move cursor to end for contenteditable
                const range = document.createRange();
                const selection = window.getSelection();
                range.selectNodeContents(editor);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        }, 100);
    }

    // Create the entry modal form
    createEntryModal(existingEntry = null) {
        const isEdit = existingEntry !== null;
        const modalId = `narrative-modal-${Date.now()}`;
        
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = modalId;
        
        modal.innerHTML = `
            <div class="modal-content narrative-modal">
                <div class="modal-header">
                    <h3>${isEdit ? 'Edit' : 'Add'} Narrative Entry</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <form id="narrative-entry-form">
                        <div class="form-field">
                            <label for="narrative-entry-type">Entry Type:</label>
                            <select id="narrative-entry-type" required>
                                <option value="">Select type...</option>
                                <option value="initial_observation" ${existingEntry?.entry_type === 'initial_observation' ? 'selected' : ''}>Initial Observation</option>
                                <option value="action_taken" ${existingEntry?.entry_type === 'action_taken' ? 'selected' : ''}>Action Taken</option>
                                <option value="status_update" ${existingEntry?.entry_type === 'status_update' ? 'selected' : ''}>Status Update</option>
                                <option value="follow_up" ${existingEntry?.entry_type === 'follow_up' ? 'selected' : ''}>Follow-up</option>
                                <option value="contact_made" ${existingEntry?.entry_type === 'contact_made' ? 'selected' : ''}>Contact Made</option>
                                <option value="note" ${existingEntry?.entry_type === 'note' ? 'selected' : ''}>General Note</option>
                                <option value="other" ${existingEntry?.entry_type === 'other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>
                        <div class="form-field">
                            <label for="narrative-entry-time">Event Time:</label>
                            <input type="datetime-local" id="narrative-entry-time" value="${existingEntry ? this.formatDateTimeForInput(existingEntry.event_time || existingEntry.timestamp) : this.getCurrentDateTimeLocal()}" />
                            <small class="field-help">
                                Set the specific time when this event occurred (defaults to current time).
                            </small>
                        </div>
                        <div class="form-field">
                            <label for="narrative-content">Content:</label>
                            <div class="text-formatting-toolbar" id="formatting-toolbar-${modalId}">
                                <button type="button" class="format-btn" data-format="bold" title="Bold (Ctrl+B)" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeEditor.applyWysiwygFormat('bold');">
                                    <b>B</b>
                                </button>
                                <button type="button" class="format-btn" data-format="italic" title="Italic (Ctrl+I)" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeEditor.applyWysiwygFormat('italic');">
                                    <i>I</i>
                                </button>
                                <button type="button" class="format-btn" data-format="underline" title="Underline (Ctrl+U)" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeEditor.applyWysiwygFormat('underline');">
                                    <u>U</u>
                                </button>
                                <span class="toolbar-divider">|</span>
                                <button type="button" class="format-btn" data-format="bullet" title="Bullet List" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeEditor.applyWysiwygFormat('bullet');">
                                    •
                                </button>
                                <button type="button" class="format-btn" data-format="number" title="Numbered List" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeEditor.applyWysiwygFormat('number');">
                                    1.
                                </button>
                                <span class="toolbar-divider">|</span>
                                <button type="button" class="format-btn ai-enhance-btn" data-format="ai-enhance" title="Enhance with AI" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeEditor.showAiEnhanceModal();">
                                    🤖 AI
                                </button>
                            </div>
                            <div id="narrative-content" contenteditable="true" class="wysiwyg-editor" role="textbox" aria-label="Narrative content" data-placeholder="Enter detailed narrative content here...">${existingEntry ? this.convertStoredToHtml(existingEntry.content) : ''}</div>
                            <small class="field-help">
                                Use the toolbar buttons above for formatting. Text will appear formatted as you type.
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="action-btn primary" onclick="window.narrativeEditor.saveEntry('${modalId}')">
                        ${isEdit ? 'Update' : 'Save'} Entry
                    </button>
                    <button class="action-btn" onclick="this.closest('.modal-overlay').remove()">
                        Cancel
                    </button>
                </div>
            </div>
        `;
        
        // Setup simplified text formatting
        this.setupTextFormatting(modal);
        
        return modal;
    }

    // Setup WYSIWYG text formatting
    setupTextFormatting(modal) {
        const editor = modal.querySelector('#narrative-content');
        if (!editor) return;

        // Store reference to current editor for formatting methods
        this.currentEditor = editor;

        // Handle placeholder behavior
        this.setupPlaceholder(editor);

        // Handle keyboard shortcuts
        editor.addEventListener('keydown', (e) => {
            // Handle tab key for proper indentation and list creation
            if (e.key === 'Tab') {
                e.preventDefault();
                e.stopPropagation();
                
                // Allow normal tab behavior in the rich text editor
                const selection = window.getSelection();
                const range = selection.getRangeAt(0);
                
                // Insert tab spaces or handle list indentation
                if (e.shiftKey) {
                    // Shift+Tab for outdenting
                    this.handleOutdent();
                } else {
                    // Tab for indenting
                    this.handleIndent();
                }
                return;
            }
            
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault();
                        this.applyWysiwygFormat('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.applyWysiwygFormat('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        this.applyWysiwygFormat('underline');
                        break;
                }
            }
        });

        // Handle paste events to clean up formatting
        editor.addEventListener('paste', (e) => {
            e.preventDefault();
            const text = (e.clipboardData || window.clipboardData).getData('text/plain');
            document.execCommand('insertText', false, text);
        });
    }

    // Setup placeholder functionality for contenteditable
    setupPlaceholder(editor) {
        const placeholder = editor.getAttribute('data-placeholder');
        
        const showPlaceholder = () => {
            if (!editor.textContent.trim()) {
                editor.innerHTML = `<span class="placeholder-text">${placeholder}</span>`;
                editor.classList.add('empty');
            }
        };

        const hidePlaceholder = () => {
            const placeholderSpan = editor.querySelector('.placeholder-text');
            if (placeholderSpan) {
                editor.innerHTML = '';
                editor.classList.remove('empty');
            }
        };

        editor.addEventListener('focus', hidePlaceholder);
        editor.addEventListener('blur', showPlaceholder);
        editor.addEventListener('input', () => {
            if (editor.textContent.trim()) {
                editor.classList.remove('empty');
            } else {
                editor.classList.add('empty');
            }
        });

        // Initialize placeholder
        if (!editor.textContent.trim()) {
            showPlaceholder();
        }
    }

    // Apply WYSIWYG formatting using execCommand
    applyWysiwygFormat(format) {
        const editor = this.currentEditor;
        if (!editor) return;

        // Focus the editor first
        editor.focus();

        // Clear placeholder if present
        const placeholderSpan = editor.querySelector('.placeholder-text');
        if (placeholderSpan) {
            editor.innerHTML = '';
            editor.classList.remove('empty');
        }

        switch (format) {
            case 'bold':
                document.execCommand('bold', false, null);
                break;
            case 'italic':
                document.execCommand('italic', false, null);
                break;
            case 'underline':
                document.execCommand('underline', false, null);
                break;
            case 'bullet':
                document.execCommand('insertUnorderedList', false, null);
                break;
            case 'number':
                document.execCommand('insertOrderedList', false, null);
                break;
        }

        // Keep focus on editor
        editor.focus();
    }

    // Handle tab indentation in rich text editor
    handleIndent() {
        const editor = this.currentEditor;
        if (!editor) return;

        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        const currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
            ? range.startContainer.parentElement 
            : range.startContainer;

        // Check if we're in a list item
        const listItem = currentElement.closest('li');
        if (listItem) {
            // Create nested list
            document.execCommand('indent', false, null);
        } else {
            // Insert tab character for regular text
            const tabSpaces = '\u00A0\u00A0\u00A0\u00A0'; // 4 non-breaking spaces
            document.execCommand('insertText', false, tabSpaces);
        }
        
        editor.focus();
    }

    // Handle shift+tab outdentation in rich text editor
    handleOutdent() {
        const editor = this.currentEditor;
        if (!editor) return;

        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        const currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
            ? range.startContainer.parentElement 
            : range.startContainer;

        // Check if we're in a list item
        const listItem = currentElement.closest('li');
        if (listItem) {
            // Outdent list item
            document.execCommand('outdent', false, null);
        } else {
            // Remove tab spaces for regular text
            const textContent = range.startContainer.textContent || '';
            const cursorPosition = range.startOffset;
            const beforeCursor = textContent.substring(0, cursorPosition);
            
            // Remove up to 4 spaces/non-breaking spaces before cursor
            const spaceMatch = beforeCursor.match(/[\u00A0\s]{1,4}$/);
            if (spaceMatch) {
                const newRange = document.createRange();
                newRange.setStart(range.startContainer, cursorPosition - spaceMatch[0].length);
                newRange.setEnd(range.startContainer, cursorPosition);
                newRange.deleteContents();
            }
        }
        
        editor.focus();
    }

    // Convert stored markdown-like content to HTML for editing
    convertStoredToHtml(content) {
        if (!content) return '';
        
        // If content is already HTML (contains tags), return as-is
        if (content.includes('<') && content.includes('>')) {
            return content;
        }

        // Convert markdown-like content to HTML
        return content
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<u>$1</u>')
            .replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em>$1</em>')
            .replace(/^• (.+)$/gm, '<li>$1</li>')
            .replace(/^(\d+)\. (.+)$/gm, '<li>$1. $2</li>')
            .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');
    }

    // Convert HTML content to storage format
    convertHtmlToStorage(htmlContent) {
        if (!htmlContent) return '';

        // Create a temporary div to parse HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        // Convert HTML back to plain text with basic formatting
        let text = tempDiv.innerHTML
            .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
            .replace(/<b>(.*?)<\/b>/g, '**$1**')
            .replace(/<em>(.*?)<\/em>/g, '*$1*')
            .replace(/<i>(.*?)<\/i>/g, '*$1*')
            .replace(/<u>(.*?)<\/u>/g, '__$1__')
            .replace(/<br\s*\/?>/g, '\n')
            .replace(/<\/div>/g, '\n')
            .replace(/<div>/g, '')
            .replace(/<ul>/g, '')
            .replace(/<\/ul>/g, '')
            .replace(/<ol>/g, '')
            .replace(/<\/ol>/g, '')
            .replace(/<li>(.*?)<\/li>/g, '• $1\n')
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#x27;/g, "'");

        // Clean up extra newlines
        return text.replace(/\n\s*\n/g, '\n').trim();
    }

    // Save a narrative entry
    async saveEntry(modalId) {
        const modal = document.getElementById(modalId);
        const form = modal.querySelector('#narrative-entry-form');
        const formData = new FormData(form);
        
        const entryType = modal.querySelector('#narrative-entry-type').value;
        const eventTime = modal.querySelector('#narrative-entry-time').value;
        const contentEditor = modal.querySelector('#narrative-content');
        const content = this.convertHtmlToStorage(contentEditor.innerHTML);
        
        if (!entryType || !content.trim()) {
            alert('Please fill in all required fields.');
            return;
        }

        try {
            const currentUser = window.app?.auth?.getCurrentUser();
            const entry = {
                id: this.isEditing ? this.editingEntryId : this.generateEntryId(),
                timestamp: this.isEditing ? 
                    this.entries.find(e => e.id === this.editingEntryId)?.timestamp : 
                    new Date().toISOString(),
                event_time: eventTime ? new Date(eventTime).toISOString() : new Date().toISOString(),
                user: currentUser?.email || 'Unknown User',
                entry_type: entryType,
                content: content.trim()
            };

            if (this.isEditing) {
                // Update existing entry
                const index = this.entries.findIndex(e => e.id === this.editingEntryId);
                if (index !== -1) {
                    this.entries[index] = entry;
                }
                this.isEditing = false;
                this.editingEntryId = null;
            } else {
                // Add new entry
                this.entries.push(entry);
            }

            // Save to incident if we have an incident ID
            if (this.currentIncidentId) {
                await this.saveToIncident();
            }

            // Close modal first
            modal.remove();
            
            // Show success message - the data change handler will handle re-rendering
            if (window.app?.showToast) {
                window.app.showToast(`Narrative entry ${this.isEditing ? 'updated' : 'added'} successfully`, 'success');
            }

        } catch (error) {
            console.error('Error saving narrative entry:', error);
            alert('Failed to save narrative entry. Please try again.');
        }
    }

    // Delete a narrative entry
    async deleteEntry(entryId) {
        if (!confirm('Are you sure you want to delete this narrative entry?')) {
            return;
        }

        try {
            this.entries = this.entries.filter(e => e.id !== entryId);
            
            // Save to incident if we have an incident ID
            if (this.currentIncidentId) {
                await this.saveToIncident();
            }

            // Show success message - the data change handler will handle re-rendering
            if (window.app?.showToast) {
                window.app.showToast('Narrative entry deleted successfully', 'success');
            }

        } catch (error) {
            console.error('Error deleting narrative entry:', error);
            alert('Failed to delete narrative entry. Please try again.');
        }
    }

    // Save entries to the incident
    async saveToIncident() {
        if (!this.currentIncidentId || !window.app?.data) {
            return;
        }

        try {
            const updateData = {
                log_entries: JSON.stringify(this.entries)
            };

            await window.app.data.update('incidents', this.currentIncidentId, updateData);
            
        } catch (error) {
            console.error('Error saving narrative entries to incident:', error);
            throw error;
        }
    }

    // Generate a unique entry ID
    generateEntryId() {
        return `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Get current date-time in local format for datetime-local input
    getCurrentDateTimeLocal() {
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        return now.toISOString().slice(0, 16);
    }

    // Format a date/time for datetime-local input
    formatDateTimeForInput(dateTime) {
        if (!dateTime) return this.getCurrentDateTimeLocal();
        
        const date = new Date(dateTime);
        date.setMinutes(date.getMinutes() - date.getTimezoneOffset());
        return date.toISOString().slice(0, 16);
    }

    // Load entries from incident data
    loadFromIncident(incident) {
        if (!incident) {
            this.entries = [];
            return;
        }

        try {
            const logEntries = incident.log_entries;

            // Handle different data types for log_entries
            if (!logEntries) {
                // null, undefined, or empty string
                this.entries = [];
            } else if (Array.isArray(logEntries)) {
                // Already parsed as array (from SQLite manager)
                this.entries = logEntries;
            } else if (typeof logEntries === 'object') {
                // Already parsed as object but not array
                this.entries = [];
                console.warn('log_entries is an object but not an array:', logEntries);
            } else if (typeof logEntries === 'string') {
                // JSON string that needs parsing
                if (logEntries.trim() === '') {
                    // Empty string
                    this.entries = [];
                } else {
                    // Try to parse JSON string
                    this.entries = JSON.parse(logEntries);
                    // Ensure it's an array
                    if (!Array.isArray(this.entries)) {
                        this.entries = [];
                        console.warn('Parsed log_entries is not an array:', this.entries);
                    }
                }
            } else {
                // Unknown type
                this.entries = [];
                console.warn('Unknown log_entries type:', typeof logEntries, logEntries);
            }
        } catch (error) {
            console.error('Error parsing log entries:', error);
            console.error('log_entries value:', incident.log_entries);
            console.error('log_entries type:', typeof incident.log_entries);
            this.entries = [];
        }
    }

    // Get entries for form submission
    getEntriesForSubmission() {
        return JSON.stringify(this.entries);
    }

    // Initialize Gemini service when data manager becomes available
    initializeGeminiService(dataManager, configManager) {
        if (!this.geminiService && dataManager && configManager) {
            // Use configManager.vaultManager instead of passing vaultManager directly
            this.geminiService = new GeminiService(dataManager, configManager.vaultManager);
            console.log('NarrativeEditor: Gemini service initialized');
        }
    }

    // Show AI enhancement modal
    async showAiEnhanceModal() {
        if (!this.currentEditor) {
            console.warn('No editor currently active for AI enhancement');
            return;
        }

        // Get current text content
        const currentText = this.currentEditor.textContent || this.currentEditor.innerText;
        if (!currentText || currentText.trim().length === 0) {
            alert('Please enter some text before using AI enhancement.');
            return;
        }

        // Initialize Gemini service if needed
        if (!this.geminiService && window.app?.data && window.app?.config) {
            this.initializeGeminiService(window.app.data, window.app.config);
        }

        if (!this.geminiService) {
            alert('AI enhancement service is not available. Please check your configuration.');
            return;
        }

        const modalId = `ai-enhance-modal-${Date.now()}`;
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = modalId;
        
        modal.innerHTML = `
            <div class="modal-content ai-enhance-modal">
                <div class="modal-header">
                    <h3>🤖 AI Text Enhancement</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="ai-enhance-content">
                        <div class="original-text-section">
                            <h4>Original Text:</h4>
                            <div class="text-preview original-text">${this.escapeHtml(currentText)}</div>
                        </div>
                        
                        <div class="enhanced-text-section">
                            <h4>AI Enhanced Text:</h4>
                            <div class="text-preview enhanced-text" id="enhanced-text-${modalId}">
                                <div class="loading-spinner">🤖 Enhancing text...</div>
                            </div>
                        </div>
                        
                        <div class="ai-enhancement-status" id="ai-status-${modalId}" style="display: none;">
                            <span class="status-message"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="action-btn primary" id="approve-enhancement-${modalId}" onclick="window.narrativeEditor.approveEnhancement('${modalId}')" disabled>
                        ✓ Approve & Replace
                    </button>
                    <button class="action-btn secondary" id="regenerate-enhancement-${modalId}" onclick="window.narrativeEditor.regenerateEnhancement('${modalId}')" disabled>
                        🔄 Regenerate
                    </button>
                    <button class="action-btn" onclick="this.closest('.modal-overlay').remove()">
                        ✗ Cancel
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Start AI enhancement
        this.performAiEnhancement(modalId, currentText);
    }

    // Perform AI text enhancement
    async performAiEnhancement(modalId, originalText) {
        try {
            console.log('Starting AI enhancement for text:', originalText.substring(0, 100) + '...');
            
            // Get incident context if available
            let incidentContext = null;
            if (this.currentIncidentId && window.app?.data) {
                try {
                    const incidents = await window.app.data.query('SELECT * FROM incidents WHERE id = ?', [this.currentIncidentId]);
                    if (incidents && incidents.length > 0) {
                        incidentContext = incidents[0];
                        console.log('Using incident context for AI enhancement:', incidentContext.incident_number);
                    }
                } catch (error) {
                    console.warn('Failed to load incident context for AI enhancement:', error);
                }
            }
            
            const enhancedText = await this.geminiService.enhanceText(originalText, null, incidentContext);
            
            // Update the enhanced text display
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            if (enhancedTextDiv) {
                enhancedTextDiv.innerHTML = this.formatTextForDisplay(enhancedText);
                enhancedTextDiv.dataset.enhancedText = enhancedText;
            }
            
            // Enable action buttons
            const approveBtn = document.getElementById(`approve-enhancement-${modalId}`);
            const regenerateBtn = document.getElementById(`regenerate-enhancement-${modalId}`);
            
            if (approveBtn) approveBtn.disabled = false;
            if (regenerateBtn) regenerateBtn.disabled = false;
            
            console.log('AI enhancement completed successfully');
            
        } catch (error) {
            console.error('AI enhancement failed:', error);
            
            // Show error message
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            if (enhancedTextDiv) {
                enhancedTextDiv.innerHTML = `
                    <div class="error-message">
                        <p><strong>Enhancement failed:</strong> ${error.message}</p>
                        <p>Please try again or check your AI configuration.</p>
                    </div>
                `;
            }
            
            // Show status message
            const statusDiv = document.getElementById(`ai-status-${modalId}`);
            if (statusDiv) {
                statusDiv.style.display = 'block';
                statusDiv.querySelector('.status-message').textContent = `Error: ${error.message}`;
                statusDiv.className = 'ai-enhancement-status error';
            }
            
            // Enable regenerate button only
            const regenerateBtn = document.getElementById(`regenerate-enhancement-${modalId}`);
            if (regenerateBtn) regenerateBtn.disabled = false;
        }
    }

    // Approve enhancement and replace original text
    approveEnhancement(modalId) {
        try {
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            const enhancedText = enhancedTextDiv?.dataset.enhancedText;
            
            if (!enhancedText) {
                alert('No enhanced text available to approve.');
                return;
            }
            
            if (!this.currentEditor) {
                alert('No editor available to update.');
                return;
            }
            
            // Clear current editor content
            this.currentEditor.innerHTML = '';
            
            // Insert enhanced text
            this.currentEditor.innerHTML = this.convertTextToHtml(enhancedText);
            
            // Remove empty class if present
            this.currentEditor.classList.remove('empty');
            
            // Focus the editor
            this.currentEditor.focus();
            
            // Close modal
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
            
            console.log('AI enhancement approved and applied');
            
        } catch (error) {
            console.error('Error applying AI enhancement:', error);
            alert('Failed to apply enhancement. Please try again.');
        }
    }

    // Regenerate enhancement
    regenerateEnhancement(modalId) {
        try {
            const modal = document.getElementById(modalId);
            const originalTextDiv = modal?.querySelector('.original-text');
            const originalText = originalTextDiv?.textContent;
            
            if (!originalText) {
                alert('Cannot regenerate - original text not found.');
                return;
            }
            
            // Reset enhanced text display
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            if (enhancedTextDiv) {
                enhancedTextDiv.innerHTML = '<div class="loading-spinner">🤖 Regenerating...</div>';
            }
            
            // Disable buttons during regeneration
            const approveBtn = document.getElementById(`approve-enhancement-${modalId}`);
            const regenerateBtn = document.getElementById(`regenerate-enhancement-${modalId}`);
            
            if (approveBtn) approveBtn.disabled = true;
            if (regenerateBtn) regenerateBtn.disabled = true;
            
            // Hide status message
            const statusDiv = document.getElementById(`ai-status-${modalId}`);
            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
            
            // Perform enhancement again
            this.performAiEnhancement(modalId, originalText);
            
        } catch (error) {
            console.error('Error regenerating enhancement:', error);
            alert('Failed to regenerate enhancement. Please try again.');
        }
    }

    // Helper method to escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Helper method to format text for display (similar to formatContent but for display)
    formatTextForDisplay(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // Helper method to convert plain text to HTML for editor
    convertTextToHtml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
}

// Create global instance
window.narrativeEditor = new NarrativeEditor();
