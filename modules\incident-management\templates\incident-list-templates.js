// Incident List Templates
// Extracted from app.js for better maintainability

export const incidentListTemplates = {
    // Incident list item template
    incidentListItem: (incident) => {
        const statusClass = `status-${incident.status}`;
        const priorityClass = `priority-${incident.priority}`;
        const typeDisplay = incident.incident_type?.replace('_', ' ').toUpperCase() || 'UNKNOWN';
        const dateDisplay = incident.incident_date ? new Date(incident.incident_date).toLocaleDateString() : 'N/A';
        const timeDisplay = incident.incident_time || 'N/A';
        const isDraft = incident.status === 'draft';
        const draftLabel = isDraft ? ' [DRAFT]' : '';
        
        return `
            <div class="incident-item ${isDraft ? 'incident-draft' : ''}" onclick="window.app.selectIncident(${incident.id})">
                <div class="incident-header">
                    <div class="incident-id">${incident.incident_number || `ID: ${incident.id}`}${draftLabel}</div>
                    <div class="incident-status ${statusClass}">${incident.status?.toUpperCase()}</div>
                </div>
                <div class="incident-details">
                    <div class="incident-type ${priorityClass}">${typeDisplay}</div>
                    <div class="incident-date">${dateDisplay} ${timeDisplay}</div>
                </div>
                <div class="incident-description">
                    ${incident.description ? incident.description.substring(0, 100) + (incident.description.length > 100 ? '...' : '') : 'No description'}
                </div>
                <div class="incident-location">
                    ${incident.street_address || incident.city ? 
                        `📍 ${incident.street_address || ''}${incident.street_address && incident.city ? ', ' : ''}${incident.city || ''}` : 
                        'No location specified'
                    }
                </div>
            </div>
        `;
    },

    // Incident table row template
    incidentTableRow: (incident) => {
        const statusClass = `status-${incident.status}`;
        const priorityClass = `priority-${incident.priority}`;
        const typeDisplay = incident.incident_type?.replace('_', ' ').toUpperCase() || 'UNKNOWN';
        const dateDisplay = incident.incident_date ? new Date(incident.incident_date).toLocaleDateString() : 'N/A';
        const timeDisplay = incident.incident_time || 'N/A';
        const isDraft = incident.status === 'draft';
        const draftLabel = isDraft ? ' [DRAFT]' : '';
        
        return `
            <tr class="incident-row ${isDraft ? 'incident-draft' : ''}" onclick="window.app.selectIncident(${incident.id})">
                <td>${incident.id}${draftLabel}</td>
                <td class="incident-type ${priorityClass}">${typeDisplay}</td>
                <td class="incident-status ${statusClass}">${incident.status?.toUpperCase()}</td>
                <td class="incident-priority ${priorityClass}">${incident.priority?.toUpperCase()}</td>
                <td>${dateDisplay}</td>
                <td>${timeDisplay}</td>
                <td class="incident-location">
                    ${incident.street_address || incident.city ? 
                        `${incident.street_address || ''}${incident.street_address && incident.city ? ', ' : ''}${incident.city || ''}` : 
                        'N/A'
                    }
                </td>
                <td class="incident-description">
                    ${incident.description ? incident.description.substring(0, 50) + (incident.description.length > 50 ? '...' : '') : 'No description'}
                </td>
            </tr>
        `;
    },

    // Incident search interface template
    incidentSearchInterface: () => `
        <div class="content-area">
            <div class="section-header">
                <h2>&gt; INCIDENT SEARCH</h2>
                <div class="header-actions">
                    <button onclick="window.app.loadIncidentsContent()" class="action-btn">[BACK TO INCIDENTS]</button>
                </div>
            </div>
            
            <div class="search-container">
                <div class="search-form">
                    <div class="form-section">
                        <h3>SEARCH CRITERIA</h3>
                        <div class="form-row">
                            <div class="form-group quarter-width">
                                <label for="search-incident-type">TYPE:</label>
                                <select id="search-incident-type">
                                    <option value="">ALL TYPES</option>
                                    <option value="outreach">OUTREACH</option>
                                    <option value="welfare_check">WELFARE CHECK</option>
                                    <option value="medical">MEDICAL</option>
                                    <option value="mental_health">MENTAL HEALTH</option>
                                    <option value="overdose">OVERDOSE</option>
                                    <option value="death">DEATH</option>
                                    <option value="assault">ASSAULT</option>
                                    <option value="theft">THEFT</option>
                                    <option value="property_damage">PROPERTY DAMAGE</option>
                                    <option value="fire">FIRE</option>
                                    <option value="cleanup">CLEANUP</option>
                                    <option value="supply_distribution">SUPPLY DISTRIBUTION</option>
                                    <option value="other">OTHER</option>
                                </select>
                            </div>
                            <div class="form-group quarter-width">
                                <label for="search-incident-status">STATUS:</label>
                                <select id="search-incident-status">
                                    <option value="">ALL STATUS</option>
                                    <option value="open">OPEN</option>
                                    <option value="in_progress">IN PROGRESS</option>
                                    <option value="resolved">RESOLVED</option>
                                    <option value="closed">CLOSED</option>
                                </select>
                            </div>
                            <div class="form-group quarter-width">
                                <label for="search-incident-priority">PRIORITY:</label>
                                <select id="search-incident-priority">
                                    <option value="">ALL PRIORITIES</option>
                                    <option value="low">LOW</option>
                                    <option value="medium">MEDIUM</option>
                                    <option value="high">HIGH</option>
                                    <option value="critical">CRITICAL</option>
                                </select>
                            </div>
                            <div class="form-group quarter-width">
                                <label for="search-police-involved">POLICE:</label>
                                <select id="search-police-involved">
                                    <option value="">ANY</option>
                                    <option value="true">INVOLVED</option>
                                    <option value="false">NOT INVOLVED</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group half-width">
                                <label for="search-date-from">FROM DATE:</label>
                                <input type="date" id="search-date-from">
                            </div>
                            <div class="form-group half-width">
                                <label for="search-date-to">TO DATE:</label>
                                <input type="date" id="search-date-to">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="search-keywords">KEYWORDS:</label>
                            <input type="text" id="search-keywords" placeholder="Search in description, location, people...">
                        </div>
                        
                        <div class="form-group">
                            <label for="search-location">LOCATION:</label>
                            <input type="text" id="search-location" placeholder="Street, city, or area...">
                        </div>
                        
                        <div class="form-actions">
                            <button onclick="window.app.performIncidentSearch()" class="action-btn primary">SEARCH</button>
                            <button onclick="window.app.clearIncidentSearch()" class="action-btn">CLEAR</button>
                        </div>
                    </div>
                </div>
                
                <div class="search-results-container">
                    <div id="search-results-header" class="results-header" style="display: none;">
                        <h3>SEARCH RESULTS <span id="results-count"></span></h3>
                    </div>
                    
                    <div id="search-results" class="search-results">
                        <div class="no-search">
                            <p>ENTER SEARCH CRITERIA AND CLICK SEARCH</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    // Search result item template
    searchResultItem: (incident) => {
        const statusClass = `status-${incident.status}`;
        const priorityClass = `priority-${incident.priority}`;
        const typeDisplay = incident.incident_type?.replace('_', ' ').toUpperCase() || 'UNKNOWN';
        const dateDisplay = incident.incident_date ? new Date(incident.incident_date).toLocaleDateString() : 'N/A';
        const timeDisplay = incident.incident_time || 'N/A';
        
        return `
            <div class="search-result-item" onclick="window.app.selectIncident(${incident.id})">
                <div class="result-header">
                    <div class="result-id">${incident.incident_number || `INCIDENT #${incident.id}`}</div>
                    <div class="result-status ${statusClass}">${incident.status?.toUpperCase()}</div>
                    <div class="result-priority ${priorityClass}">${incident.priority?.toUpperCase()}</div>
                </div>
                <div class="result-details">
                    <div class="result-type">${typeDisplay}</div>
                    <div class="result-date">${dateDisplay} ${timeDisplay}</div>
                </div>
                <div class="result-description">
                    ${incident.description ? incident.description.substring(0, 150) + (incident.description.length > 150 ? '...' : '') : 'No description'}
                </div>
                <div class="result-location">
                    ${incident.street_address || incident.city ? 
                        `📍 ${incident.street_address || ''}${incident.street_address && incident.city ? ', ' : ''}${incident.city || ''}` : 
                        'No location specified'
                    }
                </div>
                ${incident.police_involved ? '<div class="result-badge">🚔 POLICE INVOLVED</div>' : ''}
            </div>
        `;
    },

    // Dispatch incident detail template
    dispatchIncidentDetail: (incident) => `
        <div class="content-area dispatch-detail">
            <div class="section-header">
                <h2>&gt; INCIDENT DETAILS ${incident.incident_number || `#${incident.id}`}</h2>
                <div class="header-actions">
                    <button onclick="window.app.loadDispatchContent()" class="action-btn">[BACK TO DISPATCH]</button>
                    <button onclick="window.app.editIncident(${incident.id})" class="action-btn">EDIT</button>
                </div>
            </div>
            
            <div class="dispatch-incident-container">
                <div class="incident-summary">
                    <div class="summary-grid">
                        <div class="summary-item">
                            <label>TYPE:</label>
                            <span class="incident-type type-${incident.incident_type}">${incident.incident_type?.replace('_', ' ').toUpperCase()}</span>
                        </div>
                        <div class="summary-item">
                            <label>STATUS:</label>
                            <span class="incident-status status-${incident.status}">${incident.status?.toUpperCase()}</span>
                        </div>
                        <div class="summary-item">
                            <label>PRIORITY:</label>
                            <span class="incident-priority priority-${incident.priority}">${incident.priority?.toUpperCase()}</span>
                        </div>
                        <div class="summary-item">
                            <label>DATE/TIME:</label>
                            <span>${incident.incident_date ? new Date(incident.incident_date).toLocaleDateString() : 'N/A'} ${incident.incident_time || ''}</span>
                        </div>
                        <div class="summary-item">
                            <label>REPORTED BY:</label>
                            <span>${incident.reported_by || 'N/A'}</span>
                        </div>
                        <div class="summary-item">
                            <label>CREATED BY:</label>
                            <span>${incident.created_by_name || 'Unknown'}</span>
                        </div>
                    </div>
                </div>
                
                <div class="incident-location-summary">
                    <h3>LOCATION</h3>
                    <div class="location-details">
                        ${incident.street_address ? `<div><strong>Address:</strong> ${incident.street_address}</div>` : ''}
                        ${incident.city || incident.province || incident.postal_code ? 
                            `<div><strong>City:</strong> ${incident.city || 'N/A'}, ${incident.province || 'N/A'} ${incident.postal_code || ''}</div>` : ''
                        }
                        ${incident.location_notes ? `<div><strong>Notes:</strong> ${incident.location_notes}</div>` : ''}
                        ${incident.coordinates ? `<div><strong>Coordinates:</strong> ${incident.coordinates}</div>` : ''}
                    </div>
                </div>
                
                <div class="incident-description-detail">
                    <h3>DESCRIPTION</h3>
                    <div class="description-text">${incident.description || 'No description provided'}</div>
                </div>
                
                ${incident.immediate_actions ? `
                    <div class="incident-actions-detail">
                        <h3>IMMEDIATE ACTIONS TAKEN</h3>
                        <div class="actions-text">${incident.immediate_actions}</div>
                    </div>
                ` : ''}
                
                <div class="emergency-services-detail">
                    <h3>EMERGENCY SERVICES</h3>
                    <div class="services-grid">
                        <div class="service-status ${incident.police_involved ? 'active' : 'inactive'}">
                            <span class="service-icon">🚔</span>
                            <span class="service-label">POLICE</span>
                            <span class="service-value">${incident.police_involved ? 'YES' : 'NO'}</span>
                        </div>
                        <div class="service-status ${incident.fire_involved ? 'active' : 'inactive'}">
                            <span class="service-icon">🚒</span>
                            <span class="service-label">FIRE</span>
                            <span class="service-value">${incident.fire_involved ? 'YES' : 'NO'}</span>
                        </div>
                        <div class="service-status ${incident.ems_involved ? 'active' : 'inactive'}">
                            <span class="service-icon">🚑</span>
                            <span class="service-label">EMS</span>
                            <span class="service-value">${incident.ems_involved ? 'YES' : 'NO'}</span>
                        </div>
                    </div>
                    
                    ${incident.police_involved && (incident.police_file_number || incident.officers_attending) ? `
                        <div class="police-details">
                            ${incident.police_file_number ? `<div><strong>File Number:</strong> ${incident.police_file_number}</div>` : ''}
                            ${incident.officers_attending ? `<div><strong>Officers:</strong> ${incident.officers_attending}</div>` : ''}
                        </div>
                    ` : ''}
                </div>
                
                ${incident.follow_up_required ? `
                    <div class="follow-up-detail">
                        <h3>FOLLOW-UP REQUIRED</h3>
                        <div class="follow-up-text">${incident.follow_up_required}</div>
                    </div>
                ` : ''}
                
                ${incident.additional_notes ? `
                    <div class="notes-detail">
                        <h3>ADDITIONAL NOTES</h3>
                        <div class="notes-text">${incident.additional_notes}</div>
                    </div>
                ` : ''}
                
                <div class="incident-metadata">
                    <div class="metadata-grid">
                        <div class="metadata-item">
                            <label>Created:</label>
                            <span>${incident.created_at ? new Date(incident.created_at).toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="metadata-item">
                            <label>Last Updated:</label>
                            <span>${incident.updated_at ? new Date(incident.updated_at).toLocaleString() : 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    // Empty state template
    emptyIncidentList: () => `
        <div class="empty-state">
            <div class="empty-icon">📋</div>
            <div class="empty-message">NO INCIDENTS FOUND</div>
            <div class="empty-actions">
                <button onclick="window.app.loadCreateIncidentContent()" class="action-btn primary">CREATE FIRST INCIDENT</button>
            </div>
        </div>
    `,

    // Loading state template
    loadingIncidents: () => `
        <div class="loading-state">
            <div class="loading-spinner">⟳</div>
            <div class="loading-message">LOADING INCIDENTS...</div>
        </div>
    `
};