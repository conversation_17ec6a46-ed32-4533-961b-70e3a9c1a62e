import { BaseManager } from '../../shared/base-manager.js';
import { Logger } from '../../shared/logger.js';

/**
 * Dashboard Manager - Handles dashboard initialization, data loading, and widget management
 * Extracted from app.js to follow modular architecture
 */
export class DashboardManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, weatherService) {
        super(dataManager, authManager, 'dashboard', uiManager);
        this.weather = weatherService;
        this.logger = Logger.forModule('Dashboard');
        
        this.logger.info('Dashboard Manager initialized');
    }

    /**
     * Load dashboard content HTML
     */
    async loadDashboardContent() {
        // Force redesigned modular template to render and add a visible/console marker
        try {
            const { dashboardTemplates } = await import('../templates/dashboard-templates.js');
            const content = dashboardTemplates.dashboardLayout();
            this.logger.info('Dashboard content loaded');
            return content;
        } catch (error) {
            this.logger.error('Failed to load dashboard content', error);
            throw error;
        }
    }

    /**
     * Initialize dashboard components after DOM is ready
     */
    async initializeDashboard() {
        try {
            this.logger.info('🔄 Initializing dashboard components...');

            // Initialize weather widget
            await this.initializeWeatherWidget();

            // Initialize alerts panel placeholder content if present
            const alertsContainer = document.getElementById('alerts-content');
            if (alertsContainer) {
                alertsContainer.innerHTML = '<div class="placeholder">No alerts</div>';
            }

            // Set up event handlers for dashboard buttons
            this.setupDashboardEventHandlers();

            // Initialize forecast scroll if present
            const forecastScroll = document.getElementById('forecast-scroll');
            if (forecastScroll) {
                // Add keyboard navigation for forecast
                forecastScroll.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') {
                        forecastScroll.scrollBy({ left: -100, behavior: 'smooth' });
                    } else if (e.key === 'ArrowRight') {
                        forecastScroll.scrollBy({ left: 100, behavior: 'smooth' });
                    }
                });

                // Add mouse wheel support
                forecastScroll.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    forecastScroll.scrollBy({ left: e.deltaY, behavior: 'smooth' });
                });
            }

            // Initialize incidents list
            await this.loadDashboardIncidents();

            // Initialize stats
            await this.loadDashboardStats();

            // If using the new dashboard layout, ensure slider hooks are bound
            if (document.querySelector('.dashboard-container.redesigned')) {
                this.logger.info('✅ New dashboard layout detected and initialized');
            }

            this.logger.info('✅ Dashboard initialization complete');

        } catch (error) {
            this.logger.error('Dashboard initialization failed', error);
            throw error;
        }
    }

    /**
     * Set up event handlers for dashboard buttons
     */
    setupDashboardEventHandlers() {
        // Incidents refresh button
        const incidentsRefresh = document.querySelector('[data-action="refresh-incidents"]');
        if (incidentsRefresh) {
            incidentsRefresh.addEventListener('click', () => {
                this.loadDashboardIncidents();
            });
        }

        // Stats refresh button
        const statsRefresh = document.querySelector('[data-action="refresh-stats"]');
        if (statsRefresh) {
            statsRefresh.addEventListener('click', () => {
                this.loadDashboardStats();
            });
        }
    }

    /**
     * Initialize the weather widget
     */
    async initializeWeatherWidget() {
        try {
            this.logger.info('🔄 Initializing weather widget...');

            const weatherContent = document.getElementById('weather-content');
            if (!weatherContent) {
                this.logger.warn('Weather content container not found');
                return;
            }

            // Check if this is the new dashboard template structure
            const isNewDashboard = document.querySelector('.dashboard-container.redesigned') !== null;

            if (this.weather && isNewDashboard) {
                // Use weather service to get formatted data
                if (this.weather?.renderDashboardWeather) {
                    const weatherHtml = await this.weather.renderDashboardWeather();
                    if (weatherHtml) {
                        weatherContent.innerHTML = weatherHtml;
                        return;
                    }
                }
            }

            // Fallback: Get weather data and format it
            const weatherData = await this.weather?.getCurrentWeather();
            if (weatherData) {
                // Format and display weather data
                const formattedData = this.weather.formatWeatherData(weatherData);
                weatherContent.innerHTML = this.createWeatherDisplay(formattedData, weatherData);

                // If new template exists with slider hooks, populate slider container if empty
                const forecastGrid = document.getElementById('forecast-grid');
                if (forecastGrid && weatherData.forecast24h && Array.isArray(weatherData.forecast24h)) {
                    const forecastHtml = weatherData.forecast24h.map(hour => {
                        const time = new Date(hour.time).getHours().toString().padStart(2, '0') + ':00';
                        const icon = this.weather.getWeatherIcon(hour.condition);
                        return `
                            <div class="forecast-hour">
                                <div class="fh-time">${time}</div>
                                <div class="fh-icon">${icon}</div>
                                <div class="fh-temp">${Math.round(hour.temperature)}°</div>
                            </div>
                        `;
                    }).join('');
                    forecastGrid.innerHTML = forecastHtml;
                }
            } else {
                // Show loading or error state
                if (weatherContent) {
                    weatherContent.innerHTML = '<div class="weather-error">Weather data unavailable</div>';
                }
            }

            this.logger.info('✅ Weather widget initialized');

        } catch (error) {
            this.logger.error('Weather widget initialization failed', error);
        }
    }

    /**
     * Create weather display HTML
     */
    createWeatherDisplay(formattedData, rawData) {
        const icon = this.weather.getWeatherIcon(rawData.condition);
        const alertClass = rawData.alert ? 'weather-alert' : '';
        
        return `
            <div class="weather-display ${alertClass}">
                <div class="weather-main">
                    <div class="weather-icon">${icon}</div>
                    <div class="weather-temp">${formattedData.temperature}</div>
                    <div class="weather-condition">${formattedData.condition}</div>
                </div>
                <div class="weather-details">
                    <div class="weather-location">${formattedData.location}</div>
                    <div class="weather-meta">
                        <span>Feels like ${Math.round(rawData.feelsLike)}°</span>
                        <span>Humidity ${rawData.humidity}%</span>
                        <span>Wind ${Math.round(rawData.windSpeed)} km/h</span>
                    </div>
                </div>
                ${rawData.alert ? `<div class="weather-alert-text">${rawData.alert.title}</div>` : ''}
            </div>
        `;
    }

    /**
     * Load dashboard incidents
     * - Shows active incidents (not closed)
     * - Limited to 5 most recent for space efficiency
     * - Handles empty and error states
     */
    async loadDashboardIncidents() {
        const container = document.getElementById('incidents-content');
        const countEl = document.getElementById('incident-count');

        if (!container) {
            this.logger.warn('Incidents container not found');
            return;
        }

        try {
            this.logger.info('Loading dashboard incidents...');

            // Get active incidents
            const incidents = await this.data.search('incidents', {});
            const activeIncidents = incidents
                .filter(i => i.status !== 'closed')
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                .slice(0, 5);

            // Update count display
            if (countEl) {
                countEl.textContent = activeIncidents.length;
            }

            if (activeIncidents.length === 0) {
                container.innerHTML = `
                    <div class="no-incidents">
                        <p style="color: #00ff00;">✓ No active incidents</p>
                        <small>All incidents are resolved</small>
                    </div>
                `;
                return;
            }

            // Generate incident list HTML
            const incidentsHtml = activeIncidents.map(incident => {
                const priority = incident.priority || 'medium';
                const priorityClass = priority === 'high' ? 'priority-high' : 
                                   priority === 'low' ? 'priority-low' : 'priority-medium';
                const statusClass = `status-${incident.status || 'open'}`;
                
                return `
                    <div class="incident-row" data-incident-id="${incident.id}" onclick="window.app.selectIncident(${incident.id})">
                        <div class="row-main">
                            <span class="badge ${statusClass}">${(incident.status || 'open').toUpperCase()}</span>
                            <span class="badge ${priorityClass}">${priority.toUpperCase()}</span>
                            <span class="incident-type">${incident.incident_type || 'General'}</span>
                        </div>
                        <div class="incident-location">${(incident.location || 'No location').substring(0, 40)}${(incident.location || '').length > 40 ? '...' : ''}</div>
                    </div>
                `;
            }).join('');

            container.innerHTML = `<div class="incidents-dashboard-list">${incidentsHtml}</div>`;
            this.logger.info(`Loaded ${activeIncidents.length} dashboard incidents`);

        } catch (error) {
            this.logger.error('Failed to load dashboard incidents', error);
            if (container) {
                container.innerHTML = `
                    <div class="error-state">
                        <p style="color: #ff0000;">Error loading incidents</p>
                        <small>Please refresh or check connection</small>
                    </div>
                `;
            }
        }
    }

    /**
     * Load dashboard stats
     */
    async loadDashboardStats() {
        try {
            this.logger.info('🔄 Loading dashboard stats...');

            // Get counts for different entity types
            const [incidents, people, outreach] = await Promise.all([
                this.data.search('incidents', {}),
                this.data.search('people', {}),
                this.data.search('outreach_transactions', {})
            ]);

            // Calculate today's activity
            const today = new Date().toISOString().split('T')[0];
            const todayIncidents = incidents.filter(i => 
                new Date(i.created_at).toISOString().split('T')[0] === today
            );

            // Update stat displays
            const statsIncidents = document.getElementById('stats-incidents');
            const statsPeople = document.getElementById('stats-people');
            const statsOutreach = document.getElementById('stats-outreach');
            const statsToday = document.getElementById('stats-today');

            if (statsIncidents) statsIncidents.textContent = incidents.length;
            if (statsPeople) statsPeople.textContent = people.length;
            if (statsOutreach) statsOutreach.textContent = outreach.length;
            if (statsToday) statsToday.textContent = todayIncidents.length;

            this.logger.info('✅ Dashboard stats loaded');

        } catch (error) {
            this.logger.error('Failed to load dashboard stats', error);
            
            // Show error state in stats
            const errorText = 'Error';
            const statsIncidents = document.getElementById('stats-incidents');
            const statsPeople = document.getElementById('stats-people');
            const statsOutreach = document.getElementById('stats-outreach');
            const statsToday = document.getElementById('stats-today');

            if (statsIncidents) statsIncidents.textContent = errorText;
            if (statsPeople) statsPeople.textContent = errorText;
            if (statsOutreach) statsOutreach.textContent = errorText;
            if (statsToday) statsToday.textContent = errorText;
        }
    }
}