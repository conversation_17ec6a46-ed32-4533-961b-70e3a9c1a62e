/**
 * Vehicle List Manager
 * Handles vehicle list display and management
 */

import { BaseManager } from '../../shared/base-manager.js';
import { vehicleListTemplates } from '../templates/vehicle-list-templates.js';

export class VehicleListManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    /**
     * Load vehicles data
     */
    async loadVehiclesData() {
        try {
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: vehicles, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            return vehicles || [];
        } catch (error) {
            console.error('Error loading vehicles data:', error);
            throw error;
        }
    }

    /**
     * Show vehicle list view
     */
    async showVehicleListView(vehicles) {
        try {
            // Create modal overlay
            const listModal = document.createElement('div');
            listModal.className = 'modal-overlay vehicle-list-overlay';
            listModal.innerHTML = vehicleListTemplates.vehicleListModal(vehicles);

            document.body.appendChild(listModal);

            // Set up event handlers
            this.setupListHandlers(listModal, vehicles);

        } catch (error) {
            console.error('Error showing vehicle list view:', error);
            throw error;
        }
    }

    /**
     * Set up event handlers for list view
     */
    setupListHandlers(modal, vehicles) {
        // Back to records button
        const backBtn = modal.querySelector('#back-to-records-btn');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        }

        // Vehicle filter
        const filterInput = modal.querySelector('#vehicle-filter');
        if (filterInput) {
            filterInput.addEventListener('input', () => {
                this.filterVehicles(modal, vehicles, filterInput.value);
            });
        }

        // Clear filter button
        const clearFilterBtn = modal.querySelector('#clear-filter');
        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', () => {
                filterInput.value = '';
                this.filterVehicles(modal, vehicles, '');
            });
        }

        // Sort dropdown
        const sortSelect = modal.querySelector('#vehicle-sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.sortVehicles(modal, vehicles, sortSelect.value);
            });
        }

        // Vehicle card click handlers
        this.setupVehicleCardHandlers(modal);

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Set up vehicle card click handlers
     */
    setupVehicleCardHandlers(modal) {
        const vehicleCards = modal.querySelectorAll('.vehicle-list-item');
        vehicleCards.forEach(card => {
            card.addEventListener('click', () => {
                const vehicleId = card.getAttribute('data-vehicle-id');
                this.showVehicleDetail(vehicleId);
            });
        });
    }

    /**
     * Filter vehicles based on search term
     */
    filterVehicles(modal, vehicles, searchTerm) {
        const filteredVehicles = vehicles.filter(vehicle => {
            const searchLower = searchTerm.toLowerCase();
            return (
                (vehicle.plate_number && vehicle.plate_number.toLowerCase().includes(searchLower)) ||
                (vehicle.vehicle_make && vehicle.vehicle_make.toLowerCase().includes(searchLower)) ||
                (vehicle.vehicle_model && vehicle.vehicle_model.toLowerCase().includes(searchLower)) ||
                (vehicle.owner_name && vehicle.owner_name.toLowerCase().includes(searchLower)) ||
                (vehicle.province && vehicle.province.toLowerCase().includes(searchLower))
            );
        });

        this.updateVehicleGrid(modal, filteredVehicles);
    }

    /**
     * Sort vehicles based on selected field
     */
    sortVehicles(modal, vehicles, sortField) {
        const sortedVehicles = [...vehicles].sort((a, b) => {
            let aVal = a[sortField] || '';
            let bVal = b[sortField] || '';

            // Handle date sorting
            if (sortField === 'created_at') {
                return new Date(b.created_at) - new Date(a.created_at);
            }

            // Handle numeric sorting
            if (sortField === 'vehicle_year') {
                return (parseInt(bVal) || 0) - (parseInt(aVal) || 0);
            }

            // Handle string sorting
            return aVal.toString().localeCompare(bVal.toString());
        });

        this.updateVehicleGrid(modal, sortedVehicles);
    }

    /**
     * Update vehicle grid with new data
     */
    updateVehicleGrid(modal, vehicles) {
        const gridContainer = modal.querySelector('.vehicle-list-grid');
        if (gridContainer) {
            gridContainer.innerHTML = vehicles.map(vehicle => 
                vehicleListTemplates.vehicleCard(vehicle)
            ).join('');

            // Re-setup click handlers for new cards
            this.setupVehicleCardHandlers(modal);
        }
    }

    /**
     * Show vehicle detail (delegates to detail manager)
     */
    async showVehicleDetail(vehicleId) {
        try {
            // Close list modal
            const listModal = document.querySelector('.vehicle-list-overlay');
            if (listModal) {
                document.body.removeChild(listModal);
            }

            // Emit event to show vehicle detail
            const event = new CustomEvent('showVehicleDetail', {
                detail: { vehicleId: vehicleId }
            });
            window.dispatchEvent(event);

        } catch (error) {
            console.error('Error showing vehicle detail:', error);
            this.ui.showDialog('Error', `Failed to show vehicle details: ${error.message}`, 'error');
        }
    }

    /**
     * Refresh vehicles list
     */
    async refreshVehicles() {
        try {
            const vehicles = await this.loadVehiclesData();
            
            // Update existing list if open
            const listModal = document.querySelector('.vehicle-list-overlay');
            if (listModal) {
                const gridContainer = listModal.querySelector('.vehicle-list-grid');
                if (gridContainer) {
                    this.updateVehicleGrid(listModal, vehicles);
                }
            }

            return vehicles;
        } catch (error) {
            console.error('Error refreshing vehicles:', error);
            throw error;
        }
    }

    /**
     * Load vehicles content (for integration with app screens)
     */
    async loadVehiclesContent() {
        try {
            const vehicles = await this.loadVehiclesData();
            return vehicleListTemplates.vehicleListContent(vehicles);
        } catch (error) {
            console.error('Error loading vehicles content:', error);
            throw error;
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners or intervals if needed
    }
}
