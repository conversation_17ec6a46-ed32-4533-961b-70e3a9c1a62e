// Location Manager for S.T.E.V.I Retro
// Provides unified location services using GPS, native location, and fallbacks
export class LocationManager {
    constructor() {
        this.currentPosition = null;
        this.isGPSAvailable = false;
        this.isNativeLocationAvailable = false;
        this.subscribers = new Set();
        this.watchId = null;
        this.updateInterval = null;
        this.lastUpdateTime = null;
        
        // Default fallback location (Cobourg, ON)
        this.fallbackLocation = {
            latitude: 43.9589,
            longitude: -78.1648,
            accuracy: 500, // 500m accuracy for fallback location
            source: 'fallback'
        };
        
        // Configuration
        this.config = {
            updateInterval: 60000, // 60 seconds (1 minute) - battery saving
            maxAge: 120000, // 2 minutes
            timeout: 15000, // 15 seconds
            enableHighAccuracy: false // Disable high accuracy to save battery
        };
        
        this.initialize();
    }

    async initialize() {
        console.log('📍 LocationManager: Initializing...');
        
        try {
            // Wait a moment for electronAPI to be fully available
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Debug: Check what APIs are actually available
            console.log('📍 LocationManager: Checking API availability...');
            console.log('📍 LocationManager: window.electronAPI exists:', !!window.electronAPI);
            console.log('📍 LocationManager: window.electronAPI.gps exists:', !!(window.electronAPI && window.electronAPI.gps));
            console.log('📍 LocationManager: window.electronAPI.location exists:', !!(window.electronAPI && window.electronAPI.location));
            
            // Check if GPS API is available
            if (window.electronAPI && window.electronAPI.gps) {
                this.isGPSAvailable = true;
                console.log('✅ LocationManager: GPS API available');
                
                // Test GPS connection status
                try {
                    const isConnected = await window.electronAPI.gps.isConnected();
                    console.log('📍 LocationManager: GPS connected status:', isConnected);
                    
                    const gpsStatus = await window.electronAPI.gps.getStatus();
                    console.log('📍 LocationManager: GPS status:', gpsStatus);
                } catch (error) {
                    console.warn('📍 LocationManager: GPS status check failed:', error.message);
                }
                
                // Subscribe to GPS updates
                this.setupGPSSubscription();
                
                // Try to get initial GPS position with retry
                try {
                    console.log('📍 LocationManager: Requesting initial GPS position...');
                    const gpsPosition = await this.getGPSPositionWithRetry();
                    console.log('📍 LocationManager: GPS position received:', gpsPosition);
                    if (gpsPosition) {
                        this.updateCurrentPosition(gpsPosition, 'gps');
                    } else {
                        console.warn('📍 LocationManager: GPS position is null/undefined');
                    }
                } catch (error) {
                    console.warn('📍 LocationManager: Initial GPS position failed:', error.message);
                }
            } else {
                console.warn('📍 LocationManager: GPS API not available');
            }
            
            // Check native location availability
            if (window.electronAPI && window.electronAPI.location) {
                this.isNativeLocationAvailable = true;
                console.log('✅ LocationManager: Native location API available');
            } else {
                console.warn('📍 LocationManager: Native location API not available');
            }
            
            // If no position yet, try to get one from available sources
            if (!this.currentPosition) {
                console.log('📍 LocationManager: No position available, trying to get current position...');
                await this.getCurrentPosition();
            } else {
                console.log('📍 LocationManager: Already have position:', this.currentPosition);
            }
            
            // Listen for GPS becoming available
            this.setupGPSAvailabilityListener();
            
            // Start periodic updates
            this.startPeriodicUpdates();
            
            console.log('✅ LocationManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ LocationManager: Initialization failed:', error.message);
            // Use fallback location
            this.updateCurrentPosition(this.fallbackLocation, 'fallback');
        }
    }

    async getGPSPositionWithRetry(maxRetries = 3, delay = 2000) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                console.log(`📍 LocationManager: GPS position attempt ${i + 1}/${maxRetries}`);
                const position = await window.electronAPI.gps.getLatest();
                if (position) {
                    return position;
                }
                console.log(`📍 LocationManager: No GPS position on attempt ${i + 1}, waiting...`);
            } catch (error) {
                console.warn(`📍 LocationManager: GPS attempt ${i + 1} failed:`, error.message);
            }
            
            if (i < maxRetries - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        return null;
    }

    setupGPSAvailabilityListener() {
        try {
            // Listen for GPS becoming available
            if (window.electronAPI && window.electronAPI.gps && window.electronAPI.gps.onAvailable) {
                this.gpsAvailabilityUnsubscribe = window.electronAPI.gps.onAvailable(async () => {
                    console.log('📍 LocationManager: GPS API now available, setting up connection...');
                    
                    // Wait a moment for APIs to be fully available
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    if (window.electronAPI && window.electronAPI.gps) {
                        this.isGPSAvailable = true;
                        this.setupGPSSubscription();
                        
                        // Try to get initial GPS position
                        try {
                            const gpsPosition = await this.getGPSPositionWithRetry(3, 2000);
                            if (gpsPosition) {
                                console.log('📍 LocationManager: GPS position acquired after availability notification:', gpsPosition);
                                this.updateCurrentPosition(gpsPosition, 'gps');
                            }
                        } catch (error) {
                            console.warn('📍 LocationManager: GPS position failed after availability notification:', error.message);
                        }
                    }
                });
            }
        } catch (error) {
            console.warn('📍 LocationManager: Failed to setup GPS availability listener:', error.message);
        }
    }

    setupGPSSubscription() {
        if (!this.isGPSAvailable) return;
        
        try {
            let lastUpdateTime = 0;
            const updateThrottleMs = 60000; // 60 seconds
            
            // Subscribe to GPS updates with throttling
            this.gpsUnsubscribe = window.electronAPI.gps.onUpdate((position) => {
                const now = Date.now();
                
                // Only process updates every 60 seconds to reduce spam
                if (now - lastUpdateTime >= updateThrottleMs) {
                    console.log('📍 LocationManager: GPS position updated:', 
                               `${position.latitude.toFixed(6)}, ${position.longitude.toFixed(6)} (${position.source})`);
                    this.updateCurrentPosition(position, 'gps');
                    lastUpdateTime = now;
                } else {
                    // Still update the position but don't log it
                    this.updateCurrentPosition(position, 'gps');
                }
            });
            
            console.log('✅ LocationManager: GPS subscription established with 60s throttling');
        } catch (error) {
            console.error('❌ LocationManager: GPS subscription failed:', error.message);
        }
    }

    updateCurrentPosition(position, source) {
        const now = Date.now();
        
        // Create standardized position object
        const standardizedPosition = {
            latitude: position.latitude,
            longitude: position.longitude,
            accuracy: position.accuracy || 50,
            altitude: position.altitude || null,
            speed: position.speed || null,
            heading: position.course || position.heading || null,
            timestamp: position.timestamp || now,
            source: source
        };
        
        // Update current position
        this.currentPosition = standardizedPosition;
        this.lastUpdateTime = now;
        
        console.log(`📍 LocationManager: Position updated from ${source}:`, 
                   `${standardizedPosition.latitude.toFixed(6)}, ${standardizedPosition.longitude.toFixed(6)}`);
        
        // Notify subscribers
        this.notifySubscribers(standardizedPosition);
    }

    async getCurrentPosition(options = {}) {
        const mergedOptions = { ...this.config, ...options };
        
        console.log('📍 LocationManager: Getting current position...');
        
        // If we have a recent position, return it
        if (this.currentPosition && this.lastUpdateTime) {
            const age = Date.now() - this.lastUpdateTime;
            if (age < mergedOptions.maxAge) {
                console.log('📍 LocationManager: Returning cached position');
                return this.currentPosition;
            }
        }
        
        // Try GPS first
        if (this.isGPSAvailable) {
            try {
                console.log('📍 LocationManager: Trying GPS...');
                const gpsPosition = await this.getGPSPositionWithRetry(2, 1000); // Shorter retry for getCurrentPosition
                if (gpsPosition) {
                    this.updateCurrentPosition(gpsPosition, 'gps');
                    return this.currentPosition;
                }
            } catch (error) {
                console.warn('📍 LocationManager: GPS failed:', error.message);
            }
        }
        
        // Try native location services
        if (this.isNativeLocationAvailable) {
            try {
                console.log('📍 LocationManager: Trying native location...');
                const nativePosition = await window.electronAPI.location.getCurrentPosition(mergedOptions);
                if (nativePosition && nativePosition.coords) {
                    this.updateCurrentPosition({
                        latitude: nativePosition.coords.latitude,
                        longitude: nativePosition.coords.longitude,
                        accuracy: nativePosition.coords.accuracy,
                        altitude: nativePosition.coords.altitude,
                        speed: nativePosition.coords.speed,
                        heading: nativePosition.coords.heading,
                        timestamp: nativePosition.timestamp
                    }, 'native');
                    return this.currentPosition;
                }
            } catch (error) {
                console.warn('📍 LocationManager: Native location failed:', error.message);
            }
        }
        
        // Fallback to default location
        console.warn('📍 LocationManager: Using fallback location');
        this.updateCurrentPosition(this.fallbackLocation, 'fallback');
        return this.currentPosition;
    }

    subscribe(callback) {
        this.subscribers.add(callback);
        
        // Immediately call with current position if available
        if (this.currentPosition) {
            try {
                callback(this.currentPosition);
            } catch (error) {
                console.error('📍 LocationManager: Subscriber callback error:', error);
            }
        }
        
        // Return unsubscribe function
        return () => {
            this.subscribers.delete(callback);
        };
    }

    notifySubscribers(position) {
        this.subscribers.forEach(callback => {
            try {
                callback(position);
            } catch (error) {
                console.error('📍 LocationManager: Subscriber callback error:', error);
            }
        });
    }

    startPeriodicUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.updateInterval = setInterval(async () => {
            try {
                // Check if GPS became available since initialization
                if (!this.isGPSAvailable && window.electronAPI && window.electronAPI.gps) {
                    console.log('📍 LocationManager: GPS API now available, setting up connection...');
                    this.isGPSAvailable = true;
                    this.setupGPSSubscription();
                    
                    // Try to get initial GPS position
                    try {
                        const gpsPosition = await this.getGPSPositionWithRetry(3, 2000);
                        if (gpsPosition) {
                            console.log('📍 LocationManager: GPS position acquired during periodic check:', gpsPosition);
                            this.updateCurrentPosition(gpsPosition, 'gps');
                        }
                    } catch (error) {
                        console.warn('📍 LocationManager: GPS position failed during periodic check:', error.message);
                    }
                }
                
                await this.getCurrentPosition();
            } catch (error) {
                console.error('📍 LocationManager: Periodic update failed:', error.message);
            }
        }, this.config.updateInterval);
        
        console.log(`📍 LocationManager: Periodic updates started (${this.config.updateInterval/1000}s interval for battery saving)`);
    }

    stopPeriodicUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            console.log('📍 LocationManager: Periodic updates stopped');
        }
    }

    getLocationString() {
        if (!this.currentPosition) {
            return 'Location unavailable';
        }
        
        const { latitude, longitude, source } = this.currentPosition;
        return `${latitude.toFixed(6)}, ${longitude.toFixed(6)} (${source})`;
    }

    getCoordinates() {
        if (!this.currentPosition) {
            return this.fallbackLocation;
        }
        
        return {
            lat: this.currentPosition.latitude,
            lng: this.currentPosition.longitude,
            latitude: this.currentPosition.latitude,
            longitude: this.currentPosition.longitude
        };
    }

    isLocationAvailable() {
        return this.currentPosition !== null;
    }

    getLocationSource() {
        return this.currentPosition ? this.currentPosition.source : 'none';
    }

    async getLocationName() {
        if (!this.currentPosition) {
            return 'Unknown location';
        }
        
        const { latitude, longitude, source } = this.currentPosition;
        
        try {
            // Try reverse geocoding first
            const locationName = await this.reverseGeocode(latitude, longitude);
            if (locationName) {
                return `${locationName} (${source})`;
            }
        } catch (error) {
            console.warn('LocationManager: Reverse geocoding failed:', error.message);
        }
        
        // Fallback to simple distance check for Cobourg area
        const cobourgLat = 43.9589;
        const cobourgLng = -78.1648;
        const distance = this.calculateDistance(latitude, longitude, cobourgLat, cobourgLng);
        
        if (distance < 10) {
            return `Cobourg, ON area (${source})`;
        }
        
        return `${latitude.toFixed(4)}, ${longitude.toFixed(4)} (${source})`;
    }

    async reverseGeocode(latitude, longitude) {
        try {
            // Use Google Maps Geocoding API for reverse geocoding
            const apiKey = await this.getGoogleApiKey();
            if (!apiKey) {
                console.warn('LocationManager: No Google API key available for reverse geocoding');
                return null;
            }

            const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Geocoding API error: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.status === 'OK' && data.results.length > 0) {
                // Look for city/locality in the address components
                const result = data.results[0];
                let city = null;
                let province = null;
                
                for (const component of result.address_components) {
                    if (component.types.includes('locality') || component.types.includes('sublocality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        province = component.short_name;
                    }
                }
                
                if (city && province) {
                    return `${city}, ${province}`;
                } else if (city) {
                    return city;
                } else {
                    // Use formatted address as fallback
                    const formattedAddress = result.formatted_address;
                    // Extract first part (usually city, province)
                    const parts = formattedAddress.split(',');
                    if (parts.length >= 2) {
                        return `${parts[0].trim()}, ${parts[1].trim()}`;
                    }
                    return parts[0].trim();
                }
            }
            
            return null;
        } catch (error) {
            console.error('LocationManager: Reverse geocoding error:', error.message);
            return null;
        }
    }

    async getGoogleApiKey() {
        try {
            // Access the global config manager if available
            if (window.app && window.app.config) {
                return await window.app.config.getGoogleApiKey();
            }
            
            // Fallback: try to get it from the vault manager directly
            if (window.electronAPI && window.electronAPI.invoke) {
                const result = await window.electronAPI.invoke('vault-get-secret', 'google_api_key');
                return result.success ? result.value : null;
            }
            
            return null;
        } catch (error) {
            console.warn('LocationManager: Could not get Google API key:', error.message);
            return null;
        }
    }

    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Earth's radius in km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    destroy() {
        console.log('📍 LocationManager: Destroying...');
        
        this.stopPeriodicUpdates();
        
        if (this.gpsUnsubscribe) {
            this.gpsUnsubscribe();
        }
        
        if (this.gpsAvailabilityUnsubscribe) {
            this.gpsAvailabilityUnsubscribe();
        }
        
        this.subscribers.clear();
        this.currentPosition = null;
        
        console.log('✅ LocationManager: Destroyed');
    }
}
