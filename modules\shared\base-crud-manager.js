// Base CRUD Manager - Eliminates duplication of CRUD operations across managers
// Extends BaseManager with standardized Create, Read, Update, Delete operations

import { BaseManager } from './base-manager.js';
import { IdGenerator } from './id-generator.js';
import { SearchUtility } from './search-utility.js';
import { StatsGenerator } from './stats-generator.js';

export class BaseCrudManager extends BaseManager {
    constructor(dataManager, authManager, tableName, entityType, uiManager = null, configManager = null) {
        super(dataManager, authManager, uiManager, configManager);
        this.tableName = tableName;
        this.entityType = entityType;
        this.idField = 'id';
        this.searchConfig = SearchUtility.getEntitySearchConfig(entityType);
        this.statsConfig = StatsGenerator.getEntityStatsConfig(entityType);
    }

    /**
     * Create a new entity record
     * @param {Object} entityData - Data for the new entity
     * @param {Object} options - Creation options
     * @returns {Promise<Object>} Created entity
     */
    async create(entityData, options = {}) {
        return this.executeWithErrorHandling(async () => {
            // Clean and validate data
            const cleanedData = this.cleanData(entityData, options.cleaningOptions);
            
            // Add metadata
            const dataWithMetadata = this.addCreateMetadata(cleanedData);
            
            // Generate ID if needed
            if (options.generateId && !dataWithMetadata[this.idField]) {
                dataWithMetadata[this.idField] = this.generateEntityId(options.idType);
            }
            
            // Validate if schema available
            if (options.validate && this.validateCreate) {
                const validation = this.validateCreate(dataWithMetadata);
                if (!validation.isValid) {
                    throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
                }
            }
            
            // Create the record
            const result = await this.data.insert(this.tableName, dataWithMetadata);
            
            // Log activity if enabled
            if (options.logActivity !== false) {
                await this.logEntityActivity(result[this.idField], 'created', dataWithMetadata);
            }
            
            return result;
        }, `creating ${this.entityType}`, options);
    }

    /**
     * Get entity by ID
     * @param {string} entityId - Entity ID
     * @param {Object} options - Retrieval options
     * @returns {Promise<Object|null>} Entity or null
     */
    async getById(entityId, options = {}) {
        return this.executeWithErrorHandling(async () => {
            if (!entityId) {
                throw new Error(`${this.entityType} ID is required`);
            }
            
            return await this.data.get(this.tableName, entityId);
        }, `getting ${this.entityType} by ID`, options);
    }

    /**
     * Get all entities with optional filtering
     * @param {Object} filters - Filter criteria
     * @param {Object} options - Retrieval options
     * @returns {Promise<Array>} Array of entities
     */
    async getAll(filters = {}, options = {}) {
        return this.executeWithErrorHandling(async () => {
            let entities = await this.data.getAll(this.tableName);
            
            if (!entities) return [];
            
            // Apply filters if provided
            if (Object.keys(filters).length > 0) {
                entities = SearchUtility.filter(entities, filters, options.filterOptions);
            }
            
            // Apply default sorting
            if (this.searchConfig.defaultSort && !options.skipDefaultSort) {
                entities = SearchUtility.sort(entities, this.searchConfig.defaultSort);
            }
            
            return entities;
        }, `getting all ${this.entityType}s`, options);
    }

    /**
     * Update entity by ID
     * @param {string} entityId - Entity ID
     * @param {Object} updateData - Data to update
     * @param {Object} options - Update options
     * @returns {Promise<Object>} Updated entity
     */
    async update(entityId, updateData, options = {}) {
        return this.executeWithErrorHandling(async () => {
            if (!entityId) {
                throw new Error(`${this.entityType} ID is required`);
            }
            
            // Clean data
            const cleanedData = this.cleanData(updateData, options.cleaningOptions);
            
            // Add update metadata
            const dataWithMetadata = this.addUpdateMetadata(cleanedData);
            
            // Validate if schema available
            if (options.validate && this.validateUpdate) {
                const validation = this.validateUpdate(dataWithMetadata);
                if (!validation.isValid) {
                    throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
                }
            }
            
            // Update the record
            const result = await this.data.update(this.tableName, entityId, dataWithMetadata);
            
            // Log activity if enabled
            if (options.logActivity !== false) {
                await this.logEntityActivity(entityId, 'updated', dataWithMetadata);
            }
            
            return result;
        }, `updating ${this.entityType}`, options);
    }

    /**
     * Delete entity by ID
     * @param {string} entityId - Entity ID
     * @param {Object} options - Deletion options
     * @returns {Promise<boolean>} Success status
     */
    async delete(entityId, options = {}) {
        return this.executeWithErrorHandling(async () => {
            if (!entityId) {
                throw new Error(`${this.entityType} ID is required`);
            }
            
            // Get entity for logging before deletion
            let entityData = null;
            if (options.logActivity !== false) {
                entityData = await this.getById(entityId, { suppressError: true });
            }
            
            // Delete the record
            const result = await this.data.delete(this.tableName, entityId);
            
            // Log activity if enabled
            if (options.logActivity !== false && entityData) {
                await this.logEntityActivity(entityId, 'deleted', entityData);
            }
            
            return result;
        }, `deleting ${this.entityType}`, options);
    }

    /**
     * Search entities
     * @param {string} searchTerm - Search term
     * @param {Object} options - Search options
     * @returns {Promise<Array>} Search results
     */
    async search(searchTerm, options = {}) {
        return this.executeWithErrorHandling(async () => {
            const entities = await this.getAll({}, { skipDefaultSort: true });
            
            const searchFields = options.searchFields || this.searchConfig.searchFields;
            const results = SearchUtility.search(entities, searchTerm, searchFields, options.searchOptions);
            
            // Apply sorting
            const sortCriteria = options.sortCriteria || this.searchConfig.defaultSort;
            return SearchUtility.sort(results, sortCriteria);
        }, `searching ${this.entityType}s`, options);
    }

    /**
     * Get statistics for entities
     * @param {Object} options - Statistics options
     * @returns {Promise<Object>} Statistics object
     */
    async getStats(options = {}) {
        return this.executeWithErrorHandling(async () => {
            const entities = await this.getAll();
            const config = { ...this.statsConfig, ...options.customConfig };
            
            return StatsGenerator.generateEntityStats(entities, this.entityType, config);
        }, `generating ${this.entityType} statistics`, options);
    }

    /**
     * Generate ID for entity (override in subclasses for custom logic)
     * @param {string} idType - Type of ID to generate
     * @returns {string} Generated ID
     */
    generateEntityId(idType = 'default') {
        // Try to use specific generator method if available
        const methodName = `generate${this.entityType.charAt(0).toUpperCase() + this.entityType.slice(1)}Id`;
        if (typeof IdGenerator[methodName] === 'function') {
            return IdGenerator[methodName]();
        }
        
        // Fallback to generic generation
        switch (idType) {
            case 'date':
                return IdGenerator.generateDateBasedId(this.entityType.toUpperCase());
            case 'base36':
                return IdGenerator.generateBase36Id(this.entityType);
            case 'uuid':
                return IdGenerator.generateUuidStyleId(this.entityType);
            default:
                return IdGenerator.generateDateBasedId(this.entityType.toUpperCase(), { timeBasedSuffix: true });
        }
    }

    /**
     * Log entity activity (override in subclasses if needed)
     * @param {string} entityId - Entity ID
     * @param {string} action - Action performed
     * @param {Object} data - Related data
     * @returns {Promise<void>}
     */
    async logEntityActivity(entityId, action, data = {}) {
        try {
            const context = this.getCurrentUserContext();
            const activityData = {
                entity_type: this.entityType,
                entity_id: entityId,
                action,
                user_email: context.userEmail,
                timestamp: context.timestamp,
                details: JSON.stringify(data)
            };
            
            // Use audit schema if available
            if (this.data.insert) {
                await this.data.insert('activity_logs', activityData);
            }
        } catch (error) {
            console.warn(`Failed to log ${this.entityType} activity:`, error);
            // Don't throw - activity logging shouldn't break main operations
        }
    }

    /**
     * Bulk operations
     */
    
    /**
     * Create multiple entities
     * @param {Array} entitiesData - Array of entity data objects
     * @param {Object} options - Bulk creation options
     * @returns {Promise<Array>} Created entities
     */
    async bulkCreate(entitiesData, options = {}) {
        return this.executeWithErrorHandling(async () => {
            const results = [];
            
            for (const entityData of entitiesData) {
                try {
                    const result = await this.create(entityData, { 
                        ...options, 
                        logActivity: false // Bulk log at the end
                    });
                    results.push(result);
                } catch (error) {
                    if (options.continueOnError) {
                        console.warn(`Failed to create ${this.entityType}:`, error);
                        results.push({ error: error.message, data: entityData });
                    } else {
                        throw error;
                    }
                }
            }
            
            // Bulk activity logging
            if (options.logActivity !== false) {
                await this.logEntityActivity('bulk', 'bulk_created', { 
                    count: results.length,
                    successful: results.filter(r => !r.error).length
                });
            }
            
            return results;
        }, `bulk creating ${this.entityType}s`, options);
    }

    /**
     * Update multiple entities
     * @param {Array} updates - Array of {id, data} objects
     * @param {Object} options - Bulk update options
     * @returns {Promise<Array>} Update results
     */
    async bulkUpdate(updates, options = {}) {
        return this.executeWithErrorHandling(async () => {
            const results = [];
            
            for (const { id, data } of updates) {
                try {
                    const result = await this.update(id, data, { 
                        ...options, 
                        logActivity: false // Bulk log at the end
                    });
                    results.push(result);
                } catch (error) {
                    if (options.continueOnError) {
                        console.warn(`Failed to update ${this.entityType} ${id}:`, error);
                        results.push({ error: error.message, id, data });
                    } else {
                        throw error;
                    }
                }
            }
            
            // Bulk activity logging
            if (options.logActivity !== false) {
                await this.logEntityActivity('bulk', 'bulk_updated', { 
                    count: results.length,
                    successful: results.filter(r => !r.error).length
                });
            }
            
            return results;
        }, `bulk updating ${this.entityType}s`, options);
    }
}