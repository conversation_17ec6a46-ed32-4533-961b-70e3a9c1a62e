// Timeline View
// Displays ordered events and provides Add Event actions

import { timelineTemplates } from '../../templates/timeline-templates.js';

export class TimelineView {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.container = null;
        this.episode = null;
        this.events = [];
    }
    
    async mount(container, episode) {
        this.container = container;
        this.episode = episode;
        
        try {
            // Load events for the episode
            this.events = await this.justice.api.getEpisodeEvents(episode.id);
            
            // Update state
            this.justice.state.setEvents(this.events);
            
            // Render timeline
            this.render();
            
        } catch (error) {
            console.error('Failed to mount timeline view:', error);
            this.renderError(error.message);
        }
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = timelineTemplates.timelineView({
            episode: this.episode,
            events: this.events
        });
        
        this.setupEventHandlers();
    }
    
    renderError(message) {
        if (!this.container) return;
        
        this.container.innerHTML = timelineTemplates.errorView(message);
    }
    
    setupEventHandlers() {
        if (!this.container) return;
        
        // Add Event buttons
        const addEventBtns = this.container.querySelectorAll('[data-action^="je:add-"]');
        addEventBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                const episodeId = btn.dataset.episodeId || this.episode.id;
                
                // Delegate to command system
                if (window.app && window.app.commands) {
                    window.app.commands.executeCommand(action, { episodeId });
                }
            });
        });
        
        // Event detail toggles
        const eventItems = this.container.querySelectorAll('.timeline-event');
        eventItems.forEach(item => {
            const header = item.querySelector('.event-header');
            if (header) {
                header.addEventListener('click', () => {
                    this.toggleEventDetails(item);
                });
            }
        });
        
        // Refresh button
        const refreshBtn = this.container.querySelector('[data-action="refresh-timeline"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.refresh();
            });
        }
    }
    
    toggleEventDetails(eventItem) {
        const details = eventItem.querySelector('.event-details');
        const toggle = eventItem.querySelector('.event-toggle');
        
        if (details && toggle) {
            const isExpanded = details.style.display !== 'none';
            details.style.display = isExpanded ? 'none' : 'block';
            toggle.textContent = isExpanded ? '▶' : '▼';
        }
    }
    
    async refresh() {
        if (!this.episode) return;
        
        try {
            // Reload events
            this.events = await this.justice.api.getEpisodeEvents(this.episode.id);
            this.justice.state.setEvents(this.events);
            
            // Re-render
            this.render();
            
        } catch (error) {
            console.error('Failed to refresh timeline:', error);
            this.justice.ui.showDialog('Error', `Failed to refresh timeline: ${error.message}`, 'error');
        }
    }
    
    async addEvent(eventType, eventData) {
        try {
            // Add event via API
            const newEvent = await this.justice.api.addEvent(
                this.episode.id,
                eventType,
                eventData.event_dt,
                eventData.payload
            );
            
            // Update local state
            this.events.push(newEvent);
            this.events.sort((a, b) => new Date(a.event_dt) - new Date(b.event_dt));
            this.justice.state.setEvents(this.events);
            
            // Re-render timeline
            this.render();
            
            return newEvent;
            
        } catch (error) {
            console.error('Failed to add event:', error);
            throw error;
        }
    }
    
    formatEventDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    getEventTypeLabel(eventType) {
        const labels = {
            'ARREST': 'Arrest',
            'SUMMONS': 'Summons',
            'BAIL_HEARING': 'Bail Hearing',
            'TRANSFER_TO_FACILITY': 'Transfer to Facility',
            'RELEASE_ORDER': 'Release Order',
            'CONDITIONS_SET': 'Conditions Set',
            'PROBATION_ORDER': 'Probation Order',
            'COURT_APPEARANCE': 'Court Appearance',
            'SENTENCE': 'Sentence',
            'WARRANT_ISSUED': 'Warrant Issued',
            'WARRANT_EXECUTED': 'Warrant Executed'
        };
        
        return labels[eventType] || eventType;
    }
    
    getEventIcon(eventType) {
        const icons = {
            'ARREST': '👮',
            'SUMMONS': '📋',
            'BAIL_HEARING': '⚖️',
            'TRANSFER_TO_FACILITY': '🏢',
            'RELEASE_ORDER': '🚪',
            'CONDITIONS_SET': '📝',
            'PROBATION_ORDER': '📋',
            'COURT_APPEARANCE': '🏛️',
            'SENTENCE': '⚖️',
            'WARRANT_ISSUED': '📜',
            'WARRANT_EXECUTED': '👮'
        };
        
        return icons[eventType] || '📅';
    }
    
    unmount() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.container = null;
        this.episode = null;
        this.events = [];
    }
}
