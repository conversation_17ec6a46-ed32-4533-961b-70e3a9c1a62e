/**
 * Medical Manager
 * Handles medical information management for people
 */

import { BaseManager } from '../../shared/base-manager.js';

export class MedicalManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.onMedicalChanged = null; // Callback for when medical info changes
    }

    /**
     * Add medical issue to person
     */
    async addMedicalIssue(personId) {
        try {
            // Get current user info
            const currentUser = this.auth.getCurrentUser();
            
            // Pre-populate default values
            const defaultData = {
                person_id: personId,
                category: 'Other',
                source_type: 'Observation',
                is_active: true,
                created_by: currentUser?.email || 'unknown',
                created_at: new Date().toISOString()
            };

            // Generate all form fields and filter for general medical issues
            const allFields = this.data.schema.generateFormFields('medical_issues', ['id', 'created_at', 'created_by', 'person_id', 'updated_at', 'updated_by']);
            
            const generalMedicalFields = [
                'condition_name',
                'category',
                'subcategory',
                'source_type',
                'diagnosis_date',
                'severity',
                'status',
                'is_active',
                'treatment_notes',
                'medication',
                'healthcare_provider',
                'follow_up_required',
                'follow_up_date',
                'notes'
            ];

            const fields = allFields.filter(field => generalMedicalFields.includes(field.name));

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            this.ui.showFullScreenForm('Add Medical Issue', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('medical_issues', formData);
                    
                    const medicalData = {
                        ...defaultData,
                        ...dbData
                    };

                    await this.data.insert('medical_issues', medicalData);

                    this.ui.showDialog(
                        'Medical Issue Added',
                        `Medical condition "${formData.condition_name}" has been added.`,
                        'success'
                    );

                    // Refresh the medical issues list
                    this.loadPersonMedicalIssues(personId);

                    // Trigger change callback
                    if (this.onMedicalChanged) {
                        this.onMedicalChanged();
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add medical issue: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error adding medical issue:', error);
            this.ui.showDialog('Error', 'Failed to add medical issue', 'error');
        }
    }

    /**
     * Show medical info dialog for person index (legacy support)
     */
    showMedicalInfoDialog(personIndex) {
        // This appears to be a legacy method for handling medical info
        // In the modular system, we'll redirect to the proper medical issue workflow
        console.warn('showMedicalInfoDialog called - this method is deprecated in the modular system');
        
        // For now, show a simple dialog explaining the new workflow
        this.ui.showDialog(
            'Medical Information',
            'Medical information is now managed through the person detail view. Please navigate to the person\'s detail page to add or edit medical issues.',
            'info'
        );
    }

    /**
     * Save medical info (legacy support)
     */
    async saveMedicalInfo(personIndex) {
        // This appears to be a legacy method for saving medical info
        // In the modular system, medical info is saved through the form workflow
        console.warn('saveMedicalInfo called - this method is deprecated in the modular system');
        
        this.ui.showDialog(
            'Medical Information',
            'Medical information is now saved automatically through the medical issue forms. Please use the "Add Medical Issue" button in the person detail view.',
            'info'
        );
    }

    /**
     * Load medical issues for a specific person
     */
    async loadPersonMedicalIssues(personId) {
        try {
            const medicalIssues = await this.data.search('medical_issues', { person_id: personId });
            
            const medicalContainer = document.getElementById('person-medical-issues');
            if (medicalContainer) {
                if (medicalIssues.length === 0) {
                    medicalContainer.innerHTML = `
                        <div class="no-medical-issues">
                            <p>No medical issues recorded for this person.</p>
                            <button class="primary-button" data-action="add-medical-issue" data-person-id="${personId}">
                                <span class="button-icon">🏥</span>
                                Add Medical Issue
                            </button>
                        </div>
                    `;
                } else {
                    medicalContainer.innerHTML = `
                        <div class="medical-issues-header">
                            <h4>Medical Issues (${medicalIssues.length})</h4>
                            <button class="primary-button" data-action="add-medical-issue" data-person-id="${personId}">
                                <span class="button-icon">🏥</span>
                                Add Medical Issue
                            </button>
                        </div>
                        <div class="medical-issues-list">
                            ${medicalIssues.map(issue => this.renderMedicalIssueCard(issue)).join('')}
                        </div>
                    `;
                }

                // Set up event handlers for medical issue actions
                this.setupMedicalEventHandlers(medicalContainer);
            }

            return medicalIssues;
        } catch (error) {
            console.error('Error loading person medical issues:', error);
            const medicalContainer = document.getElementById('person-medical-issues');
            if (medicalContainer) {
                medicalContainer.innerHTML = '<p class="error">Failed to load medical issues.</p>';
            }
            return [];
        }
    }

    /**
     * Render medical issue card HTML
     */
    renderMedicalIssueCard(issue) {
        const severityClass = this.getSeverityClass(issue.severity);
        const statusClass = this.getStatusClass(issue.status);
        
        return `
            <div class="medical-issue-card ${severityClass}" data-issue-id="${issue.id}">
                <div class="medical-issue-header">
                    <h5 class="condition-name">${issue.condition_name}</h5>
                    <div class="medical-issue-actions">
                        <button class="action-btn edit-btn" data-action="edit-medical-issue" data-issue-id="${issue.id}" title="Edit Medical Issue">✏️</button>
                        <button class="action-btn delete-btn" data-action="delete-medical-issue" data-issue-id="${issue.id}" title="Delete Medical Issue">🗑️</button>
                    </div>
                </div>
                <div class="medical-issue-details">
                    ${issue.category ? `<div class="medical-detail"><strong>Category:</strong> ${issue.category}</div>` : ''}
                    ${issue.subcategory ? `<div class="medical-detail"><strong>Subcategory:</strong> ${issue.subcategory}</div>` : ''}
                    ${issue.severity ? `<div class="medical-detail"><strong>Severity:</strong> <span class="severity ${severityClass}">${issue.severity}</span></div>` : ''}
                    ${issue.status ? `<div class="medical-detail"><strong>Status:</strong> <span class="status ${statusClass}">${issue.status}</span></div>` : ''}
                    ${issue.diagnosis_date ? `<div class="medical-detail"><strong>Diagnosis Date:</strong> ${new Date(issue.diagnosis_date).toLocaleDateString()}</div>` : ''}
                    ${issue.healthcare_provider ? `<div class="medical-detail"><strong>Provider:</strong> ${issue.healthcare_provider}</div>` : ''}
                    ${issue.medication ? `<div class="medical-detail"><strong>Medication:</strong> ${issue.medication}</div>` : ''}
                    ${issue.treatment_notes ? `<div class="medical-detail"><strong>Treatment:</strong> ${issue.treatment_notes}</div>` : ''}
                    ${issue.follow_up_required && issue.follow_up_date ? `<div class="medical-detail"><strong>Follow-up:</strong> ${new Date(issue.follow_up_date).toLocaleDateString()}</div>` : ''}
                    ${issue.notes ? `<div class="medical-detail"><strong>Notes:</strong> ${issue.notes}</div>` : ''}
                </div>
                <div class="medical-issue-meta">
                    <small>Added: ${this.uiUtilities.formatDate(issue.created_at)}</small>
                    ${issue.updated_at ? `<small>Updated: ${this.uiUtilities.formatDate(issue.updated_at)}</small>` : ''}
                    <small>Active: ${issue.is_active ? '✅' : '❌'}</small>
                </div>
            </div>
        `;
    }

    /**
     * Get CSS class for severity level
     */
    getSeverityClass(severity) {
        switch (severity?.toLowerCase()) {
            case 'critical': return 'severity-critical';
            case 'high': return 'severity-high';
            case 'moderate': return 'severity-moderate';
            case 'low': return 'severity-low';
            default: return 'severity-unknown';
        }
    }

    /**
     * Get CSS class for status
     */
    getStatusClass(status) {
        switch (status?.toLowerCase()) {
            case 'active': return 'status-active';
            case 'resolved': return 'status-resolved';
            case 'improving': return 'status-improving';
            case 'worsening': return 'status-worsening';
            case 'stable': return 'status-stable';
            default: return 'status-unknown';
        }
    }

    /**
     * Set up event handlers for medical issue actions
     */
    setupMedicalEventHandlers(container) {
        // Add medical issue buttons
        const addMedicalButtons = container.querySelectorAll('[data-action="add-medical-issue"]');
        addMedicalButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const personId = btn.getAttribute('data-person-id');
                if (personId) {
                    this.addMedicalIssue(personId);
                }
            });
        });

        // Edit medical issue buttons
        const editMedicalButtons = container.querySelectorAll('[data-action="edit-medical-issue"]');
        editMedicalButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const issueId = btn.getAttribute('data-issue-id');
                if (issueId) {
                    this.editMedicalIssue(issueId);
                }
            });
        });

        // Delete medical issue buttons
        const deleteMedicalButtons = container.querySelectorAll('[data-action="delete-medical-issue"]');
        deleteMedicalButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const issueId = btn.getAttribute('data-issue-id');
                if (issueId) {
                    this.deleteMedicalIssue(issueId);
                }
            });
        });
    }

    /**
     * Edit medical issue
     */
    async editMedicalIssue(issueId) {
        try {
            const issue = await this.data.get('medical_issues', issueId);
            if (!issue) {
                this.ui.showDialog('Error', 'Medical issue not found', 'error');
                return;
            }

            // Generate form fields
            const allFields = this.data.schema.generateFormFields('medical_issues', ['id', 'created_at', 'created_by', 'person_id', 'updated_at', 'updated_by']);
            
            const generalMedicalFields = [
                'condition_name',
                'category',
                'subcategory',
                'source_type',
                'diagnosis_date',
                'severity',
                'status',
                'is_active',
                'treatment_notes',
                'medication',
                'healthcare_provider',
                'follow_up_required',
                'follow_up_date',
                'notes'
            ];

            const fields = allFields.filter(field => generalMedicalFields.includes(field.name));

            // Pre-populate fields with current data
            fields.forEach(field => {
                if (issue[field.name] !== undefined) {
                    field.value = issue[field.name];
                }
            });

            this.ui.showFullScreenForm('Edit Medical Issue', fields, async (formData) => {
                try {
                    const dbData = this.data.schema.convertFormToDatabase('medical_issues', formData);
                    
                    const updatedData = {
                        ...dbData,
                        updated_by: this.auth.getCurrentUser()?.email,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('medical_issues', issueId, updatedData);

                    this.ui.showDialog(
                        'Medical Issue Updated',
                        `Medical condition "${formData.condition_name}" has been updated.`,
                        'success'
                    );

                    // Refresh the medical issues list
                    this.loadPersonMedicalIssues(issue.person_id);

                    // Trigger change callback
                    if (this.onMedicalChanged) {
                        this.onMedicalChanged();
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update medical issue: ${error.message}`, 'error');
                }
            });
        } catch (error) {
            console.error('Error editing medical issue:', error);
            this.ui.showDialog('Error', 'Failed to edit medical issue', 'error');
        }
    }

    /**
     * Delete medical issue
     */
    async deleteMedicalIssue(issueId) {
        try {
            const issue = await this.data.get('medical_issues', issueId);
            if (!issue) {
                this.ui.showDialog('Error', 'Medical issue not found', 'error');
                return;
            }

            this.ui.showConfirmDialog(
                'Delete Medical Issue',
                `Are you sure you want to delete the medical condition "${issue.condition_name}"? This action cannot be undone.`,
                async () => {
                    try {
                        await this.data.delete('medical_issues', issueId);

                        this.ui.showDialog(
                            'Medical Issue Deleted',
                            `Medical condition "${issue.condition_name}" has been deleted.`,
                            'success'
                        );

                        // Refresh the medical issues list
                        this.loadPersonMedicalIssues(issue.person_id);

                        // Trigger change callback
                        if (this.onMedicalChanged) {
                            this.onMedicalChanged();
                        }
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to delete medical issue: ${error.message}`, 'error');
                    }
                }
            );
        } catch (error) {
            console.error('Error deleting medical issue:', error);
            this.ui.showDialog('Error', 'Failed to delete medical issue', 'error');
        }
    }

    /**
     * Get medical issues for a person
     */
    async getPersonMedicalIssues(personId) {
        try {
            return await this.data.search('medical_issues', { person_id: personId });
        } catch (error) {
            console.error('Error getting person medical issues:', error);
            return [];
        }
    }

    /**
     * Get medical issue by ID
     */
    async getMedicalIssue(issueId) {
        try {
            return await this.data.get('medical_issues', issueId);
        } catch (error) {
            console.error('Error getting medical issue:', error);
            return null;
        }
    }

    /**
     * Search medical issues
     */
    async searchMedicalIssues(criteria) {
        try {
            return await this.data.search('medical_issues', criteria);
        } catch (error) {
            console.error('Error searching medical issues:', error);
            return [];
        }
    }

    /**
     * Update medical issue
     */
    async updateMedicalIssue(issueId, issueData) {
        try {
            const updatedData = {
                ...issueData,
                updated_by: this.auth.getCurrentUser()?.email,
                updated_at: new Date().toISOString()
            };

            const result = await this.data.update('medical_issues', issueId, updatedData);

            // Trigger change callback
            if (this.onMedicalChanged) {
                this.onMedicalChanged();
            }

            return result;
        } catch (error) {
            console.error('Error updating medical issue:', error);
            throw error;
        }
    }

    /**
     * Create new medical issue
     */
    async createMedicalIssue(issueData) {
        try {
            const fullIssueData = {
                ...issueData,
                created_by: this.auth.getCurrentUser()?.email,
                created_at: new Date().toISOString()
            };

            const result = await this.data.insert('medical_issues', fullIssueData);

            // Trigger change callback
            if (this.onMedicalChanged) {
                this.onMedicalChanged();
            }

            return result;
        } catch (error) {
            console.error('Error creating medical issue:', error);
            throw error;
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners if needed
    }
}