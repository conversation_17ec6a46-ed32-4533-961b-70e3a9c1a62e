/**
 * Incident CRUD Manager
 * Handles incident create, read, update, delete operations
 * Extracted from app.js - manages ~12 CRUD-related functions
 * Extends BaseCrudManager for standardized CRUD operations
 */

import { BaseCrudManager } from '../../shared/base-crud-manager.js';

export class IncidentCrudManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super(dataManager, authManager, 'incidents', 'incident', uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
    }

    async closeIncident(incidentId) {
        try {
            console.log('Closing incident:', incidentId);

            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Show confirmation dialog
            const confirmed = await this.ui.showConfirmDialog('Confirm Close Incident', 
                `Are you sure you want to close incident #${incident.incident_number}?\n\nThis action cannot be undone.`);
            
            if (!confirmed) {
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Update incident status to closed
            const updateData = {
                status: 'closed',
                closed_at: new Date().toISOString(),
                closed_by: userContext.user_id,
                updated_at: new Date().toISOString(),
                updated_by: userContext.user_id
            };

            await this.data.update('incidents', incidentId, updateData);

            // Log the closure
            await this.logIncidentActivity(incidentId, 'incident_closed', {
                action: 'Incident Closed',
                details: `Incident #${incident.incident_number} was closed`,
                performed_by: userContext.display_name
            });

            // Show success message
            this.uiUtilities.showToast(`Incident #${incident.incident_number} has been closed`, 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Incident closed successfully');

        } catch (error) {
            console.error('Error closing incident:', error);
            this.ui.showDialog('Error', `Failed to close incident: ${error.message}`, 'error');
        }
    }

    async deleteIncident(incidentId) {
        try {
            console.log('Deleting incident:', incidentId);

            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Show confirmation dialog with warning
            const confirmed = await this.ui.showConfirmDialog('⚠️ Confirm Delete Incident', 
                `WARNING: This will permanently delete incident #${incident.incident_number}.\n\n` +
                `This action cannot be undone and will remove:\n` +
                `• All incident data\n` +
                `• Associated files and attachments\n` +
                `• People relationships\n` +
                `• Activity logs\n\n` +
                `Are you absolutely sure you want to proceed?`);
            
            if (!confirmed) {
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Log the deletion before actually deleting
            await this.logIncidentActivity(incidentId, 'incident_deleted', {
                action: 'Incident Deletion Initiated',
                details: `Incident #${incident.incident_number} deletion started by ${userContext.display_name}`,
                performed_by: userContext.display_name
            });

            // Delete related records first (cascade delete)
            await this.deleteIncidentRelationships(incidentId);

            // Delete the incident
            await this.data.delete('incidents', incidentId);

            // Show success message
            this.uiUtilities.showToast(`Incident #${incident.incident_number} has been deleted`, 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            // Close any open incident detail views
            const detailView = document.getElementById('incident-detail-view');
            if (detailView) {
                detailView.style.display = 'none';
            }

            console.log('Incident deleted successfully');

        } catch (error) {
            console.error('Error deleting incident:', error);
            this.ui.showDialog('Error', `Failed to delete incident: ${error.message}`, 'error');
        }
    }

    async updateIncidentStatus(incidentId, newStatus, statusReason = null) {
        try {
            console.log('Updating incident status:', incidentId, 'to', newStatus);

            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Prepare update data
            const updateData = {
                status: newStatus,
                updated_at: new Date().toISOString(),
                updated_by: userContext.user_id
            };

            // Add status-specific fields
            if (newStatus === 'closed') {
                updateData.closed_at = new Date().toISOString();
                updateData.closed_by = userContext.user_id;
            } else if (newStatus === 'in_progress') {
                updateData.started_at = new Date().toISOString();
                updateData.started_by = userContext.user_id;
            }

            // Update the incident
            await this.data.update('incidents', incidentId, updateData);

            // Log the status change
            await this.logIncidentActivity(incidentId, 'status_changed', {
                action: 'Status Changed',
                details: `Status changed from "${incident.status || 'open'}" to "${newStatus}"${statusReason ? ` - Reason: ${statusReason}` : ''}`,
                performed_by: userContext.display_name,
                old_status: incident.status || 'open',
                new_status: newStatus,
                reason: statusReason
            });

            // Add status history entry
            await this.addStatusHistoryEntry(incidentId, {
                old_status: incident.status || 'open',
                new_status: newStatus,
                reason: statusReason,
                changed_by: userContext.user_id,
                changed_at: new Date().toISOString()
            });

            // Show success message
            this.uiUtilities.showToast(`Incident status updated to ${newStatus}`, 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Incident status updated successfully');

        } catch (error) {
            console.error('Error updating incident status:', error);
            this.ui.showDialog('Error', `Failed to update incident status: ${error.message}`, 'error');
        }
    }

    async assignRanger(incidentId, rangerName) {
        try {
            console.log('Assigning ranger to incident:', incidentId, rangerName);

            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Update incident with assigned ranger
            const updateData = {
                assigned_ranger: rangerName,
                assigned_at: new Date().toISOString(),
                assigned_by: userContext.user_id,
                updated_at: new Date().toISOString(),
                updated_by: userContext.user_id
            };

            await this.data.update('incidents', incidentId, updateData);

            // Log the assignment
            await this.logIncidentActivity(incidentId, 'ranger_assigned', {
                action: 'Ranger Assigned',
                details: `${rangerName} assigned to incident #${incident.incident_number}`,
                performed_by: userContext.display_name,
                assigned_ranger: rangerName
            });

            // Show success message
            this.uiUtilities.showToast(`${rangerName} assigned to incident #${incident.incident_number}`, 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Ranger assigned successfully');

        } catch (error) {
            console.error('Error assigning ranger:', error);
            this.ui.showDialog('Error', `Failed to assign ranger: ${error.message}`, 'error');
        }
    }

    async addDispatchNote(incidentId, note) {
        try {
            console.log('Adding dispatch note to incident:', incidentId);

            if (!note || note.trim().length === 0) {
                this.ui.showDialog('Error', 'Note cannot be empty.', 'error');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Log the dispatch note
            await this.logIncidentActivity(incidentId, 'note_added', {
                action: 'Dispatch Note Added',
                details: note.trim(),
                performed_by: userContext.display_name,
                note_type: 'dispatch'
            });

            // Show success message
            this.uiUtilities.showToast('Dispatch note added successfully', 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Dispatch note added successfully');

        } catch (error) {
            console.error('Error adding dispatch note:', error);
            this.ui.showDialog('Error', `Failed to add dispatch note: ${error.message}`, 'error');
        }
    }

    async addStatusHistoryEntry(incidentId, entry) {
        try {
            // Create status history entry
            const historyEntry = {
                incident_id: incidentId,
                old_status: entry.old_status,
                new_status: entry.new_status,
                reason: entry.reason,
                changed_by: entry.changed_by,
                changed_at: entry.changed_at,
                ...this.addCreateMetadata()
            };

            // Insert into status history table (if it exists)
            try {
                await this.data.insert('incident_status_history', historyEntry);
            } catch (error) {
                // If table doesn't exist, log as activity instead
                console.warn('Status history table not found, logging as activity');
                await this.logIncidentActivity(incidentId, 'status_changed', {
                    action: 'Status History',
                    details: `Status changed from ${entry.old_status} to ${entry.new_status}`,
                    old_status: entry.old_status,
                    new_status: entry.new_status,
                    reason: entry.reason
                });
            }

        } catch (error) {
            console.error('Error adding status history entry:', error);
            // Don't throw - this is a supporting function
        }
    }

    async deleteIncidentRelationships(incidentId) {
        try {
            console.log('Deleting incident relationships for:', incidentId);

            // Delete people relationships
            const peopleRelationships = await this.data.search('incident_people', { incident_id: incidentId });
            for (const relationship of peopleRelationships) {
                await this.data.delete('incident_people', relationship.id);
            }

            // Delete property relationships
            const propertyRelationships = await this.data.search('incident_property', { incident_id: incidentId });
            for (const relationship of propertyRelationships) {
                await this.data.delete('incident_property', relationship.id);
            }

            // Delete activity logs
            const activityLogs = await this.data.search('activity_logs', { related_incident_id: incidentId });
            for (const log of activityLogs) {
                await this.data.delete('activity_logs', log.id);
            }

            // Delete file attachments
            const attachments = await this.data.search('incident_attachments', { incident_id: incidentId });
            for (const attachment of attachments) {
                // TODO: Delete actual file from storage
                await this.data.delete('incident_attachments', attachment.id);
            }

            console.log('Incident relationships deleted successfully');

        } catch (error) {
            console.error('Error deleting incident relationships:', error);
            // Don't throw - allow main deletion to proceed
        }
    }

    async logIncidentActivity(incidentId, activityType, activityData) {
        try {
            const userContext = this.getCurrentUserContext();

            const logEntry = {
                activity_type: activityType,
                description: activityData.details || activityData.action || `Incident ${activityType}`,
                table_name: 'incidents',
                record_id: incidentId.toString(),
                field_name: null,
                old_value: null,
                new_value: JSON.stringify(activityData),
                user_email: userContext.email || '<EMAIL>',
                user_name: userContext.display_name || 'Unknown User',
                notes: activityData.details || activityData.action || '',
                timestamp: new Date().toISOString(),
                ...this.addCreateMetadata()
            };

            await this.data.insert('activity_logs', logEntry);

        } catch (error) {
            console.error('Error logging incident activity:', error);
            // Don't throw - this is a supporting function
        }
    }

    async updateIncidentPriority(incidentId, newPriority) {
        try {
            console.log('Updating incident priority:', incidentId, 'to', newPriority);

            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Update incident priority
            const updateData = {
                priority: newPriority,
                updated_at: new Date().toISOString(),
                updated_by: userContext.user_id
            };

            await this.data.update('incidents', incidentId, updateData);

            // Log the priority change
            await this.logIncidentActivity(incidentId, 'priority_changed', {
                action: 'Priority Changed',
                details: `Priority changed from "${incident.priority || 'medium'}" to "${newPriority}"`,
                performed_by: userContext.display_name,
                old_priority: incident.priority || 'medium',
                new_priority: newPriority
            });

            // Show success message
            this.uiUtilities.showToast(`Incident priority updated to ${newPriority}`, 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Incident priority updated successfully');

        } catch (error) {
            console.error('Error updating incident priority:', error);
            this.ui.showDialog('Error', `Failed to update incident priority: ${error.message}`, 'error');
        }
    }

    async duplicateIncident(incidentId) {
        try {
            console.log('Duplicating incident:', incidentId);

            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Create duplicate incident data
            const duplicateData = {
                ...incident,
                id: undefined, // Remove ID to create new
                incident_number: await this.generateIncidentNumber(),
                status: 'open',
                assigned_ranger: null,
                assigned_at: null,
                assigned_by: null,
                closed_at: null,
                closed_by: null,
                started_at: null,
                started_by: null,
                incident_time: new Date().toISOString(),
                description: `[DUPLICATE] ${incident.description || ''}`,
                ...this.addCreateMetadata()
            };

            // Create the duplicate incident
            const duplicateIncident = await this.data.insert('incidents', duplicateData);

            // Log the duplication
            await this.logIncidentActivity(duplicateIncident.id, 'incident_duplicated', {
                action: 'Incident Duplicated',
                details: `Duplicated from incident #${incident.incident_number}`,
                performed_by: userContext.display_name,
                original_incident_id: incidentId,
                original_incident_number: incident.incident_number
            });

            // Show success message
            this.uiUtilities.showToast(`Incident duplicated as #${duplicateData.incident_number}`, 'success');

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Incident duplicated successfully');

            return duplicateIncident;

        } catch (error) {
            console.error('Error duplicating incident:', error);
            this.ui.showDialog('Error', `Failed to duplicate incident: ${error.message}`, 'error');
        }
    }

    async generateIncidentNumber() {
        try {
            // Get current date
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            
            // Base incident number format: YYYYMMDD-001
            const datePrefix = `${year}${month}${day}`;
            
            // Find existing incidents with the same date prefix
            const incidents = await this.data.search('incidents', {});
            const todayIncidents = incidents.filter(incident => 
                incident.incident_number && incident.incident_number.startsWith(datePrefix)
            );
            
            // Get the next sequence number
            let nextSequence = 1;
            if (todayIncidents.length > 0) {
                const sequences = todayIncidents
                    .map(incident => {
                        const parts = incident.incident_number.split('-');
                        return parts.length > 1 ? parseInt(parts[1]) : 0;
                    })
                    .filter(seq => !isNaN(seq));
                
                if (sequences.length > 0) {
                    nextSequence = Math.max(...sequences) + 1;
                }
            }
            
            // Format sequence with leading zeros
            const sequenceStr = String(nextSequence).padStart(3, '0');
            
            return `${datePrefix}-${sequenceStr}`;
            
        } catch (error) {
            console.error('Error generating incident number:', error);
            // Fallback to timestamp-based number
            return `INC-${Date.now()}`;
        }
    }

    async bulkUpdateIncidents(incidentIds, updateData) {
        try {
            console.log('Bulk updating incidents:', incidentIds.length, 'incidents');

            if (!incidentIds || incidentIds.length === 0) {
                this.ui.showDialog('Error', 'No incidents selected for bulk update.', 'error');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Add metadata to update data
            const finalUpdateData = {
                ...updateData,
                updated_at: new Date().toISOString(),
                updated_by: userContext.user_id
            };

            // Update each incident
            const results = [];
            for (const incidentId of incidentIds) {
                try {
                    await this.data.update('incidents', incidentId, finalUpdateData);
                    
                    // Log the bulk update
                    await this.logIncidentActivity(incidentId, 'bulk_updated', {
                        action: 'Bulk Update',
                        details: `Incident updated via bulk operation`,
                        performed_by: userContext.display_name,
                        update_data: updateData
                    });
                    
                    results.push({ id: incidentId, success: true });
                } catch (error) {
                    console.error(`Error updating incident ${incidentId}:`, error);
                    results.push({ id: incidentId, success: false, error: error.message });
                }
            }

            // Show results
            const successCount = results.filter(r => r.success).length;
            const failureCount = results.length - successCount;

            if (failureCount === 0) {
                this.uiUtilities.showToast(`Successfully updated ${successCount} incidents`, 'success');
            } else {
                this.ui.showDialog('Bulk Update Results', 
                    `${successCount} incidents updated successfully\n${failureCount} incidents failed to update`, 
                    failureCount > successCount ? 'error' : 'warning');
            }

            // Refresh the incidents view
            this.triggerIncidentsRefresh();

            console.log('Bulk update completed');

            return results;

        } catch (error) {
            console.error('Error in bulk update:', error);
            this.ui.showDialog('Error', `Failed to perform bulk update: ${error.message}`, 'error');
        }
    }

    triggerIncidentsRefresh() {
        // Emit a data change event to trigger UI refresh
        const event = new CustomEvent('dataChange', {
            detail: {
                table: 'incidents',
                operation: 'update'
            }
        });
        window.dispatchEvent(event);
    }

    // Status update modal integration
    async showUpdateStatusModal(incidentId) {
        if (this.modalManagement) {
            await this.modalManagement.showUpdateStatusModal(incidentId);
        } else {
            console.warn('Modal management not available for status update');
        }
    }

    async saveIncidentCreationLinks(incidentId, incidentCreationLinks) {
        try {
            const allLinks = [
                ...incidentCreationLinks.people.map(link => ({
                    incident_id: incidentId,
                    linked_record_type: 'person',
                    linked_record_id: link.record.id,
                    link_type: link.link_type,
                    notes: link.notes,
                    created_at: new Date().toISOString(),
                    created_by: this.getCurrentUserContext().user_email || 'System'
                })),
                ...incidentCreationLinks.vehicles.map(link => ({
                    incident_id: incidentId,
                    linked_record_type: 'vehicle',
                    linked_record_id: link.record.id,
                    link_type: link.link_type,
                    notes: link.notes,
                    created_at: new Date().toISOString(),
                    created_by: this.getCurrentUserContext().user_email || 'System'
                })),
                ...incidentCreationLinks.addresses.map(link => ({
                    incident_id: incidentId,
                    linked_record_type: 'address',
                    linked_record_id: link.record.id,
                    link_type: link.link_type,
                    notes: link.notes,
                    created_at: new Date().toISOString(),
                    created_by: this.getCurrentUserContext().user_email || 'System'
                }))
            ];

            // Save all links
            for (const linkData of allLinks) {
                await this.data.insert('incident_links', linkData);
            }

            console.log(`Saved ${allLinks.length} incident links for incident ${incidentId}`);

        } catch (error) {
            console.error('Error saving incident links:', error);
            // Don't throw error to prevent incident creation from failing
        }
    }
}