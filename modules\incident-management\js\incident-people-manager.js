/**
 * Incident People Manager
 * Handles incident-people relationships, involvement tracking, and people search
 * Extracted from app.js - manages ~12 people-related functions
 */

import { BaseManager } from '../../shared/base-manager.js';

export class IncidentPeopleManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.involvedPeople = [];
        this.currentIncidentId = null;
    }

    initializePeopleManagement() {
        console.log('Initializing people management for incidents...');
        
        // Set up people search functionality
        this.setupPeopleSearch();
        
        // Set up add person button
        const addPersonBtn = document.getElementById('add-person-to-incident');
        if (addPersonBtn) {
            addPersonBtn.addEventListener('click', () => {
                this.showAddPersonDialog();
            });
        }
        
        // Listen for people selection events
        window.addEventListener('addPersonToIncident', (event) => {
            this.addPersonToIncident(event.detail.personId);
        });
    }

    setupPeopleSearch() {
        const searchInput = document.getElementById('person-search-input');
        const searchBtn = document.getElementById('person-search-btn');
        const clearBtn = document.getElementById('person-search-clear');

        console.log('Setting up people search - elements found:', {
            searchInput: !!searchInput,
            searchBtn: !!searchBtn,
            clearBtn: !!clearBtn
        });

        if (searchInput && searchBtn) {
            searchBtn.addEventListener('click', () => {
                console.log('Search button clicked');
                this.searchPeopleForIncident();
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('Enter key pressed in search input');
                    this.searchPeopleForIncident();
                }
            });

            // Real-time search as user types
            searchInput.addEventListener('input', () => {
                if (searchInput.value.length >= 2) {
                    console.log('Input detected, scheduling search for:', searchInput.value);
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(() => {
                        this.searchPeopleForIncident();
                    }, 300);
                } else if (searchInput.value.length === 0) {
                    // Clear results when input is empty
                    this.clearPeopleSearch();
                }
            });
        } else {
            console.warn('People search elements not found - search functionality will not work');
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                console.log('Clear button clicked');
                this.clearPeopleSearch();
            });
        }
    }

    async searchPeopleForIncident() {
        const searchInput = document.getElementById('person-search-input');
        const resultsContainer = document.getElementById('person-search-results');
        
        if (!searchInput || !resultsContainer) return;

        const searchTerm = searchInput.value.trim().toLowerCase();
        
        if (searchTerm.length < 2) {
            resultsContainer.innerHTML = '<div class="search-hint">Enter at least 2 characters to search</div>';
            return;
        }

        try {
            // Show loading state
            resultsContainer.innerHTML = '<div class="loading">Searching people...</div>';

            // Search people
            const people = await this.data.search('people', {});
            
            const filteredPeople = people.filter(person => {
                const searchableText = [
                    person.first_name,
                    person.last_name,
                    person.preferred_name,
                    person.date_of_birth,
                    person.physical_description,
                    person.notes
                ].join(' ').toLowerCase();
                
                return searchableText.includes(searchTerm);
            });

            if (filteredPeople.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="no-results">
                        <p>No people found matching "${searchTerm}"</p>
                        <button class="create-new-person-btn primary-button" data-search-term="${searchTerm}">
                            Create New Person
                        </button>
                    </div>
                `;
                
                // Add handler for create new person
                resultsContainer.querySelector('.create-new-person-btn').addEventListener('click', (e) => {
                    this.createNewPersonFromSearch(e.target.dataset.searchTerm);
                });
                
                return;
            }

            // Sort by relevance (exact name matches first)
            filteredPeople.sort((a, b) => {
                const aFullName = `${a.first_name} ${a.last_name}`.toLowerCase();
                const bFullName = `${b.first_name} ${b.last_name}`.toLowerCase();
                
                const aExact = aFullName.includes(searchTerm);
                const bExact = bFullName.includes(searchTerm);
                
                if (aExact && !bExact) return -1;
                if (!aExact && bExact) return 1;
                
                return aFullName.localeCompare(bFullName);
            });

            const resultsHTML = `
                <div class="search-results-header">
                    <h4>Found ${filteredPeople.length} people</h4>
                </div>
                <div class="people-search-results">
                    ${filteredPeople.map(person => `
                        <div class="person-search-result" data-person-id="${person.id}">
                            <div class="person-info">
                                <div class="person-name">
                                    ${person.first_name} ${person.last_name}
                                    ${person.preferred_name ? `(${person.preferred_name})` : ''}
                                </div>
                                <div class="person-details">
                                    ${person.date_of_birth ? `DOB: ${this.uiUtilities.formatDate(person.date_of_birth)}` : ''}
                                    ${person.housing_status ? ` • Housing: ${person.housing_status}` : ''}
                                </div>
                                ${person.physical_description ? 
                                    `<div class="person-description">${person.physical_description.substring(0, 100)}${person.physical_description.length > 100 ? '...' : ''}</div>` 
                                    : ''
                                }
                            </div>
                            <div class="person-actions">
                                <button class="add-to-incident-btn primary-button" data-person-id="${person.id}">
                                    Add to Incident
                                </button>
                                <button class="view-person-btn secondary-button" data-person-id="${person.id}">
                                    View Details
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;

            resultsContainer.innerHTML = resultsHTML;

            // Add click handlers
            resultsContainer.querySelectorAll('.add-to-incident-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const personId = btn.dataset.personId;
                    this.addPersonToIncident(personId);
                });
            });

            resultsContainer.querySelectorAll('.view-person-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const personId = btn.dataset.personId;
                    this.viewPersonDetails(personId);
                });
            });

        } catch (error) {
            console.error('Error searching people for incident:', error);
            resultsContainer.innerHTML = '<div class="search-error">Error searching people</div>';
        }
    }

    async addPersonToIncident(personId) {
        try {
            console.log('Adding person to incident:', personId);

            // Get person data
            const person = await this.data.get('people', personId);
            if (!person) {
                this.ui.showDialog('Error', 'Person not found.', 'error');
                return;
            }

            // Check if person is already involved
            const existingIndex = this.involvedPeople.findIndex(p => p.id === personId);
            if (existingIndex !== -1) {
                this.ui.showDialog('Info', `${person.first_name} ${person.last_name} is already involved in this incident.`, 'info');
                return;
            }

            // Show involvement type selection dialog
            const involvementType = await this.selectInvolvementType(person);
            if (!involvementType) {
                return; // User cancelled
            }

            // Add to involved people list
            this.involvedPeople.push({
                id: personId,
                first_name: person.first_name,
                last_name: person.last_name,
                preferred_name: person.preferred_name,
                involvement_type: involvementType.type,
                involvement_details: involvementType.details,
                medical_info: involvementType.medicalInfo,
                injury_details: involvementType.injuryDetails
            });

            // Update the involved people display
            this.updateInvolvedPeopleDisplay();

            // Show success message
            this.uiUtilities.showToast(`${person.first_name} ${person.last_name} added to incident`, 'success');

            console.log('Person added to incident successfully');

        } catch (error) {
            console.error('Error adding person to incident:', error);
            this.ui.showDialog('Error', `Failed to add person to incident: ${error.message}`, 'error');
        }
    }

    async removePersonFromIncident(index) {
        try {
            if (index < 0 || index >= this.involvedPeople.length) {
                this.ui.showDialog('Error', 'Invalid person selection.', 'error');
                return;
            }

            const person = this.involvedPeople[index];
            
            // Show confirmation dialog
            const confirmed = await this.ui.showConfirmDialog('Remove Person', 
                `Are you sure you want to remove ${person.first_name} ${person.last_name} from this incident?`);
            
            if (!confirmed) {
                return;
            }

            // Remove from involved people list
            this.involvedPeople.splice(index, 1);

            // Update the display
            this.updateInvolvedPeopleDisplay();

            // Show success message
            this.uiUtilities.showToast(`${person.first_name} ${person.last_name} removed from incident`, 'success');

            console.log('Person removed from incident successfully');

        } catch (error) {
            console.error('Error removing person from incident:', error);
            this.ui.showDialog('Error', `Failed to remove person from incident: ${error.message}`, 'error');
        }
    }

    async selectInvolvementType(person) {
        return new Promise((resolve) => {
            // Create involvement type selection modal
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content involvement-type-modal">
                    <div class="modal-header">
                        <h3>Select Involvement Type</h3>
                        <p>How is ${person.first_name} ${person.last_name} involved in this incident?</p>
                    </div>
                    <div class="modal-body">
                        <form id="involvement-type-form">
                            <div class="form-group">
                                <label>Involvement Type:</label>
                                <select id="involvement-type" class="form-control" required>
                                    <option value="">Select involvement type...</option>
                                    <option value="subject">Subject</option>
                                    <option value="witness">Witness</option>
                                    <option value="complainant">Complainant</option>
                                    <option value="victim">Victim</option>
                                    <option value="suspect">Suspect</option>
                                    <option value="person_of_interest">Person of Interest</option>
                                    <option value="contact">Contact/Associate</option>
                                    <option value="service_recipient">Service Recipient</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="involvement-details">Additional Details:</label>
                                <textarea id="involvement-details" class="form-control" rows="3" 
                                    placeholder="Describe the person's specific involvement..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="has-medical-info"> 
                                    Medical information or injuries
                                </label>
                            </div>
                            
                            <div id="medical-info-section" class="form-group" style="display: none;">
                                <label for="medical-info">Medical Information:</label>
                                <textarea id="medical-info" class="form-control" rows="2" 
                                    placeholder="Medical conditions, medications, etc."></textarea>
                                
                                <label for="injury-details">Injury Details:</label>
                                <textarea id="injury-details" class="form-control" rows="2" 
                                    placeholder="Description of any injuries..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-involvement" class="secondary-button">Cancel</button>
                        <button id="confirm-involvement" class="primary-button">Add Person</button>
                    </div>
                </div>
            `;

            // Add event handlers
            const medicalCheckbox = modal.querySelector('#has-medical-info');
            const medicalSection = modal.querySelector('#medical-info-section');
            
            medicalCheckbox.addEventListener('change', () => {
                medicalSection.style.display = medicalCheckbox.checked ? 'block' : 'none';
            });

            modal.querySelector('#cancel-involvement').addEventListener('click', () => {
                document.body.removeChild(modal);
                resolve(null);
            });

            modal.querySelector('#confirm-involvement').addEventListener('click', () => {
                const type = modal.querySelector('#involvement-type').value;
                if (!type) {
                    alert('Please select an involvement type.');
                    return;
                }

                const result = {
                    type: type,
                    details: modal.querySelector('#involvement-details').value,
                    medicalInfo: medicalCheckbox.checked ? modal.querySelector('#medical-info').value : null,
                    injuryDetails: medicalCheckbox.checked ? modal.querySelector('#injury-details').value : null
                };

                document.body.removeChild(modal);
                resolve(result);
            });

            // Close on outside click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    resolve(null);
                }
            });

            document.body.appendChild(modal);
        });
    }

    updateInvolvedPeopleDisplay() {
        const peopleList = document.getElementById('involved-people-list');
        if (!peopleList) return;

        if (this.involvedPeople.length === 0) {
            peopleList.innerHTML = '<div class="no-people">No people involved in this incident yet.</div>';
            return;
        }

        const peopleHTML = this.involvedPeople.map((person, index) => `
            <div class="involved-person-item">
                <div class="person-info">
                    <div class="person-name">
                        ${person.first_name} ${person.last_name}
                        ${person.preferred_name ? `(${person.preferred_name})` : ''}
                    </div>
                    <div class="person-involvement">
                        <span class="involvement-type ${person.involvement_type}">${this.formatInvolvementType(person.involvement_type)}</span>
                        ${person.involvement_details ? `<span class="involvement-details">${person.involvement_details}</span>` : ''}
                    </div>
                    ${person.medical_info ? 
                        `<div class="medical-info">
                            <strong>Medical:</strong> ${person.medical_info}
                            ${person.injury_details ? `<br><strong>Injuries:</strong> ${person.injury_details}` : ''}
                        </div>` 
                        : ''
                    }
                </div>
                <div class="person-actions">
                    <button class="edit-involvement-btn secondary-button" data-index="${index}">
                        Edit
                    </button>
                    <button class="remove-person-btn secondary-button" data-index="${index}">
                        Remove
                    </button>
                </div>
            </div>
        `).join('');

        peopleList.innerHTML = peopleHTML;

        // Add event handlers
        peopleList.querySelectorAll('.remove-person-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                this.removePersonFromIncident(index);
            });
        });

        peopleList.querySelectorAll('.edit-involvement-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.dataset.index);
                this.editPersonInvolvement(index);
            });
        });

        // Update people count display
        const peopleCount = document.getElementById('involved-people-count');
        if (peopleCount) {
            peopleCount.textContent = this.involvedPeople.length;
        }
    }

    async editPersonInvolvement(index) {
        if (index < 0 || index >= this.involvedPeople.length) return;

        const person = this.involvedPeople[index];
        
        // Show the same involvement type dialog but pre-populated
        const involvementType = await this.selectInvolvementType(person);
        if (!involvementType) {
            return; // User cancelled
        }

        // Update the person's involvement information
        this.involvedPeople[index] = {
            ...person,
            involvement_type: involvementType.type,
            involvement_details: involvementType.details,
            medical_info: involvementType.medicalInfo,
            injury_details: involvementType.injuryDetails
        };

        // Update the display
        this.updateInvolvedPeopleDisplay();

        this.uiUtilities.showToast('Involvement information updated', 'success');
    }

    formatInvolvementType(type) {
        const types = {
            'subject': 'Subject',
            'witness': 'Witness',
            'complainant': 'Complainant',
            'victim': 'Victim',
            'suspect': 'Suspect',
            'person_of_interest': 'Person of Interest',
            'contact': 'Contact/Associate',
            'service_recipient': 'Service Recipient',
            'other': 'Other'
        };
        
        return types[type] || type;
    }

    async saveIncidentPeopleRelationships(incidentId) {
        try {
            console.log('Saving incident people relationships...');

            this.currentIncidentId = incidentId;

            if (this.involvedPeople.length === 0) {
                console.log('No people to save');
                return;
            }

            // Get current user context
            const userContext = this.getCurrentUserContext();

            // Save each person relationship
            for (const person of this.involvedPeople) {
                const relationshipData = {
                    incident_id: incidentId,
                    person_id: person.id,
                    involvement_type: person.involvement_type,
                    involvement_details: person.involvement_details,
                    medical_info: person.medical_info,
                    injury_details: person.injury_details,
                    ...this.addCreateMetadata()
                };

                await this.data.insert('incident_people', relationshipData);

                // Log activity for each person
                await this.logIncidentActivity(incidentId, 'note_added', {
                    action: 'Person Added to Incident',
                    details: `${person.first_name} ${person.last_name} added as ${this.formatInvolvementType(person.involvement_type)}`,
                    performed_by: userContext.display_name,
                    person_id: person.id,
                    involvement_type: person.involvement_type
                });
            }

            console.log('Incident people relationships saved successfully');

        } catch (error) {
            console.error('Error saving incident people relationships:', error);
            throw error;
        }
    }

    async updateIncidentPeopleRelationships(incidentId) {
        try {
            console.log('Updating incident people relationships...');

            this.currentIncidentId = incidentId;

            // Delete existing relationships
            await this.deleteIncidentPeopleRelationships(incidentId);

            // Save new relationships
            await this.saveIncidentPeopleRelationships(incidentId);

            console.log('Incident people relationships updated successfully');

        } catch (error) {
            console.error('Error updating incident people relationships:', error);
            throw error;
        }
    }

    async deleteIncidentPeopleRelationships(incidentId) {
        try {
            console.log('Deleting incident people relationships...');

            // Get existing relationships
            const relationships = await this.data.search('incident_people', { incident_id: incidentId });

            // Delete each relationship
            for (const relationship of relationships) {
                await this.data.delete('incident_people', relationship.id);
            }

            console.log('Incident people relationships deleted successfully');

        } catch (error) {
            console.error('Error deleting incident people relationships:', error);
            throw error;
        }
    }

    async loadExistingIncidentPeople() {
        try {
            if (!this.currentIncidentId) {
                console.log('No current incident ID to load people for');
                return;
            }

            console.log('Loading existing incident people...');

            // Get incident people relationships
            const relationships = await this.data.search('incident_people', { 
                incident_id: this.currentIncidentId 
            });

            if (relationships.length === 0) {
                console.log('No existing people relationships found');
                return;
            }

            // Load person details for each relationship
            this.involvedPeople = [];
            
            for (const relationship of relationships) {
                const person = await this.data.get('people', relationship.person_id);
                if (person) {
                    this.involvedPeople.push({
                        id: person.id,
                        first_name: person.first_name,
                        last_name: person.last_name,
                        preferred_name: person.preferred_name,
                        involvement_type: relationship.involvement_type,
                        involvement_details: relationship.involvement_details,
                        medical_info: relationship.medical_info,
                        injury_details: relationship.injury_details
                    });
                }
            }

            // Update the display
            this.updateInvolvedPeopleDisplay();

            console.log(`Loaded ${this.involvedPeople.length} people for incident`);

        } catch (error) {
            console.error('Error loading existing incident people:', error);
        }
    }

    async logIncidentActivity(incidentId, activityType, activityData) {
        try {
            const userContext = this.getCurrentUserContext();

            const logEntry = {
                activity_type: activityType,
                description: activityData.details || activityData.action || `Incident ${activityType}`,
                table_name: 'incidents',
                record_id: incidentId.toString(),
                field_name: null,
                old_value: null,
                new_value: JSON.stringify(activityData),
                user_email: userContext.email || '<EMAIL>',
                user_name: userContext.display_name || 'Unknown User',
                notes: activityData.details || activityData.action || '',
                timestamp: new Date().toISOString(),
                ...this.addCreateMetadata()
            };

            await this.data.insert('activity_logs', logEntry);

        } catch (error) {
            console.error('Error logging incident activity:', error);
        }
    }

    clearPeopleSearch() {
        const searchInput = document.getElementById('person-search-input');
        const resultsContainer = document.getElementById('person-search-results');
        
        if (searchInput) searchInput.value = '';
        if (resultsContainer) resultsContainer.innerHTML = '<div class="search-hint">Enter at least 2 characters to search</div>';
    }

    async createNewPersonFromSearch(searchTerm) {
        console.log('Creating new person from search:', searchTerm);
        
        // This would integrate with the people management system
        // For now, just show a message
        this.ui.showDialog('Info', 
            `This feature would create a new person with the search term: "${searchTerm}"\n\n` +
            'This would typically open the person creation form with the search term pre-filled.',
            'info'
        );
    }

    async viewPersonDetails(personId) {
        console.log('Viewing person details:', personId);
        
        // This would integrate with the people management system
        // For now, just show a message
        this.ui.showDialog('Info', 
            'This feature would open the person details view.\n\n' +
            'This would typically show the full person profile in a modal or navigate to the person detail page.',
            'info'
        );
    }

    showAddPersonDialog() {
        // Show the people search interface
        const searchSection = document.getElementById('people-search-section');
        if (searchSection) {
            searchSection.style.display = 'block';
            
            // Focus on search input
            const searchInput = document.getElementById('person-search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
    }

    async saveIncidentPeopleRelationships(incidentId, involvedPeople = null) {
        const peopleToSave = involvedPeople || this.involvedPeople;

        if (!peopleToSave || peopleToSave.length === 0) {
            return;
        }

        try {
            console.log('Saving people relationships for incident:', incidentId, peopleToSave);

            // Save each person relationship and their medical information
            for (const involvement of peopleToSave) {
                const relationshipData = {
                    incident_id: incidentId,
                    person_id: involvement.person_id,
                    involvement_type: involvement.involvement_type,
                    is_unknown_party: involvement.is_unknown_party || false,
                    unknown_party_description: involvement.unknown_party_description || null,
                    notes: involvement.notes || null,
                    ...this.addCreateMetadata()
                };

                // Create incident person relationship
                const incidentPerson = await this.createIncidentPersonRelationship(relationshipData);

                // Save medical information if present
                if (involvement.medical_info && involvement.medical_info.required_medical_attention && incidentPerson) {
                    await this.createIncidentPersonMedical(incidentPerson.id, involvement.medical_info);
                }

                // Create activity record for this person if they have a valid person_id (not unknown party)
                if (involvement.person_id && !involvement.is_unknown_party) {
                    await this.createIncidentActivity(incidentId, involvement.person_id, involvement.involvement_type);
                }
            }

            console.log('✅ All people relationships saved successfully');

        } catch (error) {
            console.error('❌ Error saving people relationships:', error);
            this.uiUtilities.showToast('⚠️ Incident created but failed to save people relationships', 'warning');
        }
    }

    async updateIncidentPeopleRelationships(incidentId, involvedPeople = null) {
        try {
            console.log(`Updating people relationships for incident ${incidentId}`);

            // First, delete existing relationships
            await this.deleteIncidentPeopleRelationships(incidentId);

            // Then create new relationships if any exist
            const peopleToUpdate = involvedPeople || this.involvedPeople;
            if (peopleToUpdate && peopleToUpdate.length > 0) {
                console.log(`Creating ${peopleToUpdate.length} new people relationships`);
                await this.saveIncidentPeopleRelationships(incidentId, peopleToUpdate);
            } else {
                console.log('No people relationships to create');
            }

        } catch (error) {
            console.error('❌ Error updating people relationships:', error);
            this.uiUtilities.showToast('⚠️ Incident updated but failed to update people relationships', 'warning');
        }
    }

    // Getter for involved people data (used by form manager)
    getInvolvedPeopleData() {
        return this.involvedPeople;
    }

    // Setter for current incident ID
    setCurrentIncidentId(incidentId) {
        this.currentIncidentId = incidentId;
    }

    // Reset involved people list
    resetInvolvedPeople() {
        this.involvedPeople = [];
        this.updateInvolvedPeopleDisplay();
    }
}