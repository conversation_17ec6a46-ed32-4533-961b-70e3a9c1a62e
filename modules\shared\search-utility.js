// Search Utility - Eliminates duplication of search logic across managers
// Provides configurable search functionality for any entity type

export class SearchUtility {
    /**
     * Generic search function with configurable field mapping
     * @param {Array} data - Array of objects to search
     * @param {string} searchTerm - Search term
     * @param {Array} searchFields - Fields to search in
     * @param {Object} options - Search options
     * @returns {Array} Filtered results
     */
    static search(data, searchTerm, searchFields, options = {}) {
        if (!data || !searchTerm || !searchFields.length) {
            return data || [];
        }

        const term = searchTerm.toLowerCase();
        const caseSensitive = options.caseSensitive || false;
        const exactMatch = options.exactMatch || false;
        const searchValue = caseSensitive ? searchTerm : term;

        return data.filter(item => {
            return searchFields.some(field => {
                let fieldValue = this.getNestedFieldValue(item, field);
                
                if (fieldValue === null || fieldValue === undefined) {
                    return false;
                }

                fieldValue = String(fieldValue);
                if (!caseSensitive) {
                    fieldValue = fieldValue.toLowerCase();
                }

                if (exactMatch) {
                    return fieldValue === searchValue;
                } else {
                    return fieldValue.includes(searchValue);
                }
            });
        });
    }

    /**
     * Get nested field value using dot notation (e.g., 'person.name')
     * @param {Object} obj - Object to search in
     * @param {string} fieldPath - Field path (supports dot notation)
     * @returns {*} Field value
     */
    static getNestedFieldValue(obj, fieldPath) {
        return fieldPath.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }

    /**
     * Search with multiple criteria (AND/OR logic)
     * @param {Array} data - Array of objects to search
     * @param {Array} criteria - Array of search criteria objects
     * @param {string} logic - 'AND' or 'OR' logic
     * @returns {Array} Filtered results
     */
    static searchMultiCriteria(data, criteria, logic = 'AND') {
        if (!data || !criteria.length) {
            return data || [];
        }

        return data.filter(item => {
            const results = criteria.map(criterion => {
                const { searchTerm, searchFields, options = {} } = criterion;
                const matches = this.search([item], searchTerm, searchFields, options);
                return matches.length > 0;
            });

            return logic === 'AND' ? results.every(r => r) : results.some(r => r);
        });
    }

    /**
     * Filter data by field values
     * @param {Array} data - Array of objects to filter
     * @param {Object} filters - Object with field names as keys and filter values
     * @param {Object} options - Filter options
     * @returns {Array} Filtered results
     */
    static filter(data, filters, options = {}) {
        if (!data || !filters || Object.keys(filters).length === 0) {
            return data || [];
        }

        return data.filter(item => {
            return Object.entries(filters).every(([field, filterValue]) => {
                if (filterValue === undefined || filterValue === '') {
                    return true; // Skip empty filters
                }

                const itemValue = this.getNestedFieldValue(item, field);

                // Handle different filter types
                if (Array.isArray(filterValue)) {
                    return filterValue.includes(itemValue);
                } else if (typeof filterValue === 'object' && filterValue !== null) {
                    // Range filters, etc.
                    if (filterValue.min !== undefined && itemValue < filterValue.min) {
                        return false;
                    }
                    if (filterValue.max !== undefined && itemValue > filterValue.max) {
                        return false;
                    }
                    return true;
                } else {
                    return itemValue === filterValue;
                }
            });
        });
    }

    /**
     * Sort data by field with multiple sort criteria
     * @param {Array} data - Array of objects to sort
     * @param {Array} sortCriteria - Array of sort objects {field, direction}
     * @returns {Array} Sorted data
     */
    static sort(data, sortCriteria) {
        if (!data || !sortCriteria.length) {
            return data || [];
        }

        return [...data].sort((a, b) => {
            for (const { field, direction = 'asc' } of sortCriteria) {
                const aValue = this.getNestedFieldValue(a, field);
                const bValue = this.getNestedFieldValue(b, field);

                let comparison = 0;

                if (aValue === null || aValue === undefined) {
                    comparison = bValue === null || bValue === undefined ? 0 : 1;
                } else if (bValue === null || bValue === undefined) {
                    comparison = -1;
                } else if (typeof aValue === 'string' && typeof bValue === 'string') {
                    comparison = aValue.localeCompare(bValue);
                } else if (aValue instanceof Date && bValue instanceof Date) {
                    comparison = aValue.getTime() - bValue.getTime();
                } else {
                    comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
                }

                if (comparison !== 0) {
                    return direction === 'desc' ? -comparison : comparison;
                }
            }
            return 0;
        });
    }

    /**
     * Predefined search configurations for common entity types
     */
    static getEntitySearchConfig(entityType) {
        const configs = {
            people: {
                searchFields: ['first_name', 'last_name', 'known_as', 'phone', 'email'],
                defaultSort: [{ field: 'created_at', direction: 'desc' }]
            },
            bikes: {
                searchFields: ['serial_number', 'make', 'model', 'color', 'description'],
                defaultSort: [{ field: 'created_at', direction: 'desc' }]
            },
            properties: {
                searchFields: ['item_type', 'description', 'location_found', 'finder_name'],
                defaultSort: [{ field: 'found_date', direction: 'desc' }]
            },
            encampments: {
                searchFields: ['name', 'location', 'description', 'coordinator'],
                defaultSort: [{ field: 'created_at', direction: 'desc' }]
            },
            organizations: {
                searchFields: ['name', 'description', 'contact_person', 'email', 'phone'],
                defaultSort: [{ field: 'name', direction: 'asc' }]
            },
            incidents: {
                searchFields: ['incident_type', 'description', 'location', 'reporting_person'],
                defaultSort: [{ field: 'created_at', direction: 'desc' }]
            }
        };

        return configs[entityType] || {
            searchFields: ['name', 'description'],
            defaultSort: [{ field: 'created_at', direction: 'desc' }]
        };
    }

    /**
     * Combined search, filter, and sort operation
     * @param {Array} data - Data to process
     * @param {Object} params - Processing parameters
     * @returns {Array} Processed results
     */
    static searchFilterSort(data, params = {}) {
        let result = data || [];

        // Apply search
        if (params.searchTerm && params.searchFields) {
            result = this.search(result, params.searchTerm, params.searchFields, params.searchOptions);
        }

        // Apply filters
        if (params.filters) {
            result = this.filter(result, params.filters, params.filterOptions);
        }

        // Apply sort
        if (params.sortCriteria) {
            result = this.sort(result, params.sortCriteria);
        }

        // Apply pagination
        if (params.page && params.pageSize) {
            const start = (params.page - 1) * params.pageSize;
            const end = start + params.pageSize;
            result = result.slice(start, end);
        }

        return result;
    }
}