// Module Interface - Standardized interface contract for all modules
// Ensures consistent module structure and lifecycle management

import { Logger } from './logger.js';

export class ModuleInterface {
    constructor(name, version = '1.0.0', dependencies = []) {
        this.name = name;
        this.version = version;
        this.dependencies = dependencies;
        this.initialized = false;
        this.logger = Logger.forModule(name);
        this.startTime = Date.now();
    }

    /**
     * Initialize the module - Must be implemented by subclasses
     * @returns {Promise<void>}
     */
    async initialize() {
        throw new Error(`Module ${this.name} must implement initialize() method`);
    }

    /**
     * Get commands provided by this module - Must be implemented by subclasses
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        throw new Error(`Module ${this.name} must implement getCommands() method`);
    }

    /**
     * Cleanup module resources - Must be implemented by subclasses
     * @returns {Promise<void>}
     */
    async cleanup() {
        throw new Error(`Module ${this.name} must implement cleanup() method`);
    }

    /**
     * Get module status and health information
     * @returns {Object} Status information
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            initialized: this.initialized,
            uptime: Date.now() - this.startTime,
            dependencies: this.dependencies
        };
    }

    /**
     * Check if module dependencies are satisfied
     * @param {ModuleRegistry} registry - Module registry to check against
     * @returns {Object} Dependency check result
     */
    checkDependencies(registry) {
        const missing = [];
        const satisfied = [];

        for (const dep of this.dependencies) {
            if (registry.isRegistered(dep)) {
                satisfied.push(dep);
            } else {
                missing.push(dep);
            }
        }

        return {
            satisfied,
            missing,
            allSatisfied: missing.length === 0
        };
    }

    /**
     * Validate module configuration
     * Override in subclasses for custom validation
     * @returns {Object} Validation result
     */
    validateConfiguration() {
        return {
            valid: true,
            errors: []
        };
    }

    /**
     * Get module metadata
     * @returns {Object} Module metadata
     */
    getMetadata() {
        return {
            name: this.name,
            version: this.version,
            dependencies: this.dependencies,
            initialized: this.initialized,
            uptime: Date.now() - this.startTime
        };
    }

    /**
     * Standard initialization wrapper with error handling and logging
     * @returns {Promise<void>}
     */
    async _standardInitialize() {
        try {
            this.logger.info(`Initializing module ${this.name} v${this.version}`);
            
            // Check dependencies
            // Note: Registry would be passed in if needed for dependency checking
            
            // Validate configuration
            const validation = this.validateConfiguration();
            if (!validation.valid) {
                throw new Error(`Configuration validation failed: ${validation.errors.join(', ')}`);
            }

            // Call the actual initialize method
            await this.initialize();
            
            this.initialized = true;
            this.logger.info(`Module ${this.name} initialized successfully`);
            
        } catch (error) {
            this.logger.error(`Failed to initialize module ${this.name}`, error);
            throw error;
        }
    }

    /**
     * Standard cleanup wrapper with error handling and logging
     * @returns {Promise<void>}
     */
    async _standardCleanup() {
        try {
            this.logger.info(`Cleaning up module ${this.name}`);
            
            await this.cleanup();
            
            this.initialized = false;
            this.logger.info(`Module ${this.name} cleaned up successfully`);
            
        } catch (error) {
            this.logger.error(`Failed to cleanup module ${this.name}`, error);
            throw error;
        }
    }
}

/**
 * Feature Module Interface - Specialized interface for feature modules
 * Extends ModuleInterface with feature-specific methods
 */
export class FeatureModuleInterface extends ModuleInterface {
    constructor(name, version, dependencies, entityTypes = []) {
        super(name, version, dependencies);
        this.entityTypes = entityTypes; // What entities this module manages
        this.isFeatureModule = true;
    }

    /**
     * Get workflows provided by this module
     * Override in subclasses to provide module workflows
     * @returns {Object} Available workflows
     */
    getWorkflows() {
        return {};
    }

    /**
     * Get templates provided by this module
     * Override in subclasses to provide module templates
     * @returns {Object} Available templates
     */
    getTemplates() {
        return {};
    }

    /**
     * Get API endpoints provided by this module
     * Override in subclasses to provide module API
     * @returns {Array} Available API endpoints
     */
    getApiEndpoints() {
        return [];
    }

    /**
     * Get module statistics and metrics
     * Override in subclasses to provide custom metrics
     * @returns {Promise<Object>} Module statistics
     */
    async getStatistics() {
        return {
            module: this.name,
            uptime: Date.now() - this.startTime,
            initialized: this.initialized
        };
    }

    getMetadata() {
        return {
            ...super.getMetadata(),
            entityTypes: this.entityTypes,
            isFeatureModule: this.isFeatureModule
        };
    }
}

/**
 * Service Module Interface - Specialized interface for service modules
 * Extends ModuleInterface with service-specific methods
 */
export class ServiceModuleInterface extends ModuleInterface {
    constructor(name, version, dependencies, serviceType = 'utility') {
        super(name, version, dependencies);
        this.serviceType = serviceType; // 'utility', 'integration', 'core'
        this.isServiceModule = true;
    }

    /**
     * Get service health status
     * Override in subclasses to provide service health checks
     * @returns {Promise<Object>} Service health information
     */
    async getHealthStatus() {
        return {
            healthy: this.initialized,
            lastCheck: new Date().toISOString(),
            uptime: Date.now() - this.startTime
        };
    }

    getMetadata() {
        return {
            ...super.getMetadata(),
            serviceType: this.serviceType,
            isServiceModule: this.isServiceModule
        };
    }
}