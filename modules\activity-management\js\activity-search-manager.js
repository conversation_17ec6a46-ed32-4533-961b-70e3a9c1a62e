/**
 * Activity Search Manager
 * Handles record search functionality for activity creation
 */

import { BaseManager } from '../../shared/base-manager.js';
import { SearchUtility } from '../../shared/search-utility.js';

export class ActivitySearchManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
    }

    /**
     * Show record search modal for specific activity type
     */
    async showRecordSearch(activityType) {
        try {
            if (activityType === 'people') {
                await this.showPersonSearch();
            } else if (activityType === 'addresses') {
                await this.showAddressSearch();
            } else if (activityType === 'license_plates') {
                await this.showVehicleSearch();
            } else {
                throw new Error(`Unsupported activity type: ${activityType}`);
            }
        } catch (error) {
            console.error('Error showing record search:', error);
            this.ui.showDialog('Error', `Failed to show search: ${error.message}`, 'error');
        }
    }

    /**
     * Show person search modal with create new person option
     */
    async showPersonSearch() {
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay person-search-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Select Person for Activity</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="person-search-query">Search For Person:</label>
                            <input type="text" id="person-search-query" name="person-search-query" placeholder="Enter name, phone, email...">
                        </div>
                        <div class="search-actions">
                            <button type="button" id="person-search-submit" class="primary-button">Search</button>
                            <button type="button" id="create-new-person" class="secondary-button">Create New Person</button>
                        </div>
                    </div>
                    <div class="search-results" id="person-search-results" style="display: none;">
                        <h4>Search Results:</h4>
                        <div class="results-container" id="person-results-container"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        const searchQuery = searchModal.querySelector('#person-search-query');
        const searchSubmit = searchModal.querySelector('#person-search-submit');
        const createNewBtn = searchModal.querySelector('#create-new-person');
        const searchResults = searchModal.querySelector('#person-search-results');
        const resultsContainer = searchModal.querySelector('#person-results-container');

        // Handle search submission
        const performSearch = async () => {
            const query = searchQuery.value.trim();

            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = 'Searching...';

                const results = await this.searchPeople(query);
                this.displayPersonResults(results, resultsContainer);
                searchResults.style.display = 'block';

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = 'Search';
            }
        };

        // Handle create new person
        const handleCreateNew = async () => {
            searchModal.remove();
            await this.showPersonCreationForm();
        };

        // Event listeners
        searchSubmit.addEventListener('click', performSearch);
        createNewBtn.addEventListener('click', handleCreateNew);
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        searchQuery.focus();
    }

    /**
     * Search people with proper filtering (fixes the bug)
     */
    async searchPeople(query) {
        try {
            // Get all people records
            const allPeople = await this.data.search('people', {});
            if (!allPeople || allPeople.length === 0) {
                return [];
            }

            // If no query, return all (but this shouldn't happen in our UI)
            if (!query || query.trim() === '') {
                return allPeople;
            }

            const lowerQuery = query.toLowerCase();
            
            // Search in relevant person fields
            const searchFields = ['first_name', 'last_name', 'nickname', 'phone', 'email', 'emergency_contact_name', 'emergency_contact_phone'];
            
            // Use SearchUtility for consistent search behavior
            return SearchUtility.search(allPeople, query, searchFields);
        } catch (error) {
            console.error('Error searching people:', error);
            return [];
        }
    }

    /**
     * Display person search results
     */
    displayPersonResults(results, container) {
        container.innerHTML = '';

        if (results.length === 0) {
            container.innerHTML = `
                <div class="no-results">
                    <p>No people found matching your search.</p>
                    <p>Try different search terms or create a new person record.</p>
                </div>
            `;
            return;
        }

        results.forEach(person => {
            const personDiv = document.createElement('div');
            personDiv.className = 'search-result-item person-result';
            personDiv.innerHTML = `
                <div class="person-info">
                    <div class="person-name">${person.first_name || ''} ${person.last_name || ''}</div>
                    <div class="person-details">
                        ${person.phone ? `Phone: ${person.phone}<br>` : ''}
                        ${person.email ? `Email: ${person.email}<br>` : ''}
                        ${person.housing_status ? `Housing: ${person.housing_status}` : ''}
                    </div>
                </div>
                <div class="person-actions">
                    <button class="primary-button select-person-btn">Select</button>
                </div>
            `;

            const selectBtn = personDiv.querySelector('.select-person-btn');
            selectBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handlePersonSelection(person);
            });

            container.appendChild(personDiv);
        });
    }

    /**
     * Handle person selection
     */
    async handlePersonSelection(person) {
        // Close the search modal
        document.querySelector('.person-search-overlay').remove();

        // Import and show activity form
        const { ActivityManager } = await import('./activity-manager.js');
        const activityManager = new ActivityManager(this.data, this.auth, this.ui, this.uiUtilities);
        await activityManager.showActivityForm('people', person);
    }

    /**
     * Show person creation form
     */
    async showPersonCreationForm() {
        try {
            // Generate form fields for person creation
            const fields = this.data.schema.generateFormFields('people');

            this.ui.showForm('Create New Person', fields, async (formData) => {
                try {
                    const currentUser = this.auth.getCurrentUser();
                    const personData = {
                        ...formData,
                        created_at: new Date().toISOString(),
                        created_by: currentUser?.email || 'System'
                    };

                    const newPerson = await this.data.insert('people', personData);
                    this.ui.showDialog('Success', 'Person created successfully!', 'success');

                    // Automatically show activity form for the new person
                    const { ActivityManager } = await import('./activity-manager.js');
                    const activityManager = new ActivityManager(this.data, this.auth, this.ui, this.uiUtilities);
                    await activityManager.showActivityForm('people', newPerson);

                    return true; // Close the form
                } catch (error) {
                    console.error('Error creating person:', error);
                    this.ui.showDialog('Error', 'Failed to create person record', 'error');
                    return false; // Keep the form open
                }
            });
        } catch (error) {
            console.error('Error showing person creation form:', error);
            this.ui.showDialog('Error', 'Failed to show person creation form', 'error');
        }
    }

    /**
     * Show address search modal (placeholder for future implementation)
     */
    async showAddressSearch() {
        this.ui.showDialog('Not Implemented', 'Address activity search is not yet implemented.', 'info');
    }

    /**
     * Show vehicle search modal (placeholder for future implementation)
     */
    async showVehicleSearch() {
        this.ui.showDialog('Not Implemented', 'Vehicle activity search is not yet implemented.', 'info');
    }

    /**
     * Search records by type and query
     */
    async searchRecords(recordType, query) {
        try {
            switch (recordType) {
                case 'people':
                    return await this.searchPeople(query);
                case 'addresses':
                    return await this.data.search('addresses', {});
                case 'license_plates':
                    return await this.data.search('license_plates', {});
                default:
                    throw new Error(`Unsupported record type: ${recordType}`);
            }
        } catch (error) {
            console.error(`Error searching ${recordType}:`, error);
            throw error;
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners if needed
    }
}
