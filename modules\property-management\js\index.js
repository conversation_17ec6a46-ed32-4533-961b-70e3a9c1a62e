/**
 * Property Management Module - Main Entry Point
 * Handles property records and found property tracking
 */

import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { PropertyManager } from './property-manager.js';
import { Logger } from '../../shared/logger.js';

export class PropertyManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities = null, modalManagement = null) {
        super('PropertyManagement', '1.0.0', [], ['property_records']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.logger = Logger.forModule('PropertyManagement');

        // Initialize managers
        this.propertyManager = new PropertyManager(dataManager, authManager, uiManager);
    }

    /**
     * Initialize the property management module
     * @returns {Promise<void>}
     */
    async initialize() {
        this.logger.info('Initializing Property Management module');
        this.initialized = true;
    }

    /**
     * Cleanup module resources
     * @returns {Promise<void>}
     */
    async cleanup() {
        this.logger.info('Cleaning up Property Management module');
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // TODO: Implement property management commands
        return new Map();
    }

    /**
     * Get module status
     * @returns {Object} Module status
     */
    getStatus() {
        return {
            name: this.name,
            version: this.version,
            initialized: this.initialized,
            uptime: Date.now() - this.startTime
        };
    }

    /**
     * Get workflows provided by this module
     * @returns {Object} Available workflows
     */
    getWorkflows() {
        return {
            logProperty: () => this.propertyManager.logProperty.bind(this.propertyManager),
            searchProperties: () => this.propertyManager.search.bind(this.propertyManager),
            getPropertyById: () => this.propertyManager.getById.bind(this.propertyManager)
        };
    }

    /**
     * Get templates provided by this module
     * @returns {Object} Available templates
     */
    getTemplates() {
        return {
            // TODO: Add property templates when available
        };
    }

    /**
     * Get API endpoints provided by this module
     * @returns {Object} Available API endpoints
     */
    getApiEndpoints() {
        return {
            // Property management uses standard CRUD endpoints
        };
    }

    /**
     * Get statistics for this module
     * @returns {Promise<Object>} Module statistics
     */
    async getStatistics() {
        try {
            const totalProperties = await this.propertyManager.getAll();
            return {
                totalProperties: totalProperties.length,
                // TODO: Add more detailed statistics
            };
        } catch (error) {
            this.logger.error('Failed to get property statistics', error);
            return {
                totalProperties: 0,
                error: error.message
            };
        }
    }

    // Expose manager methods for backward compatibility
    async logProperty(propertyData) {
        return await this.propertyManager.logProperty(propertyData);
    }

    async search(searchTerm) {
        return await this.propertyManager.search(searchTerm);
    }

    async getById(id) {
        return await this.propertyManager.getById(id);
    }

    async getAll() {
        return await this.propertyManager.getAll();
    }
}

// Export both for compatibility
export { PropertyManager } from './property-manager.js';
