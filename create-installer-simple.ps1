# S.T.E.V.I Retro Simple Installer Builder
# Bypasses dependency issues by using a simplified approach

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "S.T.E.V.I Retro Simple Installer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found." -ForegroundColor Red
    Write-Host "Please run this script from the S.T.E.V.I Retro project directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Stop any running processes
Write-Host "Stopping any running processes..." -ForegroundColor Yellow
Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "S.T.E.V.I Retro" -ErrorAction SilentlyContinue | Stop-Process -Force
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force

# Wait for processes to fully stop
Start-Sleep -Seconds 3

# Create a temporary package.json with fixed dependencies
Write-Host "Creating temporary build configuration..." -ForegroundColor Yellow

$originalPackageJson = Get-Content "package.json" -Raw | ConvertFrom-Json

# Create a simplified build configuration
$buildConfig = @{
    appId = "ca.iharc.steviretro"
    productName = "S.T.E.V.I Retro"
    copyright = "Copyright © 2024 I.H.A.R.C. All rights reserved."
    directories = @{
        output = "dist"
        buildResources = "build"
    }
    files = @(
        "electron/**/*",
        "renderer/**/*",
        "templates/**/*",
        "assets/**/*",
        "!node_modules/@azure/**/*",
        "node_modules/better-sqlite3/**/*",
        "node_modules/@supabase/**/*",
        "node_modules/fs-extra/**/*",
        "node_modules/node-fetch/**/*",
        "node_modules/semver/**/*",
        "node_modules/uuid/**/*"
    )
    extraResources = @(
        @{
            from = "templates"
            to = "templates"
            filter = @("**/*")
        }
    )
    win = @{
        target = @(
            @{
                target = "nsis"
                arch = @("x64")
            }
        )
        verifyUpdateCodeSignature = $false
        requestedExecutionLevel = "asInvoker"
        legalTrademarks = "I.H.A.R.C. - Supportive Technology to Enable Vulnerable Individuals"
    }
    nsis = @{
        oneClick = $false
        allowToChangeInstallationDirectory = $true
        allowElevation = $true
        createDesktopShortcut = $true
        createStartMenuShortcut = $true
        shortcutName = "S.T.E.V.I Retro"
        runAfterFinish = $true
        menuCategory = "I.H.A.R.C"
        artifactName = "S.T.E.V.I-Retro-Setup-`${version}.`${ext}"
        deleteAppDataOnUninstall = $false
        perMachine = $true
        packElevateHelper = $true
        unicode = $true
        warningsAsErrors = $false
        displayLanguageSelector = $false
        multiLanguageInstaller = $false
        removeDefaultUninstallWelcomePage = $false
        guid = "ca.iharc.steviretro"
        differentialPackage = $false
    }
    publish = $null
}

# Create temporary build package.json
$tempPackage = @{
    name = $originalPackageJson.name
    version = $originalPackageJson.version
    description = $originalPackageJson.description
    main = $originalPackageJson.main
    type = $originalPackageJson.type
    scripts = $originalPackageJson.scripts
    keywords = $originalPackageJson.keywords
    author = $originalPackageJson.author
    license = $originalPackageJson.license
    dependencies = @{
        "@supabase/supabase-js" = "^2.50.5"
        "better-sqlite3" = "^12.2.0"
        "fs-extra" = "^11.3.0"
        "node-fetch" = "^3.3.2"
        "semver" = "^7.7.2"
        "uuid" = "^11.1.0"
    }
    devDependencies = @{
        "electron" = "^37.2.3"
        "electron-builder" = "^26.0.12"
    }
    build = $buildConfig
}

# Save temporary package.json
$tempPackageJson = $tempPackage | ConvertTo-Json -Depth 10
$tempPackageJson | Out-File "package-temp.json" -Encoding UTF8

# Clean dist directory
if (Test-Path "dist") {
    Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "Building installer with simplified configuration..." -ForegroundColor Yellow
Write-Host "This may take several minutes..." -ForegroundColor Yellow
Write-Host ""

try {
    # Use the temporary package.json for building
    $env:npm_config_package = "package-temp.json"
    
    # Build with explicit electron version and simplified config
    npx electron-builder --config package-temp.json --win --x64
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "SUCCESS: Installer created!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        # Find the installer file
        $installerFiles = Get-ChildItem -Path "dist" -Filter "*.exe" -ErrorAction SilentlyContinue
        if ($installerFiles) {
            foreach ($file in $installerFiles) {
                Write-Host "Installer: $($file.FullName)" -ForegroundColor Cyan
                Write-Host "Size: $([math]::Round($file.Length / 1MB, 2)) MB" -ForegroundColor Cyan
            }
        }
        
        Write-Host ""
        Write-Host "Opening dist folder..." -ForegroundColor Yellow
        Start-Process "dist"
    } else {
        throw "Build failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "ERROR: Build failed!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Try alternative approach with electron-packager
    Write-Host ""
    Write-Host "Trying alternative approach with electron-packager..." -ForegroundColor Yellow
    
    try {
        npx electron-packager . "S.T.E.V.I Retro" --platform=win32 --arch=x64 --out=dist --overwrite --electron-version=37.2.3
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Packager succeeded! Creating portable version..." -ForegroundColor Green
            
            # Create a simple zip file as installer alternative
            $packagedDir = Get-ChildItem -Path "dist" -Directory | Where-Object { $_.Name -like "*S.T.E.V.I Retro*" } | Select-Object -First 1
            if ($packagedDir) {
                $zipPath = "dist\S.T.E.V.I-Retro-Portable-$($originalPackageJson.version).zip"
                Compress-Archive -Path "$($packagedDir.FullName)\*" -DestinationPath $zipPath -Force
                Write-Host "Portable version created: $zipPath" -ForegroundColor Green
            }
        }
    } catch {
        Write-Host "Alternative approach also failed: $($_.Exception.Message)" -ForegroundColor Red
    }
} finally {
    # Clean up temporary file
    if (Test-Path "package-temp.json") {
        Remove-Item "package-temp.json" -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
