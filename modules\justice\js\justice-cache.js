// Justice Cache Layer
// SQLite tables mirroring Supabase justice schema for offline functionality

export class JusticeCache {
    constructor(dataManager) {
        this.data = dataManager;
        this.sqlite = dataManager.sqlite;
        this.lastSyncAt = new Map(); // Track last sync per table
    }
    
    async initialize() {
        try {
            await this.createTables();
            console.log('✅ Justice cache tables initialized');
        } catch (error) {
            console.error('❌ Failed to initialize justice cache:', error);
            throw error;
        }
    }
    
    async createTables() {
        const db = this.sqlite.getDatabase();
        
        // Justice Episode table
        db.exec(`
            CREATE TABLE IF NOT EXISTS justice_episode (
                id TEXT PRIMARY KEY,
                person_id TEXT NOT NULL,
                origin TEXT NOT NULL,
                origin_agency TEXT,
                origin_dt TEXT NOT NULL,
                jurisdiction TEXT,
                current_state TEXT NOT NULL DEFAULT 'UNKNOWN',
                created_by TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT
            )
        `);
        
        // Justice Event table
        db.exec(`
            CREATE TABLE IF NOT EXISTS je_event (
                id TEXT PRIMARY KEY,
                je_id TEXT NOT NULL,
                event_type TEXT NOT NULL,
                event_dt TEXT NOT NULL,
                payload TEXT NOT NULL DEFAULT '{}',
                entered_by TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT,
                FOREIGN KEY (je_id) REFERENCES justice_episode(id)
            )
        `);
        
        // Justice Charge table
        db.exec(`
            CREATE TABLE IF NOT EXISTS je_charge (
                id TEXT PRIMARY KEY,
                je_id TEXT NOT NULL,
                code TEXT,
                label TEXT,
                severity TEXT NOT NULL DEFAULT 'OTHER',
                status TEXT NOT NULL DEFAULT 'PENDING',
                amended_from_id TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT,
                FOREIGN KEY (je_id) REFERENCES justice_episode(id)
            )
        `);
        
        // Justice Condition table
        db.exec(`
            CREATE TABLE IF NOT EXISTS je_condition (
                id TEXT PRIMARY KEY,
                je_id TEXT NOT NULL,
                source_event_id TEXT,
                type TEXT NOT NULL,
                details TEXT NOT NULL DEFAULT '{}',
                start_dt TEXT NOT NULL,
                end_dt TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT,
                FOREIGN KEY (je_id) REFERENCES justice_episode(id)
            )
        `);
        
        // Justice Contact table
        db.exec(`
            CREATE TABLE IF NOT EXISTS je_contact (
                id TEXT PRIMARY KEY,
                person_id TEXT NOT NULL,
                je_id TEXT,
                role TEXT NOT NULL,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                org TEXT,
                started_at TEXT,
                ended_at TEXT,
                active INTEGER NOT NULL DEFAULT 1,
                notes TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT
            )
        `);
        
        // Custody Episode table
        db.exec(`
            CREATE TABLE IF NOT EXISTS je_custody_episode (
                id TEXT PRIMARY KEY,
                je_id TEXT NOT NULL,
                facility_id TEXT NOT NULL,
                reason TEXT NOT NULL DEFAULT 'REMAND',
                start_dt TEXT NOT NULL,
                end_dt TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT,
                FOREIGN KEY (je_id) REFERENCES justice_episode(id)
            )
        `);
        
        // Warrant table
        db.exec(`
            CREATE TABLE IF NOT EXISTS je_warrant (
                id TEXT PRIMARY KEY,
                je_id TEXT NOT NULL,
                type TEXT NOT NULL,
                issued_dt TEXT NOT NULL,
                executed_dt TEXT,
                status TEXT NOT NULL DEFAULT 'OUTSTANDING',
                notes TEXT,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT,
                FOREIGN KEY (je_id) REFERENCES justice_episode(id)
            )
        `);
        
        // Facility table (reference data)
        db.exec(`
            CREATE TABLE IF NOT EXISTS facility (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                region TEXT,
                address TEXT,
                phone TEXT,
                active INTEGER NOT NULL DEFAULT 1,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT
            )
        `);
        
        // Condition Pack table (reference data)
        db.exec(`
            CREATE TABLE IF NOT EXISTS condition_pack (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                scope TEXT NOT NULL DEFAULT 'ANY',
                version INTEGER NOT NULL DEFAULT 1,
                description TEXT,
                active INTEGER NOT NULL DEFAULT 1,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT
            )
        `);
        
        // Condition Pack Item table (reference data)
        db.exec(`
            CREATE TABLE IF NOT EXISTS condition_pack_item (
                id TEXT PRIMARY KEY,
                pack_id TEXT NOT NULL,
                type TEXT NOT NULL,
                label TEXT NOT NULL,
                required INTEGER NOT NULL DEFAULT 0,
                default_details TEXT NOT NULL DEFAULT '{}',
                sort_order INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                synced_at TEXT,
                FOREIGN KEY (pack_id) REFERENCES condition_pack(id)
            )
        `);
        
        // Create indexes for performance
        db.exec(`
            CREATE INDEX IF NOT EXISTS idx_justice_episode_person ON justice_episode(person_id);
            CREATE INDEX IF NOT EXISTS idx_je_event_episode ON je_event(je_id);
            CREATE INDEX IF NOT EXISTS idx_je_charge_episode ON je_charge(je_id);
            CREATE INDEX IF NOT EXISTS idx_je_condition_episode ON je_condition(je_id);
            CREATE INDEX IF NOT EXISTS idx_je_contact_episode ON je_contact(je_id);
            CREATE INDEX IF NOT EXISTS idx_je_custody_episode ON je_custody_episode(je_id);
            CREATE INDEX IF NOT EXISTS idx_je_warrant_episode ON je_warrant(je_id);
        `);
    }
    
    /**
     * Sync data from Supabase to local cache
     * @param {string} table - Table name to sync
     */
    async syncFromSupabase(table) {
        try {
            const supabase = await this.data.getSupabaseClient();
            const lastSync = this.lastSyncAt.get(table) || '1970-01-01T00:00:00Z';
            
            // Get schema-qualified table name
            const schemaTable = this.getSchemaTable(table);
            
            const { data, error } = await supabase
                .from(schemaTable)
                .select('*')
                .gt('created_at', lastSync)
                .order('created_at');
                
            if (error) throw error;
            
            if (data && data.length > 0) {
                await this.upsertLocalRecords(table, data);
                this.lastSyncAt.set(table, new Date().toISOString());
                console.log(`✅ Synced ${data.length} records from ${table}`);
            }
            
        } catch (error) {
            console.error(`❌ Failed to sync ${table}:`, error);
        }
    }
    
    /**
     * Get schema-qualified table name for Supabase
     * @param {string} table - Local table name
     * @returns {string} Schema-qualified table name
     */
    getSchemaTable(table) {
        const mapping = {
            'justice_episode': 'justice.justice_episode',
            'je_event': 'justice.je_event',
            'je_charge': 'justice.je_charge',
            'je_condition': 'justice.je_condition',
            'je_contact': 'justice.je_contact',
            'je_custody_episode': 'justice.je_custody_episode',
            'je_warrant': 'justice.je_warrant',
            'facility': 'justice.facility',
            'condition_pack': 'justice.condition_pack',
            'condition_pack_item': 'justice.condition_pack_item'
        };
        return mapping[table] || table;
    }
    
    /**
     * Upsert records into local cache
     * @param {string} table - Table name
     * @param {Array} records - Records to upsert
     */
    async upsertLocalRecords(table, records) {
        const db = this.sqlite.getDatabase();
        
        for (const record of records) {
            const columns = Object.keys(record);
            const placeholders = columns.map(() => '?').join(', ');
            const values = columns.map(col => {
                const value = record[col];
                return typeof value === 'object' ? JSON.stringify(value) : value;
            });
            
            const sql = `
                INSERT OR REPLACE INTO ${table} (${columns.join(', ')}, synced_at)
                VALUES (${placeholders}, ?)
            `;
            
            db.prepare(sql).run(...values, new Date().toISOString());
        }
    }
    
    /**
     * Get local records with optional filtering
     * @param {string} table - Table name
     * @param {Object} filters - Filter conditions
     * @returns {Array} Records
     */
    getLocalRecords(table, filters = {}) {
        const db = this.sqlite.getDatabase();
        
        let sql = `SELECT * FROM ${table}`;
        const conditions = [];
        const values = [];
        
        for (const [key, value] of Object.entries(filters)) {
            conditions.push(`${key} = ?`);
            values.push(value);
        }
        
        if (conditions.length > 0) {
            sql += ` WHERE ${conditions.join(' AND ')}`;
        }
        
        return db.prepare(sql).all(...values);
    }
    
    /**
     * Insert a record locally (for offline creation)
     * @param {string} table - Table name
     * @param {Object} record - Record to insert
     */
    insertLocalRecord(table, record) {
        const db = this.sqlite.getDatabase();
        
        const columns = Object.keys(record);
        const placeholders = columns.map(() => '?').join(', ');
        const values = columns.map(col => {
            const value = record[col];
            return typeof value === 'object' ? JSON.stringify(value) : value;
        });
        
        const sql = `
            INSERT INTO ${table} (${columns.join(', ')})
            VALUES (${placeholders})
        `;
        
        return db.prepare(sql).run(...values);
    }
    
    /**
     * Sync all justice tables
     */
    async syncAll() {
        const tables = [
            'facility',
            'condition_pack',
            'condition_pack_item',
            'justice_episode',
            'je_event',
            'je_charge',
            'je_condition',
            'je_contact',
            'je_custody_episode',
            'je_warrant'
        ];
        
        for (const table of tables) {
            await this.syncFromSupabase(table);
        }
    }
}
