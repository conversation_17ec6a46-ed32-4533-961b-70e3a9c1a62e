/**
 * Modal Template Loader
 * 
 * Handles dynamic loading and caching of modal templates.
 * Provides a consistent interface for template-based modal creation.
 */

export class ModalTemplateLoader {
    constructor() {
        this.templateCache = new Map();
        this.templatePaths = new Map();
        this.setupDefaultPaths();
    }

    /**
     * Setup default template paths
     */
    setupDefaultPaths() {
        this.templatePaths.set('incidents', '../../../../modules/incident-management/templates/incident-detail-templates.js');
        this.templatePaths.set('reports', '../../../../modules/reports-management/templates/index.js');
        this.templatePaths.set('vehicles', '../../../../modules/vehicle-management/templates/vehicle-list-templates.js');
        this.templatePaths.set('addresses', '../../../../modules/address-management/templates/address-search-templates.js');
        this.templatePaths.set('organizations', '../../../../modules/organizations-management/templates/organization-search-templates.js');
        this.templatePaths.set('encampments', '../../../../modules/encampment-management/templates/encampment-templates.js');
        this.templatePaths.set('property', '../../../../modules/property-management/templates/property-return-templates.js');
    }

    /**
     * Load a template by name
     * @param {string} templateName - Template name (e.g., 'updateStatusModal')
     * @param {string} templateGroup - Template group (e.g., 'incidents')
     * @returns {Promise<Function>} Template function
     */
    async loadTemplate(templateName, templateGroup = null) {
        const cacheKey = templateGroup ? `${templateGroup}.${templateName}` : templateName;

        // Return cached template if available
        if (this.templateCache.has(cacheKey)) {
            return this.templateCache.get(cacheKey);
        }

        // Try to find template in known groups
        if (!templateGroup) {
            for (const [group, path] of this.templatePaths) {
                try {
                    const template = await this.loadFromPath(path, templateName);
                    if (template) {
                        this.templateCache.set(cacheKey, template);
                        return template;
                    }
                } catch (error) {
                    // Continue searching in other groups
                    continue;
                }
            }
            throw new Error(`Template '${templateName}' not found in any template group`);
        }

        // Load from specific group
        const path = this.templatePaths.get(templateGroup);
        if (!path) {
            throw new Error(`Template group '${templateGroup}' not found`);
        }

        const template = await this.loadFromPath(path, templateName);
        if (!template) {
            throw new Error(`Template '${templateName}' not found in group '${templateGroup}'`);
        }

        this.templateCache.set(cacheKey, template);
        return template;
    }

    /**
     * Load template from a specific path
     * @param {string} path - Template file path
     * @param {string} templateName - Template name
     * @returns {Promise<Function|null>} Template function or null if not found
     */
    async loadFromPath(path, templateName) {
        try {
            const module = await import(path);
            
            // Check different possible export patterns
            if (module[templateName]) {
                return module[templateName];
            }

            // Check for template objects
            for (const exportKey of Object.keys(module)) {
                const exportValue = module[exportKey];
                if (exportValue && typeof exportValue === 'object' && exportValue[templateName]) {
                    return exportValue[templateName];
                }
            }

            // Check default export
            if (module.default && module.default[templateName]) {
                return module.default[templateName];
            }

            return null;
        } catch (error) {
            console.warn(`Failed to load template from ${path}:`, error);
            return null;
        }
    }

    /**
     * Add a custom template path
     * @param {string} group - Template group name
     * @param {string} path - Template file path
     */
    addTemplatePath(group, path) {
        this.templatePaths.set(group, path);
    }

    /**
     * Cache a template directly
     * @param {string} name - Template name
     * @param {Function} template - Template function
     * @param {string} group - Optional template group
     */
    cacheTemplate(name, template, group = null) {
        const cacheKey = group ? `${group}.${name}` : name;
        this.templateCache.set(cacheKey, template);
    }

    /**
     * Clear template cache
     * @param {string} pattern - Optional pattern to match (clears all if not provided)
     */
    clearCache(pattern = null) {
        if (!pattern) {
            this.templateCache.clear();
            return;
        }

        const keysToDelete = [];
        for (const key of this.templateCache.keys()) {
            if (key.includes(pattern)) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => this.templateCache.delete(key));
    }

    /**
     * Get cache statistics
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            cachedTemplates: this.templateCache.size,
            templatePaths: this.templatePaths.size,
            cacheKeys: Array.from(this.templateCache.keys())
        };
    }

    /**
     * Preload commonly used templates
     * @param {Array<string>} templateNames - Template names to preload
     * @returns {Promise<void>}
     */
    async preloadTemplates(templateNames) {
        const loadPromises = templateNames.map(async (templateName) => {
            try {
                await this.loadTemplate(templateName);
                console.log(`Preloaded template: ${templateName}`);
            } catch (error) {
                console.warn(`Failed to preload template ${templateName}:`, error);
            }
        });

        await Promise.allSettled(loadPromises);
    }
}