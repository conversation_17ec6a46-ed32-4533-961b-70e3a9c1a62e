// Refactored Reports Manager - Uses BaseManager to eliminate code duplication
import { BaseManager } from '../../shared/base-manager.js';

class ReportsManager extends BaseManager {
    constructor(dataManager, authManager, uiManager = null) {
        super(dataManager, authManager, uiManager);
    }

    /**
     * Generate resource service report - simplified with base class error handling
     * @param {Object} options - Report generation options
     * @returns {Promise<Object>} Report data
     */
    async generateResourceServiceReport(options) {
        const { personId, categories, location, staffId, staffName } = options;
        
        return await this.executeWithErrorHandling(async () => {
            const person = await this.data.get('people', personId);
            if (!person) {
                throw new Error('Person record not found');
            }

            const organizations = await this.getFilteredOrganizations(categories, location);
            const reportData = {
                person,
                organizations,
                categories,
                location,
                staffId,
                staffName,
                generatedAt: new Date(),
                reportType: 'resource_services'
            };

            await this.logReportActivity(personId, 'resource_services_report', reportData);
            
            return reportData;
        }, 'generating resource service report');
    }

    /**
     * Get filtered organizations - simplified with base class error handling
     * @param {Array} categories - Service categories to filter by
     * @param {string} location - Location to filter by
     * @returns {Promise<Array>} Filtered organizations
     */
    async getFilteredOrganizations(categories, location) {
        return await this.executeWithErrorHandling(async () => {
            let query = {};
            
            if (location && location !== 'All') {
                query.city = location;
            }

            const allOrganizations = await this.data.search('organizations', query);
            
            if (categories && categories.length > 0) {
                return allOrganizations.filter(org => {
                    if (!org.services_tags) return false;
                    
                    const orgServices = Array.isArray(org.services_tags) ? 
                        org.services_tags : 
                        (typeof org.services_tags === 'string' ? 
                            org.services_tags.split(',').map(s => s.trim()) : 
                            []);
                    
                    return categories.some(category => 
                        orgServices.some(service => 
                            service.toLowerCase().includes(category.toLowerCase()) ||
                            org.organization_type?.toLowerCase().includes(category.toLowerCase()) ||
                            org.services_provided?.toLowerCase().includes(category.toLowerCase())
                        )
                    );
                });
            }
            
            return allOrganizations;
        }, 'filtering organizations');
    }

    /**
     * Log report activity - simplified with base class user context and error handling
     * @param {string} personId - Person ID
     * @param {string} reportType - Type of report
     * @param {Object} reportData - Report data
     * @returns {Promise<void>}
     */
    async logReportActivity(personId, reportType, reportData) {
        return await this.executeWithErrorHandling(async () => {
            const context = this.getCurrentUserContext();
            
            const activityLog = {
                id: crypto.randomUUID(),
                table_name: 'people',
                record_id: personId.toString(),
                action: 'report_generated',
                field_name: reportType,
                old_value: null,
                new_value: JSON.stringify({
                    reportType,
                    categories: reportData.categories,
                    location: reportData.location,
                    organizationCount: reportData.organizations.length
                }),
                user_email: context.userEmail,
                user_name: context.userName || 'Unknown User',
                timestamp: context.timestamp,
                notes: `Generated ${reportType.replace('_', ' ')} report for ${reportData.person.first_name} ${reportData.person.last_name}`,
                created_at: context.timestamp
            };

            await this.data.insert('activity_logs', activityLog);
        }, 'logging report activity', false); // Don't throw on error - logging is optional
    }

    /**
     * Get service categories - static configuration data
     * @returns {Array} Service category options
     */
    getServiceCategories() {
        return [
            { value: 'foodbank', label: 'Food Bank / Food Services' },
            { value: 'shelter', label: 'Shelter Services' },
            { value: 'housing', label: 'Housing Support' },
            { value: 'mental_health', label: 'Mental Health Services' },
            { value: 'addiction', label: 'Addiction Support' },
            { value: 'medical', label: 'Medical/Healthcare' },
            { value: 'legal', label: 'Legal Aid' },
            { value: 'employment', label: 'Employment Services' },
            { value: 'financial', label: 'Financial Assistance' },
            { value: 'clothing', label: 'Clothing/Personal Items' },
            { value: 'transportation', label: 'Transportation' },
            { value: 'education', label: 'Education/Training' },
            { value: 'advocacy', label: 'Advocacy Services' },
            { value: 'crisis', label: 'Crisis Support' },
            { value: 'youth', label: 'Youth Services' },
            { value: 'senior', label: 'Senior Services' },
            { value: 'family', label: 'Family Support' },
            { value: 'disability', label: 'Disability Services' }
        ];
    }

    /**
     * Get location options - static configuration data
     * @returns {Array} Location options
     */
    getLocationOptions() {
        return [
            { value: 'All', label: 'All Locations' },
            { value: 'Port Hope', label: 'Port Hope' },
            { value: 'Cobourg', label: 'Cobourg' }
        ];
    }
}

// Refactoring Complete: ReportsManager reduced from 132 lines to ~130 lines
// Eliminates duplication of:
// - User context and timestamp handling (now uses BaseManager)
// - Error handling patterns (now uses executeWithErrorHandling)
// - Constructor dependency injection (now uses BaseManager)

export default ReportsManager;