// Release Conditions Event Modal
// Handles release orders with condition pack picker and overrides

import { addEventTemplates } from '../../../templates/add-event-templates.js';

export class ReleaseConditionsModal {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.episodeId = null;
        this.conditionPacks = [];
        this.selectedPack = null;
        this.packItems = [];
        this.overrides = {};
    }
    
    async show(episodeId, options = {}) {
        this.episodeId = episodeId;
        
        try {
            // Load condition packs
            this.conditionPacks = await this.justice.api.listConditionPacks();
            
            // Pre-select pack if specified
            if (options.packName) {
                this.selectedPack = this.conditionPacks.find(p => 
                    p.name.toLowerCase().includes(options.packName.toLowerCase())
                );
            }
            
            // Create modal
            this.modal = this.justice.ui.createModal({
                id: 'add-release-conditions-modal',
                title: 'Release Order with Conditions',
                size: 'large'
            });
            
            // Render form
            this.render(options);
            
            // Show modal
            this.justice.ui.showModal(this.modal);
            
            // Setup event handlers
            this.setupEventHandlers();
            
            // Load pack items if pack is pre-selected
            if (this.selectedPack) {
                await this.loadPackItems(this.selectedPack.id);
            }
            
        } catch (error) {
            console.error('Failed to show release conditions modal:', error);
            this.justice.ui.showDialog('Error', `Failed to open release conditions form: ${error.message}`, 'error');
        }
    }
    
    render(options = {}) {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = addEventTemplates.releaseConditionsForm({
            episodeId: this.episodeId,
            conditionPacks: this.conditionPacks,
            selectedPack: this.selectedPack,
            packItems: this.packItems,
            overrides: this.overrides,
            defaultDateTime: new Date().toISOString().slice(0, 16),
            replace: options.replace || false
        });
        
        this.updatePackItemsDisplay();
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#release-conditions-form');
        const packSelect = this.modal.querySelector('#condition-pack-select');
        
        // Pack selection change
        if (packSelect) {
            packSelect.addEventListener('change', async (e) => {
                const packId = e.target.value;
                if (packId) {
                    this.selectedPack = this.conditionPacks.find(p => p.id === packId);
                    await this.loadPackItems(packId);
                } else {
                    this.selectedPack = null;
                    this.packItems = [];
                    this.overrides = {};
                    this.updatePackItemsDisplay();
                }
            });
        }
        
        // Override input changes (delegated)
        this.modal.addEventListener('input', (e) => {
            if (e.target.classList.contains('override-input')) {
                const itemId = e.target.dataset.itemId;
                const field = e.target.dataset.field;
                const value = e.target.value;
                
                if (!this.overrides[itemId]) {
                    this.overrides[itemId] = {};
                }
                this.overrides[itemId][field] = value;
            }
        });
        
        // Save button
        const saveBtn = this.modal.querySelector('#save-release-conditions-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveReleaseConditions(form);
            });
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-release-conditions-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }
    
    async loadPackItems(packId) {
        try {
            this.packItems = await this.justice.api.listPackItems(packId);
            this.overrides = {}; // Reset overrides when pack changes
            this.updatePackItemsDisplay();
        } catch (error) {
            console.error('Failed to load pack items:', error);
            this.justice.ui.showDialog('Error', `Failed to load condition pack items: ${error.message}`, 'error');
        }
    }
    
    updatePackItemsDisplay() {
        const container = this.modal.querySelector('#pack-items-container');
        if (!container) return;
        
        if (this.packItems.length === 0) {
            container.innerHTML = '<p>Select a condition pack to see available conditions.</p>';
            return;
        }
        
        container.innerHTML = addEventTemplates.packItemsList({
            packItems: this.packItems,
            overrides: this.overrides
        });
    }
    
    async saveReleaseConditions(form) {
        try {
            const formData = new FormData(form);
            
            const eventData = {
                event_dt: formData.get('release_dt'),
                payload: {
                    pack_id: this.selectedPack?.id || null,
                    replace: formData.get('replace') === 'on',
                    start_dt: formData.get('start_dt') || formData.get('release_dt'),
                    end_dt: formData.get('end_dt') || null,
                    overrides: this.overrides,
                    conditions: this.buildConditionsFromPack()
                }
            };
            
            // Validate required fields
            if (!eventData.event_dt) {
                this.justice.ui.showDialog('Error', 'Release date/time is required.', 'error');
                return;
            }
            
            // Show loading state
            const saveBtn = this.modal.querySelector('#save-release-conditions-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
            
            // Determine event type based on context
            const eventType = this.determineEventType(eventData.payload);
            
            // Create the event
            const newEvent = await this.justice.api.addEvent(
                this.episodeId,
                eventType,
                eventData.event_dt,
                eventData.payload
            );
            
            // Update state
            this.justice.state.addEvent(newEvent);
            
            // Close modal
            this.close();
            
            // Show success
            this.justice.ui.showDialog('Success', 'Release conditions added successfully!', 'success');
            
            // Refresh views
            await this.refreshViews();
            
        } catch (error) {
            console.error('Failed to save release conditions:', error);
            this.justice.ui.showDialog('Error', `Failed to save release conditions: ${error.message}`, 'error');
            
            // Reset button
            const saveBtn = this.modal.querySelector('#save-release-conditions-btn');
            if (saveBtn) {
                saveBtn.textContent = 'Save Release Conditions';
                saveBtn.disabled = false;
            }
        }
    }
    
    buildConditionsFromPack() {
        if (!this.selectedPack || this.packItems.length === 0) {
            return [];
        }
        
        return this.packItems.map(item => {
            const override = this.overrides[item.id] || {};
            const details = { ...item.default_details, ...override };
            
            return {
                type: item.type,
                label: item.label,
                details: details,
                required: item.required
            };
        });
    }
    
    determineEventType(payload) {
        // Determine the appropriate event type based on the payload
        if (payload.pack_id) {
            const pack = this.conditionPacks.find(p => p.id === payload.pack_id);
            if (pack) {
                if (pack.scope === 'RELEASE') return 'RELEASE_ORDER';
                if (pack.scope === 'PROBATION') return 'PROBATION_ORDER';
            }
        }
        
        // Default to CONDITIONS_SET
        return 'CONDITIONS_SET';
    }
    
    async refreshViews() {
        // Refresh timeline if visible
        if (this.justice.timelineView && this.justice.timelineView.container) {
            await this.justice.timelineView.refresh();
        }
        
        // Refresh status ribbon if visible
        if (this.justice.statusRibbon && this.justice.statusRibbon.container) {
            await this.justice.statusRibbon.refresh();
        }
        
        // Refresh conditions panel if visible
        if (this.justice.conditionsPanel && this.justice.conditionsPanel.container) {
            await this.justice.conditionsPanel.refresh();
        }
    }
    
    close() {
        if (this.modal) {
            this.justice.ui.closeModal(this.modal.id);
            this.modal = null;
        }
        this.episodeId = null;
        this.conditionPacks = [];
        this.selectedPack = null;
        this.packItems = [];
        this.overrides = {};
    }
}
