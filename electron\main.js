import { app, BrowserWindow, ipcMain, dialog, session } from 'electron';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';
import { UpdateManager } from './updater.js';
import { SecureSettingsManager } from './settings.js';
import { NativeLocationService } from './native-location.js';
import { GPSService } from './gps-service.js';
import Database from 'better-sqlite3';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Keep a global reference of the window object
let mainWindow;

// Initialize update manager and settings
let updateManager;
let settingsManager;
let nativeLocationService;
let gpsService;
let sqliteDB;

// Enable live reload for development
const isDev = process.argv.includes('--dev');

// Add minimal command line switches for geolocation (more secure approach)
app.commandLine.appendSwitch('disable-features', 'VizDisplayCompositor');
// Only disable user gesture requirement for geolocation, not all web security
app.commandLine.appendSwitch('disable-features', 'UserActivationV2,UserMediaPermissionRequiresGesture');
app.commandLine.appendSwitch('autoplay-policy', 'no-user-gesture-required');
// Allow geolocation without gesture but maintain other security
app.commandLine.appendSwitch('enable-features', 'OverrideGeolocationPermissionRequiresGesture');

// User data directories
let userDataPaths = {};

// Geolocation permissions setup
function setupGeolocationPermissions() {
  // Grant geolocation permissions
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback, details) => {
    console.log(`Permission requested: ${permission}`, details);
    
    // Grant geolocation permissions for the app
    if (permission === 'geolocation') {
      console.log('✅ Granting geolocation permission for native location services');
      callback(true);
    } else if (permission === 'media') {
      // Allow media access which may be needed for location services
      console.log('✅ Granting media permission (may be needed for location)');
      callback(true);
    } else {
      console.log(`⚠️ Permission denied for: ${permission}`);
      callback(false);
    }
  });

  // Also set permission check handler
  session.defaultSession.setPermissionCheckHandler((webContents, permission, requestingOrigin, details) => {
    console.log(`Permission check: ${permission} from ${requestingOrigin}`);
    
    if (permission === 'geolocation') {
      console.log('✅ Geolocation permission check passed - using native location services');
      return true;
    }
    
    if (permission === 'media') {
      console.log('✅ Media permission check passed - may be needed for location');
      return true;
    }
    
    return false;
  });
  
  console.log('🌍 Geolocation permissions configured for native OS location services');
}

// Azure configuration setup
async function setupAzureConfigIfNeeded() {
  try {
    if (!settingsManager.isAzureConfigured()) {
      console.log('Setting up Azure configuration...');

      // Azure configuration from setup
      const azureConfig = {
        tenantId: '760c5d63-f924-4812-8337-05f4efae48ce',
        clientId: '212c48b0-40d2-448e-8fea-e65c4dd55a32',
        clientSecret: '****************************************',
        storageAccountName: 'stevistorage',
        containerName: 'incident-attachments',
        blobEndpoint: 'https://stevistorage.blob.core.windows.net/'
      };

      await settingsManager.setAzureConfig(azureConfig);
      console.log('✅ Azure configuration set up successfully');
    } else {
      console.log('Azure configuration already exists');
    }
  } catch (error) {
    console.error('Error setting up Azure configuration:', error);
  }
}

// Initialize user data directories
function initializeUserDataDirectories() {
  const userDataDir = app.getPath('userData');

  userDataPaths = {
    userData: userDataDir,
    data: join(userDataDir, 'data'),
    cache: join(userDataDir, 'cache'),
    reports: join(userDataDir, 'reports'),
    templates: join(userDataDir, 'templates'),
    media: join(userDataDir, 'media'),
    config: join(userDataDir, 'config.json')
  };

  // Create directories if they don't exist
  Object.values(userDataPaths).forEach(path => {
    if (path.endsWith('.json')) return; // Skip config file
    if (!existsSync(path)) {
      mkdirSync(path, { recursive: true });
      console.log(`Created directory: ${path}`);
    }
  });

  // Create default config file if it doesn't exist
  if (!existsSync(userDataPaths.config)) {
    const defaultConfig = {
      dataPath: userDataPaths.data,
      cachePath: userDataPaths.cache,
      reportsPath: userDataPaths.reports,
      templatesPath: userDataPaths.templates,
      mediaPath: userDataPaths.media,
      version: app.getVersion(),
      firstRun: true,
      created: new Date().toISOString()
    };

    writeFileSync(userDataPaths.config, JSON.stringify(defaultConfig, null, 2));
    console.log(`Created config file: ${userDataPaths.config}`);
  }

  console.log('User data directories initialized:', userDataPaths);
}

async function createWindow() {
  // Create the browser window with retro terminal styling
  mainWindow = new BrowserWindow({
    width: 1024,
    height: 768,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: false,
      preload: join(__dirname, 'preload.js'),
      // Keep web security enabled - use permission handlers instead
      webSecurity: true,
      // Don't allow insecure content
      allowRunningInsecureContent: false,
      // Enable geolocation API with native OS services - handled via permissions
      geolocation: true,
      // Only enable experimental features if specifically needed
      experimentalFeatures: false,
      // Enable additional features
      backgroundThrottling: false
    },
    // Retro terminal window styling
    backgroundColor: '#000000',
    titleBarStyle: 'default',
    icon: join(__dirname, '../assets/icon.ico'),
    show: false, // Don't show until ready
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true
  });

  // Load the renderer
  const rendererPath = join(__dirname, '../renderer/index.html');
  console.log('🔧 Electron Debug Info:');
  console.log('  __dirname:', __dirname);
  console.log('  process.cwd():', process.cwd());
  console.log('  rendererPath:', rendererPath);

  mainWindow.loadFile(rendererPath);

  // Initialize GPS service immediately after window creation
  console.log('🛰️ Initializing GPS service...');
  try {
    gpsService = new GPSService();
    await initializeGPSService();
    console.log('✅ GPS service initialized during window creation');
  } catch (error) {
    console.error('❌ GPS initialization failed during window creation:', error.message);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', async () => {
    console.log('🔧 Main: ready-to-show event fired');
    mainWindow.maximize();
    mainWindow.show();

    // Focus the window
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Set window title
  mainWindow.setTitle('S.T.E.V.I DOS - IHARC Field Staff Terminal');
}

// Configure Electron for native geolocation
app.commandLine.appendSwitch('enable-blink-features', 'GeolocationAPI');
app.commandLine.appendSwitch('disable-features', 'NetworkLocationProvider,LocationProvider');
app.commandLine.appendSwitch('disable-background-networking');
app.commandLine.appendSwitch('disable-default-apps');
app.commandLine.appendSwitch('disable-extensions');
app.commandLine.appendSwitch('disable-sync');
app.commandLine.appendSwitch('no-pings');
app.commandLine.appendSwitch('use-mock-keychain');
// Force geolocation to use OS services
process.env.GOOGLE_API_KEY = '';
process.env.GOOGLE_DEFAULT_CLIENT_ID = '';
process.env.GOOGLE_DEFAULT_CLIENT_SECRET = '';

// App event handlers
app.whenReady().then(async () => {
  // Initialize secure settings manager first
  settingsManager = new SecureSettingsManager();

  // Set up Azure configuration if not already configured
  await setupAzureConfigIfNeeded();

  // Initialize user data directories
  initializeUserDataDirectories();

  // Set up geolocation permissions
  setupGeolocationPermissions();

  await createWindow();

  // Initialize update manager
  updateManager = new UpdateManager();
  
  // Initialize native location service
  nativeLocationService = new NativeLocationService();

  app.on('activate', async () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      await createWindow();
    }
  });
});

// Centralized cleanup function for all background processes
async function cleanupAllServices() {
  console.log('🧹 Starting comprehensive app cleanup...');

  try {
    // 1. Notify renderer processes to cleanup their resources
    if (mainWindow && !mainWindow.isDestroyed()) {
      console.log('📢 Notifying renderer to cleanup...');
      mainWindow.webContents.send('app-shutdown-cleanup');

      // Give renderer time to cleanup (max 3 seconds)
      await new Promise(resolve => {
        const timeout = setTimeout(resolve, 3000);

        // Listen for cleanup completion from renderer
        const cleanupHandler = () => {
          clearTimeout(timeout);
          resolve();
        };

        ipcMain.once('renderer-cleanup-complete', cleanupHandler);
      });
    }

    // 2. Cleanup GPS Service
    if (gpsService) {
      console.log('🛰️ Cleaning up GPS Service...');
      try {
        await gpsService.disconnect();
        console.log('✅ GPS Service cleanup completed');
      } catch (error) {
        console.error('❌ GPS Service cleanup failed:', error.message);
      }
    }

    // 3. Close SQLite database
    if (sqliteDB) {
      console.log('🗄️ Closing SQLite database...');
      try {
        sqliteDB.close();
        sqliteDB = null;
        console.log('✅ SQLite database closed');
      } catch (error) {
        console.error('❌ SQLite database close failed:', error.message);
      }
    }

    // 4. Cleanup UpdateManager
    if (updateManager) {
      console.log('🔄 Cleaning up UpdateManager...');
      try {
        // Stop periodic cleanup timers
        updateManager.stopPeriodicCleanup();

        // Perform final cleanup of temp files
        await updateManager.cleanup(false);
        console.log('✅ UpdateManager cleanup completed');
      } catch (error) {
        console.error('❌ UpdateManager cleanup failed:', error.message);
      }
    }

    // 5. Cleanup Native Location Service
    if (nativeLocationService) {
      console.log('📍 Cleaning up Native Location Service...');
      try {
        // If there are any cleanup methods, call them here
        console.log('✅ Native Location Service cleanup completed');
      } catch (error) {
        console.error('❌ Native Location Service cleanup failed:', error.message);
      }
    }

    // 6. Cleanup Settings Manager
    if (settingsManager) {
      console.log('⚙️ Cleaning up Settings Manager...');
      try {
        // If there are any cleanup methods, call them here
        console.log('✅ Settings Manager cleanup completed');
      } catch (error) {
        console.error('❌ Settings Manager cleanup failed:', error.message);
      }
    }

    console.log('✅ Comprehensive app cleanup completed successfully');
  } catch (error) {
    console.error('❌ Error during app cleanup:', error.message);
  }
}

// App shutdown event handlers
app.on('before-quit', async (event) => {
  console.log('🚪 App before-quit event triggered');

  // Prevent immediate quit to allow cleanup
  event.preventDefault();

  try {
    // Perform cleanup
    await cleanupAllServices();

    // Now allow the app to quit
    console.log('🚪 Cleanup completed, allowing app to quit');
    app.exit(0);
  } catch (error) {
    console.error('❌ Error during before-quit cleanup:', error.message);
    // Force quit even if cleanup fails
    app.exit(1);
  }
});

app.on('will-quit', async (event) => {
  console.log('🚪 App will-quit event triggered');

  // Additional safety net - ensure cleanup happens
  try {
    await cleanupAllServices();
  } catch (error) {
    console.error('❌ Error during will-quit cleanup:', error.message);
  }
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
  });
});

// IPC handlers for communication with renderer
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('get-user-data-paths', () => {
  return userDataPaths;
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

ipcMain.handle('open-external', async (event, url) => {
  const { shell } = await import('electron');
  await shell.openExternal(url);
});

// GPS Service initialization
async function initializeGPSService() {
  try {
    console.log('🛰️ Initializing GPS Service...');

    const initialized = await gpsService.initialize();
    if (initialized) {
      console.log('✅ GPS Service initialized successfully');

      // Set up GPS event handlers
      gpsService.on('position', (position) => {
        console.log('🛰️ GPS Position update:', position);
        // Broadcast to all renderer windows
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('gps-update', position);
        }
      });

      gpsService.on('connected', () => {
        console.log('🛰️ GPS Connected');
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('gps-status', { connected: true });
          // Notify renderer that GPS API is now available
          mainWindow.webContents.send('gps-available');
        }
      });

      gpsService.on('error', (error) => {
        console.error('🛰️ GPS Error:', error.message);
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('gps-error', { error: error.message });
        }
      });

    } else {
      console.warn('⚠️ GPS Service failed to initialize - continuing without GPS');
    }
  } catch (error) {
    console.error('❌ GPS Service initialization error:', error.message);
  }
}

// GPS IPC handlers
ipcMain.handle('gps-is-connected', () => {
  return gpsService ? gpsService.isGPSConnected() : false;
});

ipcMain.handle('gps-get-status', () => {
  if (!gpsService) {
    return { connected: false, error: 'GPS service not available' };
  }

  return {
    connected: gpsService.isGPSConnected(),
    lastPosition: gpsService.getCurrentPosition()
  };
});

ipcMain.on('gps-request', (event) => {
  if (!gpsService) {
    event.reply('gps-response', { success: false, error: 'GPS service not available' });
    return;
  }

  const position = gpsService.getCurrentPosition();
  if (position) {
    event.reply('gps-response', { success: true, position });
  } else {
    event.reply('gps-response', { success: false, error: 'No GPS position available' });
  }
});

// Native geolocation IPC handlers
ipcMain.handle('get-native-location', async () => {
  try {
    console.log('📍 IPC: Native location requested');
    console.log('📍 IPC: Platform:', process.platform);
    console.log('📍 IPC: Native location service available:', nativeLocationService ? 'Yes' : 'No');

    const location = await nativeLocationService.getCurrentPosition();
    console.log('📍 IPC: Native location result:', location);
    return { success: true, location };
  } catch (error) {
    console.error('📍 IPC: Native location failed with error:', error.message);
    console.error('📍 IPC: Full error object:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-location-setup-instructions', async () => {
  try {
    const instructions = await nativeLocationService.getLocationSetupInstructions();
    return instructions;
  } catch (error) {
    console.error('Failed to get location setup instructions:', error);
    return "Please enable Windows Location Services in Settings > Privacy & Security > Location";
  }
});

// Direct location service that uses a simpler approach
ipcMain.handle('get-direct-location', async () => {
  try {
    console.log('📍 IPC: Direct location requested');
    
    // Try a simple PowerShell command to get location
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);
    
    console.log('📍 IPC: Trying simple PowerShell location command...');
    
    const simpleScript = `
      try {
        Add-Type -AssemblyName System.Device
        $watcher = New-Object System.Device.Location.GeoCoordinateWatcher
        $watcher.Start()
        Start-Sleep -Seconds 5
        if ($watcher.Status -eq 'Ready' -and !$watcher.Position.Location.IsUnknown) {
          $coord = $watcher.Position.Location
          Write-Output "$($coord.Latitude),$($coord.Longitude)"
        } else {
          Write-Output "FAILED"
        }
        $watcher.Stop()
      } catch {
        Write-Output "ERROR"
      }
    `;
    
    const { stdout } = await execAsync(`powershell -ExecutionPolicy Bypass -Command "${simpleScript}"`, { timeout: 10000 });
    console.log('📍 IPC: Simple PowerShell result:', stdout.trim());
    
    if (stdout.trim() && !stdout.includes('FAILED') && !stdout.includes('ERROR')) {
      const [lat, lng] = stdout.trim().split(',');
      if (lat && lng && !isNaN(parseFloat(lat)) && !isNaN(parseFloat(lng))) {
        const location = {
          latitude: parseFloat(lat),
          longitude: parseFloat(lng),
          accuracy: 0
        };
        console.log('📍 IPC: Direct location success:', location);
        return { success: true, location };
      }
    }
    
    console.log('📍 IPC: Direct location failed - no valid coordinates');
    return { success: false, error: 'No location data available from Windows Location Services' };
    
  } catch (error) {
    console.error('📍 IPC: Direct location failed with error:', error.message);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('check-location-service', async () => {
  try {
    const isEnabled = await nativeLocationService.isLocationServiceEnabled();
    return { success: true, enabled: isEnabled };
  } catch (error) {
    console.error('📍 IPC: Location service check failed:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('show-error-dialog', async (event, title, content) => {
  const result = await dialog.showErrorBox(title, content);
  return result;
});

ipcMain.handle('show-message-dialog', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

// Handle app closing
ipcMain.handle('quit-app', () => {
  app.quit();
});

// Handle renderer cleanup completion signal
ipcMain.handle('renderer-cleanup-complete', () => {
  console.log('✅ Renderer cleanup completed');
  // This is handled by the promise in cleanupAllServices
});

// Azure configuration handler - securely provides Azure credentials
ipcMain.handle('get-azure-config', async () => {
  try {
    // Get Azure configuration from secure settings
    const azureConfig = await settingsManager.getAzureConfig();

    if (!azureConfig) {
      throw new Error('Azure configuration not found');
    }

    return azureConfig;
  } catch (error) {
    console.error('Error getting Azure configuration:', error);
    throw error;
  }
});

// File upload handler - handles Azure blob storage uploads from main process
ipcMain.handle('upload-files-to-azure', async (event, files, incidentId) => {
  try {
    // Import Azure modules in main process
    const { BlobServiceClient } = await import('@azure/storage-blob');
    const { ClientSecretCredential } = await import('@azure/identity');

    // Get Azure configuration
    const azureConfig = await settingsManager.getAzureConfig();
    if (!azureConfig) {
      throw new Error('Azure configuration not found');
    }

    // Create credential and blob service client
    const credential = new ClientSecretCredential(
      azureConfig.tenantId,
      azureConfig.clientId,
      azureConfig.clientSecret
    );

    const blobServiceClient = new BlobServiceClient(
      azureConfig.blobEndpoint,
      credential
    );

    const containerClient = blobServiceClient.getContainerClient(azureConfig.containerName);
    
    // Verify container exists and we have access
    try {
      await containerClient.getProperties();
      console.log(`✅ Container '${azureConfig.containerName}' is accessible`);
    } catch (error) {
      console.error(`❌ Container '${azureConfig.containerName}' access failed:`, {
        message: error.message,
        code: error.code,
        statusCode: error.statusCode
      });
      throw new Error(`Cannot access Azure container '${azureConfig.containerName}': ${error.message}`);
    }

    // Upload files
    const uploadResults = {
      successful: [],
      failed: [],
      totalUploaded: 0,
      totalFailed: 0
    };

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      try {
        const fileName = `${incidentId}/${Date.now()}_${file.name}`;
        const blockBlobClient = containerClient.getBlockBlobClient(fileName);

        // Convert file data to buffer
        const buffer = Buffer.from(file.data);

        // Upload with metadata
        const uploadResponse = await blockBlobClient.upload(buffer, buffer.length, {
          blobHTTPHeaders: {
            blobContentType: file.type
          },
          metadata: {
            incidentId: incidentId.toString(),
            originalName: file.name,
            uploadedAt: new Date().toISOString()
          }
        });

        uploadResults.successful.push({
          fileName: file.name,
          url: blockBlobClient.url,
          fileSize: file.size,
          contentType: file.type,
          uploadedAt: new Date().toISOString()
        });
        uploadResults.totalUploaded++;

        // Send progress update
        event.sender.send('upload-progress', {
          fileIndex: i,
          percentage: Math.round(((i + 1) / files.length) * 100)
        });

      } catch (error) {
        console.error(`Failed to upload file ${file.name}:`, {
          message: error.message,
          code: error.code,
          statusCode: error.statusCode,
          details: error.details || error.response?.data
        });
        uploadResults.failed.push({
          fileName: file.name,
          error: error.message,
          code: error.code,
          statusCode: error.statusCode
        });
        uploadResults.totalFailed++;
      }
    }

    return uploadResults;

  } catch (error) {
    console.error('Error uploading files to Azure:', error);
    throw error;
  }
});

// Update-related IPC handlers
ipcMain.handle('check-for-updates', async () => {
  try {
    if (!updateManager) {
      throw new Error('Update manager not initialized');
    }
    return await updateManager.checkForUpdates();
  } catch (error) {
    console.error('Error checking for updates:', error);
    throw error;
  }
});

ipcMain.handle('download-update', async (event, downloadUrl, expectedChecksum) => {
  try {
    if (!updateManager) {
      throw new Error('Update manager not initialized');
    }

    return await updateManager.downloadUpdate(downloadUrl, (progress) => {
      // Send progress updates to renderer
      mainWindow?.webContents.send('update-progress', progress);
    }, expectedChecksum);
  } catch (error) {
    console.error('Error downloading update:', error);
    throw error;
  }
});

// Get app version
ipcMain.handle('get-app-version', async () => {
  return app.getVersion();
});

ipcMain.handle('install-update', async (event, installerPath) => {
  try {
    if (!updateManager) {
      throw new Error('Update manager not initialized');
    }

    await updateManager.installUpdate(installerPath);
  } catch (error) {
    console.error('Error installing update:', error);
    throw error;
  }
});

ipcMain.handle('get-update-progress', () => {
  if (!updateManager) {
    return 0;
  }
  return updateManager.getProgress();
});

ipcMain.handle('is-update-in-progress', () => {
  if (!updateManager) {
    return false;
  }
  return updateManager.isUpdateInProgress();
});

ipcMain.handle('open-releases-page', () => {
  if (updateManager) {
    updateManager.openReleasesPage();
  }
});

ipcMain.handle('cleanup-updates', async () => {
  if (updateManager) {
    await updateManager.cleanup();
  }
});

ipcMain.handle('get-update-storage-usage', async () => {
  if (updateManager) {
    return await updateManager.getStorageUsage();
  }
  return { totalFiles: 0, totalSize: 0, formattedSize: '0 B', files: [] };
});

ipcMain.handle('is-development-environment', () => {
  if (!updateManager) {
    return false;
  }
  return updateManager.isDevelopmentEnvironment();
});

ipcMain.handle('are-updates-allowed', () => {
  if (!updateManager) {
    return false;
  }
  return updateManager.areUpdatesAllowed();
});

ipcMain.handle('set-development-mode', (event, isDev) => {
  if (updateManager) {
    updateManager.setDevelopmentMode(isDev);
  }
});

// SQLite IPC handlers
ipcMain.handle('sqlite-initialize', async (event, dbPath) => {
  try {
    console.log('Initializing SQLite database at:', dbPath);
    sqliteDB = new Database(dbPath);
    sqliteDB.pragma('journal_mode = WAL');
    console.log('✅ SQLite database initialized successfully');
    return { success: true };
  } catch (error) {
    console.error('❌ SQLite initialization failed:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sqlite-run', async (event, sql, params) => {
  try {
    if (!sqliteDB) {
      throw new Error('SQLite database not initialized');
    }
    const result = sqliteDB.prepare(sql).run(params || []);
    return { success: true, result };
  } catch (error) {
    console.error('SQLite run error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sqlite-get', async (event, sql, params) => {
  try {
    if (!sqliteDB) {
      throw new Error('SQLite database not initialized');
    }
    const result = sqliteDB.prepare(sql).get(params || []);
    return { success: true, result };
  } catch (error) {
    console.error('SQLite get error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sqlite-all', async (event, sql, params) => {
  try {
    if (!sqliteDB) {
      throw new Error('SQLite database not initialized');
    }
    const result = sqliteDB.prepare(sql).all(params || []);
    return { success: true, result };
  } catch (error) {
    console.error('SQLite all error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sqlite-prepare', async (event, sql) => {
  try {
    if (!sqliteDB) {
      throw new Error('SQLite database not initialized');
    }
    // Return statement info rather than the actual statement object
    const stmt = sqliteDB.prepare(sql);
    return { 
      success: true, 
      result: {
        columnNames: stmt.columns().map(col => col.name),
        parameterCount: stmt.reader ? stmt.reader.length : 0
      }
    };
  } catch (error) {
    console.error('SQLite prepare error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sqlite-close', async (event) => {
  try {
    if (sqliteDB) {
      sqliteDB.close();
      sqliteDB = null;
      console.log('✅ SQLite database closed');
    }
    return { success: true };
  } catch (error) {
    console.error('SQLite close error:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('sqlite-exists', async (event, dbPath) => {
  try {
    return { success: true, exists: existsSync(dbPath) };
  } catch (error) {
    console.error('SQLite exists check error:', error);
    return { success: false, error: error.message };
  }
});

// Settings-related IPC handlers
ipcMain.handle('get-setting', (event, key, defaultValue) => {
  if (!settingsManager) {
    return defaultValue;
  }
  return settingsManager.get(key, defaultValue);
});

ipcMain.handle('set-setting', (event, key, value) => {
  if (settingsManager) {
    settingsManager.set(key, value);
    return true;
  }
  return false;
});

ipcMain.handle('get-all-settings', () => {
  if (!settingsManager) {
    return {};
  }
  return settingsManager.getAll();
});

ipcMain.handle('reset-settings', () => {
  if (settingsManager) {
    settingsManager.reset();
    return true;
  }
  return false;
});

ipcMain.handle('export-settings', () => {
  if (!settingsManager) {
    return null;
  }
  return settingsManager.export();
});

ipcMain.handle('import-settings', (event, jsonData) => {
  if (!settingsManager) {
    return false;
  }
  return settingsManager.import(jsonData);
});

ipcMain.handle('get-app-paths', () => {
  if (!settingsManager) {
    return {};
  }
  return settingsManager.getPaths();
});

// Secure configuration handler - provides app configuration without exposing sensitive data
ipcMain.handle('get-config', () => {
  if (!settingsManager) {
    return null;
  }

  // Return only necessary configuration for renderer process
  return {
    supabase: {
      url: settingsManager.get('supabase.url'),
      anonKey: settingsManager.get('supabase.anonKey')
    },
    app: {
      debug: isDev || settingsManager.get('app.debug', false),
      version: app.getVersion()
    }
  };
});

// Development helpers
if (isDev) {
  // Enable live reload
  try {
    require('electron-reload')(__dirname, {
      electron: require('electron'),
      hardResetMethod: 'exit'
    });
  } catch (e) {
    console.log('electron-reload not available');
  }
}
