// Secure Configuration Manager for S.T.E.V.I DOS Electron App
// Handles secure loading of configuration from main process and Supabase Vault

import { VaultManager } from './vault-manager.js';
import { supabaseConfig } from './supabase-config.js';

export class SecureConfigManager {
    constructor() {
        this.config = {
            supabase: {
                url: null,
                anonKey: null
            },
            app: {
                name: 'S.T.E.V.I DOS',
                version: '1.0.0',
                debug: false
            },
            cache: {
                ttl: 3600, // 1 hour
                maxSize: 1000
            },
            paths: {
                data: './data',
                templates: './templates',
                reports: './reports',
                media: './media'
            }
        };

        // VaultManager for third-party API keys
        this.vaultManager = null;
        this.supabaseClient = null;
        this.configLoaded = false;
        this.loadPromise = null;
    }

    /**
     * Initialize secure configuration by loading from main process
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.loadPromise) {
            return this.loadPromise;
        }

        this.loadPromise = this._loadSecureConfig();
        return this.loadPromise;
    }

    async _loadSecureConfig() {
        try {
            console.log('Loading secure configuration...');

            // Load configuration from main process via IPC
            if (window.electronAPI && window.electronAPI.getConfig) {
                const mainConfig = await window.electronAPI.getConfig();

                if (mainConfig) {
                    // Merge configuration from main process
                    this.config.supabase = {
                        url: mainConfig.supabase?.url,
                        anonKey: mainConfig.supabase?.anonKey
                    };

                    this.config.app.debug = mainConfig.app?.debug || false;

                    console.log('✅ Configuration loaded from main process');
                } else {
                    console.warn('⚠️ No configuration received from main process, using fallbacks');
                }

                // Load proper paths from main process
                if (window.electronAPI.getAppPaths) {
                    try {
                        const paths = await window.electronAPI.getAppPaths();
                        if (paths && paths.data) {
                            this.config.paths = {
                                data: paths.data,
                                templates: paths.templates || `${paths.data}/templates`,
                                reports: paths.reports || `${paths.data}/reports`,
                                media: paths.media || `${paths.data}/media`
                            };
                            console.log('✅ Paths loaded from main process:', this.config.paths);
                        }
                    } catch (error) {
                        console.warn('⚠️ Failed to load paths from main process:', error);
                    }
                }
            } else {
                console.warn('⚠️ Electron API not available, using fallback configuration');
                // Fallback for development/testing - uses centralized config
                this.config.supabase = {
                    url: supabaseConfig.getUrl(),
                    anonKey: supabaseConfig.getAnonKey()
                };
            }

            // Validate required configuration
            if (!this.config.supabase.url || !this.config.supabase.anonKey) {
                throw new Error('Missing required Supabase configuration');
            }

            this.configLoaded = true;
            console.log('✅ Secure configuration initialized successfully');

        } catch (error) {
            console.error('❌ Failed to load secure configuration:', error);
            throw error;
        }
    }

    /**
     * Initialize VaultManager with Supabase client for API keys
     * @param {Object} supabaseClient - The Supabase client instance
     */
    initializeVault(supabaseClient) {
        this.supabaseClient = supabaseClient;
        this.vaultManager = new VaultManager(supabaseClient);
        console.log('✅ VaultManager initialized for secure API key management');
    }

    /**
     * Get configuration value by key path
     * @param {string} key - Dot-separated key path (e.g., 'supabase.url')
     * @returns {any} Configuration value
     */
    get(key) {
        if (!this.configLoaded) {
            console.warn('Configuration not loaded yet, call initialize() first');
            return undefined;
        }

        const keys = key.split('.');
        let value = this.config;

        for (const k of keys) {
            if (value && typeof value === 'object' && k in value) {
                value = value[k];
            } else {
                return undefined;
            }
        }

        return value;
    }

    /**
     * Set configuration value (for runtime updates only)
     * @param {string} key - Dot-separated key path
     * @param {any} value - Value to set
     */
    set(key, value) {
        const keys = key.split('.');
        let target = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const k = keys[i];
            if (!(k in target) || typeof target[k] !== 'object') {
                target[k] = {};
            }
            target = target[k];
        }

        target[keys[keys.length - 1]] = value;
    }

    // Supabase configuration getters
    getSupabaseUrl() {
        return this.get('supabase.url');
    }

    getSupabaseAnonKey() {
        return this.get('supabase.anonKey');
    }

    // API key methods using Vault
    async getGoogleApiKey() {
        if (this.vaultManager) {
            try {
                const apiKey = await this.vaultManager.getSecret('google_api_key');
                if (apiKey) {
                    return apiKey;
                }
            } catch (error) {
                console.error('Error retrieving Google API key from Vault:', error);
            }
        }

        console.warn('Google API key not available from Vault');
        return null;
    }

    // App configuration getters
    isDebugMode() {
        return this.get('app.debug');
    }

    getCacheTTL() {
        return this.get('cache.ttl');
    }

    // Path getters
    getDataPath() {
        return this.config?.paths?.data || localStorage.getItem('stevidos_data_path') || './data';
    }

    getTemplatesPath() {
        return this.config?.paths?.templates || localStorage.getItem('stevidos_templates_path') || './templates';
    }

    getReportsPath() {
        return this.config?.paths?.reports || localStorage.getItem('stevidos_reports_path') || './reports';
    }

    getMediaPath() {
        return this.config?.paths?.media || localStorage.getItem('stevidos_media_path') || './media';
    }

    // Test mode detection (should be disabled in production)
    isTestMode() {
        // Check environment variable or debug flag
        return this.isDebugMode() && (
            process.env.STEVIDOS_TEST_MODE === 'true' ||
            localStorage.getItem('stevidos_test_mode') === 'true'
        );
    }

    /**
     * Validate configuration completeness and security
     * @returns {Object} Validation result
     */
    validateConfiguration() {
        const issues = [];
        const warnings = [];
        const securityChecks = [];

        // Check required Supabase configuration
        if (!this.getSupabaseUrl()) {
            issues.push('Missing Supabase URL');
        } else {
            // Validate URL format
            try {
                const url = new URL(this.getSupabaseUrl());
                if (!url.hostname.includes('supabase.co')) {
                    warnings.push('Supabase URL does not appear to be official Supabase domain');
                }
                securityChecks.push('✅ Supabase URL format valid');
            } catch (error) {
                issues.push('Invalid Supabase URL format');
            }
        }

        if (!this.getSupabaseAnonKey()) {
            issues.push('Missing Supabase anonymous key');
        } else {
            // Validate JWT format
            const key = this.getSupabaseAnonKey();
            if (!key.startsWith('eyJ')) {
                issues.push('Supabase anonymous key does not appear to be valid JWT');
            } else {
                securityChecks.push('✅ Supabase anonymous key format valid');
            }
        }

        // Check for test mode in production
        if (this.isTestMode() && !this.isDebugMode()) {
            warnings.push('Test mode enabled in production environment');
        }

        // Security validations
        if (this.isDebugMode()) {
            warnings.push('Debug mode enabled - ensure this is intentional');
        } else {
            securityChecks.push('✅ Production mode active');
        }

        // Check if running in secure context (HTTPS or localhost)
        if (typeof window !== 'undefined') {
            if (window.location.protocol === 'https:' || window.location.hostname === 'localhost') {
                securityChecks.push('✅ Secure context detected');
            } else {
                warnings.push('Application not running in secure context (HTTPS)');
            }
        }

        return {
            valid: issues.length === 0,
            issues,
            warnings,
            securityChecks
        };
    }
}
