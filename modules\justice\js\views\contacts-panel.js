// Contacts Panel View
// Displays and manages justice-related contacts (lawyer, probation, surety)

import { contactsTemplates } from '../../templates/contacts-templates.js';

export class ContactsPanel {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.container = null;
        this.episode = null;
        this.contacts = [];
    }
    
    async mount(container, episode) {
        this.container = container;
        this.episode = episode;
        
        try {
            // Load contacts for the episode
            this.contacts = await this.justice.api.getEpisodeContacts(episode.id);
            
            // Update state
            this.justice.state.setContacts(this.contacts);
            
            // Render contacts panel
            this.render();
            
        } catch (error) {
            console.error('Failed to mount contacts panel:', error);
            this.renderError(error.message);
        }
    }
    
    render() {
        if (!this.container) return;
        
        this.container.innerHTML = contactsTemplates.contactsPanel({
            episode: this.episode,
            contacts: this.contacts
        });
        
        this.setupEventHandlers();
    }
    
    renderError(message) {
        if (!this.container) return;
        
        this.container.innerHTML = contactsTemplates.errorView(message);
    }
    
    setupEventHandlers() {
        if (!this.container) return;
        
        // Add contact buttons for each role
        const addContactBtns = this.container.querySelectorAll('.add-contact-btn');
        addContactBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const role = e.target.dataset.role;
                this.showAddContactModal(role);
            });
        });
        
        // Edit contact buttons
        const editContactBtns = this.container.querySelectorAll('.edit-contact-btn');
        editContactBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const contactId = e.target.dataset.contactId;
                this.showEditContactModal(contactId);
            });
        });
        
        // Deactivate contact buttons
        const deactivateBtns = this.container.querySelectorAll('.deactivate-contact-btn');
        deactivateBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const contactId = e.target.dataset.contactId;
                this.showDeactivateContactModal(contactId);
            });
        });
        
        // Contact detail toggles
        const contactItems = this.container.querySelectorAll('.contact-item');
        contactItems.forEach(item => {
            const header = item.querySelector('.contact-header');
            if (header) {
                header.addEventListener('click', () => {
                    this.toggleContactDetails(item);
                });
            }
        });
        
        // Refresh button
        const refreshBtn = this.container.querySelector('[data-action="refresh-contacts"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.refresh();
            });
        }
    }
    
    toggleContactDetails(contactItem) {
        const details = contactItem.querySelector('.contact-details');
        const toggle = contactItem.querySelector('.contact-toggle');
        
        if (details && toggle) {
            const isExpanded = details.style.display !== 'none';
            details.style.display = isExpanded ? 'none' : 'block';
            toggle.textContent = isExpanded ? '▶' : '▼';
        }
    }
    
    showAddContactModal(role) {
        const modal = this.justice.ui.createModal({
            id: 'add-contact-modal',
            title: `Add ${this.formatRole(role)}`,
            size: 'medium'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = contactsTemplates.addContactForm(role);
        
        this.justice.ui.showModal(modal);
        
        // Setup form handlers
        const form = modal.querySelector('#add-contact-form');
        const saveBtn = modal.querySelector('#save-contact-btn');
        const cancelBtn = modal.querySelector('#cancel-contact-btn');
        
        saveBtn.addEventListener('click', async () => {
            await this.saveNewContact(form, modal, role);
        });
        
        cancelBtn.addEventListener('click', () => {
            this.justice.ui.closeModal(modal.id);
        });
    }
    
    async saveNewContact(form, modal, role) {
        try {
            const formData = new FormData(form);
            const contactData = {
                person_id: this.justice.state.getCurrentPerson().id,
                je_id: this.episode.id,
                role: role,
                name: formData.get('name'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                org: formData.get('org'),
                notes: formData.get('notes'),
                started_at: formData.get('started_at') || new Date().toISOString()
            };
            
            // Validate required fields
            if (!contactData.name) {
                this.justice.ui.showDialog('Error', 'Contact name is required.', 'error');
                return;
            }
            
            // Create contact
            const newContact = await this.justice.api.addContact(contactData);
            
            // Update local state
            this.contacts.push(newContact);
            this.justice.state.setContacts(this.contacts);
            
            // Re-render
            this.render();
            
            // Close modal
            this.justice.ui.closeModal(modal.id);
            
            this.justice.ui.showDialog('Success', 'Contact added successfully.', 'success');
            
        } catch (error) {
            console.error('Failed to add contact:', error);
            this.justice.ui.showDialog('Error', `Failed to add contact: ${error.message}`, 'error');
        }
    }
    
    showEditContactModal(contactId) {
        const contact = this.contacts.find(c => c.id === contactId);
        if (!contact) return;
        
        const modal = this.justice.ui.createModal({
            id: 'edit-contact-modal',
            title: `Edit ${this.formatRole(contact.role)}`,
            size: 'medium'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = contactsTemplates.editContactForm(contact);
        
        this.justice.ui.showModal(modal);
        
        // Setup form handlers
        const form = modal.querySelector('#edit-contact-form');
        const saveBtn = modal.querySelector('#save-contact-btn');
        const cancelBtn = modal.querySelector('#cancel-contact-btn');
        
        saveBtn.addEventListener('click', async () => {
            await this.saveContactEdit(form, modal, contactId);
        });
        
        cancelBtn.addEventListener('click', () => {
            this.justice.ui.closeModal(modal.id);
        });
    }
    
    async saveContactEdit(form, modal, contactId) {
        try {
            const formData = new FormData(form);
            const updates = {
                name: formData.get('name'),
                phone: formData.get('phone'),
                email: formData.get('email'),
                org: formData.get('org'),
                notes: formData.get('notes')
            };
            
            // Note: In a real implementation, you'd need an API method to update contacts
            // For now, we'll just update locally
            const index = this.contacts.findIndex(c => c.id === contactId);
            if (index !== -1) {
                this.contacts[index] = { ...this.contacts[index], ...updates };
                this.justice.state.updateContact(contactId, updates);
            }
            
            // Re-render
            this.render();
            
            // Close modal
            this.justice.ui.closeModal(modal.id);
            
            this.justice.ui.showDialog('Success', 'Contact updated successfully.', 'success');
            
        } catch (error) {
            console.error('Failed to edit contact:', error);
            this.justice.ui.showDialog('Error', `Failed to edit contact: ${error.message}`, 'error');
        }
    }
    
    showDeactivateContactModal(contactId) {
        const contact = this.contacts.find(c => c.id === contactId);
        if (!contact) return;
        
        const modal = this.justice.ui.createModal({
            id: 'deactivate-contact-modal',
            title: 'Deactivate Contact',
            size: 'small'
        });
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.innerHTML = contactsTemplates.deactivateContactModal(contact);
        
        this.justice.ui.showModal(modal);
        
        // Setup handlers
        const deactivateBtn = modal.querySelector('#deactivate-contact-btn');
        const cancelBtn = modal.querySelector('#cancel-deactivate-btn');
        
        deactivateBtn.addEventListener('click', async () => {
            await this.deactivateContact(contactId, modal);
        });
        
        cancelBtn.addEventListener('click', () => {
            this.justice.ui.closeModal(modal.id);
        });
    }
    
    async deactivateContact(contactId, modal) {
        try {
            // Update contact to inactive
            const index = this.contacts.findIndex(c => c.id === contactId);
            if (index !== -1) {
                this.contacts[index].active = false;
                this.contacts[index].ended_at = new Date().toISOString();
                this.justice.state.updateContact(contactId, {
                    active: false,
                    ended_at: this.contacts[index].ended_at
                });
            }
            
            // Re-render
            this.render();
            
            // Close modal
            this.justice.ui.closeModal(modal.id);
            
            this.justice.ui.showDialog('Success', 'Contact deactivated successfully.', 'success');
            
        } catch (error) {
            console.error('Failed to deactivate contact:', error);
            this.justice.ui.showDialog('Error', `Failed to deactivate contact: ${error.message}`, 'error');
        }
    }
    
    async refresh() {
        if (!this.episode) return;
        
        try {
            // Reload contacts
            this.contacts = await this.justice.api.getEpisodeContacts(this.episode.id);
            this.justice.state.setContacts(this.contacts);
            
            // Re-render
            this.render();
            
        } catch (error) {
            console.error('Failed to refresh contacts:', error);
            this.justice.ui.showDialog('Error', `Failed to refresh contacts: ${error.message}`, 'error');
        }
    }
    
    formatRole(role) {
        const labels = {
            'LAWYER': 'Lawyer',
            'DUTY_COUNSEL': 'Duty Counsel',
            'PROBATION_OFFICER': 'Probation Officer',
            'SURETY': 'Surety',
            'BAIL_SUPERVISOR': 'Bail Supervisor',
            'CASE_WORKER': 'Case Worker',
            'OTHER': 'Other Contact'
        };
        
        return labels[role] || role;
    }
    
    getRoleIcon(role) {
        const icons = {
            'LAWYER': '⚖️',
            'DUTY_COUNSEL': '📋',
            'PROBATION_OFFICER': '👮',
            'SURETY': '🤝',
            'BAIL_SUPERVISOR': '👁️',
            'CASE_WORKER': '📝',
            'OTHER': '👤'
        };
        
        return icons[role] || '👤';
    }
    
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }
    
    getContactsByRole() {
        const contactsByRole = {};
        const roles = ['LAWYER', 'DUTY_COUNSEL', 'PROBATION_OFFICER', 'SURETY', 'BAIL_SUPERVISOR', 'CASE_WORKER', 'OTHER'];
        
        roles.forEach(role => {
            contactsByRole[role] = this.contacts.filter(c => c.role === role && c.active);
        });
        
        return contactsByRole;
    }
    
    unmount() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.container = null;
        this.episode = null;
        this.contacts = [];
    }
}
