// Justice Commands
// Lightweight commands that delegate to app methods without importing templates

export class JusticeCommands {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.app = null; // Will be set when registered
    }

    /**
     * Register justice commands with the app's command system
     */
    register(app) {
        // Get app reference from parameter or fallback to window.app
        this.app = app || window.app;

        if (!this.app || !this.app.commands) {
            console.warn('App or command system not available for justice commands');
            return;
        }

        // Register justice-specific commands - use modular components
        this.app.commands.commands.set('je:new', {
            execute: async (args) => {
                const personId = args.personId || args[0];
                if (!personId) {
                    this.justice.ui.showDialog('Error', 'Person ID is required to create a new episode.', 'error');
                    return;
                }
                // Use the modular StartEpisodeWizard instead of app.js method
                await this.justice.startEpisodeWizard.show(personId);
            }
        });

        this.app.commands.commands.set('je:delete', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required to delete an episode.', 'error');
                    return;
                }
                await this.app.deleteJusticeEpisode(episodeId);
            }
        });

        this.app.commands.commands.set('je:view', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                if (episodeId) {
                    await this.app.showEpisodeDetail(episodeId);
                } else {
                    // If no episode ID, focus on justice tab for current person
                    const personId = args.personId || args[0];
                    if (personId && this.app.selectedPerson?.id === personId) {
                        const justiceTab = document.querySelector('[data-tab="criminal-justice"]');
                        if (justiceTab) {
                            justiceTab.click();
                        }
                    }
                }
            }
        });

        this.app.commands.commands.set('je:add-bail-hearing', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                const outcome = args.outcome || args[1];
                const notes = args.notes || args[2];

                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required.', 'error');
                    return;
                }

                if (outcome) {
                    // Quick add with provided outcome
                    await this.app.addBailHearingEvent(episodeId, outcome, notes);
                } else {
                    // Open modal for full form
                    await this.app.openAddEventModal('bail-hearing', episodeId);
                }
            }
        });

        this.app.commands.commands.set('je:add-transfer', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                const facilityNameOrId = args.facilityNameOrUUID || args[1];

                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required.', 'error');
                    return;
                }

                if (facilityNameOrId) {
                    // Quick transfer with provided facility
                    await this.app.addTransferEvent(episodeId, facilityNameOrId);
                } else {
                    // Open modal for facility selection
                    await this.app.openAddEventModal('transfer', episodeId);
                }
            }
        });

        this.app.commands.commands.set('je:add-court-appearance', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                const dateTime = args.dateTime || args[1];
                const courtName = args.courtName || args[2];
                const appearanceType = args.appearanceType || args[3];

                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required.', 'error');
                    return;
                }

                if (dateTime && courtName && appearanceType) {
                    // Quick add with provided details
                    await this.app.addCourtAppearanceEvent(episodeId, dateTime, courtName, appearanceType);
                } else {
                    // Open modal for full form
                    await this.app.openAddEventModal('court-appearance', episodeId);
                }
            }
        });

        this.app.commands.commands.set('je:add-release-conditions', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];

                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required.', 'error');
                    return;
                }

                // Open release conditions modal (pack selection handled in modal)
                await this.app.openAddEventModal('release-conditions', episodeId);
            }
        });

        this.app.commands.commands.set('je:add-sentence', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                const mode = args.mode || args[1];

                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required.', 'error');
                    return;
                }

                if (mode) {
                    const lengthDays = args.lengthDays ? parseInt(args.lengthDays) : null;
                    const creditDays = args.creditDays ? parseInt(args.creditDays) : null;

                    await this.app.addSentenceEvent(episodeId, mode, lengthDays, creditDays);
                } else {
                    // Open modal for full form
                    await this.app.openAddEventModal('sentence', episodeId);
                }
            }
        });

        this.app.commands.commands.set('je:add-warrant', {
            execute: async (args) => {
                const episodeId = args.episodeId || args[0];
                const action = args.action || args[1] || 'issue';
                const type = args.type || args[2];

                if (!episodeId) {
                    this.app.ui.showDialog('Error', 'Episode ID is required.', 'error');
                    return;
                }

                if (type) {
                    const notes = args.notes;

                    if (action === 'issue') {
                        await this.app.addWarrantIssuedEvent(episodeId, type, notes);
                    } else if (action === 'execute') {
                        await this.app.addWarrantExecutedEvent(episodeId, type, notes);
                    } else {
                        this.app.ui.showDialog('Error', 'Invalid warrant action. Use "issue" or "execute".', 'error');
                    }
                } else {
                    // Open modal for full form
                    await this.app.openAddEventModal('warrant', episodeId);
                }
            }
        });

        // Legacy command aliases for backwards compatibility
        this.app.commands.commands.set('je:bail', this.app.commands.commands.get('je:add-bail-hearing'));
        this.app.commands.commands.set('je:xfer', this.app.commands.commands.get('je:add-transfer'));
        this.app.commands.commands.set('je:court', this.app.commands.commands.get('je:add-court-appearance'));
        this.app.commands.commands.set('je:release', this.app.commands.commands.get('je:add-release-conditions'));
        this.app.commands.commands.set('je:sentence', this.app.commands.commands.get('je:add-sentence'));
        this.app.commands.commands.set('je:warrant', this.app.commands.commands.get('je:add-warrant'));

        console.log('✅ Justice commands registered successfully');
    }
}
