// Address List Templates
// Extracted from commands.js for better maintainability

export const addressListTemplates = {

    // Main address list view modal
    addressListModal: (addresses) => {
        return `
            <div class="modal-dialog address-list-modal" style="max-width: 90%; max-height: 90%; width: 1000px;">
                <div class="modal-header">
                    <h3>🏠 Address Database - ${addresses.length} Addresses</h3>
                    <button type="button" class="btn-secondary back-to-records" id="back-to-records-btn">
                        ← Back to Records
                    </button>
                </div>
                <div class="modal-body address-list-body">
                    ${addressListTemplates.addressListContent(addresses)}
                </div>
            </div>
        `;
    },

    // Address list content with controls and grid
    addressListContent: (addresses) => {
        if (addresses.length === 0) {
            return addressListTemplates.noAddressesMessage();
        }

        return `
            <div class="address-list-container">
                <div class="address-list-header">
                    <div class="list-controls">
                        <div class="search-container">
                            <input type="text" id="address-filter" placeholder="Filter addresses..." class="filter-input">
                            <button type="button" id="clear-filter" class="btn-small">Clear</button>
                        </div>
                        <div class="sort-container">
                            <select id="address-sort" class="sort-select">
                                <option value="street_address">Sort by Street Address</option>
                                <option value="city">Sort by City</option>
                                <option value="created_at">Sort by Date Added</option>
                                <option value="address_type">Sort by Type</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="address-list-grid">
                    ${addresses.map(address => addressListTemplates.addressCard(address)).join('')}
                </div>
            </div>
        `;
    },

    // Individual address card template
    addressCard: (address) => {
        const fullAddress = [
            address.street_address,
            address.city,
            address.province,
            address.postal_code
        ].filter(Boolean).join(', ');

        return `
            <div class="address-list-item" data-address-id="${address.id}">
                <div class="address-card">
                    <div class="address-header">
                        <div class="address-type">${address.address_type || 'Address'}</div>
                        <div class="address-date">${new Date(address.created_at).toLocaleDateString()}</div>
                    </div>
                    <div class="address-info">
                        <div class="street-address">
                            <strong>${address.street_address || 'Unknown Address'}</strong>
                        </div>
                        ${address.unit_number ? `<div class="unit-number">Unit: ${address.unit_number}</div>` : ''}
                        <div class="address-details">
                            <span class="city">${address.city || 'Unknown City'}</span>
                            <span class="province">${address.province || 'N/A'}</span>
                        </div>
                        ${address.postal_code ? `<div class="postal-code">${address.postal_code}</div>` : ''}
                        ${address.notes ? `<div class="notes-preview">${address.notes.substring(0, 100)}${address.notes.length > 100 ? '...' : ''}</div>` : ''}
                    </div>
                    <div class="address-actions">
                        <span class="click-hint">Click to view details</span>
                    </div>
                </div>
            </div>
        `;
    },

    // No addresses message
    noAddressesMessage: () => {
        return `
            <div class="no-addresses">
                <h4>No Addresses Found</h4>
                <p>No address records are currently in the database.</p>
                <p>Use the "Add Address" button to register a new address.</p>
            </div>
        `;
    },

    // Address detail modal
    addressDetailModal: (address) => {
        const fullAddress = [
            address.street_address,
            address.city,
            address.province,
            address.postal_code
        ].filter(Boolean).join(', ');

        return `
            <div class="modal-dialog address-detail-modal">
                <div class="modal-header">
                    <h3>🏠 Address Details: ${address.street_address || 'Unknown Address'}</h3>
                    <button type="button" class="btn-secondary" id="close-detail-btn">
                        ← Back to List
                    </button>
                </div>
                <div class="modal-body">
                    ${addressListTemplates.addressDetailContent(address)}
                </div>
            </div>
        `;
    },

    // Address detail content grid
    addressDetailContent: (address) => {
        return `
            <div class="address-detail-grid">
                <div class="detail-section">
                    <h4>Address Information</h4>
                    <div class="detail-row">
                        <label>Type:</label>
                        <span>${address.address_type || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Street Address:</label>
                        <span>${address.street_address || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Unit Number:</label>
                        <span>${address.unit_number || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>City:</label>
                        <span>${address.city || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Province:</label>
                        <span>${address.province || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Postal Code:</label>
                        <span>${address.postal_code || 'Not provided'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Country:</label>
                        <span>${address.country || 'Not provided'}</span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4>Record Information</h4>
                    <div class="detail-row">
                        <label>Created:</label>
                        <span>${new Date(address.created_at).toLocaleString()}</span>
                    </div>
                    <div class="detail-row">
                        <label>Created By:</label>
                        <span>${address.created_by || 'Unknown'}</span>
                    </div>
                    ${address.updated_at ? `
                    <div class="detail-row">
                        <label>Last Updated:</label>
                        <span>${new Date(address.updated_at).toLocaleString()}</span>
                    </div>
                    ` : ''}
                </div>
                
                ${address.notes ? `
                <div class="detail-section full-width">
                    <h4>Notes</h4>
                    <div class="notes-content">
                        ${address.notes}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    }
};