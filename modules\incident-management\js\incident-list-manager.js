/**
 * Incident List Manager
 * Handles incident list display, filtering, and selection
 * Extracted from app.js - manages ~15 list-related functions
 */

import { BaseManager } from '../../shared/base-manager.js';
import { incidentDetailTemplates } from '../templates/index.js';

export class IncidentListManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.selectedIncident = null;
        this.currentIncidentsView = 'dispatch';
        this.incidentsUpdateInterval = null;
        this.incidentsDataChangeHandler = null;
    }

    async loadIncidentsContent() {
        return incidentDetailTemplates.incidentsDashboard();
    }

    async initializeUnifiedIncidents() {
        try {
            console.log('Initializing unified incidents interface...');

            // Initialize state
            this.selectedIncident = null;
            this.currentIncidentsView = 'dispatch';
            this.incidentsUpdateInterval = null;

            // Set up view switching
            this.setupIncidentsViewSwitching();

            // Set up event handlers
            this.setupUnifiedIncidentsEventHandlers();

            // Ensure menu handlers are working for dynamically loaded content
            this.verifyEventHandlers();

            // Load initial data based on current view
            await this.loadIncidentsData();

            // Start real-time updates
            this.startIncidentsUpdates();

            console.log('Unified incidents interface initialized');

        } catch (error) {
            console.error('Error initializing unified incidents:', error);
        }
    }

    setupIncidentsViewSwitching() {
        const viewButtons = document.querySelectorAll('.terminal-cmd[data-view]');
        const views = document.querySelectorAll('.terminal-view');

        viewButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const viewName = btn.dataset.view;

                // Update button states
                viewButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // Update view visibility
                views.forEach(v => v.classList.remove('active'));
                const targetView = document.getElementById(`${viewName}-view`);
                if (targetView) {
                    targetView.classList.add('active');
                }

                // Update current view and load data
                this.currentIncidentsView = viewName;
                this.loadIncidentsData();

                // Update terminal status
                this.uiUtilities.updateTerminalStatus();
            });
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (document.querySelector('.incidents-content') === null) return;

            switch(e.key) {
                case 'F1':
                    e.preventDefault();
                    document.querySelector('[data-view="dispatch"]')?.click();
                    break;
                case 'F2':
                    e.preventDefault();
                    document.querySelector('[data-view="list"]')?.click();
                    break;
                case 'F3':
                    e.preventDefault();
                    document.querySelector('[data-view="search"]')?.click();
                    break;
                case 'F4':
                    e.preventDefault();
                    document.querySelector('[data-action="create-incident-screen"]')?.click();
                    break;
                case 'F5':
                    e.preventDefault();
                    document.getElementById('refresh-incidents')?.click();
                    break;
                case 'Escape':
                    // Clear search or go back to dispatch view
                    if (this.currentIncidentsView === 'search') {
                        const searchInput = document.getElementById('search-input');
                        if (searchInput && searchInput.value) {
                            searchInput.value = '';
                            document.getElementById('clear-search')?.click();
                        } else {
                            document.querySelector('[data-view="dispatch"]')?.click();
                        }
                    }
                    break;
                case 'Enter':
                    // Quick search when in search view
                    if (this.currentIncidentsView === 'search' && e.target.id === 'search-input') {
                        e.preventDefault();
                        document.getElementById('search-btn')?.click();
                    }
                    break;
            }
        });
    }

    setupUnifiedIncidentsEventHandlers() {
        // Refresh button
        const refreshBtn = document.getElementById('refresh-incidents');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadIncidentsData();
            });
        }

        // Search functionality
        const searchBtn = document.getElementById('search-btn');
        const searchInput = document.getElementById('search-input');
        const clearBtn = document.getElementById('clear-search');

        if (searchBtn && searchInput) {
            searchBtn.addEventListener('click', () => {
                this.filterIncidents();
            });

            searchInput.addEventListener('input', () => {
                this.filterIncidents();
            });

            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.filterIncidents();
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                if (searchInput) searchInput.value = '';
                const statusFilter = document.getElementById('status-filter');
                const priorityFilter = document.getElementById('priority-filter');
                if (statusFilter) statusFilter.value = '';
                if (priorityFilter) priorityFilter.value = '';
                this.loadIncidentsData();
            });
        }

        // Filter functionality
        const statusFilter = document.getElementById('status-filter');
        const priorityFilter = document.getElementById('priority-filter');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.loadIncidentsData();
            });
        }

        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => {
                this.loadIncidentsData();
            });
        }

        // Listen for data changes to automatically refresh
        const dataChangeHandler = (event) => {
            const { table, operation, record } = event.detail;
            if (table === 'incidents') {
                // Only refresh for actual data changes, not cache refreshes
                if (operation !== 'refresh') {
                    console.log(`🔄 Incident ${operation} detected, refreshing incidents...`);
                    
                    // Check if we're currently viewing incident details and this is an update to the current incident
                    if (this.selectedIncident && operation === 'update' && record && record.id === this.selectedIncident.id) {
                        console.log('🎯 Updating current incident details without resetting view...');
                        
                        // Store current tab state - use the new tab system
                        const activeTab = document.querySelector('.incident-detail-tab.active');
                        const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : null;
                        
                        // Update the selected incident data
                        this.selectedIncident = record;
                        
                        // Repopulate the incident detail tabs with updated data
                        this.populateIncidentTabContent(record);
                        
                        // Restore the active tab if it was stored
                        if (activeTabId) {
                            setTimeout(() => {
                                // Use the current tab system to restore active state
                                const tabToActivate = document.querySelector(`.incident-detail-tab[data-tab="${activeTabId}"]`);
                                const paneToActivate = document.getElementById(`${activeTabId}-pane`);
                                
                                if (tabToActivate && paneToActivate) {
                                    // Remove active from all tabs and panes
                                    document.querySelectorAll('.incident-detail-tab').forEach(t => t.classList.remove('active'));
                                    document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
                                    
                                    // Activate the correct tab and pane
                                    tabToActivate.classList.add('active');
                                    paneToActivate.classList.add('active');
                                    
                                    // Re-initialize narrative editor if on narrative tab
                                    if (activeTabId === 'narrative') {
                                        setTimeout(() => {
                                            // TODO: Initialize narrative editor
                                        }, 50);
                                    }
                                }
                            }, 50);
                        }
                    } else {
                        // For other operations (create, delete) or when not viewing details, do full refresh
                        this.loadIncidentsData();
                    }
                }
            }
        };

        // Remove existing listener if any
        if (this.incidentsDataChangeHandler) {
            window.removeEventListener('dataChange', this.incidentsDataChangeHandler);
        }

        // Add new listener and store reference for cleanup
        this.incidentsDataChangeHandler = dataChangeHandler;
        window.addEventListener('dataChange', dataChangeHandler);
    }

    async loadIncidentsData() {
        await this.loadDispatchView();
    }

    async refreshIncidents() {
        console.log('Refreshing incidents...');
        try {
            await this.loadIncidentsData();
            this.uiUtilities.showToast('Incidents refreshed successfully', 'success');
        } catch (error) {
            console.error('Error refreshing incidents:', error);
            this.uiUtilities.showToast('Failed to refresh incidents', 'error');
        }
    }

    verifyEventHandlers() {
        // Verify that action buttons are properly accessible
        const actionButtons = document.querySelectorAll('[data-action]');
        console.log(`Found ${actionButtons.length} action buttons`);
        
        // Specifically check for create incident button
        const createButton = document.querySelector('[data-action="create-incident-screen"]');
        if (createButton) {
            console.log('Create incident button found and accessible');
        } else {
            console.warn('Create incident button not found - this may cause issues');
        }
        
        // Test if click handlers are working by adding a temporary test
        actionButtons.forEach(button => {
            if (!button.dataset.testHandler) {
                button.dataset.testHandler = 'true';
                // The global event delegation should handle this
            }
        });
    }

    async loadDispatchView() {
        const incidentList = document.getElementById('incident-list');
        const incidentCountDisplay = document.getElementById('incident-count-display');
        if (!incidentList) return;

        try {
            // Show loading state
            incidentList.innerHTML = '<div class="loading">Loading incidents...</div>';

            // Get all incidents
            const incidents = await this.data.search('incidents', {});

            // Apply filters
            let filteredIncidents = this.applyIncidentFilters(incidents);

            // Update count
            if (incidentCountDisplay) {
                incidentCountDisplay.textContent = filteredIncidents.length;
            }

            if (filteredIncidents.length === 0) {
                incidentList.innerHTML = '<div class="loading">No incidents found</div>';
                return;
            }

            // Sort by priority and time
            filteredIncidents.sort((a, b) => {
                const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
                const aPriority = priorityOrder[a.priority] || 2;
                const bPriority = priorityOrder[b.priority] || 2;
                if (bPriority !== aPriority) return bPriority - aPriority;
                return new Date(b.created_at) - new Date(a.created_at);
            });

            // Generate HTML for incidents
            const incidentsHTML = filteredIncidents.map(incident => {
                const isDraft = incident.status === 'draft' || incident.is_draft;
                const draftIndicator = isDraft ? '<span class="draft-indicator">📝 DRAFT</span>' : '';
                const itemClass = isDraft ? 'incident-item draft-incident' : 'incident-item';

                return `
                <div class="${itemClass}" data-incident-id="${incident.id}" data-action="select-incident">
                    <div class="incident-header">
                        <span class="incident-number">#${incident.incident_number || 'DRAFT'}</span>
                        ${draftIndicator}
                        <span class="incident-priority ${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'Medium').toUpperCase()}</span>
                        <span class="incident-status ${(incident.status || 'open').toLowerCase()}">${incident.status || 'Open'}</span>
                    </div>
                    <div class="incident-location">📍 ${(incident.location && incident.location !== 'Draft - Location TBD' ? incident.location : 'No location').substring(0, 50)}</div>
                    <div class="incident-time">🕒 ${this.uiUtilities.formatTime(incident.incident_time)} | ${this.uiUtilities.formatDate(incident.updated_at || incident.created_at)}</div>
                    <div class="incident-description">${(incident.description || incident.narrative || '').substring(0, 100)}${(incident.description || incident.narrative || '').length > 100 ? '...' : ''}</div>
                    <div class="incident-assigned">${incident.assigned_ranger ? `🎯 ${incident.assigned_ranger}` : '👤 Unassigned'}</div>
                </div>
            `;
            }).join('');

            incidentList.innerHTML = incidentsHTML;

            // Add click handlers for incident items
            incidentList.querySelectorAll('.incident-item').forEach(item => {
                item.addEventListener('click', () => {
                    // Remove selection from other items
                    incidentList.querySelectorAll('.incident-item').forEach(i => i.classList.remove('selected'));
                    item.classList.add('selected');
                    
                    const incidentId = item.dataset.incidentId;
                    this.selectIncident(incidentId);
                });
            });

        } catch (error) {
            console.error('Error loading dispatch view:', error);
            incidentList.innerHTML = '<div class="error">Error loading incidents</div>';
        }
    }

    applyIncidentFilters(incidents) {
        let filtered = incidents;

        // Apply status filter
        const statusFilter = document.getElementById('status-filter');
        if (statusFilter && statusFilter.value) {
            filtered = filtered.filter(incident => 
                (incident.status || 'open').toLowerCase() === statusFilter.value.toLowerCase()
            );
        }

        // Apply priority filter
        const priorityFilter = document.getElementById('priority-filter');
        if (priorityFilter && priorityFilter.value) {
            filtered = filtered.filter(incident => 
                (incident.priority || 'medium').toLowerCase() === priorityFilter.value.toLowerCase()
            );
        }

        // Apply search filter
        const searchInput = document.getElementById('search-input');
        if (searchInput && searchInput.value.trim()) {
            const searchTerm = searchInput.value.toLowerCase().trim();
            filtered = filtered.filter(incident => {
                return (
                    (incident.incident_number || '').toLowerCase().includes(searchTerm) ||
                    (incident.description || '').toLowerCase().includes(searchTerm) ||
                    (incident.location || '').toLowerCase().includes(searchTerm) ||
                    (incident.incident_type || '').toLowerCase().includes(searchTerm) ||
                    (incident.assigned_ranger || '').toLowerCase().includes(searchTerm)
                );
            });
        }

        return filtered;
    }

    filterIncidents() {
        this.loadDispatchView();
    }

    startIncidentsUpdates() {
        // Clean up existing interval if any
        if (this.incidentsUpdateInterval) {
            clearInterval(this.incidentsUpdateInterval);
        }

        // Start new update interval
        this.incidentsUpdateInterval = setInterval(async () => {
            try {
                // Only auto-refresh if we're on the incidents tab and not viewing details
                if (document.querySelector('.incidents-content') && !this.selectedIncident) {
                    console.log('🔄 Auto-refreshing incidents...');
                    await this.loadIncidentsData();
                }
            } catch (error) {
                console.error('Error during auto-refresh:', error);
            }
        }, 30000); // Refresh every 30 seconds
    }

    async selectIncident(incidentId) {
        try {
            console.log('Selecting incident:', incidentId);

            // Get incident data
            const incidents = await this.data.search('incidents', {});
            const incident = incidents.find(i => i.id == incidentId);

            if (!incident) {
                this.ui.showDialog('Error', 'Incident not found.', 'error');
                return;
            }

            // Store selected incident
            this.selectedIncident = incident;

            // Check if we're in the incidents tab with the details container
            const detailView = document.getElementById('incident-details');
            if (detailView) {
                detailView.style.display = 'block';
                detailView.innerHTML = incidentDetailTemplates.incidentDetailView(incident);

                // Populate tab content
                this.populateIncidentTabContent(incident);

                // Set up incident detail tabs
                this.setupIncidentDetailTabs();
            } else {
                // If we're not in the incidents tab, navigate there first
                console.log('Incident details container not found, navigating to incidents tab');
                if (window.app && window.app.loadTabContent) {
                    await window.app.loadTabContent('incidents');
                    // Try again after navigation
                    setTimeout(() => {
                        this.selectIncident(incidentId);
                    }, 100);
                } else {
                    console.error('Cannot navigate to incidents tab - app navigation not available');
                }
            }

        } catch (error) {
            console.error('Error selecting incident:', error);
            this.ui.showDialog('Error', `Failed to load incident details: ${error.message}`, 'error');
        }
    }

    populateIncidentTabContent(incident) {
        // Basic Info Tab
        const basicPane = document.getElementById('basic-pane');
        if (basicPane) {
            basicPane.innerHTML = incidentDetailTemplates.basicInfoTab(incident);
        }

        // Location Tab
        const locationPane = document.getElementById('location-pane');
        if (locationPane) {
            locationPane.innerHTML = incidentDetailTemplates.locationTab(incident);
        }

        // People Tab
        const peoplePane = document.getElementById('people-pane');
        if (peoplePane) {
            peoplePane.innerHTML = incidentDetailTemplates.peopleTab(incident);
        }

        // Property Tab
        const propertyPane = document.getElementById('property-pane');
        if (propertyPane) {
            propertyPane.innerHTML = incidentDetailTemplates.propertyTab(incident);
        }

        // Services Tab
        const servicesPane = document.getElementById('services-pane');
        if (servicesPane) {
            servicesPane.innerHTML = incidentDetailTemplates.servicesTab(incident);
        }

        // Agencies Tab
        const agenciesPane = document.getElementById('agencies-pane');
        if (agenciesPane) {
            agenciesPane.innerHTML = incidentDetailTemplates.agenciesTab(incident);
        }

        // Safety Tab
        const safetyPane = document.getElementById('safety-pane');
        if (safetyPane) {
            safetyPane.innerHTML = incidentDetailTemplates.safetyTab(incident);
        }

        // Follow-up Tab
        const followupPane = document.getElementById('followup-pane');
        if (followupPane) {
            followupPane.innerHTML = incidentDetailTemplates.followupTab(incident);
        }

        // Narrative Tab
        const narrativePane = document.getElementById('narrative-pane');
        if (narrativePane) {
            narrativePane.innerHTML = incidentDetailTemplates.narrativeTab(incident);
            
            // Load and display log entries (narrative entries)
            this.loadNarrativeEntries(incident.id);
        }

        // Files Tab
        const filesPane = document.getElementById('files-pane');
        if (filesPane) {
            filesPane.innerHTML = incidentDetailTemplates.filesTab(incident);
        }
    }

    setupIncidentDetailTabs() {
        const tabs = document.querySelectorAll('.incident-detail-tab');
        const panes = document.querySelectorAll('.tab-pane');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and panes
                tabs.forEach(t => t.classList.remove('active'));
                panes.forEach(p => p.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Show corresponding pane
                const targetPane = document.getElementById(`${tab.dataset.tab}-pane`);
                if (targetPane) {
                    targetPane.classList.add('active');
                }
            });
        });
    }

    async loadNarrativeEntries(incidentId) {
        try {
            console.log('Loading narrative entries for incident:', incidentId);
            
            // Get the incident with log entries
            const incident = await this.data.get('incidents', incidentId);
            
            const narrativeContainer = document.getElementById(`narrative-entries-${incidentId}`);
            if (!narrativeContainer) {
                console.error('Narrative container not found for incident:', incidentId);
                return;
            }
            
            // Check if log_entries exists and is valid JSON
            let logEntries = [];
            if (incident.log_entries) {
                console.log('Raw log_entries data:', incident.log_entries);
                console.log('Type of log_entries:', typeof incident.log_entries);
                
                try {
                    logEntries = typeof incident.log_entries === 'string' 
                        ? JSON.parse(incident.log_entries) 
                        : incident.log_entries;
                    
                    console.log('Parsed log entries:', logEntries);
                    
                    // Ensure it's an array
                    if (!Array.isArray(logEntries)) {
                        console.warn('log_entries is not an array, converting to array');
                        logEntries = [];
                    }
                } catch (parseError) {
                    console.error('Error parsing log entries:', parseError);
                    logEntries = [];
                }
            } else {
                console.log('No log_entries found in incident data');
            }
            
            // Display narrative entries
            if (logEntries.length === 0) {
                narrativeContainer.innerHTML = `
                    <div class="no-entries">
                        <p>No narrative entries recorded for this incident.</p>
                        <p>Click "📝 Add Entry" to add the first narrative entry.</p>
                    </div>
                `;
            } else {
                // Sort entries by incident event time for chronological order (earliest first for incident timeline)
                logEntries.sort((a, b) => {
                    // Primary sort: by incident event time (event_time, incident_time, or timestamp)
                    const eventTimeA = new Date(a.event_time || a.incident_time || a.timestamp || a.created_at || 0);
                    const eventTimeB = new Date(b.event_time || b.incident_time || b.timestamp || b.created_at || 0);
                    return eventTimeA - eventTimeB; // Earliest event first for chronological incident timeline
                });
                
                // Generate HTML for entries with dual timestamp display
                const entriesHtml = logEntries.map((entry, index) => {
                    console.log(`Processing entry ${index}:`, entry);
                    
                    // Extract and format the data
                    const entryType = entry.entry_type || entry.type || 'GENERAL_NOTE';
                    const formattedType = entryType.replace(/_/g, ' ').toUpperCase();
                    
                    const user = entry.user || entry.created_by || entry.author || 'Unknown User';
                    
                    // Get both timestamps
                    // 1. Event time - when the incident event actually occurred (user-defined)
                    const eventTime = entry.event_time || entry.incident_time || entry.timestamp;
                    const formattedEventTime = eventTime ? this.uiUtilities.formatDateTime(eventTime) : 'No event time';
                    
                    // 2. Creation time - when the narrative entry was created (system-generated)
                    const createdTime = entry.created_at || entry.timestamp;
                    const formattedCreatedTime = createdTime ? this.uiUtilities.formatDateTime(createdTime) : 'No creation time';
                    
                    console.log(`Entry ${index} - Type: ${formattedType}, User: ${user}, Event Time: ${formattedEventTime}, Created: ${formattedCreatedTime}`);
                    
                    return `
                        <div class="narrative-entry" data-entry-index="${index}">
                            <div class="narrative-entry-header">
                                <div class="entry-header-line">
                                    <span class="entry-type-badge">[${formattedType}]</span>
                                    <span class="entry-user-info">Created by: <strong>${user}</strong></span>
                                </div>
                                <div class="entry-timestamps">
                                    <span class="event-time">📅 Incident Time: <strong>${formattedEventTime}</strong></span>
                                    <span class="created-time">🕒 Entry Created: ${formattedCreatedTime}</span>
                                </div>
                            </div>
                            <div class="narrative-entry-content">
                                ${(entry.content || entry.description || entry.notes || entry.text || 'No content provided').replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    `;
                }).join('');
                
                narrativeContainer.innerHTML = entriesHtml;
            }
            
        } catch (error) {
            console.error('Error loading narrative entries:', error);
            const narrativeContainer = document.getElementById(`narrative-entries-${incidentId}`);
            if (narrativeContainer) {
                narrativeContainer.innerHTML = `
                    <div class="error">
                        Failed to load narrative entries: ${error.message}
                    </div>
                `;
            }
        }
    }

    cleanup() {
        // Clean up unified incidents resources
        if (this.incidentsUpdateInterval) {
            clearInterval(this.incidentsUpdateInterval);
            this.incidentsUpdateInterval = null;
        }

        if (this.incidentsDataChangeHandler) {
            window.removeEventListener('dataChange', this.incidentsDataChangeHandler);
            this.incidentsDataChangeHandler = null;
        }
    }
}