// Weather Service for S.T.E.V.I Retro - Google Weather API Integration
import { ConfigManager } from './config.js';

export class WeatherService {
    constructor(configManager = null, locationManager = null) {
        this.config = configManager || new ConfigManager();
        this.locationManager = locationManager;
        this.apiKey = null; // Will be loaded asynchronously from Vault
        this.location = 'Current Location';
        this.fallbackCoordinates = { lat: 43.9589, lng: -78.1648 }; // Cobourg coordinates as fallback
        this.cache = new Map();
        this.cacheTimeout = 15 * 60 * 1000; // 15 minutes - longer cache for battery saving
        this.lastLocationSource = null; // Track location source changes
        
        // Subscribe to location updates if location manager is provided
        if (this.locationManager) {
            this.setupLocationSubscription();
        }

        // Bind UI renderers for dashboard compact widget
        this._bound = false;
        // Periodic refresh of dashboard widget if present
        setInterval(() => {
            if (document.getElementById('forecast-grid')) {
                this.renderDashboardWeather().catch(() => {});
            }
        }, 5 * 60 * 1000);
    }

    /**
     * Setup location change subscription to clear cache when location changes
     */
    setupLocationSubscription() {
        try {
            this.locationManager.subscribe((position) => {
                const newSource = position.source;
                
                // If location source has changed, clear weather cache for fresh data
                if (this.lastLocationSource && this.lastLocationSource !== newSource) {
                    console.log(`WeatherService: Location source changed from ${this.lastLocationSource} to ${newSource}, clearing cache`);
                    this.clearCache();
                    
                    // If we upgraded from fallback to GPS, log it once
                    if (this.lastLocationSource === 'fallback' && (newSource === 'gps' || newSource === 'GGA' || newSource === 'RMC')) {
                        console.log('WeatherService: GPS location available, will fetch fresh weather data on next request');
                    }
                }
                
                this.lastLocationSource = newSource;
            });
        } catch (error) {
            console.warn('WeatherService: Failed to setup location subscription:', error.message);
        }
    }

    /**
     * Get Google API key from Vault or fallback
     * @returns {Promise<string>} The Google API key
     */
    async getApiKey() {
        try {
            console.log('WeatherService: Attempting to get Google API key from vault...');

            // Check if config manager is available
            if (!this.config) {
                console.warn('WeatherService: Config manager not available');
                return null;
            }

            // Always try to get fresh API key from vault, don't cache it
            // This ensures we get the key even if vault manager was initialized after WeatherService
            this.apiKey = await this.config.getGoogleApiKey();

            if (this.apiKey) {
                console.log('WeatherService: Successfully retrieved API key from vault');
                return this.apiKey;
            } else {
                console.log('WeatherService: Config has vaultManager:', !!this.config.vaultManager);
                console.log('WeatherService: Config has supabaseClient:', !!this.config.supabaseClient);
                console.warn('WeatherService: No API key available from vault');
            }
        } catch (error) {
            console.error('WeatherService: Error getting Google API key from vault:', error);
        }

        // No caching of null/undefined values - always try vault first
        return null;
    }

    /**
     * Get current coordinates from location manager or fallback
     * @returns {Object} Coordinates object with lat and lng
     */
    getCurrentCoordinates() {
        if (this.locationManager) {
            const coords = this.locationManager.getCoordinates();
            const source = this.locationManager.getLocationSource();
            const isAvailable = this.locationManager.isLocationAvailable();
            
            console.log('WeatherService: LocationManager status:', {
                isAvailable,
                source,
                coords: coords ? `${coords.lat.toFixed(6)}, ${coords.lng.toFixed(6)}` : 'null'
            });
            
            // Use coordinates if we have them, even if location manager says not "available"
            // This handles the case where GPS data exists but isLocationAvailable() returns false
            if (coords && coords.lat !== undefined && coords.lng !== undefined) {
                // Always prefer GPS coordinates over fallback, even if they're similar
                if (source === 'gps' || source === 'GGA' || source === 'RMC') {
                    console.log(`WeatherService: Using GPS coordinates from ${source}:`, coords);
                    return coords;
                }
                
                // For other sources, check if they're significantly different from fallback
                if (Math.abs(coords.lat - this.fallbackCoordinates.lat) > 0.001 || 
                    Math.abs(coords.lng - this.fallbackCoordinates.lng) > 0.001) {
                    console.log(`WeatherService: Using live coordinates from ${source}:`, coords);
                    return coords;
                }
                
                // If coordinates match fallback, check if source indicates real location
                if (source === 'native') {
                    console.log(`WeatherService: Using ${source} coordinates (may be near fallback location):`, coords);
                    return coords;
                }
            }
        }

        console.log('WeatherService: Using fallback coordinates (Cobourg, ON)');
        return this.fallbackCoordinates;
    }

    /**
     * Get current location name for display
     * @returns {Promise<string>} Location name
     */
    async getCurrentLocationName() {
        if (this.locationManager && this.locationManager.isLocationAvailable()) {
            try {
                const locationName = await this.locationManager.getLocationName();
                const source = this.locationManager.getLocationSource();
                return `${locationName} (${source})`;
            } catch (error) {
                console.warn('WeatherService: Error getting location name:', error.message);
            }
        }

        return 'Cobourg, ON (fallback)';
    }

    /**
     * Get current weather for current location
     * @returns {Promise<Object>} Weather data
     */
    async getCurrentWeather() {
        // Get current coordinates
        const coordinates = this.getCurrentCoordinates();
        const cacheKey = `weather_${coordinates.lat.toFixed(4)}_${coordinates.lng.toFixed(4)}`;
        const cached = this.cache.get(cacheKey);

        // Return cached data if still valid
        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            console.log('Returning cached weather data');
            return cached.data;
        }

        try {
            const locationName = await this.getCurrentLocationName();
            console.log(`Fetching fresh weather data for ${locationName}`);

            // Try to get the API key first
            const apiKey = await this.getApiKey();
            if (!apiKey) {
                console.warn('No API key available, using fallback weather data');
                return this.getFallbackWeatherData('Weather data temporarily unavailable - API key not configured', locationName);
            }

            // Use Google Weather API with current coordinates
            const weatherData = await this.fetchGoogleWeatherData(apiKey, coordinates);

            // Cache the result
            this.cache.set(cacheKey, {
                data: weatherData,
                timestamp: Date.now()
            });

            // After fetching fresh data, render compact widget if present
            try {
                if (document.getElementById('forecast-grid')) {
                    // Use microtask to not block UI thread
                    Promise.resolve().then(() => this.renderDashboardWeather());
                }
            } catch (e) {
                console.debug('WeatherService: post-fetch render skipped:', e?.message);
            }

            return weatherData;
        } catch (error) {
            console.error('Error fetching weather data:', error);

            // Return cached data if available, even if expired
            if (cached) {
                console.log('Returning expired cached weather data due to error');
                return cached.data;
            }

            // Return fallback data with error message
            const locationName = await this.getCurrentLocationName();
            return this.getFallbackWeatherData(`Weather data temporarily unavailable: ${error.message}`, locationName);
        }
    }

    /**
     * Fetch weather data from Google Weather API
     * @param {string} apiKey - Google API key
     * @param {Object} coordinates - Coordinates object with lat and lng
     * @returns {Promise<Object>} Weather data
     */
    async fetchGoogleWeatherData(apiKey, coordinates = null) {
        if (!apiKey) {
            throw new Error('Google API key not available');
        }

        // Use provided coordinates or get current ones
        const coords = coordinates || this.getCurrentCoordinates();
        
        const locationName = await this.getCurrentLocationName();
        console.log(`Fetching weather from Google Weather API for ${locationName}`);

        // Fetch current conditions and forecast
        const [currentData, forecastData, alertsData] = await Promise.allSettled([
            this.fetchCurrentWeather(apiKey, coords),
            this.fetchWeatherForecast(apiKey, coords),
            this.fetchWeatherAlerts(apiKey, coords)
        ]);

        const current = currentData.status === 'fulfilled' ? currentData.value : {};
        const forecast = forecastData.status === 'fulfilled' ? forecastData.value : [];
        const alerts = alertsData.status === 'fulfilled' ? alertsData.value : [];

        // Transform Google Weather API response to our format
        return {
            location: locationName,
            temperature: Math.round(current.temperature?.degrees || 0),
            condition: this.mapGoogleConditionToText(current.weatherCondition?.type, current.weatherCondition?.description?.text),
            feelsLike: Math.round(current.feelsLikeTemperature?.degrees || current.temperature?.degrees || 0),
            windSpeed: Math.round(current.wind?.speed?.value || 0),
            humidity: current.relativeHumidity || 0,
            alert: this.generateWeatherAlert(current),
            timestamp: current.currentTime || new Date().toISOString(),
            source: 'Google Weather API',
            uvIndex: current.uvIndex || 0,
            visibility: current.visibility?.distance || 0,
            pressure: current.airPressure?.meanSeaLevelMillibars || 0,
            cloudCover: current.cloudCover || 0,
            isDaytime: current.isDaytime || true,
            coordinates: coords,
            forecast24h: forecast,
            alerts: alerts,
            // precompute icon for current conditions
            icon: this.getWeatherIcon(this.mapGoogleConditionToText(current.weatherCondition?.type, current.weatherCondition?.description?.text))
        };
    }

    /**
     * Fetch current weather conditions
     * @param {string} apiKey - Google API key
     * @param {Object} coords - Coordinates object with lat and lng
     * @returns {Promise<Object>} Current weather data
     */
    async fetchCurrentWeather(apiKey, coords) {
        const url = `https://weather.googleapis.com/v1/currentConditions:lookup?key=${apiKey}&location.latitude=${coords.lat}&location.longitude=${coords.lng}&unitsSystem=METRIC`;
        
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Current weather API error: ${response.status} ${response.statusText}`);
        }
        
        return await response.json();
    }

    /**
     * Fetch 24-hour weather forecast
     * @param {string} apiKey - Google API key
     * @param {Object} coords - Coordinates object with lat and lng
     * @returns {Promise<Array>} 24-hour forecast data
     */
    async fetchWeatherForecast(apiKey, coords) {
        if (!apiKey) {
            console.warn('WeatherService: No API key available for forecast, using fallback');
            return this.getFallbackForecast();
        }
        
        try {
            console.log('WeatherService: Fetching 24-hour forecast from Google Weather API...');
            
            // Google Weather API forecast endpoint - using correct /hours endpoint
            const url = `https://weather.googleapis.com/v1/forecast/hours:lookup?key=${apiKey}&location.latitude=${coords.lat}&location.longitude=${coords.lng}&unitsSystem=METRIC`;
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Forecast API error: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('WeatherService: Forecast data received:', data);
            
            // Transform Google forecast data to our format
            if (data.forecastHours && Array.isArray(data.forecastHours)) {
                const forecast = data.forecastHours.slice(0, 24).map((hourData, index) => ({
                    time: hourData.time || new Date(Date.now() + index * 3600000).toISOString(),
                    temperature: Math.round(hourData.temperature?.degrees || 0),
                    condition: this.mapGoogleConditionToText(hourData.weatherCondition?.type, hourData.weatherCondition?.description?.text),
                    windSpeed: Math.round(hourData.wind?.speed?.value || 0),
                    humidity: hourData.relativeHumidity || 0,
                    precipitationChance: hourData.precipitationChance || 0,
                    icon: this.getWeatherIcon(this.mapGoogleConditionToText(hourData.weatherCondition?.type, hourData.weatherCondition?.description?.text))
                }));
                
                console.log(`WeatherService: Successfully processed ${forecast.length} hours of forecast data`);
                return forecast;
            } else {
                console.warn('WeatherService: No hourly forecast data in response, using fallback');
                return this.getFallbackForecast();
            }
            
        } catch (error) {
            console.error('WeatherService: Forecast API error:', error.message);
            console.log('WeatherService: Falling back to simulated forecast data');
            return this.getFallbackForecast();
        }
    }

    /**
     * Fetch weather alerts
     * @param {string} apiKey - Google API key
     * @param {Object} coords - Coordinates object with lat and lng
     * @returns {Promise<Array>} Weather alerts data
     */
    async fetchWeatherAlerts(apiKey, coords) {
        // Note: Google Weather API doesn't have a specific alerts endpoint
        // We'll use Environment Canada or other sources for Canadian alerts
        try {
            // For Ontario, Canada - use Environment Canada alerts
            if (coords.lat >= 41.7 && coords.lat <= 56.9 && coords.lng >= -95.2 && coords.lng <= -74.3) {
                return await this.fetchEnvironmentCanadaAlerts(coords);
            }
            
            // For other locations, return empty alerts
            return [];
        } catch (error) {
            console.warn('Error fetching weather alerts:', error);
            return [];
        }
    }

    /**
     * Fetch alerts from Environment Canada
     * @param {Object} coords - Coordinates object with lat and lng
     * @returns {Promise<Array>} Weather alerts from Environment Canada
     */
    async fetchEnvironmentCanadaAlerts(coords) {
        try {
            // Environment Canada weather alerts RSS feed for Ontario
            const alertsUrl = 'https://weather.gc.ca/rss/warning/on_e.xml';
            
            // Note: This would require a CORS proxy in production
            // For now, return simulated alerts based on current conditions
            return [];
        } catch (error) {
            console.warn('Error fetching Environment Canada alerts:', error);
            return [];
        }
    }

    /**
     * Get fallback 24-hour forecast
     * @returns {Array} Fallback forecast data
     */
    getFallbackForecast() {
        const forecast = [];
        for (let i = 0; i < 24; i++) {
            const time = new Date(Date.now() + i * 3600000);
            forecast.push({
                time: time.toISOString(),
                temperature: Math.round(-2 + Math.random() * 10),
                condition: 'Partly Cloudy',
                windSpeed: Math.round(10 + Math.random() * 20),
                humidity: Math.round(60 + Math.random() * 30),
                precipitationChance: Math.round(Math.random() * 50),
                icon: '⛅'
            });
        }
        return forecast;
    }

    /**
     * Map Google Weather API condition types to readable text
     * @param {string} type - Google weather condition type
     * @param {string} description - Google weather description
     * @returns {string} Readable condition text
     */
    mapGoogleConditionToText(type, description) {
        if (description) return description;

        const conditionMap = {
            'CLEAR': 'Clear',
            'CLOUDY': 'Cloudy',
            'PARTLY_CLOUDY': 'Partly Cloudy',
            'OVERCAST': 'Overcast',
            'RAIN': 'Rain',
            'LIGHT_RAIN': 'Light Rain',
            'HEAVY_RAIN': 'Heavy Rain',
            'SNOW': 'Snow',
            'LIGHT_SNOW': 'Light Snow',
            'HEAVY_SNOW': 'Heavy Snow',
            'THUNDERSTORM': 'Thunderstorm',
            'FOG': 'Fog',
            'MIST': 'Mist'
        };

        return conditionMap[type] || 'Unknown';
    }

    /**
     * Generate weather alerts based on Google Weather API data
     * @param {Object} data - Google Weather API response
     * @returns {string|null} Alert message
     */
    generateWeatherAlert(data) {
        const temp = data.temperature?.degrees || 0;
        const feelsLike = data.feelsLikeTemperature?.degrees || temp;
        const windSpeed = data.wind?.speed?.value || 0;
        const condition = data.weatherCondition?.type || '';

        // Extreme cold warning
        if (feelsLike <= -15) {
            return 'Extreme cold warning - Warming centers open. Limit outdoor exposure.';
        }

        // Cold weather alert
        if (feelsLike <= -5) {
            return 'Cold weather alert - Extra blankets recommended for outreach.';
        }

        // Snow advisory
        if (condition.includes('SNOW') && condition.includes('HEAVY')) {
            return 'Heavy snow warning - Hazardous driving conditions expected.';
        }

        if (condition.includes('SNOW')) {
            return 'Snow advisory in effect - Slippery conditions expected.';
        }

        // High wind warning
        if (windSpeed >= 50) {
            return 'High wind warning - Secure loose objects and use caution outdoors.';
        }

        // Thunderstorm warning
        if (condition.includes('THUNDERSTORM')) {
            return 'Thunderstorm warning - Seek shelter immediately.';
        }

        // Heat warning
        if (temp >= 30) {
            return 'Heat warning - Stay hydrated and seek shade during outreach.';
        }

        return null; // No alerts
    }

    /**
     * Get fallback weather data when API is unavailable
     * @param {string} message - Error message to display
     * @param {string} locationName - Location name for display
     * @returns {Object} Fallback weather data
     */
    getFallbackWeatherData(message = 'Weather data temporarily unavailable', locationName = null) {
        const coords = this.getCurrentCoordinates();
        const location = locationName || 'Current Location';

        return {
            location: location,
            temperature: -2,
            condition: 'Data Unavailable',
            feelsLike: -5,
            windSpeed: 15,
            humidity: 70,
            alert: message,
            timestamp: new Date().toISOString(),
            source: 'Fallback Data',
            uvIndex: 0,
            visibility: 0,
            pressure: 0,
            cloudCover: 0,
            isDaytime: true,
            coordinates: coords,
            forecast24h: this.getFallbackForecast(),
            alerts: []
        };
    }

    /**
     * Format weather data for display
     * @param {Object} weatherData - Raw weather data
     * @returns {Object} Formatted weather data
     */
    formatWeatherData(weatherData) {
        return {
            temperature: `${weatherData.temperature}°C`,
            condition: weatherData.condition,
            feelsLike: `${weatherData.feelsLike}°C`,
            wind: `${weatherData.windSpeed} km/h`,
            humidity: `${weatherData.humidity}%`,
            alert: weatherData.alert,
            location: weatherData.location,
            lastUpdated: new Date(weatherData.timestamp).toLocaleTimeString(),
            source: weatherData.source,
            icon: weatherData.icon || this.getWeatherIcon(weatherData.condition)
        };
    }

    /**
     * Get weather icon based on condition
     * @param {string} condition - Weather condition
     * @returns {string} Weather emoji
     */
    getWeatherIcon(condition) {
        const iconMap = {
            // Standard conditions
            'Clear': '☀️',
            'Partly Cloudy': '⛅',
            'Overcast': '☁️',
            'Cloudy': '☁️',
            'Snow': '❄️',
            'Light Snow': '🌨️',
            'Heavy Snow': '❄️',
            'Rain': '🌧️',
            'Light Rain': '🌦️',
            'Heavy Rain': '🌧️',
            'Thunderstorm': '⛈️',
            'Fog': '🌫️',
            'Mist': '🌫️',

            // Google Weather API specific conditions
            'Sunny': '☀️',
            'Mostly sunny': '🌤️',
            'Partly sunny': '⛅',
            'Mostly cloudy': '☁️',
            'Partly cloudy': '⛅',
            'Overcast': '☁️',
            'Light rain': '🌦️',
            'Moderate rain': '🌧️',
            'Heavy rain': '🌧️',
            'Light snow': '🌨️',
            'Moderate snow': '❄️',
            'Heavy snow': '❄️',
            'Thunderstorms': '⛈️',
            'Scattered thunderstorms': '⛈️',
            'Isolated thunderstorms': '⛈️',
            'Foggy': '🌫️',
            'Hazy': '🌫️',
            'Misty': '🌫️',

            // Fallback conditions
            'Data Unavailable': '❓',
            'Unknown': '❓'
        };

        const icon = iconMap[condition];
        if (!icon) {
            console.warn(`WeatherService: No icon mapping for condition: "${condition}"`);
        }
        return icon || '❓';
    }

    /**
     * Render compact dashboard weather widget (current + 24h grid)
     * Expects dashboard template to include #weather-current and #forecast-grid
     * Only outputs hour(24h), icon, and temp for each hour.
     */
    async renderDashboardWeather() {
        try {
            console.log('WeatherService: renderDashboardWeather called');
            const data = await this.getCurrentWeather();
            const fmt = this.formatWeatherData(data);
            console.log('WeatherService: Weather data retrieved:', { hasData: !!data, hasForecast: !!(data?.forecast24h) });

            // Current conditions
            const iconEl = document.getElementById('wc-icon');
            const tempEl = document.getElementById('wc-temp');
            const descEl = document.getElementById('wc-desc');
            const feelsEl = document.getElementById('wc-feels');
            const humEl = document.getElementById('wc-hum');
            const windEl = document.getElementById('wc-wind');
            const locEl = document.getElementById('wc-loc');

            if (iconEl) iconEl.textContent = fmt.icon;
            if (tempEl) tempEl.textContent = `${data.temperature}°`;
            if (descEl) descEl.textContent = data.condition || '—';
            if (feelsEl) feelsEl.textContent = `Feels ${data.feelsLike}°`;
            if (humEl) humEl.textContent = `Hum ${data.humidity}%`;
            if (windEl) windEl.textContent = `Wind ${fmt.wind}`;
            if (locEl) locEl.textContent = data.location || '—';

            // Auto-display weather alerts
            const alertEl = document.getElementById('weather-alert');
            const alertTextEl = document.getElementById('alert-text');
            if (alertEl && alertTextEl) {
                if (data.alert) {
                    alertTextEl.textContent = data.alert;
                    alertEl.style.display = 'flex';
                    // Add severity class
                    const severity = this.getAlertSeverity(data.alert);
                    alertEl.className = `weather-alert ${severity}`;
                } else {
                    alertEl.style.display = 'none';
                }
            }

            // Compact 24h forecast grid with smart hour display
            const grid = document.getElementById('forecast-grid');
            console.log('WeatherService: Forecast grid element found:', !!grid);
            if (grid) {
                grid.innerHTML = '';
                const hours = Array.isArray(data.forecast24h) ? data.forecast24h.slice(0, 24) : [];
                console.log('WeatherService: Processing forecast hours:', hours.length);

                // Ultra-compact display: dynamically determine items based on container width
                const maxHours = Math.min(hours.length, 24);
                let itemsAdded = 0;

                // Calculate how many items can fit based on container width
                const containerWidth = grid.offsetWidth || 300; // fallback width
                const itemMinWidth = 60; // minimum width per item including gap
                const maxItems = Math.max(4, Math.min(8, Math.floor(containerWidth / itemMinWidth)));

                for (let index = 0; index < maxHours && itemsAdded < maxItems; index++) {
                    const h = hours[index];
                    const dt = new Date(h.time);
                    const hour24 = dt.getHours();

                    // Format hour display - show key hours more prominently
                    let hourDisplay;
                    if (hour24 === 0) {
                        hourDisplay = '12A';
                    } else if (hour24 === 12) {
                        hourDisplay = '12P';
                    } else if (hour24 < 12) {
                        hourDisplay = hour24 + 'A';
                    } else {
                        hourDisplay = (hour24 - 12) + 'P';
                    }

                    // For ultra-compact display, show every 3 hours to fit in smaller space
                    const shouldShow = index % 3 === 0;
                    if (!shouldShow) continue;

                    itemsAdded++;

                    const cell = document.createElement('div');
                    cell.className = 'forecast-hour';
                    cell.setAttribute('role', 'listitem');
                    cell.setAttribute('title', `${hourDisplay}: ${h.condition || 'Unknown'}, ${h.temperature}°C`);

                    // Ensure icon and temp exist
                    const icon = h.icon || this.getWeatherIcon(h.condition);
                    const temp = (typeof h.temperature === 'number' ? Math.round(h.temperature) : '—');

                    // Debug logging for missing icons
                    if (icon === '❓') {
                        console.warn(`WeatherService: Question mark icon for hour ${index}:`, {
                            condition: h.condition,
                            hasIcon: !!h.icon,
                            iconValue: h.icon,
                            mappedIcon: this.getWeatherIcon(h.condition)
                        });
                    }

                    cell.innerHTML = `
                        <div class="fh-time">${hourDisplay}</div>
                        <div class="fh-icon">${icon}</div>
                        <div class="fh-temp">${temp}°</div>
                    `;

                    grid.appendChild(cell);
                }

                // Fill remaining slots with placeholders if needed
                const currentItems = grid.children.length;
                if (currentItems < maxItems) {
                    for (let i = currentItems; i < maxItems; i++) {
                        const placeholder = document.createElement('div');
                        placeholder.className = 'forecast-hour';
                        placeholder.style.opacity = '0.3';
                        placeholder.innerHTML = `
                            <div class="fh-time">--</div>
                            <div class="fh-icon">❓</div>
                            <div class="fh-temp">--°</div>
                        `;
                        grid.appendChild(placeholder);
                    }
                }
                console.log('WeatherService: Forecast grid populated with', grid.children.length, 'items');
            }
        } catch (err) {
            console.warn('WeatherService.renderDashboardWeather failed:', err);
            // Render a placeholder grid if no data
            const grid = document.getElementById('forecast-grid');
            if (grid && grid.children.length === 0) {
                grid.innerHTML = Array.from({ length: 8 }).map((_, i) => {
                    const hour = (i * 3 + 1);
                    const hourDisplay = hour <= 12 ? `${hour}A` : `${hour - 12}P`;
                    return `
                        <div class="forecast-hour" role="listitem">
                          <div class="fh-time">${hourDisplay}</div>
                          <div class="fh-icon">❓</div>
                          <div class="fh-temp">--°</div>
                        </div>
                    `;
                }).join('');
            }
        }
    }

    /**
     * Get weather alert severity
     * @param {string} alert - Alert message
     * @returns {string} Alert severity level
     */
    getAlertSeverity(alert) {
        if (!alert) return 'none';
        
        const lowerAlert = alert.toLowerCase();
        if (lowerAlert.includes('warning') || lowerAlert.includes('extreme')) {
            return 'severe';
        } else if (lowerAlert.includes('advisory') || lowerAlert.includes('alert')) {
            return 'moderate';
        } else {
            return 'low';
        }
    }

    /**
     * Clear weather cache
     */
    clearCache() {
        this.cache.clear();
        console.log('Weather cache cleared');
    }

    /**
     * Get cache status
     * @returns {Object} Cache information
     */
    getCacheStatus() {
        const cached = this.cache.get('current_weather');
        if (!cached) {
            return { cached: false, age: 0 };
        }

        const age = Date.now() - cached.timestamp;
        return {
            cached: true,
            age: Math.floor(age / 1000), // Age in seconds
            valid: age < this.cacheTimeout
        };
    }

    /**
     * Format 24-hour forecast for display
     * @param {Array} forecast - 24-hour forecast data
     * @returns {Array} Formatted forecast for display
     */
    formatForecast24h(forecast) {
        if (!forecast || !Array.isArray(forecast)) return [];

        return forecast.slice(0, 24).map(hour => {
            const time = new Date(hour.time);
            return {
                hour: time.getHours(),
                timeDisplay: time.toLocaleTimeString('en-US', { hour: 'numeric', hour12: true }),
                temperature: `${hour.temperature}°C`,
                condition: hour.condition,
                icon: hour.icon,
                windSpeed: `${hour.windSpeed} km/h`,
                humidity: `${hour.humidity}%`,
                precipChance: hour.precipitationChance ? `${hour.precipitationChance}%` : '0%'
            };
        });
    }

    /**
     * Get weather alert priority
     * @param {Array} alerts - Array of weather alerts
     * @returns {Object} Highest priority alert
     */
    getHighestPriorityAlert(alerts) {
        if (!alerts || alerts.length === 0) return null;

        // Sort by severity (severe > moderate > low)
        const priorityOrder = { 'severe': 3, 'moderate': 2, 'low': 1 };
        
        return alerts
            .map(alert => ({
                ...alert,
                priority: priorityOrder[this.getAlertSeverity(alert.message)] || 0
            }))
            .sort((a, b) => b.priority - a.priority)[0];
    }

    /**
     * Format weather alerts for display
     * @param {Array} alerts - Weather alerts data
     * @returns {Array} Formatted alerts for display
     */
    formatAlertsForDisplay(alerts) {
        if (!alerts || alerts.length === 0) return [];

        return alerts.map(alert => ({
            title: alert.title || 'Weather Alert',
            message: alert.message || alert.description || '',
            severity: this.getAlertSeverity(alert.message || alert.description || ''),
            icon: this.getAlertIcon(alert.type || 'general'),
            issued: alert.issued ? new Date(alert.issued).toLocaleString() : null,
            expires: alert.expires ? new Date(alert.expires).toLocaleString() : null
        }));
    }

    /**
     * Get icon for alert type
     * @param {string} alertType - Type of weather alert
     * @returns {string} Alert icon emoji
     */
    getAlertIcon(alertType) {
        const iconMap = {
            'winter': '❄️',
            'storm': '⛈️',
            'wind': '💨',
            'rain': '🌧️',
            'heat': '🔥',
            'cold': '🥶',
            'fog': '🌫️',
            'general': '⚠️'
        };

        return iconMap[alertType.toLowerCase()] || iconMap['general'];
    }
}
