/**
 * Centralized Supabase Configuration for Electron Main Process
 * 
 * This module provides the same configuration as renderer/js/supabase-config.js
 * but is compatible with the Electron main process environment.
 * 
 * To change the Supabase project URL:
 * 1. Update the PROJECT_URL constant below
 * 2. Update the PROJECT_REF constant below  
 * 3. Update the ANON_KEY constant below
 * 
 * These values should match those in renderer/js/supabase-config.js
 */

// ============================================================================
// SUPABASE PROJECT CONFIGURATION
// ============================================================================
// 🔧 CHANGE THESE VALUES TO UPDATE THE SUPABASE PROJECT
const PROJECT_URL = 'https://vfavknkfiiclzgpjpntj.supabase.co';
const PROJECT_REF = 'vfavknkfiiclzgpjpntj';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZmYXZrbmtmaWljbHpncGpwbnRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyMjQzMzEsImV4cCI6MjA2NjgwMDMzMX0.ywwytraoAsGCEWcxj3U8MHs_E1xpbeKP9LDSYsEziJU';
// ============================================================================

/**
 * Get the Supabase project URL
 * @returns {string} The Supabase project URL
 */
export function getSupabaseUrl() {
    return PROJECT_URL;
}

/**
 * Get the Supabase project reference
 * @returns {string} The Supabase project reference
 */
export function getSupabaseProjectRef() {
    return PROJECT_REF;
}

/**
 * Get the Supabase anonymous key
 * @returns {string} The Supabase anonymous key
 */
export function getSupabaseAnonKey() {
    return ANON_KEY;
}

/**
 * Get the complete configuration object
 * @returns {object} Complete Supabase configuration
 */
export function getSupabaseConfig() {
    return {
        url: PROJECT_URL,
        anonKey: ANON_KEY,
        projectRef: PROJECT_REF
    };
}

// Default export for convenience
export default {
    getUrl: getSupabaseUrl,
    getAnonKey: getSupabaseAnonKey,
    getProjectRef: getSupabaseProjectRef,
    getConfig: getSupabaseConfig
};
