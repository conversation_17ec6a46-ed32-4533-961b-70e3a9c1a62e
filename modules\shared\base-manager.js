// Base Manager Class - Eliminates duplication across all manager classes
// Provides common functionality for dependency injection, user context, and error handling

export class BaseManager {
    constructor(dataManager, authManager, uiManager = null, configManager = null) {
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.config = configManager;
        
        // Initialize if extended class has init method
        if (this.init && typeof this.init === 'function') {
            this.init();
        }
    }

    /**
     * Get current user context with timestamp for record tracking
     * @returns {Object} User context with user info and timestamp
     */
    getCurrentUserContext() {
        const currentUser = this.auth.getCurrentUser();
        const timestamp = new Date().toISOString();
        
        return {
            user: currentUser,
            timestamp,
            userEmail: currentUser?.email || 'System',
            userId: currentUser?.id || null
        };
    }

    /**
     * Add created_at, created_by fields to data object
     * @param {Object} data - Data object to enhance
     * @returns {Object} Enhanced data object
     */
    addCreateMetadata(data) {
        const context = this.getCurrentUserContext();
        return {
            ...data,
            created_at: context.timestamp,
            created_by: context.userId
        };
    }

    /**
     * Add updated_at, updated_by fields to data object
     * @param {Object} data - Data object to enhance
     * @returns {Object} Enhanced data object
     */
    addUpdateMetadata(data) {
        const context = this.getCurrentUserContext();
        return {
            ...data,
            updated_at: context.timestamp,
            updated_by: context.userId
        };
    }

    /**
     * Execute operation with standardized error handling
     * @param {Function} operation - Async operation to execute
     * @param {string} operationName - Name for error logging
     * @param {Object} options - Optional configuration
     * @returns {Promise} Operation result
     */
    async executeWithErrorHandling(operation, operationName, options = {}) {
        try {
            const result = await operation();
            
            // Optional success logging
            if (options.logSuccess) {
                console.log(`✅ ${operationName} completed successfully`);
            }
            
            return result;
        } catch (error) {
            const errorMessage = `Error ${operationName}: ${error.message}`;
            console.error(errorMessage, error);
            
            // Optional UI error notification
            if (this.ui && options.showErrorToUser) {
                this.ui.showDialog('Error', errorMessage, 'error');
            }
            
            // Re-throw or return based on options
            if (options.suppressError) {
                return null;
            }
            
            throw error;
        }
    }

    /**
     * Validate required fields in data object
     * @param {Object} data - Data to validate
     * @param {Array} requiredFields - Array of required field names
     * @returns {Object} Validation result
     */
    validateRequiredFields(data, requiredFields) {
        const errors = [];
        
        for (const field of requiredFields) {
            if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
                errors.push(`${field} is required`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Clean data object by removing empty strings and undefined values
     * @param {Object} data - Data to clean
     * @param {Object} options - Cleaning options
     * @returns {Object} Cleaned data object
     */
    cleanData(data, options = {}) {
        const cleaned = {};
        
        for (const [key, value] of Object.entries(data)) {
            // Skip undefined values
            if (value === undefined) continue;
            
            // Handle empty strings
            if (value === '' && options.emptyStringToNull) {
                cleaned[key] = null;
            } else if (value === '' && options.skipEmptyStrings) {
                continue;
            } else {
                cleaned[key] = value;
            }
        }
        
        return cleaned;
    }

    /**
     * Get validation schema for entity if available
     * @param {string} entityName - Name of entity
     * @returns {Object|null} Validation schema or null
     */
    getValidationSchema(entityName) {
        if (this.data && this.data.schema && this.data.schema.getTableSchema) {
            return this.data.schema.getTableSchema(entityName);
        }
        return null;
    }
}