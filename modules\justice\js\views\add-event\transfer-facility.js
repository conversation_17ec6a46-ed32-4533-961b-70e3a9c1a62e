// Transfer to Facility Event Modal
// Handles custody transfers between facilities

import { addEventTemplates } from '../../../templates/add-event-templates.js';

export class TransferFacilityModal {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.episodeId = null;
        this.facilities = [];
    }
    
    async show(episodeId) {
        this.episodeId = episodeId;
        
        try {
            // Load facilities
            this.facilities = await this.justice.api.listFacilities();
            
            // Create modal
            this.modal = this.justice.ui.createModal({
                id: 'add-transfer-modal',
                title: 'Transfer to Facility',
                size: 'medium'
            });
            
            // Render form
            this.render();
            
            // Show modal
            this.justice.ui.showModal(this.modal);
            
            // Setup event handlers
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to show transfer modal:', error);
            this.justice.ui.showDialog('Error', `Failed to open transfer form: ${error.message}`, 'error');
        }
    }
    
    render() {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = addEventTemplates.transferFacilityForm({
            episodeId: this.episodeId,
            facilities: this.facilities,
            defaultDateTime: new Date().toISOString().slice(0, 16)
        });
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#transfer-facility-form');
        
        // Save button
        const saveBtn = this.modal.querySelector('#save-transfer-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveTransfer(form);
            });
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-transfer-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }
    
    async saveTransfer(form) {
        try {
            const formData = new FormData(form);
            
            const eventData = {
                event_dt: formData.get('transfer_dt'),
                payload: {
                    facility_id: formData.get('facility_id'),
                    reason: formData.get('reason')
                }
            };
            
            // Validate required fields
            if (!eventData.event_dt) {
                this.justice.ui.showDialog('Error', 'Transfer date/time is required.', 'error');
                return;
            }
            
            if (!eventData.payload.facility_id) {
                this.justice.ui.showDialog('Error', 'Destination facility is required.', 'error');
                return;
            }
            
            // Show loading state
            const saveBtn = this.modal.querySelector('#save-transfer-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
            
            // Create the event
            const newEvent = await this.justice.api.addEvent(
                this.episodeId,
                'TRANSFER_TO_FACILITY',
                eventData.event_dt,
                eventData.payload
            );
            
            // Update state
            this.justice.state.addEvent(newEvent);
            
            // Close modal
            this.close();
            
            // Show success
            this.justice.ui.showDialog('Success', 'Transfer recorded successfully!', 'success');
            
            // Refresh timeline if visible
            if (this.justice.timelineView && this.justice.timelineView.container) {
                await this.justice.timelineView.refresh();
            }
            
            // Refresh status ribbon if visible
            if (this.justice.statusRibbon && this.justice.statusRibbon.container) {
                await this.justice.statusRibbon.refresh();
            }
            
        } catch (error) {
            console.error('Failed to save transfer:', error);
            this.justice.ui.showDialog('Error', `Failed to save transfer: ${error.message}`, 'error');
            
            // Reset button
            const saveBtn = this.modal.querySelector('#save-transfer-btn');
            if (saveBtn) {
                saveBtn.textContent = 'Save Transfer';
                saveBtn.disabled = false;
            }
        }
    }
    
    close() {
        if (this.modal) {
            this.justice.ui.closeModal(this.modal.id);
            this.modal = null;
        }
        this.episodeId = null;
        this.facilities = [];
    }
}
