// Conditions Templates
// Templates for active conditions management

export const conditionsTemplates = {
    conditionsPanel(data) {
        const { episode, conditions } = data;
        
        return `
            <div class="conditions-panel">
                <div class="conditions-header">
                    <h4>Active Conditions</h4>
                    <div class="conditions-actions">
                        <button class="secondary-button" id="add-conditions-btn">
                            <span class="button-icon">+</span>
                            Add Conditions
                        </button>
                        <button class="secondary-button" data-action="refresh-conditions">
                            <span class="button-icon">🔄</span>
                            Refresh
                        </button>
                    </div>
                </div>
                
                <div class="conditions-content">
                    ${conditions.length > 0 ? conditionsTemplates.renderConditionsList(conditions) : conditionsTemplates.renderNoConditions()}
                </div>
            </div>
        `;
    },
    
    renderConditionsList(conditions) {
        return `
            <div class="conditions-list">
                ${conditions.map(condition => conditionsTemplates.renderConditionItem(condition)).join('')}
            </div>
        `;
    },
    
    renderConditionItem(condition) {
        return `
            <div class="condition-item" data-condition-id="${condition.id}">
                <div class="condition-header">
                    <div class="condition-info">
                        <div class="condition-type">
                            <span class="condition-icon">${conditionsTemplates.getConditionIcon(condition.type)}</span>
                            <span class="condition-label">${conditionsTemplates.formatConditionType(condition.type)}</span>
                        </div>
                        <div class="condition-summary">
                            ${conditionsTemplates.formatConditionSummary(condition)}
                        </div>
                    </div>
                    <div class="condition-controls">
                        <button class="condition-toggle">▼</button>
                        <button class="action-button view-condition-details-btn" 
                                data-condition-id="${condition.id}" title="View Details">
                            👁️
                        </button>
                        <button class="action-button end-condition-btn" 
                                data-condition-id="${condition.id}" title="End Condition">
                            ⏹️
                        </button>
                    </div>
                </div>
                
                <div class="condition-details" style="display: none;">
                    ${conditionsTemplates.renderConditionDetails(condition)}
                </div>
            </div>
        `;
    },
    
    renderConditionDetails(condition) {
        try {
            const details = typeof condition.details === 'string' 
                ? JSON.parse(condition.details) 
                : condition.details;
            
            return `
                <div class="condition-details-content">
                    <div class="detail-row">
                        <span class="detail-label">Start Date:</span>
                        <span class="detail-value">${conditionsTemplates.formatDateTime(condition.start_dt)}</span>
                    </div>
                    ${condition.end_dt ? `
                        <div class="detail-row">
                            <span class="detail-label">End Date:</span>
                            <span class="detail-value">${conditionsTemplates.formatDateTime(condition.end_dt)}</span>
                        </div>
                    ` : ''}
                    ${conditionsTemplates.renderTypeSpecificDetails(condition.type, details)}
                </div>
            `;
        } catch (error) {
            return '<div class="condition-detail-error">Error displaying condition details</div>';
        }
    },
    
    renderTypeSpecificDetails(type, details) {
        switch (type) {
            case 'NO_CONTACT':
                return `
                    <div class="detail-row">
                        <span class="detail-label">Person:</span>
                        <span class="detail-value">${details.person || 'Not specified'}</span>
                    </div>
                    ${details.exceptions ? `
                        <div class="detail-row">
                            <span class="detail-label">Exceptions:</span>
                            <span class="detail-value">${details.exceptions}</span>
                        </div>
                    ` : ''}
                `;
            case 'NO_GO_ZONE':
                return `
                    <div class="detail-row">
                        <span class="detail-label">Location:</span>
                        <span class="detail-value">${details.location || 'Not specified'}</span>
                    </div>
                    ${details.radius ? `
                        <div class="detail-row">
                            <span class="detail-label">Radius:</span>
                            <span class="detail-value">${details.radius}</span>
                        </div>
                    ` : ''}
                `;
            case 'CURFEW':
                return `
                    <div class="detail-row">
                        <span class="detail-label">Hours:</span>
                        <span class="detail-value">${details.start_time || '22:00'} - ${details.end_time || '06:00'}</span>
                    </div>
                    ${details.exceptions ? `
                        <div class="detail-row">
                            <span class="detail-label">Exceptions:</span>
                            <span class="detail-value">${details.exceptions}</span>
                        </div>
                    ` : ''}
                `;
            case 'REPORTING':
                return `
                    <div class="detail-row">
                        <span class="detail-label">Officer:</span>
                        <span class="detail-value">${details.officer || 'Not specified'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Frequency:</span>
                        <span class="detail-value">${details.frequency || 'Not specified'}</span>
                    </div>
                    ${details.location ? `
                        <div class="detail-row">
                            <span class="detail-label">Location:</span>
                            <span class="detail-value">${details.location}</span>
                        </div>
                    ` : ''}
                `;
            case 'RESIDENCE':
                return `
                    <div class="detail-row">
                        <span class="detail-label">Address:</span>
                        <span class="detail-value">${details.address || 'Not specified'}</span>
                    </div>
                    ${details.restrictions ? `
                        <div class="detail-row">
                            <span class="detail-label">Restrictions:</span>
                            <span class="detail-value">${details.restrictions}</span>
                        </div>
                    ` : ''}
                `;
            case 'SURETY':
                return `
                    <div class="detail-row">
                        <span class="detail-label">Surety:</span>
                        <span class="detail-value">${details.name || 'Not specified'}</span>
                    </div>
                    ${details.amount ? `
                        <div class="detail-row">
                            <span class="detail-label">Amount:</span>
                            <span class="detail-value">$${details.amount}</span>
                        </div>
                    ` : ''}
                    ${details.contact ? `
                        <div class="detail-row">
                            <span class="detail-label">Contact:</span>
                            <span class="detail-value">${details.contact}</span>
                        </div>
                    ` : ''}
                `;
            default:
                return details.description ? `
                    <div class="detail-row">
                        <span class="detail-label">Description:</span>
                        <span class="detail-value">${details.description}</span>
                    </div>
                ` : '';
        }
    },
    
    renderNoConditions() {
        return `
            <div class="no-conditions">
                <p>No active conditions for this episode.</p>
                <p>Click "Add Conditions" to set release or probation conditions.</p>
            </div>
        `;
    },
    
    conditionDetailsModal(condition) {
        return `
            <div class="condition-details-modal">
                <div class="condition-modal-header">
                    <h5>${conditionsTemplates.formatConditionType(condition.type)}</h5>
                    <div class="condition-dates">
                        <span>Start: ${conditionsTemplates.formatDateTime(condition.start_dt)}</span>
                        ${condition.end_dt ? `<span>End: ${conditionsTemplates.formatDateTime(condition.end_dt)}</span>` : '<span>Active</span>'}
                    </div>
                </div>
                
                <div class="condition-modal-content">
                    ${conditionsTemplates.renderConditionDetails(condition)}
                </div>
                
                <div class="modal-actions">
                    <button id="close-details-btn" class="secondary-button">
                        Close
                    </button>
                </div>
            </div>
        `;
    },
    
    endConditionModal(condition) {
        return `
            <div class="end-condition-modal">
                <div class="modal-header">
                    <h5>End Condition</h5>
                    <p>End the following condition:</p>
                    <div class="condition-summary">
                        <strong>${conditionsTemplates.formatConditionType(condition.type)}</strong><br>
                        ${conditionsTemplates.formatConditionSummary(condition)}
                    </div>
                </div>
                
                <form id="end-condition-form">
                    <div class="form-row">
                        <label for="end_dt">End Date & Time:</label>
                        <input type="datetime-local" name="end_dt" id="end_dt" 
                               value="${new Date().toISOString().slice(0, 16)}" required>
                    </div>
                    
                    <div class="form-row">
                        <label for="reason">Reason for Ending:</label>
                        <select name="reason" id="reason">
                            <option value="COMPLETED">Condition period completed</option>
                            <option value="MODIFIED">Modified by court order</option>
                            <option value="VIOLATED">Condition violated</option>
                            <option value="SUPERSEDED">Superseded by new conditions</option>
                            <option value="OTHER">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="cancel-end-btn" class="secondary-button">
                            Cancel
                        </button>
                        <button type="button" id="end-condition-btn" class="primary-button">
                            End Condition
                        </button>
                    </div>
                </form>
            </div>
        `;
    },
    
    errorView(message) {
        return `
            <div class="conditions-error">
                <div class="error-message">
                    Failed to load conditions: ${message}
                </div>
                <button class="secondary-button" data-action="refresh-conditions">
                    Retry
                </button>
            </div>
        `;
    },
    
    formatConditionType(type) {
        const labels = {
            'NO_CONTACT': 'No Contact',
            'NO_GO_ZONE': 'No-Go Zone',
            'CURFEW': 'Curfew',
            'REPORTING': 'Reporting',
            'RESIDENCE': 'Residence Requirement',
            'SURETY': 'Surety',
            'ABSTAIN_ALCOHOL': 'Abstain from Alcohol',
            'ABSTAIN_DRUGS': 'Abstain from Drugs',
            'SURRENDER_PASSPORT': 'Surrender Passport',
            'NO_WEAPONS': 'No Weapons',
            'KEEP_PEACE': 'Keep the Peace',
            'OTHER': 'Other'
        };
        
        return labels[type] || type;
    },
    
    getConditionIcon(type) {
        const icons = {
            'NO_CONTACT': '🚫',
            'NO_GO_ZONE': '📍',
            'CURFEW': '🕘',
            'REPORTING': '📋',
            'RESIDENCE': '🏠',
            'SURETY': '🤝',
            'ABSTAIN_ALCOHOL': '🍺',
            'ABSTAIN_DRUGS': '💊',
            'SURRENDER_PASSPORT': '📘',
            'NO_WEAPONS': '🔫',
            'KEEP_PEACE': '☮️',
            'OTHER': '📝'
        };
        
        return icons[type] || '📝';
    },
    
    formatConditionSummary(condition) {
        try {
            const details = typeof condition.details === 'string' 
                ? JSON.parse(condition.details) 
                : condition.details;
            
            switch (condition.type) {
                case 'NO_CONTACT':
                    return `No contact with: ${details.person || 'specified person'}`;
                case 'NO_GO_ZONE':
                    return `No-go zone: ${details.location || 'specified area'}`;
                case 'CURFEW':
                    return `Curfew: ${details.start_time || '22:00'} - ${details.end_time || '06:00'}`;
                case 'REPORTING':
                    return `Report to: ${details.officer || 'probation officer'} ${details.frequency || 'weekly'}`;
                case 'RESIDENCE':
                    return `Reside at: ${details.address || 'approved address'}`;
                case 'SURETY':
                    return `Surety: ${details.name || 'approved surety'} ($${details.amount || '0'})`;
                default:
                    return details.description || 'See full details';
            }
        } catch (error) {
            return 'See full details';
        }
    },
    
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        return date.toLocaleDateString('en-CA', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
};
