// SQLite Manager for S.T.E.V.I Retro - Local Data Caching
// Note: better-sqlite3 needs to be required in Electron renderer process
const Database = window.require ? window.require('better-sqlite3') : null;

export class SQLiteManager {
    constructor(dbPath) {
        this.dbPath = dbPath;
        this.db = null;
        this.isInitialized = false;
    }

    async init() {
        try {
            // Starting SQLite initialization

            if (!Database) {
                const errorMsg = window.require ?
                    'better-sqlite3 module failed to load - check if it\'s properly installed' :
                    'window.require not available - Electron nodeIntegration may be disabled';
                throw new Error(errorMsg);
            }

            // Initializing SQLite database

            try {
                this.db = new Database(this.dbPath);
                // SQLite database connection established
            } catch (error) {
                if (error.message.includes('EACCES') || error.message.includes('permission')) {
                    throw new Error(`Permission denied accessing database file: ${this.dbPath}. Please check file permissions and ensure the application has write access to the data directory.`);
                } else if (error.message.includes('ENOENT')) {
                    throw new Error(`Database directory does not exist: ${this.dbPath}. Please ensure the data directory is properly created.`);
                } else {
                    throw new Error(`Failed to open SQLite database: ${error.message}`);
                }
            }
            
            // Enable WAL mode for better concurrency
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('synchronous = NORMAL');
            this.db.pragma('cache_size = 1000');
            this.db.pragma('temp_store = memory');
            
            // Creating database tables
            await this.createTables();
            // Database tables created successfully

            // Running database migrations
            await this.migrateTables();
            // Database migrations completed successfully

            this.isInitialized = true;
            // SQLite database initialized successfully
        } catch (error) {
            console.error('❌ Failed to initialize SQLite database:', error.message);
            console.error('Error details:', error);

            // Clean up on failure
            if (this.db) {
                try {
                    this.db.close();
                } catch (closeError) {
                    console.warn('Failed to close database after initialization error:', closeError);
                }
                this.db = null;
            }
            this.isInitialized = false;

            throw error;
        }
    }

    createTables() {
        const tables = [
            // Cache metadata table
            `CREATE TABLE IF NOT EXISTS cache_metadata (
                table_name TEXT PRIMARY KEY,
                last_sync DATETIME,
                version INTEGER DEFAULT 1,
                record_count INTEGER DEFAULT 0
            )`,

            // People cache table
            `CREATE TABLE IF NOT EXISTS cache_people (
                id INTEGER PRIMARY KEY,
                created_at DATETIME,
                first_name TEXT,
                last_name TEXT,
                date_of_birth DATE,
                age INTEGER,     -- Auto-calculated from date_of_birth
                phone TEXT,
                email TEXT,
                emergency_contact TEXT,
                emergency_contact_phone TEXT,
                housing_status TEXT DEFAULT 'Unknown',
                notes TEXT,
                created_by TEXT,
                updated_by TEXT,
                updated_at DATETIME,

                -- Person-specific single-value fields for homeless/addiction services
                preferred_pronouns TEXT,
                primary_language TEXT,
                has_id_documents TEXT DEFAULT 'Unknown',
                veteran_status TEXT DEFAULT 'Unknown',
                income_source TEXT DEFAULT 'Unknown',
                risk_level TEXT,
                last_service_date DATE,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Disabilities cache table
            `CREATE TABLE IF NOT EXISTS cache_disabilities (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                disability_type TEXT NOT NULL,
                description TEXT,
                severity TEXT,
                accommodation_needs TEXT,
                diagnosed_date DATE,
                healthcare_provider TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Case management cache table
            `CREATE TABLE IF NOT EXISTS cache_case_management (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                case_manager_name TEXT NOT NULL,
                case_manager_contact TEXT,
                agency TEXT,
                case_number TEXT,
                start_date DATE,
                end_date DATE,
                status TEXT,
                case_type TEXT,
                priority TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Service barriers cache table
            `CREATE TABLE IF NOT EXISTS cache_service_barriers (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                barrier_type TEXT NOT NULL,
                description TEXT NOT NULL,
                severity TEXT,
                status TEXT,
                identified_date DATE,
                resolved_date DATE,
                resolution_notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Support contacts cache table
            `CREATE TABLE IF NOT EXISTS cache_support_contacts (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                contact_name TEXT NOT NULL,
                relationship TEXT,
                contact_type TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                availability TEXT,
                notes TEXT,
                is_emergency_contact BOOLEAN,
                is_primary BOOLEAN,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,


            // Pets cache table
            `CREATE TABLE IF NOT EXISTS cache_pets (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                name TEXT NOT NULL,
                species TEXT,
                breed TEXT,
                age INTEGER,
                color TEXT,
                description TEXT,
                microchip_number TEXT,
                vaccination_status TEXT,
                medical_notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,
                
                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Medical issues cache table - comprehensive medical tracking
            `CREATE TABLE IF NOT EXISTS cache_medical_issues (
                id INTEGER PRIMARY KEY,
                person_id INTEGER,
                condition_name TEXT NOT NULL,
                
                -- Category and classification
                category TEXT NOT NULL DEFAULT 'Other',
                subcategory TEXT,
                source_type TEXT NOT NULL DEFAULT 'Observation',
                
                -- Basic medical info
                diagnosis_date DATE,
                severity TEXT,
                status TEXT,
                is_active BOOLEAN DEFAULT 1,
                treatment_notes TEXT,
                medication TEXT,
                follow_up_required BOOLEAN DEFAULT 0,
                follow_up_date DATE,
                healthcare_provider TEXT,
                
                -- Infection-specific fields
                infection_site TEXT,
                infection_type TEXT,
                suspected_cause TEXT,
                wound_description TEXT,
                requires_immediate_care BOOLEAN DEFAULT 0,
                antibiotics_prescribed TEXT,
                
                -- Mental health specific fields
                mental_health_crisis BOOLEAN DEFAULT 0,
                risk_assessment TEXT,
                suicide_risk BOOLEAN DEFAULT 0,
                
                -- Addiction specific fields
                substance_type TEXT,
                route_of_administration TEXT,
                frequency_of_use TEXT,
                last_use_date DATE,
                withdrawal_symptoms TEXT,
                seeking_treatment BOOLEAN DEFAULT 0,
                
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // People activities cache table - FIXED to match database schema
            `CREATE TABLE IF NOT EXISTS cache_people_activities (
                id TEXT PRIMARY KEY, -- UUID in database
                person_id INTEGER NOT NULL, -- References people.id (BIGINT)
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                location TEXT,
                coordinates TEXT, -- Added missing field
                activity_date DATE NOT NULL,
                activity_time TIME,
                staff_member TEXT NOT NULL,
                outcome TEXT,
                follow_up_required BOOLEAN DEFAULT 0,
                follow_up_date DATE,
                priority TEXT,
                tags TEXT, -- JSON array as text (SQLite doesn't have native ARRAY)
                attachments TEXT, -- JSONB as text
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT NOT NULL,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,


            // Ranger activity log cache table
            `CREATE TABLE IF NOT EXISTS cache_ranger_activity_log (
                id INTEGER PRIMARY KEY,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                location TEXT,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Ranger activity people cache table
            `CREATE TABLE IF NOT EXISTS cache_ranger_activity_people (
                id INTEGER PRIMARY KEY,
                activity_id INTEGER,
                person_id INTEGER,
                role TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Supply provisions cache table
            `CREATE TABLE IF NOT EXISTS cache_supply_provisions (
                id INTEGER PRIMARY KEY,
                activity_id INTEGER,
                item_id TEXT,
                quantity_provided INTEGER,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Address activities cache table
            `CREATE TABLE IF NOT EXISTS cache_address_activities (
                id INTEGER PRIMARY KEY,
                address_id INTEGER,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Vehicle activities cache table
            `CREATE TABLE IF NOT EXISTS cache_vehicle_activities (
                id INTEGER PRIMARY KEY,
                vehicle_id TEXT,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Bike activities cache table
            `CREATE TABLE IF NOT EXISTS cache_bike_activities (
                id INTEGER PRIMARY KEY,
                bike_id TEXT,
                activity_type TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                activity_date DATE,
                activity_time TIME,
                staff_member TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Property records cache table
            `CREATE TABLE IF NOT EXISTS cache_property_records (
                id TEXT PRIMARY KEY,
                property_number TEXT,
                incident_id TEXT,
                property_type TEXT NOT NULL,
                category TEXT,
                description TEXT NOT NULL,
                brand TEXT,
                model TEXT,
                serial_number TEXT,
                color TEXT,
                estimated_value REAL,
                condition TEXT,
                found_location TEXT,
                found_coordinates TEXT,
                found_date DATE,
                found_time TIME,
                found_by TEXT,
                status TEXT,
                investigation_notes TEXT,
                owner_name TEXT,
                owner_contact TEXT,
                owner_address TEXT,
                return_location TEXT,
                returned_to TEXT,
                returned_date DATE,
                returned_time TIME,
                police_file_number TEXT,
                police_officer TEXT,
                handed_to_police_date DATE,
                photos TEXT, -- JSON as text
                disposition_type TEXT,
                disposition_date DATE,
                disposition_notes TEXT,
                recovery_method TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Property actions cache table
            `CREATE TABLE IF NOT EXISTS cache_property_actions (
                id TEXT PRIMARY KEY,
                property_id TEXT,
                action_type TEXT NOT NULL,
                action_date DATE,
                action_time TIME,
                performed_by TEXT,
                description TEXT,
                outcome TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Activity logs cache table
            `CREATE TABLE IF NOT EXISTS cache_activity_logs (
                id TEXT PRIMARY KEY,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                action TEXT NOT NULL,
                field_name TEXT,
                old_value TEXT,
                new_value TEXT,
                notes TEXT,
                user_email TEXT NOT NULL,
                user_name TEXT,
                timestamp DATETIME,
                created_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Found bike reports cache table
            `CREATE TABLE IF NOT EXISTS cache_found_bike_reports (
                id TEXT PRIMARY KEY,
                bike_id TEXT,
                found_date DATE,
                found_time TIME,
                found_location TEXT,
                found_by TEXT,
                condition_description TEXT,
                recovery_method TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Recovery logs cache table
            `CREATE TABLE IF NOT EXISTS cache_recovery_logs (
                id TEXT PRIMARY KEY,
                bike_id TEXT,
                recovery_date DATE,
                recovery_time TIME,
                recovery_location TEXT,
                recovered_by TEXT,
                recovery_method TEXT,
                condition_at_recovery TEXT,
                returned_to_owner BOOLEAN,
                return_date DATE,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Incidents cache table
            `CREATE TABLE IF NOT EXISTS cache_incidents (
                id INTEGER PRIMARY KEY,
                created_at DATETIME,
                reporter_id TEXT,
                location TEXT NOT NULL,
                narrative TEXT NOT NULL,
                tags TEXT, -- JSON array as text
                is_urgent BOOLEAN,
                updated_at DATETIME,
                incident_number TEXT,
                incident_date DATE,
                incident_time TIME,
                incident_type TEXT,
                description TEXT,
                priority TEXT,
                status TEXT,
                assigned_ranger TEXT,
                coordinates TEXT,
                reported_by TEXT,
                dispatch_notes TEXT,
                log_entries TEXT, -- JSONB array as text (SQLite doesn't have native JSONB)

                -- Address fields
                street_address TEXT,
                city TEXT,
                province TEXT,
                postal_code TEXT,
                location_notes TEXT,
                country TEXT DEFAULT 'Canada',
                address_search TEXT,

                -- People involved
                incident_people TEXT, -- JSONB as text
                people_involved TEXT,

                -- Services and resources
                services_offered TEXT, -- Array as text
                services_provided TEXT, -- Array as text
                resources_distributed TEXT, -- Array as text
                referrals_made TEXT, -- Array as text

                -- Safety and environment
                scene_safety TEXT,
                safety_concerns TEXT,
                substance_indicators TEXT, -- Array as text
                environmental_factors TEXT, -- Array as text
                initial_observations TEXT,
                actions_taken TEXT,
                weather_conditions TEXT,
                scene_conditions TEXT,

                -- Medical response
                fire_department_called BOOLEAN DEFAULT 0,
                fire_unit_number TEXT,
                paramedics_called BOOLEAN DEFAULT 0,
                ambulance_unit_number TEXT,
                ems_response_time TIME,
                hospital_transport_offered BOOLEAN DEFAULT 0,
                transport_declined BOOLEAN DEFAULT 0,
                transport_decline_reason TEXT,
                hospital_destination TEXT,
                ems_personnel_names TEXT,
                medical_assessment_notes TEXT,

                -- Police involvement
                police_notified BOOLEAN DEFAULT 0,
                police_file_number TEXT,
                officers_attending TEXT,

                -- Bylaw involvement
                bylaw_notified BOOLEAN DEFAULT 0,
                bylaw_file_number TEXT,
                bylaw_officers_attending TEXT,
                bylaw_response_time TIME,
                bylaw_personnel_names TEXT,
                bylaw_enforcement_action TEXT,
                bylaw_notes TEXT,

                -- Property related
                property_type TEXT,
                property_description TEXT,
                recovery_method TEXT,
                disposition_type TEXT,
                property_brand TEXT,
                property_model TEXT,
                property_serial TEXT,
                property_value TEXT,
                property_condition TEXT,
                property_recovered BOOLEAN DEFAULT 0,

                -- Fire department details
                fire_personnel TEXT,

                -- Agency response
                agency_response_notes TEXT,

                -- Follow-up
                follow_up_required BOOLEAN DEFAULT 0,
                follow_up_notes TEXT,
                follow_up_date DATE,

                -- Draft management
                is_draft BOOLEAN DEFAULT 0,
                draft_created_at DATETIME,

                -- User tracking
                created_by TEXT,

                -- Incident outcome
                outcome TEXT,

                -- Status tracking
                status_history TEXT, -- JSONB as text

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Incident people relationships cache table
            `CREATE TABLE IF NOT EXISTS cache_incident_people (
                id INTEGER PRIMARY KEY,
                incident_id INTEGER NOT NULL,
                person_id INTEGER, -- NULL for unknown parties
                involvement_type TEXT NOT NULL,
                is_unknown_party BOOLEAN NOT NULL DEFAULT 0,
                unknown_party_description TEXT,
                notes TEXT,
                created_at DATETIME,
                created_by TEXT,
                updated_at DATETIME,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Incident person medical information cache table
            `CREATE TABLE IF NOT EXISTS cache_incident_person_medical (
                id INTEGER PRIMARY KEY,
                incident_person_id INTEGER NOT NULL,
                required_medical_attention BOOLEAN NOT NULL DEFAULT 0,
                
                -- Medical issue categorization
                medical_issue_type TEXT,
                medical_issue_description TEXT,
                medical_issue_other TEXT,
                
                -- First aid provided by IHARC staff
                first_aid_provided BOOLEAN DEFAULT 0,
                first_aid_type TEXT, -- JSON array as text
                first_aid_other TEXT,
                first_aid_details TEXT,
                
                -- Transport and hospital information
                hospital_transport_offered BOOLEAN DEFAULT 0,
                transport_declined BOOLEAN DEFAULT 0,
                transport_decline_reason TEXT,
                hospital_destination TEXT,
                transported_by TEXT,
                transport_notes TEXT,
                
                -- Assessment and outcomes
                medical_assessment_notes TEXT,
                injury_severity TEXT,
                consciousness_level TEXT,
                vital_signs_notes TEXT,
                
                -- Follow-up and referrals
                medical_referral_made BOOLEAN DEFAULT 0,
                medical_referral_details TEXT,
                follow_up_required BOOLEAN DEFAULT 0,
                follow_up_notes TEXT,
                
                -- Metadata
                created_at DATETIME,
                created_by TEXT,
                updated_at DATETIME,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Addresses cache table
            `CREATE TABLE IF NOT EXISTS cache_addresses (
                id INTEGER PRIMARY KEY,
                street_address TEXT NOT NULL,
                city TEXT,
                province TEXT DEFAULT 'Ontario',
                postal_code TEXT,
                country TEXT DEFAULT 'Canada',
                notes TEXT,
                created_by TEXT,
                updated_by TEXT,
                created_at DATETIME,
                updated_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Bikes cache table
            `CREATE TABLE IF NOT EXISTS cache_bikes (
                id TEXT PRIMARY KEY, -- UUID as text
                user_id TEXT,
                owner_name TEXT NOT NULL,
                owner_email TEXT NOT NULL,
                serial_number TEXT NOT NULL,
                make TEXT NOT NULL,
                model TEXT NOT NULL,
                color TEXT NOT NULL,
                value REAL,
                photo_base64 TEXT,
                is_stolen BOOLEAN NOT NULL,
                registered_at DATETIME,
                created_at DATETIME,
                updated_at DATETIME,
                theft_date DATE,
                theft_time TIME,
                theft_location TEXT,
                suspect_details TEXT,
                theft_notes TEXT,
                reported_stolen_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Encampments cache table
            `CREATE TABLE IF NOT EXISTS cache_encampments (
                id TEXT PRIMARY KEY, -- UUID as text
                name TEXT NOT NULL,
                location TEXT NOT NULL,
                coordinates TEXT, -- lat,lng format
                status TEXT NOT NULL,
                type TEXT,
                estimated_population INTEGER,
                description TEXT,
                safety_concerns TEXT,
                services_needed TEXT,
                last_visited DATETIME,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Media cache table for file attachments
            `CREATE TABLE IF NOT EXISTS cache_media (
                id INTEGER PRIMARY KEY,
                record_type TEXT NOT NULL,
                record_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                stored_at TEXT NOT NULL,
                description TEXT,
                file_size INTEGER,
                content_type TEXT,
                uploaded_at DATETIME,
                created_at DATETIME,
                updated_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Items cache table
            `CREATE TABLE IF NOT EXISTS cache_items (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT NOT NULL,
                unit_type TEXT NOT NULL,
                current_stock INTEGER NOT NULL,
                minimum_threshold INTEGER,
                cost_per_unit REAL,
                supplier TEXT,
                active BOOLEAN NOT NULL,
                created_at DATETIME,
                updated_at DATETIME,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Organizations cache table
            `CREATE TABLE IF NOT EXISTS cache_organizations (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                organization_type TEXT,
                description TEXT,
                services_provided TEXT,
                address TEXT,
                city TEXT,
                province TEXT,
                postal_code TEXT,
                phone TEXT,
                email TEXT,
                website TEXT,
                contact_person TEXT,
                contact_title TEXT,
                contact_phone TEXT,
                contact_email TEXT,
                operating_hours TEXT,
                availability_notes TEXT,
                partnership_type TEXT,
                referral_process TEXT,
                special_requirements TEXT,
                status TEXT,
                notes TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,
                services_tags TEXT, -- JSON as text

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Sync queue for offline operations
            `CREATE TABLE IF NOT EXISTS sync_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                operation TEXT NOT NULL,
                record_id TEXT NOT NULL,
                data TEXT, -- JSON data
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                retry_count INTEGER DEFAULT 0,
                status TEXT DEFAULT 'pending',
                error_message TEXT
            )`,

            // Conflict resolution log
            `CREATE TABLE IF NOT EXISTS conflict_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                record_id TEXT NOT NULL,
                local_version TEXT,
                remote_version TEXT,
                resolution TEXT,
                resolved_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Incident links cache table
            `CREATE TABLE IF NOT EXISTS cache_incident_links (
                id TEXT PRIMARY KEY,
                incident_id TEXT NOT NULL,
                linked_record_type TEXT NOT NULL,
                linked_record_id TEXT NOT NULL,
                link_type TEXT,
                notes TEXT,
                created_at DATETIME,
                created_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Drop old license plates cache table if it exists (schema migration)
            `DROP TABLE IF EXISTS cache_license_plates`,

            // License plates cache table - Updated to match database schema
            `CREATE TABLE cache_license_plates (
                id INTEGER PRIMARY KEY,
                plate_number TEXT NOT NULL,
                province TEXT,
                vehicle_make TEXT,
                vehicle_model TEXT,
                vehicle_year INTEGER,
                vehicle_color TEXT,
                vehicle_type TEXT,
                owner_name TEXT,
                notes TEXT,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Outreach transactions cache table
            `CREATE TABLE IF NOT EXISTS cache_outreach_transactions (
                id TEXT PRIMARY KEY,
                person_id BIGINT,
                person_name TEXT,
                staff_member TEXT,
                location TEXT,
                coordinates TEXT,
                latitude DECIMAL(10, 8),
                longitude DECIMAL(11, 8),
                location_source TEXT,
                notes TEXT,
                total_items INTEGER DEFAULT 0,
                items TEXT, -- JSON array of items distributed
                activity_date DATE,
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // People aliases cache table
            `CREATE TABLE IF NOT EXISTS cache_people_aliases (
                id INTEGER PRIMARY KEY,
                person_id INTEGER NOT NULL,
                alias_name TEXT NOT NULL,
                created_at DATETIME,
                created_by TEXT,
                updated_at DATETIME,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,

            // Encampment visits cache table
            `CREATE TABLE IF NOT EXISTS cache_encampment_visits (
                id TEXT PRIMARY KEY,
                encampment_id TEXT NOT NULL,
                visit_date DATETIME NOT NULL,
                visitor_name TEXT NOT NULL,
                visitor_email TEXT,
                
                -- Population tracking
                estimated_population INTEGER,
                population_notes TEXT,
                
                -- Infrastructure assessment
                tent_count INTEGER DEFAULT 0,
                structure_count INTEGER DEFAULT 0,
                vehicle_count INTEGER DEFAULT 0,
                
                -- Infrastructure types (stored as JSON text)
                shelter_types TEXT, -- JSON array
                fuel_sources TEXT, -- JSON array
                water_access TEXT, -- JSON array
                waste_management TEXT, -- JSON array
                
                -- Conditions and safety
                overall_condition TEXT,
                safety_level TEXT,
                weather_impact TEXT,
                
                -- Services and needs
                services_provided TEXT, -- JSON array
                immediate_needs TEXT, -- JSON array
                follow_up_required BOOLEAN DEFAULT FALSE,
                follow_up_notes TEXT,
                
                -- Visit details
                visit_duration_minutes INTEGER,
                visit_notes TEXT,
                weather_conditions TEXT,
                accessibility_issues TEXT,
                
                -- Compliance and enforcement
                violations_observed TEXT, -- JSON array
                enforcement_action TEXT,
                
                -- Metadata
                created_at DATETIME,
                updated_at DATETIME,
                created_by TEXT,
                updated_by TEXT,

                -- Cache metadata
                cached_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_synced DATETIME,
                sync_status TEXT DEFAULT 'synced',
                cache_version INTEGER DEFAULT 1
            )`,


        ];

        // Create all tables
        tables.forEach(sql => {
            this.db.exec(sql);
        });

        // Add missing columns to existing tables (for database migration)
        // Note: SQLite doesn't support DROP COLUMN, so we leave full_name and "Full Name" columns
        // but the application will ignore them in favor of first_name + last_name

        // Note: age column is now included in the main table creation schema
        // so no need to add it explicitly here

        // Add new person-specific fields for homeless/addiction services
        const newColumns = [
            'preferred_pronouns TEXT',
            'primary_language TEXT',
            'has_id_documents TEXT DEFAULT \'Unknown\'',
            'veteran_status TEXT DEFAULT \'Unknown\'',
            'income_source TEXT DEFAULT \'Unknown\'',
            'risk_level TEXT',
            'last_service_date DATE'
        ];

        newColumns.forEach(column => {
            // Extract column name and type from the column definition
            const columnParts = column.split(' ');
            const columnName = columnParts[0];
            const columnType = columnParts.slice(1).join(' ');

            // Use the internal method during initialization
            this._addColumnDuringInit('people', columnName, columnType);
        });

        // Add missing columns to cache_incidents for dispatch functionality
        const incidentColumns = [
            'incident_date DATE',
            'incident_time TIME',
            'incident_type TEXT',
            'description TEXT',
            'priority TEXT',
            'status TEXT',
            'assigned_ranger TEXT',
            'coordinates TEXT',
            'reported_by TEXT',
            'dispatch_notes TEXT',
            'street_address TEXT',
            'city TEXT',
            'province TEXT',
            'postal_code TEXT',
            'location_notes TEXT',
            'is_draft BOOLEAN DEFAULT 0',
            'draft_created_at DATETIME',
            'address_search TEXT',
            'hospital_transport_offered BOOLEAN DEFAULT 0',
            'transport_declined BOOLEAN DEFAULT 0',
            'transport_decline_reason TEXT',
            'hospital_destination TEXT',
            'ems_personnel_names TEXT',
            'medical_assessment_notes TEXT',
            'fire_unit_number TEXT',
            'ambulance_unit_number TEXT',
            'ems_response_time TIME',
            'agency_contact_notes TEXT'
        ];

        incidentColumns.forEach(column => {
            // Extract column name and type from the column definition
            const columnParts = column.split(' ');
            const columnName = columnParts[0];
            const columnType = columnParts.slice(1).join(' ');

            // Use the internal method during initialization
            this._addColumnDuringInit('incidents', columnName, columnType);
        });

        // Add missing columns to cache_activity_logs for proper schema alignment
        const activityLogColumns = [
            'field_name TEXT',
            'old_value TEXT', 
            'new_value TEXT',
            'notes TEXT',
            'user_email TEXT',
            'user_name TEXT',
            'created_at DATETIME'
        ];

        activityLogColumns.forEach(column => {
            // Extract column name and type from the column definition
            const columnParts = column.split(' ');
            const columnName = columnParts[0];
            const columnType = columnParts.slice(1).join(' ');

            // Use the internal method during initialization
            this._addColumnDuringInit('activity_logs', columnName, columnType);
        });

        // Add missing columns to cache_addresses for user tracking and coordinates
        const addressColumns = [
            'created_by TEXT',
            'updated_by TEXT',
            'latitude REAL',
            'longitude REAL'
        ];
        
        addressColumns.forEach(column => {
            const columnParts = column.split(' ');
            const columnName = columnParts[0];
            const columnType = columnParts.slice(1).join(' ');

            // Use the internal method during initialization
            this._addColumnDuringInit('addresses', columnName, columnType);
        });

        // Add missing columns to cache_address_activities for proper schema alignment
        const addressActivityColumns = [
            'location_notes TEXT',
            'findings TEXT',
            'action_taken TEXT',
            'follow_up_required BOOLEAN DEFAULT 0',
            'follow_up_date DATE',
            'priority TEXT',
            'tags TEXT',
            'attachments TEXT'
        ];

        addressActivityColumns.forEach(column => {
            const columnParts = column.split(' ');
            const columnName = columnParts[0];
            const columnType = columnParts.slice(1).join(' ');

            // Use the internal method during initialization
            this._addColumnDuringInit('address_activities', columnName, columnType);
        });

        // Create indexes for performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_people_name ON cache_people(first_name, last_name)',
            'CREATE INDEX IF NOT EXISTS idx_people_sync ON cache_people(sync_status, last_synced)',
            'CREATE INDEX IF NOT EXISTS idx_pets_person ON cache_pets(person_id)',
            'CREATE INDEX IF NOT EXISTS idx_incidents_urgent ON cache_incidents(is_urgent)',
            'CREATE INDEX IF NOT EXISTS idx_incidents_date ON cache_incidents(created_at)',
            'CREATE INDEX IF NOT EXISTS idx_bikes_stolen ON cache_bikes(is_stolen)',
            'CREATE INDEX IF NOT EXISTS idx_items_category ON cache_items(category)',
            'CREATE INDEX IF NOT EXISTS idx_items_active ON cache_items(active)',
            'CREATE INDEX IF NOT EXISTS idx_sync_queue_status ON sync_queue(status, timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_conflict_log_table ON conflict_log(table_name, record_id)',
            'CREATE INDEX IF NOT EXISTS idx_incident_links_incident ON cache_incident_links(incident_id)',
            'CREATE INDEX IF NOT EXISTS idx_incident_links_record ON cache_incident_links(linked_record_type, linked_record_id)',
            'CREATE INDEX IF NOT EXISTS idx_license_plates_plate ON cache_license_plates(plate_number)',
            'CREATE INDEX IF NOT EXISTS idx_people_aliases_person ON cache_people_aliases(person_id)',
            'CREATE INDEX IF NOT EXISTS idx_people_aliases_name ON cache_people_aliases(alias_name)'
        ];

        indexes.forEach(sql => {
            try {
                this.db.exec(sql);
            } catch (error) {
                console.warn(`Failed to create index: ${sql}`, error.message);
            }
        });

        // SQLite tables and indexes created successfully
    }

    // Method to check current table schema
    getTableSchema(tableName) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        try {
            const result = this.db.prepare(`PRAGMA table_info(${cacheTableName})`).all();
            return result.map(col => ({
                name: col.name,
                type: col.type,
                nullable: !col.notnull,
                defaultValue: col.dflt_value
            }));
        } catch (error) {
            console.error(`Error getting schema for ${cacheTableName}:`, error);
            return [];
        }
    }

    // Method to force schema update for incidents table
    forceIncidentSchemaUpdate() {
        // Forcing cache_incidents schema update

        const requiredColumns = [
            'incident_date DATE',
            'incident_time TIME',
            'incident_type TEXT',
            'description TEXT',
            'priority TEXT',
            'status TEXT',
            'assigned_ranger TEXT',
            'coordinates TEXT',
            'reported_by TEXT',
            'dispatch_notes TEXT',
            'street_address TEXT',
            'city TEXT',
            'province TEXT',
            'postal_code TEXT',
            'location_notes TEXT',
            'is_draft BOOLEAN DEFAULT 0',
            'draft_created_at DATETIME',
            'address_search TEXT',
            'officers_attending TEXT',
            'fire_personnel TEXT',
            'agency_response_notes TEXT',
            'hospital_transport_offered BOOLEAN DEFAULT 0',
            'transport_declined BOOLEAN DEFAULT 0',
            'transport_decline_reason TEXT',
            'hospital_destination TEXT',
            'ems_personnel_names TEXT',
            'medical_assessment_notes TEXT',
            'fire_unit_number TEXT',
            'ambulance_unit_number TEXT',
            'ems_response_time TIME',
            'created_by TEXT',
            'log_entries TEXT',
            'incident_people TEXT',
            'services_offered TEXT',
            'services_provided TEXT',
            'resources_distributed TEXT',
            'referrals_made TEXT',
            'scene_safety TEXT',
            'safety_concerns TEXT',
            'substance_indicators TEXT',
            'environmental_factors TEXT',
            'follow_up_required BOOLEAN DEFAULT 0',
            'follow_up_notes TEXT',
            'follow_up_date DATE',
            'initial_observations TEXT',
            'actions_taken TEXT',
            'weather_conditions TEXT',
            'scene_conditions TEXT',
            'fire_department_called BOOLEAN DEFAULT 0',
            'paramedics_called BOOLEAN DEFAULT 0',
            'people_involved TEXT',
            'police_notified BOOLEAN DEFAULT 0',
            'police_file_number TEXT',
            'country TEXT DEFAULT "Canada"',
            'outcome TEXT',
            'property_type TEXT',
            'property_description TEXT',
            'recovery_method TEXT',
            'disposition_type TEXT',
            'property_brand TEXT',
            'property_model TEXT',
            'property_serial TEXT',
            'property_value TEXT',
            'property_condition TEXT',
            'property_recovered BOOLEAN DEFAULT 0',
            'recommendations TEXT',
            'property_involved BOOLEAN DEFAULT 0',
            'status_history TEXT'
        ];

        requiredColumns.forEach(column => {
            try {
                this.db.exec(`ALTER TABLE cache_incidents ADD COLUMN ${column}`);
                // Added missing column
            } catch (error) {
                // Column already exists, which is fine
                // Column already exists
            }
        });

        // Log current schema
        const schema = this.getTableSchema('incidents');
        // Current cache_incidents schema updated
    }

    // Generic CRUD operations
    insert(tableName, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;

        // Clean and prepare data for SQLite
        const cleanedData = this.sanitizeDataForSQLite(data);
        const columns = Object.keys(cleanedData);
        const placeholders = columns.map(() => '?').join(', ');
        const values = Object.values(cleanedData);

        // Properly quote column names that might have spaces or special characters
        const quotedColumns = columns.map(col => `"${col}"`);
        const sql = `INSERT INTO ${cacheTableName} (${quotedColumns.join(', ')}) VALUES (${placeholders})`;

        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...values);

            // Update cache metadata
            this.updateCacheMetadata(tableName);

            return { id: result.lastInsertRowid, ...data };
        } catch (error) {
            console.error(`❌ Error inserting into ${cacheTableName}:`, error);
            console.error('SQL:', sql);
            console.error('Columns:', columns);
            console.error('Values:', values);
            console.error('Data types:', values.map(v => typeof v));
            console.error('Number of columns:', columns.length);
            console.error('Number of values:', values.length);

            // Get current table schema for debugging
            const schema = this.getTableSchema(tableName);
            console.error('Current table schema:', schema.map(col => col.name));

            // Check for missing columns
            const missingColumns = columns.filter(col => !schema.some(schemaCol => schemaCol.name === col));
            if (missingColumns.length > 0) {
                console.error('Missing columns in table:', missingColumns);
            }

            throw error;
        }
    }

    // Upsert method - insert or update if exists
    upsert(tableName, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        const sanitizedData = this.sanitizeDataForSQLite(data);

        // Check if record exists
        if (sanitizedData.id) {
            const existing = this.get(tableName, sanitizedData.id);
            if (existing) {
                // Update existing record
                return this.update(tableName, sanitizedData.id, sanitizedData);
            }
        }

        // Insert new record
        return this.insert(tableName, sanitizedData);
    }

    // Sanitize data to ensure SQLite compatibility
    sanitizeDataForSQLite(data) {
        const sanitized = {};

        for (const [key, value] of Object.entries(data)) {
            if (value === undefined) {
                sanitized[key] = null;
            } else if (value === null) {
                sanitized[key] = null;
            } else if (Array.isArray(value)) {
                sanitized[key] = JSON.stringify(value);
            } else if (value instanceof Date) {
                sanitized[key] = value.toISOString();
            } else if (typeof value === 'object') {
                sanitized[key] = JSON.stringify(value);
            } else if (typeof value === 'boolean') {
                sanitized[key] = value ? 1 : 0; // SQLite uses integers for booleans
            } else if (typeof value === 'number') {
                // Ensure it's a valid number
                sanitized[key] = isNaN(value) ? null : value;
            } else if (typeof value === 'string') {
                sanitized[key] = value;
            } else {
                // For any other type, convert to string
                sanitized[key] = String(value);
            }
        }

        return sanitized;
    }

    get(tableName, id) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        const sql = `SELECT * FROM ${cacheTableName} WHERE id = ?`;

        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.get(id);

            if (result) {
                // Parse JSON fields back to their original types
                return this.parseJsonFields(result);
            }

            return result;
        } catch (error) {
            console.error(`Error getting from ${cacheTableName}:`, error);
            throw error;
        }
    }

    // Helper method to parse JSON fields back to their original types
    parseJsonFields(record) {
        const parsed = { ...record };

        // Common fields that might be JSON
        const jsonFields = ['tags', 'metadata', 'data', 'log_entries', 'services_tags'];

        for (const field of jsonFields) {
            if (parsed[field] && typeof parsed[field] === 'string') {
                try {
                    parsed[field] = JSON.parse(parsed[field]);
                } catch (e) {
                    // If parsing fails, keep as string
                }
            }
        }

        return parsed;
    }

    getAll(tableName, orderBy = 'created_at DESC') {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;
        const sql = `SELECT * FROM ${cacheTableName} ORDER BY ${orderBy}`;

        try {
            const stmt = this.db.prepare(sql);
            const results = stmt.all();

            // Parse JSON fields for all results
            return results.map(record => this.parseJsonFields(record));
        } catch (error) {
            console.error(`Error getting all from ${cacheTableName}:`, error);
            throw error;
        }
    }

    update(tableName, id, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');

        const cacheTableName = `cache_${tableName}`;

        // Clean and prepare data for SQLite
        const cleanedData = this.sanitizeDataForSQLite(data);
        const columns = Object.keys(cleanedData);
        const quotedColumns = columns.map(col => `"${col}" = ?`);
        const setClause = quotedColumns.join(', ');
        const values = [...Object.values(cleanedData), id];

        const sql = `UPDATE ${cacheTableName} SET ${setClause} WHERE id = ?`;

        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(...values);

            if (result.changes > 0) {
                this.updateCacheMetadata(tableName);
                return this.get(tableName, id);
            }
            return null;
        } catch (error) {
            console.error(`Error updating ${cacheTableName}:`, error);
            console.error('SQL:', sql);
            console.error('Columns:', columns);
            console.error('Values:', values);
            console.error('Data types:', values.map(v => typeof v));
            throw error;
        }
    }

    delete(tableName, id) {
        if (!this.isInitialized) throw new Error('Database not initialized');
        
        const cacheTableName = `cache_${tableName}`;
        const sql = `DELETE FROM ${cacheTableName} WHERE id = ?`;
        
        try {
            const stmt = this.db.prepare(sql);
            const result = stmt.run(id);
            
            if (result.changes > 0) {
                this.updateCacheMetadata(tableName);
            }
            
            return result.changes > 0;
        } catch (error) {
            console.error(`Error deleting from ${cacheTableName}:`, error);
            throw error;
        }
    }

    // Cache management
    updateCacheMetadata(tableName) {
        const sql = `
            INSERT OR REPLACE INTO cache_metadata (table_name, last_sync, record_count)
            VALUES (?, CURRENT_TIMESTAMP, (SELECT COUNT(*) FROM cache_${tableName}))
        `;
        
        try {
            const stmt = this.db.prepare(sql);
            stmt.run(tableName);
        } catch (error) {
            console.error('Error updating cache metadata:', error);
        }
    }

    getCacheMetadata(tableName) {
        const sql = 'SELECT * FROM cache_metadata WHERE table_name = ?';
        
        try {
            const stmt = this.db.prepare(sql);
            return stmt.get(tableName);
        } catch (error) {
            console.error('Error getting cache metadata:', error);
            return null;
        }
    }

    // Sync queue operations
    addToSyncQueue(tableName, operation, recordId, data) {
        const sql = `
            INSERT INTO sync_queue (table_name, operation, record_id, data)
            VALUES (?, ?, ?, ?)
        `;
        
        try {
            const stmt = this.db.prepare(sql);
            return stmt.run(tableName, operation, recordId, JSON.stringify(data));
        } catch (error) {
            console.error('Error adding to sync queue:', error);
            throw error;
        }
    }

    getSyncQueue() {
        const sql = 'SELECT * FROM sync_queue WHERE status = ? ORDER BY timestamp ASC';
        
        try {
            const stmt = this.db.prepare(sql);
            return stmt.all('pending');
        } catch (error) {
            console.error('Error getting sync queue:', error);
            return [];
        }
    }

    updateSyncQueueStatus(id, status, errorMessage = null) {
        const sql = 'UPDATE sync_queue SET status = ?, error_message = ? WHERE id = ?';
        
        try {
            const stmt = this.db.prepare(sql);
            stmt.run(status, errorMessage, id);
        } catch (error) {
            console.error('Error updating sync queue status:', error);
        }
    }

    clearSyncQueue() {
        const sql = 'DELETE FROM sync_queue WHERE status = ?';

        try {
            const stmt = this.db.prepare(sql);
            stmt.run('completed');
        } catch (error) {
            console.error('Error clearing sync queue:', error);
        }
    }

    // Clear all data from a table
    clear(table) {
        try {
            // First check if table exists
            const checkSql = `SELECT name FROM sqlite_master WHERE type='table' AND name=?`;
            const checkStmt = this.db.prepare(checkSql);
            const tableExists = checkStmt.get(table);

            if (!tableExists) {
                // Table does not exist, skipping clear operation
                return 0;
            }

            const sql = `DELETE FROM ${table}`;
            const stmt = this.db.prepare(sql);
            const result = stmt.run();
            // Cleared records from table
            return result.changes;
        } catch (error) {
            console.error(`Error clearing table ${table}:`, error);
            return 0;
        }
    }

    // Replace all records in a table with new data
    replaceAll(tableName, data) {
        if (!this.isInitialized) throw new Error('Database not initialized');
        if (!Array.isArray(data)) throw new Error('Data must be an array');

        const cacheTableName = `cache_${tableName}`;
        
        try {
            this.db.transaction(() => {
                // Delete all existing records
                this.db.prepare(`DELETE FROM ${cacheTableName}`).run();
                
                // Insert new records
                if (data.length > 0) {
                    // Get column names from the first record
                    const columns = Object.keys(data[0]);
                    const placeholders = columns.map(() => '?').join(', ');
                    const columnNames = columns.join(', ');
                    
                    const insertStmt = this.db.prepare(`
                        INSERT INTO ${cacheTableName} (${columnNames}, cached_at, sync_status)
                        VALUES (${placeholders}, CURRENT_TIMESTAMP, 'synced')
                    `);
                    
                    for (const record of data) {
                        const sanitizedData = this.sanitizeDataForSQLite(record);
                        const values = columns.map(col => sanitizedData[col]);
                        insertStmt.run(...values);
                    }
                }
            })();
            
            // Replaced all records in cache table
            return true;
            
        } catch (error) {
            console.error(`Failed to replace all records in ${cacheTableName}:`, error);
            throw error;
        }
    }

    // Transaction support
    transaction(callback) {
        if (!this.isInitialized) throw new Error('Database not initialized');
        
        const transaction = this.db.transaction(callback);
        return transaction();
    }

    // Database maintenance
    vacuum() {
        if (!this.isInitialized) return;
        
        try {
            this.db.exec('VACUUM');
            // Database vacuum completed
        } catch (error) {
            console.error('Error during database vacuum:', error);
        }
    }

    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
            this.isInitialized = false;
            // SQLite database closed
        }
    }

    // Get database statistics
    getStats() {
        if (!this.isInitialized) return null;
        
        try {
            const tables = ['people', 'pets', 'incidents', 'addresses', 'bikes'];
            const stats = {};
            
            tables.forEach(table => {
                const sql = `SELECT COUNT(*) as count FROM cache_${table}`;
                const stmt = this.db.prepare(sql);
                const result = stmt.get();
                stats[table] = result.count;
            });
            
            // Sync queue stats
            const queueSql = 'SELECT status, COUNT(*) as count FROM sync_queue GROUP BY status';
            const queueStmt = this.db.prepare(queueSql);
            const queueResults = queueStmt.all();
            
            stats.syncQueue = {};
            queueResults.forEach(row => {
                stats.syncQueue[row.status] = row.count;
            });
            
            return stats;
        } catch (error) {
            console.error('Error getting database stats:', error);
            return null;
        }
    }

    async migrateTables() {
        // Check if database is available before attempting migrations
        if (!this.db) {
            console.warn('Skipping table migrations: Database not available');
            return;
        }

        // Note: isInitialized check removed because this method is only called during initialization
        // and isInitialized is set to true AFTER this method completes

        try {
            // Running SQLite table migrations

            /*
             * DEVELOPER NOTE: Adding New Columns to SQLite Tables
             *
             * To add new columns to any table, simply:
             * 1. Add the column to the CREATE TABLE statement in createTables()
             * 2. Add the column to the appropriate array below (e.g., incidentColumns)
             * 3. The migration system will automatically handle the rest!
             *
             * No manual steps required - the system will:
             * - Detect missing columns on app startup
             * - Add them automatically using ALTER TABLE
             * - Handle errors gracefully if columns already exist
             * - Work seamlessly with cache rebuilds
             */

            // Check if incidents table needs migration
            const incidentsColumns = this.db.prepare("PRAGMA table_info(cache_incidents)").all();
            const columnNames = incidentsColumns.map(col => col.name);
            
            // Add missing columns to incidents table using the new helper method
            const incidentColumns = [
                { name: 'incident_date', type: 'DATE' },
                { name: 'incident_time', type: 'TIME' },
                { name: 'incident_type', type: 'TEXT' },
                { name: 'description', type: 'TEXT' },
                { name: 'priority', type: 'TEXT' },
                { name: 'status', type: 'TEXT' },
                { name: 'assigned_ranger', type: 'TEXT' },
                { name: 'coordinates', type: 'TEXT' },
                { name: 'reported_by', type: 'TEXT' },
                { name: 'dispatch_notes', type: 'TEXT' },
                { name: 'log_entries', type: 'TEXT' },
                { name: 'police_notified', type: 'BOOLEAN DEFAULT 0' },
                { name: 'police_file_number', type: 'TEXT' },
                { name: 'tags', type: 'TEXT' },
                { name: 'is_urgent', type: 'BOOLEAN DEFAULT 0' },
                { name: 'incident_number', type: 'TEXT' },
                { name: 'street_address', type: 'TEXT' },
                { name: 'city', type: 'TEXT' },
                { name: 'province', type: 'TEXT' },
                { name: 'postal_code', type: 'TEXT' },
                { name: 'country', type: 'TEXT' },
                { name: 'location_notes', type: 'TEXT' },
                { name: 'address_search', type: 'TEXT' },
                // Enhanced incident tracking fields
                { name: 'incident_people', type: 'TEXT' }, // JSON string for SQLite
                { name: 'services_offered', type: 'TEXT' }, // JSON array as string
                { name: 'services_provided', type: 'TEXT' }, // JSON array as string
                { name: 'resources_distributed', type: 'TEXT' }, // JSON array as string
                { name: 'referrals_made', type: 'TEXT' }, // JSON array as string
                { name: 'scene_safety', type: 'TEXT' },
                { name: 'safety_concerns', type: 'TEXT' },
                { name: 'substance_indicators', type: 'TEXT' }, // JSON array as string
                { name: 'environmental_factors', type: 'TEXT' }, // JSON array as string
                { name: 'follow_up_required', type: 'BOOLEAN DEFAULT 0' },
                { name: 'follow_up_notes', type: 'TEXT' },
                { name: 'follow_up_date', type: 'DATE' },
                { name: 'initial_observations', type: 'TEXT' },
                { name: 'actions_taken', type: 'TEXT' },
                { name: 'weather_conditions', type: 'TEXT' },
                { name: 'scene_conditions', type: 'TEXT' },
                // Emergency services fields
                { name: 'fire_department_called', type: 'BOOLEAN DEFAULT 0' },
                { name: 'fire_unit_number', type: 'TEXT' },
                { name: 'paramedics_called', type: 'BOOLEAN DEFAULT 0' },
                { name: 'ambulance_unit_number', type: 'TEXT' },
                { name: 'ems_response_time', type: 'TEXT' }, // TIME as TEXT in SQLite
                { name: 'hospital_transport_offered', type: 'BOOLEAN DEFAULT 0' },
                { name: 'transport_declined', type: 'BOOLEAN DEFAULT 0' },
                { name: 'transport_decline_reason', type: 'TEXT' },
                { name: 'hospital_destination', type: 'TEXT' },
                { name: 'ems_personnel_names', type: 'TEXT' },
                { name: 'medical_assessment_notes', type: 'TEXT' },
                { name: 'people_involved', type: 'TEXT' },
                // Bylaw agency fields
                { name: 'bylaw_notified', type: 'BOOLEAN DEFAULT 0' },
                { name: 'bylaw_file_number', type: 'TEXT' },
                { name: 'bylaw_officers_attending', type: 'TEXT' },
                { name: 'bylaw_response_time', type: 'TIME' },
                { name: 'bylaw_personnel_names', type: 'TEXT' },
                { name: 'bylaw_enforcement_action', type: 'TEXT' },
                { name: 'bylaw_notes', type: 'TEXT' }
            ];

            // Use the new helper method for cleaner, more reliable column addition
            // Adding missing columns to cache_incidents
            this.addColumnsIfNotExist('incidents', incidentColumns);

            // Check if property_records table needs migration
            try {
                const propertyColumns = [
                    { name: 'property_number', type: 'TEXT' },
                    { name: 'category', type: 'TEXT' },
                    { name: 'brand', type: 'TEXT' },
                    { name: 'model', type: 'TEXT' },
                    { name: 'color', type: 'TEXT' },
                    { name: 'estimated_value', type: 'REAL' },
                    { name: 'condition', type: 'TEXT' },
                    { name: 'found_coordinates', type: 'TEXT' },
                    { name: 'found_time', type: 'TIME' },
                    { name: 'investigation_notes', type: 'TEXT' },
                    { name: 'owner_name', type: 'TEXT' },
                    { name: 'owner_contact', type: 'TEXT' },
                    { name: 'owner_address', type: 'TEXT' },
                    { name: 'return_location', type: 'TEXT' },
                    { name: 'returned_to', type: 'TEXT' },
                    { name: 'returned_date', type: 'DATE' },
                    { name: 'returned_time', type: 'TIME' },
                    { name: 'police_file_number', type: 'TEXT' },
                    { name: 'police_officer', type: 'TEXT' },
                    { name: 'handed_to_police_date', type: 'DATE' },
                    { name: 'photos', type: 'TEXT' },
                    { name: 'disposition_type', type: 'TEXT' },
                    { name: 'disposition_date', type: 'DATE' },
                    { name: 'disposition_notes', type: 'TEXT' },
                    { name: 'recovery_method', type: 'TEXT' },
                    // Enhanced property return fields
                    { name: 'return_type', type: 'TEXT' },
                    { name: 'return_recipient_name', type: 'TEXT' },
                    { name: 'return_recipient_phone', type: 'TEXT' },
                    { name: 'return_recipient_email', type: 'TEXT' },
                    { name: 'return_business_name', type: 'TEXT' },
                    { name: 'return_business_address', type: 'TEXT' },
                    { name: 'return_contact_person', type: 'TEXT' },
                    { name: 'return_individual_id_type', type: 'TEXT' },
                    { name: 'return_individual_id_number', type: 'TEXT' },
                    { name: 'return_relationship', type: 'TEXT' },
                    { name: 'return_method', type: 'TEXT' },
                    { name: 'return_accepted_by', type: 'TEXT' },
                    { name: 'return_receipt_number', type: 'TEXT' },
                    { name: 'return_notes', type: 'TEXT' },
                    { name: 'return_photos', type: 'TEXT' },
                    { name: 'return_verification_method', type: 'TEXT' }
                ];

                // Adding missing columns to cache_property_records
                this.addColumnsIfNotExist('property_records', propertyColumns);
            } catch (error) {
                console.warn('Property table migration failed (this is normal for new databases):', error.message);
            }

            // Migrate cache_people_activities table
            try {
                const peopleActivitiesColumns = [
                    { name: 'updated_at', type: 'DATETIME' }
                ];

                // Adding missing columns to cache_people_activities
                this.addColumnsIfNotExist('people_activities', peopleActivitiesColumns);
            } catch (error) {
                console.warn('People activities table migration failed (this is normal for new databases):', error.message);
            }

            // Migrate cache_medical_issues table - comprehensive medical tracking
            try {
                const medicalIssuesColumns = [
                    // Category and classification
                    { name: 'category', type: 'TEXT NOT NULL DEFAULT \'Other\'' },
                    { name: 'subcategory', type: 'TEXT' },
                    { name: 'source_type', type: 'TEXT NOT NULL DEFAULT \'Observation\'' },
                    
                    // Enhanced basic medical info
                    { name: 'status', type: 'TEXT' },
                    { name: 'is_active', type: 'BOOLEAN DEFAULT 1' },
                    { name: 'treatment_notes', type: 'TEXT' },
                    
                    // Infection-specific fields
                    { name: 'infection_site', type: 'TEXT' },
                    { name: 'infection_type', type: 'TEXT' },
                    { name: 'suspected_cause', type: 'TEXT' },
                    { name: 'wound_description', type: 'TEXT' },
                    { name: 'requires_immediate_care', type: 'BOOLEAN DEFAULT 0' },
                    { name: 'antibiotics_prescribed', type: 'TEXT' },
                    
                    // Mental health specific fields
                    { name: 'mental_health_crisis', type: 'BOOLEAN DEFAULT 0' },
                    { name: 'risk_assessment', type: 'TEXT' },
                    { name: 'suicide_risk', type: 'BOOLEAN DEFAULT 0' },
                    
                    // Addiction specific fields
                    { name: 'substance_type', type: 'TEXT' },
                    { name: 'route_of_administration', type: 'TEXT' },
                    { name: 'frequency_of_use', type: 'TEXT' },
                    { name: 'last_use_date', type: 'DATE' },
                    { name: 'withdrawal_symptoms', type: 'TEXT' },
                    { name: 'seeking_treatment', type: 'BOOLEAN DEFAULT 0' }
                ];

                // Adding comprehensive medical tracking columns to cache_medical_issues
                this.addColumnsIfNotExist('medical_issues', medicalIssuesColumns);
            } catch (error) {
                console.warn('Medical issues table migration failed (this is normal for new databases):', error.message);
            }

            // Migrate cache_supply_provisions table
            try {
                const supplyProvisionsColumns = [
                    { name: 'updated_at', type: 'DATETIME' },
                    { name: 'created_by', type: 'TEXT' },
                    { name: 'updated_by', type: 'TEXT' }
                ];

                // Adding missing columns to cache_supply_provisions
                this.addColumnsIfNotExist('supply_provisions', supplyProvisionsColumns);
            } catch (error) {
                console.warn('Supply provisions table migration failed (this is normal for new databases):', error.message);
            }

            // SQLite table migrations completed
        } catch (error) {
            console.warn('Migration failed (this is normal for new databases):', error.message);
        }
    }

    /**
     * Internal helper method to add columns during initialization
     * This bypasses the isInitialized check since it's used during setup
     *
     * @param {string} tableName - Name of the table (without cache_ prefix)
     * @param {string} columnName - Name of the column to add
     * @param {string} columnType - SQL type of the column (e.g., 'TEXT', 'INTEGER', 'BOOLEAN DEFAULT 0')
     */
    _addColumnDuringInit(tableName, columnName, columnType) {
        if (!this.db) {
            console.warn(`Cannot add column ${columnName} to ${tableName}: Database not available`);
            return false;
        }

        const cacheTableName = `cache_${tableName}`;

        try {
            // Check if column already exists
            const columns = this.db.prepare(`PRAGMA table_info(${cacheTableName})`).all();
            const existingColumns = columns.map(col => col.name);

            if (existingColumns.includes(columnName)) {
                // Column already exists - no logging needed for normal operation
                return true;
            }

            // Add the column
            const sql = `ALTER TABLE ${cacheTableName} ADD COLUMN ${columnName} ${columnType}`;
            this.db.prepare(sql).run();
            // Added column to cache table
            return true;

        } catch (error) {
            console.warn(`Failed to add column ${columnName} to ${cacheTableName}:`, error.message);
            return false;
        }
    }

    /**
     * Helper method to safely add a column to a table
     * This makes it easy for developers to add new columns without manual steps
     *
     * @param {string} tableName - Name of the table (without cache_ prefix)
     * @param {string} columnName - Name of the column to add
     * @param {string} columnType - SQL type of the column (e.g., 'TEXT', 'INTEGER', 'BOOLEAN DEFAULT 0')
     */
    addColumnIfNotExists(tableName, columnName, columnType) {
        if (!this.db) {
            console.warn(`Cannot add column ${columnName} to ${tableName}: Database not available`);
            return false;
        }

        if (!this.isInitialized) {
            console.warn(`Cannot add column ${columnName} to ${tableName}: Database not initialized`);
            return false;
        }

        const cacheTableName = `cache_${tableName}`;

        try {
            // Check if column already exists
            const columns = this.db.prepare(`PRAGMA table_info(${cacheTableName})`).all();
            const existingColumns = columns.map(col => col.name);

            if (existingColumns.includes(columnName)) {
                // Column already exists - no logging needed for normal operation
                return true;
            }

            // Add the column
            const sql = `ALTER TABLE ${cacheTableName} ADD COLUMN ${columnName} ${columnType}`;
            this.db.prepare(sql).run();
            // Added column to cache table
            return true;

        } catch (error) {
            console.warn(`Failed to add column ${columnName} to ${cacheTableName}:`, error.message);
            return false;
        }
    }

    /**
     * Helper method to add multiple columns at once
     *
     * @param {string} tableName - Name of the table (without cache_ prefix)
     * @param {Array} columns - Array of {name, type} objects
     */
    addColumnsIfNotExist(tableName, columns) {
        if (!Array.isArray(columns)) {
            console.warn('addColumnsIfNotExist: columns must be an array');
            return false;
        }

        let allSuccess = true;
        for (const column of columns) {
            if (!column.name || !column.type) {
                console.warn('addColumnsIfNotExist: Each column must have name and type properties');
                allSuccess = false;
                continue;
            }

            // Use the appropriate method based on initialization state
            const success = this.isInitialized
                ? this.addColumnIfNotExists(tableName, column.name, column.type)
                : this._addColumnDuringInit(tableName, column.name, column.type);

            if (!success) {
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    /**
     * Clear all cache tables to ensure fresh data from Supabase
     */
    async clearAllCaches() {
        if (!this.isInitialized) {
            console.warn('SQLite not initialized, cannot clear caches');
            return;
        }

        try {
            // Clearing all SQLite cache tables

            // Get all cache table names
            const tables = this.db.prepare(`
                SELECT name FROM sqlite_master
                WHERE type='table' AND name LIKE 'cache_%'
            `).all();

            // Clear each cache table
            for (const table of tables) {
                try {
                    this.db.prepare(`DELETE FROM ${table.name}`).run();
                    // Cleared table
                } catch (error) {
                    console.warn(`⚠️ Failed to clear ${table.name}:`, error.message);
                }
            }

            // Clear sync queue
            try {
                this.db.prepare('DELETE FROM sync_queue').run();
                // Cleared sync_queue
            } catch (error) {
                console.warn('⚠️ Failed to clear sync_queue:', error.message);
            }

            // Clear conflict log
            try {
                this.db.prepare('DELETE FROM conflict_log').run();
                // Cleared conflict_log
            } catch (error) {
                console.warn('⚠️ Failed to clear conflict_log:', error.message);
            }

            // Reset cache metadata
            try {
                this.db.prepare('DELETE FROM cache_metadata').run();
                // Cleared cache_metadata
            } catch (error) {
                console.warn('⚠️ Failed to clear cache_metadata:', error.message);
            }

            // SQLite cache clearing completed
        } catch (error) {
            console.error('❌ Error clearing SQLite caches:', error);
        }
    }

    /**
     * Check if the database has any cached data
     */
    async hasAnyData() {
        if (!this.isInitialized) {
            return false;
        }

        try {
            // Check main cache tables for any data
            const mainTables = ['cache_people', 'cache_incidents', 'cache_property_records'];

            for (const tableName of mainTables) {
                try {
                    const count = this.db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
                    if (count && count.count > 0) {
                        return true;
                    }
                } catch (error) {
                    // Table might not exist yet, continue checking
                    console.debug(`Table ${tableName} not found or error checking:`, error.message);
                }
            }

            return false;
        } catch (error) {
            console.error('Error checking for cached data:', error);
            return false;
        }
    }
}
