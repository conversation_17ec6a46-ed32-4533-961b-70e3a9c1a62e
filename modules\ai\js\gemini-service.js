// Gemini AI Service for S.T.E.V.I Retro - Google Gemini API Integration
export class GeminiService {
    constructor(dataManager = null, vaultManagerOrConfig = null) {
        this.data = dataManager;

        // Handle both VaultManager and ConfigManager
        if (vaultManagerOrConfig && vaultManagerOrConfig.getGeminiApiKey) {
            // It's a ConfigManager
            this.config = vaultManagerOrConfig;
            this.vault = vaultManagerOrConfig.vaultManager;
        } else {
            // It's a VaultManager or null
            this.vault = vaultManagerOrConfig;
            this.config = null;
        }

        this.apiKey = null;
        this.cache = new Map();
        this.cacheTimeout = 30 * 60 * 1000; // 30 minutes cache for models
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
    }

    /**
     * Get Gemini API key from Vault
     * @returns {Promise<string|null>} The Gemini API key
     */
    async getApiKey() {
        if (!this.apiKey) {
            try {
                console.log('GeminiService: Attempting to get Gemini API key...');

                // Try config manager first (preferred method)
                if (this.config && this.config.getGeminiApiKey) {
                    console.log('GeminiService: Using ConfigManager to get API key');
                    this.apiKey = await this.config.getGeminiApiKey();
                }
                // Fallback to vault manager directly
                else if (this.vault && this.vault.getGeminiApiKey) {
                    console.log('GeminiService: Using VaultManager directly to get API key');
                    this.apiKey = await this.vault.getGeminiApiKey();
                }

                if (this.apiKey) {
                    console.log('GeminiService: Successfully retrieved API key');
                } else {
                    console.warn('GeminiService: No API key available from vault or config');
                }
            } catch (error) {
                console.error('GeminiService: Error getting Gemini API key:', error);
            }
        }
        return this.apiKey;
    }

    /**
     * Get AI settings from database
     * @returns {Promise<Object>} AI settings object
     */
    async getAiSettings() {
        if (!this.data) {
            return {
                gemini_model: 'gemini-1.5-flash',
                system_prompt: 'You are a professional writing assistant for social service field workers. Enhance the provided text to be clear, professional, and comprehensive while maintaining the original meaning and important details. Focus on improving clarity, organization, and professional tone suitable for incident reports and case documentation.'
            };
        }

        try {
            console.log('GeminiService: Fetching AI settings from database...');
            
            // Use direct Supabase client instead of data.query to avoid RPC dependency
            const supabase = await this.data.getSupabaseClient();
            console.log('GeminiService: Got supabase client:', {
                clientExists: !!supabase,
                clientType: typeof supabase,
                hasFrom: supabase && typeof supabase.from === 'function',
                fromResult: supabase ? typeof supabase.from('core.system_settings') : 'no client'
            });
            
            if (!supabase) {
                throw new Error('Supabase client not available');
            }
            
            // Get both settings in parallel
            const [modelResult, promptResult] = await Promise.all([
                supabase.schema('core').from('system_settings')
                    .select('setting_value')
                    .eq('setting_key', 'ai_gemini_model')
                    .limit(1),
                supabase.schema('core').from('system_settings')
                    .select('setting_value')
                    .eq('setting_key', 'ai_system_prompt')
                    .limit(1)
            ]);

            if (modelResult.error) throw modelResult.error;
            if (promptResult.error) throw promptResult.error;

            const modelSetting = modelResult.data;
            const promptSetting = promptResult.data;

            const gemini_model = modelSetting?.[0]?.setting_value || 'gemini-1.5-flash';
            const system_prompt = promptSetting?.[0]?.setting_value || 
                'You are a professional writing assistant for social service field workers. Enhance the provided text to be clear, professional, and comprehensive while maintaining the original meaning and important details. Focus on improving clarity, organization, and professional tone suitable for incident reports and case documentation.';

            console.log('GeminiService: Retrieved AI settings:', { gemini_model, system_prompt: !!system_prompt });
            console.log('GeminiService: Supabase client validation:', {
                clientType: typeof supabase,
                hasFrom: typeof supabase.from === 'function',
                clientKeys: Object.keys(supabase || {}).slice(0, 10)
            });
            
            return { gemini_model, system_prompt };
        } catch (error) {
            console.error('GeminiService: Error fetching AI settings from database:', error);
            return {
                gemini_model: 'gemini-1.5-flash',
                system_prompt: 'You are a professional writing assistant for social service field workers. Enhance the provided text to be clear, professional, and comprehensive while maintaining the original meaning and important details. Focus on improving clarity, organization, and professional tone suitable for incident reports and case documentation.'
            };
        }
    }

    /**
     * Update AI setting in database
     * @param {string} settingKey - The setting key to update
     * @param {string} settingValue - The new setting value
     * @returns {Promise<boolean>} Success status
     */
    async updateAiSetting(settingKey, settingValue) {
        if (!this.data) {
            console.warn('GeminiService: No data manager available for updating settings');
            return false;
        }

        try {
            console.log(`GeminiService: Updating AI setting ${settingKey}`);
            
            // Use direct Supabase client instead of data.query to avoid RPC dependency
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            // First, try to find existing setting
            const { data: existingSettings, error: selectError } = await supabase
                .schema('core').from('system_settings')
                .select('id')
                .eq('setting_key', settingKey)
                .limit(1);

            if (selectError) {
                throw selectError;
            }

            const updateData = {
                setting_value: settingValue,
                updated_at: new Date().toISOString()
            };

            if (existingSettings && existingSettings.length > 0) {
                // Update existing setting
                const { error: updateError } = await supabase
                    .schema('core').from('system_settings')
                    .update(updateData)
                    .eq('setting_key', settingKey);

                if (updateError) {
                    throw updateError;
                }
            } else {
                // Insert new setting
                const { error: insertError } = await supabase
                    .schema('core').from('system_settings')
                    .insert({
                        setting_key: settingKey,
                        setting_value: settingValue,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    });

                if (insertError) {
                    throw insertError;
                }
            }

            console.log(`GeminiService: Successfully updated ${settingKey}`);
            return true;
        } catch (error) {
            console.error(`GeminiService: Error updating ${settingKey}:`, error);
            return false;
        }
    }

    /**
     * Get available Gemini models from API
     * @returns {Promise<Array>} Array of model objects
     */
    async getAvailableModels() {
        const cacheKey = 'gemini_models';
        const cached = this.cache.get(cacheKey);
        
        // Return cached data if still valid
        if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            console.log('GeminiService: Returning cached models');
            return cached.data;
        }

        try {
            const apiKey = await this.getApiKey();
            if (!apiKey) {
                console.warn('GeminiService: No API key available, returning default models');
                return this.getDefaultModels();
            }

            console.log('GeminiService: Fetching available models from Gemini API');
            console.log('GeminiService: API Key available:', !!apiKey);
            console.log('GeminiService: API URL:', `${this.baseUrl}/models`);
            
            const response = await fetch(`${this.baseUrl}/models?key=${apiKey}`);
            
            console.log('GeminiService: API Response status:', response.status);
            console.log('GeminiService: API Response headers:', Object.fromEntries(response.headers.entries()));
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('GeminiService: API Error response:', errorText);
                throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();
            console.log('GeminiService: Raw API response:', data);
            
            if (!data.models || !Array.isArray(data.models)) {
                console.error('GeminiService: Invalid API response format - no models array:', data);
                throw new Error('Invalid API response format - no models array found');
            }

            console.log(`GeminiService: Found ${data.models.length} total models from API`);
            
            // Filter and format models
            const filteredModels = data.models.filter(model => {
                const hasGenerateContent = model.supportedGenerationMethods?.includes('generateContent');
                const isNotEmbedding = !model.name.toLowerCase().includes('embedding');
                const isNotVision = !model.name.toLowerCase().includes('vision'); // Exclude vision models for text enhancement
                
                console.log(`GeminiService: Model ${model.name} - generateContent: ${hasGenerateContent}, notEmbedding: ${isNotEmbedding}, notVision: ${isNotVision}`);
                
                return hasGenerateContent && isNotEmbedding && isNotVision;
            });

            console.log(`GeminiService: ${filteredModels.length} models after filtering`);

            const models = filteredModels.map(model => {
                const formattedModel = {
                    id: model.name.replace('models/', ''),
                    name: model.displayName || model.name.replace('models/', ''),
                    description: model.description || 'No description available'
                };
                console.log('GeminiService: Formatted model:', formattedModel);
                return formattedModel;
            });

            if (models.length === 0) {
                console.warn('GeminiService: No suitable models found after filtering, using defaults');
                return this.getDefaultModels();
            }

            // Cache the result
            this.cache.set(cacheKey, {
                data: models,
                timestamp: Date.now()
            });

            console.log(`GeminiService: Successfully retrieved ${models.length} models from API:`, models.map(m => m.name));
            return models;

        } catch (error) {
            console.error('GeminiService: Error fetching models:', error);
            
            // Return cached data if available, even if expired
            if (cached) {
                console.log('GeminiService: Returning expired cached models due to error');
                return cached.data;
            }
            
            // Return default models as fallback
            console.warn('GeminiService: Using default models as final fallback');
            return this.getDefaultModels();
        }
    }

    /**
     * Get default models when API is unavailable
     * @returns {Array} Default model list
     */
    getDefaultModels() {
        return [
            {
                id: 'gemini-1.5-flash',
                name: 'Gemini 1.5 Flash',
                description: 'Fast and efficient model for most text tasks'
            },
            {
                id: 'gemini-1.5-flash-8b',
                name: 'Gemini 1.5 Flash-8B',
                description: 'Smaller, faster version of Gemini 1.5 Flash'
            },
            {
                id: 'gemini-1.5-pro',
                name: 'Gemini 1.5 Pro',
                description: 'Advanced model for complex reasoning and analysis'
            },
            {
                id: 'gemini-2.0-flash-exp',
                name: 'Gemini 2.0 Flash (Experimental)',
                description: 'Latest experimental Gemini 2.0 Flash model'
            },
            {
                id: 'gemini-exp-1206',
                name: 'Gemini Experimental 1206',
                description: 'Experimental Gemini model from December 2024'
            },
            {
                id: 'gemini-exp-1121',
                name: 'Gemini Experimental 1121',
                description: 'Experimental Gemini model from November 2024'
            },
            {
                id: 'gemini-pro',
                name: 'Gemini Pro',
                description: 'Legacy Gemini Pro model for general-purpose text generation'
            }
        ];
    }

    /**
     * Force refresh models from API (clear cache)
     * @returns {Promise<Array>} Fresh model list
     */
    async refreshModels() {
        console.log('GeminiService: Force refreshing models from API');
        this.cache.delete('gemini_models');
        return await this.getAvailableModels();
    }

    /**
     * Enhance text using Gemini API
     * @param {string} text - Text to enhance
     * @param {string} customSystemPrompt - Optional custom system prompt
     * @param {Object} context - Optional context object (e.g., incident record)
     * @returns {Promise<string>} Enhanced text
     */
    async enhanceText(text, customSystemPrompt = null, context = null) {
        if (!text || text.trim().length === 0) {
            throw new Error('No text provided for enhancement');
        }

        try {
            const apiKey = await this.getApiKey();
            if (!apiKey) {
                throw new Error('Gemini API key not available');
            }

            const settings = await this.getAiSettings();
            const systemPrompt = customSystemPrompt || settings.system_prompt;
            const model = settings.gemini_model;

            console.log(`GeminiService: Enhancing text using model ${model}`);

            // Build the prompt with context if provided
            let promptText = systemPrompt;
            
            if (context) {
                promptText += `\n\nContext Information:\n`;
                
                // If context is an incident record, format it appropriately
                if (context.incident_number) {
                    promptText += `Incident #${context.incident_number}\n`;
                    if (context.incident_type) promptText += `Type: ${context.incident_type}\n`;
                    if (context.priority) promptText += `Priority: ${context.priority}\n`;
                    if (context.status) promptText += `Status: ${context.status}\n`;
                    if (context.location_name) promptText += `Location: ${context.location_name}\n`;
                    if (context.summary) promptText += `Summary: ${context.summary}\n`;
                    
                    // Include previous log entries if available
                    if (context.log_entries) {
                        try {
                            const logEntries = typeof context.log_entries === 'string' 
                                ? JSON.parse(context.log_entries) 
                                : context.log_entries;
                            
                            if (Array.isArray(logEntries) && logEntries.length > 0) {
                                promptText += `\nPrevious Log Entries:\n`;
                                logEntries.forEach((entry, index) => {
                                    const eventTime = new Date(entry.event_time || entry.timestamp).toLocaleString();
                                    promptText += `${index + 1}. [${entry.entry_type}] ${eventTime} - ${entry.content}\n`;
                                });
                            }
                        } catch (error) {
                            console.warn('Error parsing log entries for context:', error);
                        }
                    }
                } else {
                    // Generic context handling for other types
                    promptText += JSON.stringify(context, null, 2);
                }
                
                promptText += `\nPlease use this context to provide more relevant and informed enhancements to the text below.`;
            }
            
            promptText += `\n\nOriginal text to enhance:\n\n${text}`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: promptText
                    }]
                }],
                generationConfig: {
                    temperature: 0.3, // Lower temperature for more consistent, professional output
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                }
            };

            const response = await fetch(`${this.baseUrl}/models/${model}:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Gemini API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            
            if (!data.candidates || data.candidates.length === 0) {
                throw new Error('No response generated from Gemini API');
            }

            const enhancedText = data.candidates[0]?.content?.parts?.[0]?.text;
            
            if (!enhancedText) {
                throw new Error('Empty response from Gemini API');
            }

            console.log('GeminiService: Successfully enhanced text');
            return enhancedText.trim();

        } catch (error) {
            console.error('GeminiService: Error enhancing text:', error);
            throw error;
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
        console.log('GeminiService: Cache cleared');
    }

    /**
     * Get cache status
     * @returns {Object} Cache information
     */
    getCacheStatus() {
        const cached = this.cache.get('gemini_models');
        if (!cached) {
            return { cached: false, age: 0 };
        }

        const age = Date.now() - cached.timestamp;
        return {
            cached: true,
            age: Math.floor(age / 1000), // Age in seconds
            valid: age < this.cacheTimeout
        };
    }
}