/**
 * Narrative Form Manager
 * 
 * Handles all narrative form operations including modal creation,
 * WYSIWYG editing, validation, and form submission.
 */

import { BaseManager } from '../../shared/base-manager.js';

export class NarrativeFormManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, configManager) {
        super(dataManager, authManager, uiManager, configManager);
        
        this.narrativeManager = null;
        this.currentEditor = null;
    }

    // Set manager references
    setNarrativeManager(narrativeManager) {
        this.narrativeManager = narrativeManager;
    }

    // Show add entry form
    async showAddEntryForm() {
        const modal = this.createEntryModal();
        document.body.appendChild(modal);
        
        setTimeout(() => {
            const textarea = modal.querySelector('#narrative-content');
            if (textarea) textarea.focus();
        }, 100);

        return modal;
    }

    // Show edit entry form
    async showEditEntryForm(existingEntry) {
        const modal = this.createEntryModal(existingEntry);
        document.body.appendChild(modal);
        
        setTimeout(() => {
            const editor = modal.querySelector('#narrative-content');
            if (editor) {
                editor.focus();
                const range = document.createRange();
                const selection = window.getSelection();
                range.selectNodeContents(editor);
                range.collapse(false);
                selection.removeAllRanges();
                selection.addRange(range);
            }
        }, 100);

        return modal;
    }

    // Create entry modal
    createEntryModal(existingEntry = null) {
        const isEdit = existingEntry !== null;
        const modalId = `narrative-modal-${Date.now()}`;
        
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = modalId;
        
        modal.innerHTML = `
            <div class="modal-content narrative-modal">
                <div class="modal-header">
                    <h3>${isEdit ? 'Edit' : 'Add'} Narrative Entry</h3>
                    ${isEdit ? `<p class="editing-info">Editing ${existingEntry.entry_type.replace('_', ' ').toUpperCase()} entry from ${new Date(existingEntry.event_time || existingEntry.timestamp).toLocaleString()}</p>` : ''}
                    <button class="modal-close" onclick="window.narrativeFormManager.closeModal(this.closest('.modal-overlay'))">×</button>
                </div>
                <div class="modal-body">
                    <form id="narrative-entry-form">
                        <div class="form-field">
                            <label for="narrative-entry-type">Entry Type:</label>
                            <select id="narrative-entry-type" required>
                                <option value="">Select type...</option>
                                <option value="initial_observation" ${existingEntry?.entry_type === 'initial_observation' ? 'selected' : ''}>Initial Observation</option>
                                <option value="action_taken" ${existingEntry?.entry_type === 'action_taken' ? 'selected' : ''}>Action Taken</option>
                                <option value="status_update" ${existingEntry?.entry_type === 'status_update' ? 'selected' : ''}>Status Update</option>
                                <option value="follow_up" ${existingEntry?.entry_type === 'follow_up' ? 'selected' : ''}>Follow-up</option>
                                <option value="contact_made" ${existingEntry?.entry_type === 'contact_made' ? 'selected' : ''}>Contact Made</option>
                                <option value="note" ${existingEntry?.entry_type === 'note' ? 'selected' : ''}>General Note</option>
                                <option value="other" ${existingEntry?.entry_type === 'other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>
                        <div class="form-field">
                            <label for="narrative-entry-time">Event Time:</label>
                            <input type="datetime-local" id="narrative-entry-time" value="${existingEntry ? this.narrativeManager.formatDateTimeForInput(existingEntry.event_time || existingEntry.timestamp) : this.narrativeManager.getCurrentDateTimeLocal()}" />
                            <small class="field-help">
                                Set the specific time when this event occurred (defaults to current time).
                            </small>
                        </div>
                        <div class="form-field">
                            <label for="narrative-content">Content:</label>
                            <div class="text-formatting-toolbar" id="formatting-toolbar-${modalId}">
                                <button type="button" class="format-btn" data-format="bold" title="Bold (Ctrl+B)" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeFormManager.applyWysiwygFormat('bold');">
                                    <b>B</b>
                                </button>
                                <button type="button" class="format-btn" data-format="italic" title="Italic (Ctrl+I)" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeFormManager.applyWysiwygFormat('italic');">
                                    <i>I</i>
                                </button>
                                <button type="button" class="format-btn" data-format="underline" title="Underline (Ctrl+U)" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeFormManager.applyWysiwygFormat('underline');">
                                    <u>U</u>
                                </button>
                                <span class="toolbar-divider">|</span>
                                <button type="button" class="format-btn" data-format="bullet" title="Bullet List" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeFormManager.applyWysiwygFormat('bullet');">
                                    •
                                </button>
                                <button type="button" class="format-btn" data-format="number" title="Numbered List" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeFormManager.applyWysiwygFormat('number');">
                                    1.
                                </button>
                                <span class="toolbar-divider">|</span>
                                <button type="button" class="format-btn ai-enhance-btn" data-format="ai-enhance" title="Enhance with AI" onclick="event.preventDefault(); event.stopPropagation(); window.narrativeFormManager.showAiEnhanceModal();">
                                    🤖 AI
                                </button>
                            </div>
                            <div id="narrative-content" contenteditable="true" class="wysiwyg-editor" role="textbox" aria-label="Narrative content" data-placeholder="Enter detailed narrative content here...">${existingEntry ? this.narrativeManager.convertStoredToHtml(existingEntry.content) : ''}</div>
                            <small class="field-help">
                                Use the toolbar buttons above for formatting. Text will appear formatted as you type.
                            </small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="action-btn primary" onclick="window.narrativeFormManager.saveEntry('${modalId}')">
                        ${isEdit ? 'Update' : 'Save'} Entry
                    </button>
                    <button class="action-btn" onclick="window.narrativeFormManager.closeModal(this.closest('.modal-overlay'))">
                        Cancel
                    </button>
                </div>
            </div>
        `;
        
        this.setupTextFormatting(modal);
        
        return modal;
    }

    // Setup WYSIWYG text formatting
    setupTextFormatting(modal) {
        const editor = modal.querySelector('#narrative-content');
        if (!editor) return;

        this.currentEditor = editor;

        this.setupPlaceholder(editor);

        editor.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                e.preventDefault();
                e.stopPropagation();
                
                if (e.shiftKey) {
                    this.handleOutdent();
                } else {
                    this.handleIndent();
                }
                return;
            }
            
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault();
                        this.applyWysiwygFormat('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.applyWysiwygFormat('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        this.applyWysiwygFormat('underline');
                        break;
                }
            }
        });

        editor.addEventListener('paste', (e) => {
            e.preventDefault();
            const text = (e.clipboardData || window.clipboardData).getData('text/plain');
            document.execCommand('insertText', false, text);
        });
    }

    // Setup placeholder functionality
    setupPlaceholder(editor) {
        const placeholder = editor.getAttribute('data-placeholder');
        
        const showPlaceholder = () => {
            if (!editor.textContent.trim()) {
                editor.innerHTML = `<span class="placeholder-text">${placeholder}</span>`;
                editor.classList.add('empty');
            }
        };

        const hidePlaceholder = () => {
            const placeholderSpan = editor.querySelector('.placeholder-text');
            if (placeholderSpan) {
                editor.innerHTML = '';
                editor.classList.remove('empty');
            }
        };

        editor.addEventListener('focus', hidePlaceholder);
        editor.addEventListener('blur', showPlaceholder);
        editor.addEventListener('input', () => {
            if (editor.textContent.trim()) {
                editor.classList.remove('empty');
            } else {
                editor.classList.add('empty');
            }
        });

        if (!editor.textContent.trim()) {
            showPlaceholder();
        }
    }

    // Apply WYSIWYG formatting
    applyWysiwygFormat(format) {
        const editor = this.currentEditor;
        if (!editor) return;

        editor.focus();

        const placeholderSpan = editor.querySelector('.placeholder-text');
        if (placeholderSpan) {
            editor.innerHTML = '';
            editor.classList.remove('empty');
        }

        switch (format) {
            case 'bold':
                document.execCommand('bold', false, null);
                break;
            case 'italic':
                document.execCommand('italic', false, null);
                break;
            case 'underline':
                document.execCommand('underline', false, null);
                break;
            case 'bullet':
                document.execCommand('insertUnorderedList', false, null);
                break;
            case 'number':
                document.execCommand('insertOrderedList', false, null);
                break;
        }

        editor.focus();
    }

    // Handle indentation
    handleIndent() {
        const editor = this.currentEditor;
        if (!editor) return;

        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        const currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
            ? range.startContainer.parentElement 
            : range.startContainer;

        const listItem = currentElement.closest('li');
        if (listItem) {
            document.execCommand('indent', false, null);
        } else {
            const tabSpaces = '\u00A0\u00A0\u00A0\u00A0';
            document.execCommand('insertText', false, tabSpaces);
        }
        
        editor.focus();
    }

    // Handle outdent
    handleOutdent() {
        const editor = this.currentEditor;
        if (!editor) return;

        const selection = window.getSelection();
        if (selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        const currentElement = range.startContainer.nodeType === Node.TEXT_NODE 
            ? range.startContainer.parentElement 
            : range.startContainer;

        const listItem = currentElement.closest('li');
        if (listItem) {
            document.execCommand('outdent', false, null);
        } else {
            const textContent = range.startContainer.textContent || '';
            const cursorPosition = range.startOffset;
            const beforeCursor = textContent.substring(0, cursorPosition);
            
            const spaceMatch = beforeCursor.match(/[\u00A0\s]{1,4}$/);
            if (spaceMatch) {
                const newRange = document.createRange();
                newRange.setStart(range.startContainer, cursorPosition - spaceMatch[0].length);
                newRange.setEnd(range.startContainer, cursorPosition);
                newRange.deleteContents();
            }
        }
        
        editor.focus();
    }

    // Save entry
    async saveEntry(modalId) {
        try {
            const modal = document.getElementById(modalId);
            const form = modal.querySelector('#narrative-entry-form');
            
            const entryType = modal.querySelector('#narrative-entry-type').value;
            const eventTime = modal.querySelector('#narrative-entry-time').value;
            const contentEditor = modal.querySelector('#narrative-content');
            const content = this.narrativeManager.convertHtmlToStorage(contentEditor.innerHTML);
            
            if (!entryType || !content.trim()) {
                if (this.ui?.showToast) {
                    this.ui.showToast('Please fill in all required fields.', 'error');
                } else {
                    alert('Please fill in all required fields.');
                }
                return;
            }

            const entryData = {
                entry_type: entryType,
                event_time: eventTime,
                content: content
            };

            // Validate entry
            const validation = this.narrativeManager.validateEntry(entryData);
            if (!validation.isValid) {
                const errorMessage = validation.errors.join(', ');
                if (this.ui?.showToast) {
                    this.ui.showToast(`Validation errors: ${errorMessage}`, 'error');
                } else {
                    alert(`Validation errors: ${errorMessage}`);
                }
                return;
            }

            // Close modal first
            modal.remove();

            // Create or update the entry
            let savedEntry;
            if (window.app?.narrativeManagement) {
                // Get the current context from the main narrative management
                const narrativeManagement = window.app.narrativeManagement;
                const context = this.getCurrentUserContext();
                const wasEditing = narrativeManagement.isCurrentlyEditing();
                
                if (wasEditing) {
                    // Update existing entry
                    const existingEntry = narrativeManagement.getEntries().find(e => e.id === narrativeManagement.getEditingEntryId());
                    if (existingEntry) {
                        savedEntry = this.narrativeManager.updateEntry(existingEntry, entryData);
                        const index = narrativeManagement.entries.findIndex(e => e.id === savedEntry.id);
                        if (index !== -1) {
                            narrativeManagement.entries[index] = savedEntry;
                        }
                    }
                    narrativeManagement.isEditing = false;
                    narrativeManagement.editingEntryId = null;
                } else {
                    // Create new entry
                    savedEntry = this.narrativeManager.createEntry(entryData);
                    narrativeManagement.entries.push(savedEntry);
                }

                // Save to incident if we have an incident ID
                if (narrativeManagement.getCurrentIncidentId()) {
                    await narrativeManagement.saveToIncident();
                }

                // Force re-render
                narrativeManagement.render();
                
                if (this.ui?.showToast) {
                    this.ui.showToast(`Narrative entry ${wasEditing ? 'updated' : 'added'} successfully`, 'success');
                }
            } else {
                throw new Error('Narrative management system not available');
            }

        } catch (error) {
            console.error('Error saving narrative entry:', error);
            if (this.ui?.showToast) {
                this.ui.showToast('Failed to save narrative entry. Please try again.', 'error');
            } else {
                alert('Failed to save narrative entry. Please try again.');
            }
        }
    }

    // AI enhancement modal
    async showAiEnhanceModal() {
        if (!this.currentEditor) {
            console.warn('No editor currently active for AI enhancement');
            return;
        }

        const currentText = this.currentEditor.textContent || this.currentEditor.innerText;
        if (!currentText || currentText.trim().length === 0) {
            if (this.ui?.showToast) {
                this.ui.showToast('Please enter some text before using AI enhancement.', 'warning');
            } else {
                alert('Please enter some text before using AI enhancement.');
            }
            return;
        }

        if (!this.narrativeManager.geminiService) {
            if (this.ui?.showToast) {
                this.ui.showToast('AI enhancement service is not available. Please check your configuration.', 'error');
            } else {
                alert('AI enhancement service is not available. Please check your configuration.');
            }
            return;
        }

        const modalId = `ai-enhance-modal-${Date.now()}`;
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = modalId;
        
        modal.innerHTML = `
            <div class="modal-content ai-enhance-modal">
                <div class="modal-header">
                    <h3>🤖 AI Text Enhancement</h3>
                    <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="ai-enhance-content">
                        <div class="original-text-section">
                            <h4>Original Text:</h4>
                            <div class="text-preview original-text">${this.narrativeManager.escapeHtml(currentText)}</div>
                        </div>
                        
                        <div class="enhanced-text-section">
                            <h4>AI Enhanced Text:</h4>
                            <div class="text-preview enhanced-text" id="enhanced-text-${modalId}">
                                <div class="loading-spinner">🤖 Enhancing text...</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="action-btn primary" id="approve-enhancement-${modalId}" onclick="window.narrativeFormManager.approveEnhancement('${modalId}')" disabled>
                        ✓ Approve & Replace
                    </button>
                    <button class="action-btn secondary" id="regenerate-enhancement-${modalId}" onclick="window.narrativeFormManager.regenerateEnhancement('${modalId}')" disabled>
                        🔄 Regenerate
                    </button>
                    <button class="action-btn" onclick="this.closest('.modal-overlay').remove()">
                        ✗ Cancel
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        this.performAiEnhancement(modalId, currentText);
    }

    // Perform AI enhancement
    async performAiEnhancement(modalId, originalText) {
        try {
            console.log('Starting AI enhancement for text:', originalText.substring(0, 100) + '...');
            
            // Get incident context if available
            let incidentContext = null;
            const currentIncidentId = window.app?.narrativeManagement?.getCurrentIncidentId();
            if (currentIncidentId && this.data) {
                try {
                    const incidents = await this.data.query('SELECT * FROM incidents WHERE id = ?', [currentIncidentId]);
                    if (incidents && incidents.length > 0) {
                        incidentContext = incidents[0];
                        console.log('Using incident context for AI enhancement:', incidentContext.incident_number);
                    }
                } catch (error) {
                    console.warn('Failed to load incident context for AI enhancement:', error);
                }
            }
            
            const enhancedText = await this.narrativeManager.enhanceTextWithAI(originalText, incidentContext);
            
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            if (enhancedTextDiv) {
                enhancedTextDiv.innerHTML = this.narrativeManager.formatTextForDisplay(enhancedText);
                enhancedTextDiv.dataset.enhancedText = enhancedText;
            }
            
            const approveBtn = document.getElementById(`approve-enhancement-${modalId}`);
            const regenerateBtn = document.getElementById(`regenerate-enhancement-${modalId}`);
            
            if (approveBtn) approveBtn.disabled = false;
            if (regenerateBtn) regenerateBtn.disabled = false;
            
            console.log('AI enhancement completed successfully');
            
        } catch (error) {
            console.error('AI enhancement failed:', error);
            
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            if (enhancedTextDiv) {
                enhancedTextDiv.innerHTML = `
                    <div class="error-message">
                        <p><strong>Enhancement failed:</strong> ${error.message}</p>
                        <p>Please try again or check your AI configuration.</p>
                    </div>
                `;
            }
            
            const regenerateBtn = document.getElementById(`regenerate-enhancement-${modalId}`);
            if (regenerateBtn) regenerateBtn.disabled = false;
        }
    }

    // Approve enhancement
    approveEnhancement(modalId) {
        try {
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            const enhancedText = enhancedTextDiv?.dataset.enhancedText;
            
            if (!enhancedText) {
                alert('No enhanced text available to approve.');
                return;
            }
            
            if (!this.currentEditor) {
                alert('No editor available to update.');
                return;
            }
            
            this.currentEditor.innerHTML = '';
            this.currentEditor.innerHTML = this.narrativeManager.convertTextToHtml(enhancedText);
            this.currentEditor.classList.remove('empty');
            this.currentEditor.focus();
            
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
            
            console.log('AI enhancement approved and applied');
            
        } catch (error) {
            console.error('Error applying AI enhancement:', error);
            alert('Failed to apply enhancement. Please try again.');
        }
    }

    // Regenerate enhancement
    regenerateEnhancement(modalId) {
        try {
            const modal = document.getElementById(modalId);
            const originalTextDiv = modal?.querySelector('.original-text');
            const originalText = originalTextDiv?.textContent;
            
            if (!originalText) {
                alert('Cannot regenerate - original text not found.');
                return;
            }
            
            const enhancedTextDiv = document.getElementById(`enhanced-text-${modalId}`);
            if (enhancedTextDiv) {
                enhancedTextDiv.innerHTML = '<div class="loading-spinner">🤖 Regenerating...</div>';
            }
            
            const approveBtn = document.getElementById(`approve-enhancement-${modalId}`);
            const regenerateBtn = document.getElementById(`regenerate-enhancement-${modalId}`);
            
            if (approveBtn) approveBtn.disabled = true;
            if (regenerateBtn) regenerateBtn.disabled = true;
            
            this.performAiEnhancement(modalId, originalText);
            
        } catch (error) {
            console.error('Error regenerating enhancement:', error);
            alert('Failed to regenerate enhancement. Please try again.');
        }
    }

    // Close modal with proper cleanup
    closeModal(modalElement) {
        try {
            // Reset editing state if this was an edit modal
            if (window.app?.narrativeManagement?.isCurrentlyEditing()) {
                window.app.narrativeManagement.isEditing = false;
                window.app.narrativeManagement.editingEntryId = null;
                console.log('Reset editing state on modal close');
            }
            
            // Remove the modal
            if (modalElement) {
                modalElement.remove();
            }
        } catch (error) {
            console.error('Error closing modal:', error);
            // Fallback - just remove the modal
            if (modalElement) {
                modalElement.remove();
            }
        }
    }
}