// Module Registry - Central registry for all application modules
// Manages module lifecycle, dependencies, and inter-module communication

import { Logger } from './logger.js';

export class ModuleRegistry {
    constructor() {
        this.modules = new Map();
        this.moduleInstances = new Map();
        this.initializationOrder = [];
        this.logger = Logger.forModule('ModuleRegistry');
        this.eventListeners = new Map();
    }

    /**
     * Register a module class with the registry
     * @param {string} name - Module name
     * @param {ModuleInterface} moduleClass - Module class
     * @param {Object} config - Module configuration
     */
    register(name, moduleClass, config = {}) {
        if (this.modules.has(name)) {
            this.logger.warn(`Module ${name} is already registered, overriding...`);
        }

        this.modules.set(name, {
            moduleClass,
            config,
            registered: true,
            initialized: false
        });

        this.logger.info(`Registered module: ${name}`);
    }

    /**
     * Check if a module is registered
     * @param {string} name - Module name
     * @returns {boolean} True if registered
     */
    isRegistered(name) {
        return this.modules.has(name);
    }

    /**
     * Check if a module is initialized
     * @param {string} name - Module name
     * @returns {boolean} True if initialized
     */
    isInitialized(name) {
        const moduleInfo = this.modules.get(name);
        return moduleInfo?.initialized || false;
    }

    /**
     * Get a module instance
     * @param {string} name - Module name
     * @returns {ModuleInterface|null} Module instance or null
     */
    get(name) {
        return this.moduleInstances.get(name) || null;
    }

    /**
     * Get all registered module names
     * @returns {Array<string>} Array of module names
     */
    getRegisteredModules() {
        return Array.from(this.modules.keys());
    }

    /**
     * Get all initialized module names
     * @returns {Array<string>} Array of initialized module names
     */
    getInitializedModules() {
        return Array.from(this.modules.entries())
            .filter(([name, info]) => info.initialized)
            .map(([name]) => name);
    }

    /**
     * Get module metadata
     * @param {string} name - Module name
     * @returns {Object|null} Module metadata
     */
    getModuleMetadata(name) {
        const instance = this.moduleInstances.get(name);
        return instance ? instance.getMetadata() : null;
    }

    /**
     * Initialize a specific module
     * @param {string} name - Module name
     * @param {Object} dependencies - Dependency injection
     * @returns {Promise<ModuleInterface>} Initialized module instance
     */
    async initializeModule(name, dependencies = {}) {
        const moduleInfo = this.modules.get(name);
        
        if (!moduleInfo) {
            throw new Error(`Module ${name} is not registered`);
        }

        if (moduleInfo.initialized) {
            this.logger.warn(`Module ${name} is already initialized`);
            return this.moduleInstances.get(name);
        }

        try {
            this.logger.info(`Initializing module: ${name}`);

            // Create module instance
            const { moduleClass, config } = moduleInfo;
            const instance = new moduleClass(dependencies.dataManager, dependencies.authManager, dependencies.uiManager, dependencies.uiUtilities, dependencies.modalManagement);

            // Store the instance
            this.moduleInstances.set(name, instance);

            // Initialize the module using standard wrapper
            if (instance._standardInitialize) {
                await instance._standardInitialize();
            } else {
                await instance.initialize();
            }

            // Mark as initialized
            moduleInfo.initialized = true;
            this.initializationOrder.push(name);

            this.logger.info(`Module ${name} initialized successfully`);
            this._emitEvent('moduleInitialized', { name, instance });

            return instance;

        } catch (error) {
            this.logger.error(`Failed to initialize module ${name}`, error);
            
            // Clean up failed initialization
            this.moduleInstances.delete(name);
            moduleInfo.initialized = false;
            
            throw error;
        }
    }

    /**
     * Initialize all registered modules in dependency order
     * @param {Object} dependencies - Shared dependencies
     * @returns {Promise<void>}
     */
    async initializeAll(dependencies = {}) {
        this.logger.info('Starting initialization of all modules...');
        
        const moduleNames = Array.from(this.modules.keys());
        const initOrder = this._calculateInitializationOrder(moduleNames);
        
        for (const name of initOrder) {
            try {
                await this.initializeModule(name, dependencies);
            } catch (error) {
                this.logger.error(`Failed to initialize module ${name}, continuing with others...`, error);
                // Continue with other modules rather than failing completely
            }
        }

        this.logger.info('Module initialization completed');
        this._emitEvent('allModulesInitialized', { 
            successful: this.getInitializedModules(),
            failed: moduleNames.filter(name => !this.isInitialized(name))
        });
    }

    /**
     * Cleanup a specific module
     * @param {string} name - Module name
     * @returns {Promise<void>}
     */
    async cleanupModule(name) {
        const instance = this.moduleInstances.get(name);
        const moduleInfo = this.modules.get(name);
        
        if (!instance || !moduleInfo) {
            this.logger.warn(`Cannot cleanup module ${name}: not found`);
            return;
        }

        try {
            this.logger.info(`Cleaning up module: ${name}`);

            // Cleanup using standard wrapper if available
            if (instance._standardCleanup) {
                await instance._standardCleanup();
            } else {
                await instance.cleanup();
            }

            // Remove from instances and mark as not initialized
            this.moduleInstances.delete(name);
            moduleInfo.initialized = false;
            
            // Remove from initialization order
            const index = this.initializationOrder.indexOf(name);
            if (index > -1) {
                this.initializationOrder.splice(index, 1);
            }

            this.logger.info(`Module ${name} cleaned up successfully`);
            this._emitEvent('moduleCleanedUp', { name });

        } catch (error) {
            this.logger.error(`Failed to cleanup module ${name}`, error);
            throw error;
        }
    }

    /**
     * Cleanup all modules in reverse initialization order
     * @returns {Promise<void>}
     */
    async cleanupAll() {
        this.logger.info('Starting cleanup of all modules...');
        
        // Cleanup in reverse order
        const cleanupOrder = [...this.initializationOrder].reverse();
        
        for (const name of cleanupOrder) {
            try {
                await this.cleanupModule(name);
            } catch (error) {
                this.logger.error(`Failed to cleanup module ${name}, continuing...`, error);
            }
        }

        this.logger.info('Module cleanup completed');
        this._emitEvent('allModulesCleanedUp');
    }

    /**
     * Get system status for all modules
     * @returns {Object} System status
     */
    getSystemStatus() {
        const status = {
            totalModules: this.modules.size,
            initializedModules: this.getInitializedModules().length,
            modules: {}
        };

        for (const [name, instance] of this.moduleInstances.entries()) {
            status.modules[name] = instance.getStatus();
        }

        return status;
    }

    /**
     * Get all commands from all initialized modules
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Combined command map
     */
    getAllCommands(commandManager) {
        const allCommands = new Map();

        for (const [name, instance] of this.moduleInstances.entries()) {
            try {
                const moduleCommands = instance.getCommands(commandManager);
                
                if (moduleCommands instanceof Map) {
                    for (const [cmdName, cmdClass] of moduleCommands.entries()) {
                        if (allCommands.has(cmdName)) {
                            this.logger.warn(`Command ${cmdName} is already registered, module ${name} override ignored`);
                        } else {
                            allCommands.set(cmdName, cmdClass);
                        }
                    }
                }
            } catch (error) {
                this.logger.error(`Failed to get commands from module ${name}`, error);
            }
        }

        return allCommands;
    }

    /**
     * Add event listener for registry events
     * @param {string} event - Event name
     * @param {Function} callback - Event callback
     */
    addEventListener(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    /**
     * Remove event listener
     * @param {string} event - Event name
     * @param {Function} callback - Event callback to remove
     */
    removeEventListener(event, callback) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Calculate initialization order based on dependencies
     * @param {Array<string>} moduleNames - Module names to order
     * @returns {Array<string>} Ordered module names
     * @private
     */
    _calculateInitializationOrder(moduleNames) {
        // For now, use registration order
        // Future enhancement: Implement topological sort based on dependencies
        return moduleNames;
    }

    /**
     * Emit registry event
     * @param {string} event - Event name
     * @param {*} data - Event data
     * @private
     */
    _emitEvent(event, data = null) {
        const listeners = this.eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    this.logger.error(`Error in event listener for ${event}`, error);
                }
            });
        }
    }
}

// Export singleton instance
export const moduleRegistry = new ModuleRegistry();