// System Settings Modal Templates
// Extracted from commands.js for better maintainability

export const systemSettingsModals = {
    // Settings menu modal template
    createSettingsModal: () => `
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>System Settings</h3>
            </div>
            <div class="modal-body">
                <div class="settings-menu">
                    <div class="menu-item" data-action="seed-data">
                        <div class="menu-title">Add Sample Data</div>
                        <div class="menu-desc">Add sample records for testing search functionality</div>
                    </div>
                    <div class="menu-item" data-action="clear-data">
                        <div class="menu-title">Clear All Data</div>
                        <div class="menu-desc">Remove all local records (use with caution)</div>
                    </div>
                    <div class="menu-item" data-action="sync-status">
                        <div class="menu-title">Sync Status</div>
                        <div class="menu-desc">Check data synchronization status</div>
                    </div>
                    <div class="menu-item" data-action="cleanup-duplicates">
                        <div class="menu-title">Clean Up Duplicates</div>
                        <div class="menu-desc">Remove duplicate records from local storage</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
            </div>
        </div>
    `
};