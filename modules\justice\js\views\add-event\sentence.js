// Sentence Event Modal
// Handles adding sentence events with custodial/community options

import { addEventTemplates } from '../../../templates/add-event-templates.js';

export class SentenceModal {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.modal = null;
        this.episodeId = null;
    }
    
    async show(episodeId) {
        this.episodeId = episodeId;
        
        try {
            // Create modal
            this.modal = this.justice.ui.createModal({
                id: 'add-sentence-modal',
                title: 'Add Sentence',
                size: 'medium'
            });
            
            // Render form
            this.render();
            
            // Show modal
            this.justice.ui.showModal(this.modal);
            
            // Setup event handlers
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('Failed to show sentence modal:', error);
            this.justice.ui.showDialog('Error', `Failed to open sentence form: ${error.message}`, 'error');
        }
    }
    
    render() {
        if (!this.modal) return;
        
        const modalBody = this.modal.querySelector('.modal-body');
        modalBody.innerHTML = addEventTemplates.sentenceForm({
            episodeId: this.episodeId,
            defaultDateTime: new Date().toISOString().slice(0, 16)
        });
    }
    
    setupEventHandlers() {
        if (!this.modal) return;
        
        const form = this.modal.querySelector('#sentence-form');
        const modeSelect = this.modal.querySelector('#sentence-mode');
        const custodialSection = this.modal.querySelector('#custodial-details');
        const communitySection = this.modal.querySelector('#community-details');
        
        // Show/hide sections based on sentence mode
        if (modeSelect && custodialSection && communitySection) {
            modeSelect.addEventListener('change', (e) => {
                const mode = e.target.value;
                custodialSection.style.display = mode === 'CUSTODIAL' ? 'block' : 'none';
                communitySection.style.display = mode === 'COMMUNITY' ? 'block' : 'none';
            });
            
            // Trigger initial state
            modeSelect.dispatchEvent(new Event('change'));
        }
        
        // Save button
        const saveBtn = this.modal.querySelector('#save-sentence-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveSentence(form);
            });
        }
        
        // Cancel button
        const cancelBtn = this.modal.querySelector('#cancel-sentence-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }
    
    async saveSentence(form) {
        try {
            const formData = new FormData(form);
            
            const eventData = {
                event_dt: formData.get('sentence_dt'),
                payload: {
                    mode: formData.get('mode'),
                    length_days: formData.get('length_days') ? parseInt(formData.get('length_days')) : null,
                    credit_days: formData.get('credit_days') ? parseInt(formData.get('credit_days')) : null,
                    conditions: formData.get('conditions'),
                    notes: formData.get('notes')
                }
            };
            
            // Add mode-specific details
            if (eventData.payload.mode === 'CUSTODIAL') {
                eventData.payload.facility_id = formData.get('facility_id');
                eventData.payload.parole_eligibility = formData.get('parole_eligibility');
            } else if (eventData.payload.mode === 'COMMUNITY') {
                eventData.payload.service_hours = formData.get('service_hours') ? parseInt(formData.get('service_hours')) : null;
                eventData.payload.probation_length = formData.get('probation_length') ? parseInt(formData.get('probation_length')) : null;
            }
            
            // Validate required fields
            if (!eventData.event_dt) {
                this.justice.ui.showDialog('Error', 'Sentence date/time is required.', 'error');
                return;
            }
            
            if (!eventData.payload.mode) {
                this.justice.ui.showDialog('Error', 'Sentence mode is required.', 'error');
                return;
            }
            
            // Show loading state
            const saveBtn = this.modal.querySelector('#save-sentence-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saving...';
            saveBtn.disabled = true;
            
            // Create the event
            const newEvent = await this.justice.api.addEvent(
                this.episodeId,
                'SENTENCE',
                eventData.event_dt,
                eventData.payload
            );
            
            // Update state
            this.justice.state.addEvent(newEvent);
            
            // Close modal
            this.close();
            
            // Show success
            this.justice.ui.showDialog('Success', 'Sentence added successfully!', 'success');
            
            // Refresh views
            await this.refreshViews();
            
        } catch (error) {
            console.error('Failed to save sentence:', error);
            this.justice.ui.showDialog('Error', `Failed to save sentence: ${error.message}`, 'error');
            
            // Reset button
            const saveBtn = this.modal.querySelector('#save-sentence-btn');
            if (saveBtn) {
                saveBtn.textContent = 'Save Sentence';
                saveBtn.disabled = false;
            }
        }
    }
    
    async refreshViews() {
        // Refresh timeline if visible
        if (this.justice.timelineView && this.justice.timelineView.container) {
            await this.justice.timelineView.refresh();
        }
        
        // Refresh status ribbon if visible
        if (this.justice.statusRibbon && this.justice.statusRibbon.container) {
            await this.justice.statusRibbon.refresh();
        }
    }
    
    close() {
        if (this.modal) {
            this.justice.ui.closeModal(this.modal.id);
            this.modal = null;
        }
        this.episodeId = null;
    }
}
