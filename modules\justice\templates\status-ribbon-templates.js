// Status Ribbon Templates
// Read-only header showing current justice status snapshot

export const statusRibbonTemplates = {
    statusRibbon(status) {
        return `
            <div class="justice-status-ribbon">
                <div class="status-section current-state">
                    <div class="status-label">Current State</div>
                    <div class="status-value state-${status.current_state?.toLowerCase() || 'unknown'}">
                        ${statusRibbonTemplates.formatState(status.current_state)}
                    </div>
                </div>
                
                ${status.current_facility ? `
                    <div class="status-section custody">
                        <div class="status-label">Current Facility</div>
                        <div class="status-value">
                            <span class="facility-name">${status.current_facility}</span>
                            <span class="facility-type">(${statusRibbonTemplates.formatFacilityType(status.current_facility_type)})</span>
                        </div>
                    </div>
                ` : ''}
                
                ${status.next_court_dt ? `
                    <div class="status-section court">
                        <div class="status-label">Next Court Date</div>
                        <div class="status-value">
                            ${statusRibbonTemplates.formatDateTime(status.next_court_dt)}
                        </div>
                    </div>
                ` : ''}
                
                ${status.active_conditions_summary ? `
                    <div class="status-section conditions">
                        <div class="status-label">Active Conditions</div>
                        <div class="status-value">
                            ${status.active_conditions_summary}
                        </div>
                    </div>
                ` : ''}
                
                ${status.has_warrant ? `
                    <div class="status-section warrant warning">
                        <div class="status-label">⚠️ Outstanding Warrant</div>
                        <div class="status-value">Active</div>
                    </div>
                ` : ''}
                
                <div class="status-actions">
                    <button class="refresh-btn" data-action="refresh-status" title="Refresh Status">
                        🔄
                    </button>
                </div>
            </div>
        `;
    },
    
    errorRibbon(message) {
        return `
            <div class="justice-status-ribbon error">
                <div class="status-section error">
                    <div class="status-label">Error</div>
                    <div class="status-value">
                        Failed to load status: ${message}
                    </div>
                </div>
                <div class="status-actions">
                    <button class="refresh-btn" data-action="refresh-status" title="Retry">
                        🔄
                    </button>
                </div>
            </div>
        `;
    },
    
    formatState(state) {
        const states = {
            'UNKNOWN': 'Unknown',
            'ACTIVE': 'Active',
            'IN_CUSTODY': 'In Custody',
            'ON_BAIL': 'On Bail',
            'RELEASED': 'Released',
            'SENTENCED': 'Sentenced',
            'COMPLETED': 'Completed'
        };
        
        return states[state] || state || 'Unknown';
    },
    
    formatFacilityType(type) {
        const types = {
            'JAIL': 'Jail',
            'PRISON': 'Prison',
            'DETENTION_CENTER': 'Detention Center',
            'HOLDING_CELL': 'Holding Cell',
            'COURTHOUSE_CELL': 'Courthouse Cell',
            'OTHER': 'Other'
        };
        
        return types[type] || type || 'Facility';
    },
    
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        
        const date = new Date(dateTimeString);
        const now = new Date();
        const diffDays = Math.ceil((date - now) / (1000 * 60 * 60 * 24));
        
        let dateStr = date.toLocaleDateString('en-CA', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        if (diffDays === 0) {
            dateStr += ' (Today)';
        } else if (diffDays === 1) {
            dateStr += ' (Tomorrow)';
        } else if (diffDays > 1 && diffDays <= 7) {
            dateStr += ` (${diffDays} days)`;
        } else if (diffDays < 0) {
            dateStr += ' (Past)';
        }
        
        return dateStr;
    }
};
