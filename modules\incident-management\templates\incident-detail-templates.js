// Incident Detail Templates
// Extracted from app.js for better maintainability

export const incidentDetailTemplates = {
    // Main incidents dashboard template - matches original structure for split view
    incidentsDashboard: () => `
        <div class="incidents-container">
            <!-- Header -->
            <div class="incidents-header">
                <h2>INCIDENT MANAGEMENT</h2>
                <div class="incidents-status">
                    <span id="incident-count-display">0</span> Active Incidents |
                    Last Updated: <span id="last-update">--:--:--</span>
                </div>
            </div>

            <!-- Main Content -->
            <div class="incidents-main">
                <!-- Incident List -->
                <div class="incidents-list-section">
                    <div class="incidents-list-header">
                        <div class="list-filters">
                            <select id="status-filter">
                                <option value="">All Status</option>
                                <option value="draft">Draft</option>
                                <option value="open">Open</option>
                                <option value="assigned">Assigned</option>
                                <option value="en_route">En Route</option>
                                <option value="closed">Closed</option>
                            </select>
                            <select id="priority-filter">
                                <option value="">All Priority</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                            <input type="text" id="search-input" placeholder="Search incidents...">
                        </div>
                    </div>
                    <div class="incidents-list-container">
                        <div class="incidents-list" id="incident-list">
                            <div class="loading">Loading incidents...</div>
                        </div>
                    </div>
                </div>

                <!-- Incident Details -->
                <div class="incident-details-section" id="incident-details">
                    <div class="no-selection">
                        <h3>Select an Incident</h3>
                        <p>Choose an incident from the list to view details and manage it.</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="incidents-actions">
                <button class="action-button primary" data-action="create-incident-screen">
                    New Incident
                </button>
                <button class="action-button" id="refresh-incidents" onclick="window.app.refreshIncidents()">
                    Refresh
                </button>
                <button class="action-button" id="search-btn">
                    Search
                </button>
                <button class="action-button" id="clear-search">
                    Clear
                </button>
            </div>
        </div>
    `,

    // Incident detail view with tabs matching creation form layout
    incidentDetailView: (incident) => `
        <div class="incident-detail-container">
            <div class="incident-detail-header">
                <h2>&gt; INCIDENT ${incident.incident_number || `#${incident.id}`} - DETAILS</h2>
                <div class="header-status">
                    <div class="incident-status status-${incident.status}">[${incident.status?.toUpperCase()}]</div>
                    <div class="header-actions">
                        <button data-action="print-incident-report" data-incident-id="${incident.id}" class="action-btn">[PRINT]</button>
                        <button data-action="show-update-status-modal" data-incident-id="${incident.id}" class="action-btn">[UPDATE STATUS]</button>
                        <button data-action="edit-incident" data-incident-id="${incident.id}" class="action-btn">[EDIT]</button>
                        <button onclick="window.app.deleteIncident(${incident.id})" class="action-btn danger">[DELETE]</button>
                    </div>
                </div>
            </div>
            
            <div class="incident-detail-wrapper">
                <div class="incident-detail-tabs">
                    <button type="button" class="incident-detail-tab active" data-tab="basic">BASIC INFO</button>
                    <button type="button" class="incident-detail-tab" data-tab="narrative">EVENTS/NARRATIVE</button>
                    <button type="button" class="incident-detail-tab" data-tab="location">LOCATION</button>
                    <button type="button" class="incident-detail-tab" data-tab="people">PEOPLE</button>
                    <button type="button" class="incident-detail-tab" data-tab="property">PROPERTY</button>
                    <button type="button" class="incident-detail-tab" data-tab="services">SERVICES</button>
                    <button type="button" class="incident-detail-tab" data-tab="agencies">AGENCIES</button>
                    <button type="button" class="incident-detail-tab" data-tab="safety">SAFETY</button>
                    <button type="button" class="incident-detail-tab" data-tab="followup">FOLLOW-UP</button>

                    <button type="button" class="incident-detail-tab" data-tab="files">FILES</button>
                </div>
                
                <div class="incident-detail-content">
                    <div class="detail-content-scroll">
                        <div class="tab-pane active" id="basic-pane" data-tab="basic">
                            <!-- Basic info content will be populated -->
                        </div>
                        <div class="tab-pane" id="location-pane" data-tab="location">
                            <!-- Location content will be populated -->
                        </div>
                        <div class="tab-pane" id="people-pane" data-tab="people">
                            <!-- People content will be populated -->
                        </div>
                        <div class="tab-pane" id="property-pane" data-tab="property">
                            <!-- Property content will be populated -->
                        </div>
                        <div class="tab-pane" id="services-pane" data-tab="services">
                            <!-- Services content will be populated -->
                        </div>
                        <div class="tab-pane" id="agencies-pane" data-tab="agencies">
                            <!-- Agencies content will be populated -->
                        </div>
                        <div class="tab-pane" id="safety-pane" data-tab="safety">
                            <!-- Safety content will be populated -->
                        </div>
                        <div class="tab-pane" id="followup-pane" data-tab="followup">
                            <!-- Follow-up content will be populated -->
                        </div>
                        <div class="tab-pane" id="narrative-pane" data-tab="narrative">
                            <!-- Events/Narrative content will be populated -->
                        </div>
                        <div class="tab-pane" id="files-pane" data-tab="files">
                            <!-- Files content will be populated -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,

    // Basic info tab (replaces overview)
    basicInfoTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; INCIDENT DETAILS</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>TYPE:</label>
                        <span class="detail-value incident-type-${incident.incident_type}">${incident.incident_type?.replace('_', ' ').toUpperCase() || 'N/A'}</span>
                    </div>
                    <div class="detail-item">
                        <label>PRIORITY:</label>
                        <span class="detail-value priority-${incident.priority}">[${incident.priority?.toUpperCase() || 'N/A'}]</span>
                    </div>
                    <div class="detail-item">
                        <label>STATUS:</label>
                        <span class="detail-value status-${incident.status}">[${incident.status?.toUpperCase() || 'N/A'}]</span>
                    </div>
                    <div class="detail-item">
                        <label>DATE & TIME:</label>
                        <span class="detail-value">${incident.incident_date ? new Date(incident.incident_date).toLocaleDateString() : 'N/A'} ${incident.incident_time || ''}</span>
                    </div>
                    <div class="detail-item">
                        <label>ASSIGNED RANGER:</label>
                        <span class="detail-value">${incident.assigned_ranger || 'Unassigned'}</span>
                    </div>
                    <div class="detail-item">
                        <label>CREATED BY:</label>
                        <span class="detail-value">${incident.created_by || 'Unknown'}</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4>&gt; SUMMARY</h4>
                <div class="description-display">
                    ${incident.description || '[NO SUMMARY PROVIDED]'}
                </div>
            </div>
            
            <div class="detail-section">
                <h4>&gt; AUDIT TRAIL</h4>
                <div class="audit-grid">
                    <div class="audit-item">
                        <label>CREATED:</label>
                        <span>${incident.created_at ? new Date(incident.created_at).toLocaleString() : 'N/A'}</span>
                    </div>
                    <div class="audit-item">
                        <label>LAST UPDATED:</label>
                        <span>${incident.updated_at ? new Date(incident.updated_at).toLocaleString() : 'N/A'}</span>
                    </div>
                </div>
            </div>
        </div>
    `,

    locationTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; LOCATION INFORMATION</h4>
                <div class="location-display">
                    ${incident.street_address || incident.city || incident.location ? `
                        <div class="address-detail">
                            <label>ADDRESS:</label>
                            <div class="address-value">
                                ${incident.street_address ? `${incident.street_address}` : ''}
                                ${incident.street_address && (incident.city || incident.province) ? '<br>' : ''}
                                ${incident.city ? `${incident.city}` : ''}${incident.province ? `, ${incident.province}` : ''}${incident.postal_code ? ` ${incident.postal_code}` : ''}
                                ${incident.country && incident.country !== 'Canada' ? `<br>${incident.country}` : ''}
                                ${!incident.street_address && !incident.city && incident.location ? `<br><strong>General Location:</strong> ${incident.location}` : ''}
                            </div>
                        </div>
                    ` : ''}
                    
                    ${incident.coordinates ? `
                        <div class="coordinates-detail">
                            <label>GPS COORDINATES:</label>
                            <div class="coordinates-value">${incident.coordinates}</div>
                        </div>
                    ` : ''}
                    
                    ${!incident.street_address && !incident.city && !incident.coordinates && !incident.location ? `
                        <div class="no-location">
                            [NO SPECIFIC LOCATION INFORMATION RECORDED]
                        </div>
                    ` : ''}
                </div>
            </div>
            
            ${incident.location_notes ? `
                <div class="detail-section">
                    <h4>&gt; LOCATION NOTES</h4>
                    <div class="location-notes-display">
                        ${incident.location_notes}
                    </div>
                </div>
            ` : ''}
        </div>
    `,

    peopleTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; PEOPLE INVOLVED</h4>
                <div class="people-display">
                    ${incident.incident_people && Array.isArray(incident.incident_people) && incident.incident_people.length > 0 ? `
                        <div class="people-cards">
                            ${incident.incident_people.map((person, index) => `
                                <div class="person-card">
                                    <div class="person-header">
                                        <div class="person-identity">
                                            <div class="person-name">
                                                <strong>${person.name || person.unnamed_description || 'Unnamed Person'}</strong>
                                                ${person.age ? `<span class="person-age">(Age: ${person.age})</span>` : ''}
                                            </div>
                                            <div class="person-type">
                                                <span class="involvement-badge involvement-${person.role || 'unknown'}">
                                                    ${(person.role || 'unknown').replace('_', ' ').toUpperCase()}
                                                </span>
                                            </div>
                                        </div>
                                        ${person.has_medical_info ? `<div class="medical-flag">[MEDICAL]</div>` : ''}
                                    </div>
                                    
                                    <div class="person-details">
                                        ${person.has_medical_info ? `
                                            <div class="detail-row medical-section">
                                                <div class="medical-toggle collapsed" onclick="toggleMedicalDetails(this)">
                                                    <span class="toggle-icon">▶</span>
                                                    <label>MEDICAL INFORMATION</label>
                                                    <span class="medical-summary">${person.injury_status && person.injury_status !== 'none' ? `(${person.injury_status.toUpperCase()})` : ''}</span>
                                                </div>
                                                <div class="medical-details collapsed">
                                                    ${person.medical_info.required_medical_attention ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Required Medical Attention:</span>
                                                            <span class="field-value">YES</span>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.medical_issue_type ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Medical Issue Type:</span>
                                                            <span class="field-value">${person.medical_info.medical_issue_type}</span>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.medical_issue_other ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Other Medical Issue:</span>
                                                            <span class="field-value">${person.medical_info.medical_issue_other}</span>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.medical_issue_description ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Medical Description:</span>
                                                            <div class="field-value multiline">${person.medical_info.medical_issue_description}</div>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.injury_severity ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Injury Severity:</span>
                                                            <span class="field-value injury-status status-${person.medical_info.injury_severity}">${person.medical_info.injury_severity.toUpperCase()}</span>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.consciousness_level ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Consciousness Level:</span>
                                                            <span class="field-value">${person.medical_info.consciousness_level}</span>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.vital_signs_notes ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Vital Signs Notes:</span>
                                                            <div class="field-value multiline">${person.medical_info.vital_signs_notes}</div>
                                                        </div>
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.first_aid_provided ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">First Aid Provided:</span>
                                                            <span class="field-value">YES</span>
                                                        </div>
                                                        
                                                        ${person.medical_info.first_aid_type ? `
                                                            <div class="medical-field indent">
                                                                <span class="field-label">First Aid Type:</span>
                                                                <span class="field-value">${Array.isArray(person.medical_info.first_aid_type) ? person.medical_info.first_aid_type.join(', ') : person.medical_info.first_aid_type}</span>
                                                            </div>
                                                        ` : ''}
                                                        
                                                        ${person.medical_info.first_aid_other ? `
                                                            <div class="medical-field indent">
                                                                <span class="field-label">Other First Aid:</span>
                                                                <span class="field-value">${person.medical_info.first_aid_other}</span>
                                                            </div>
                                                        ` : ''}
                                                        
                                                        ${person.medical_info.first_aid_details ? `
                                                            <div class="medical-field indent">
                                                                <span class="field-label">First Aid Details:</span>
                                                                <div class="field-value multiline">${person.medical_info.first_aid_details}</div>
                                                            </div>
                                                        ` : ''}
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.hospital_transport_offered ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Hospital Transport Offered:</span>
                                                            <span class="field-value">YES</span>
                                                        </div>
                                                        
                                                        ${person.medical_info.transport_declined ? `
                                                            <div class="medical-field indent">
                                                                <span class="field-label">Transport Declined:</span>
                                                                <span class="field-value">YES</span>
                                                            </div>
                                                            
                                                            ${person.medical_info.transport_decline_reason ? `
                                                                <div class="medical-field indent">
                                                                    <span class="field-label">Decline Reason:</span>
                                                                    <span class="field-value">${person.medical_info.transport_decline_reason}</span>
                                                                </div>
                                                            ` : ''}
                                                        ` : `
                                                            ${person.medical_info.hospital_destination ? `
                                                                <div class="medical-field indent">
                                                                    <span class="field-label">Hospital Destination:</span>
                                                                    <span class="field-value">${person.medical_info.hospital_destination}</span>
                                                                </div>
                                                            ` : ''}
                                                            
                                                            ${person.medical_info.transported_by ? `
                                                                <div class="medical-field indent">
                                                                    <span class="field-label">Transported By:</span>
                                                                    <span class="field-value">${person.medical_info.transported_by}</span>
                                                                </div>
                                                            ` : ''}
                                                            
                                                            ${person.medical_info.transport_notes ? `
                                                                <div class="medical-field indent">
                                                                    <span class="field-label">Transport Notes:</span>
                                                                    <div class="field-value multiline">${person.medical_info.transport_notes}</div>
                                                                </div>
                                                            ` : ''}
                                                        `}
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.medical_referral_made ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Medical Referral Made:</span>
                                                            <span class="field-value">YES</span>
                                                        </div>
                                                        
                                                        ${person.medical_info.medical_referral_details ? `
                                                            <div class="medical-field indent">
                                                                <span class="field-label">Referral Details:</span>
                                                                <div class="field-value multiline">${person.medical_info.medical_referral_details}</div>
                                                            </div>
                                                        ` : ''}
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.follow_up_required ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Follow Up Required:</span>
                                                            <span class="field-value">YES</span>
                                                        </div>
                                                        
                                                        ${person.medical_info.follow_up_notes ? `
                                                            <div class="medical-field indent">
                                                                <span class="field-label">Follow Up Notes:</span>
                                                                <div class="field-value multiline">${person.medical_info.follow_up_notes}</div>
                                                            </div>
                                                        ` : ''}
                                                    ` : ''}
                                                    
                                                    ${person.medical_info.medical_assessment_notes ? `
                                                        <div class="medical-field">
                                                            <span class="field-label">Medical Assessment Notes:</span>
                                                            <div class="field-value multiline">${person.medical_info.medical_assessment_notes}</div>
                                                        </div>
                                                    ` : ''}
                                                </div>
                                            </div>
                                        ` : ''}
                                        
                                        ${person.description ? `
                                            <div class="detail-row">
                                                <label>DESCRIPTION:</label>
                                                <div class="person-description">${person.description}</div>
                                            </div>
                                        ` : ''}
                                        
                                        ${person.involvement_notes ? `
                                            <div class="detail-row">
                                                <label>INVOLVEMENT NOTES:</label>
                                                <div class="involvement-notes">${person.involvement_notes}</div>
                                            </div>
                                        ` : ''}
                                        
                                        ${person.medical_info && person.medical_info.medical_assessment_notes ? `
                                            <div class="detail-row">
                                                <label>MEDICAL NOTES:</label>
                                                <div class="medical-notes">${person.medical_info.medical_assessment_notes}</div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : incident.people_involved ? `
                        <div class="people-text">
                            ${incident.people_involved}
                        </div>
                    ` : `
                        <div class="no-people">
                            [NO PEOPLE INVOLVED RECORDED]
                        </div>
                    `}
                </div>
            </div>
        </div>
    `,

    detailsTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; DETAILED DESCRIPTION</h4>
                <div class="description-display">
                    ${incident.description || incident.narrative || '[NO DESCRIPTION PROVIDED]'}
                </div>
            </div>
            
            ${incident.initial_observations ? `
                <div class="detail-section">
                    <h4>&gt; INITIAL OBSERVATIONS</h4>
                    <div class="observations-display">
                        ${incident.initial_observations}
                    </div>
                </div>
            ` : ''}
            
            ${incident.actions_taken ? `
                <div class="detail-section">
                    <h4>&gt; ACTIONS TAKEN</h4>
                    <div class="actions-display">
                        ${incident.actions_taken}
                    </div>
                </div>
            ` : ''}
            
            ${incident.follow_up_required ? `
                <div class="detail-section">
                    <h4>&gt; FOLLOW-UP REQUIRED</h4>
                    <div class="followup-display">
                        ${incident.follow_up_required}
                    </div>
                </div>
            ` : ''}
            
            ${incident.additional_notes ? `
                <div class="detail-section">
                    <h4>&gt; ADDITIONAL NOTES</h4>
                    <div class="notes-display">
                        ${incident.additional_notes}
                    </div>
                </div>
            ` : ''}
        </div>
    `,

    servicesTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; EMERGENCY SERVICES</h4>
                <div class="services-grid">
                    <div class="service-item ${incident.police_notified ? 'active' : ''}">
                        <span class="service-label">POLICE:</span>
                        <span class="service-status">${incident.police_notified ? 'YES' : 'NO'}</span>
                    </div>
                    <div class="service-item ${incident.fire_department_called ? 'active' : ''}">
                        <span class="service-label">FIRE DEPT:</span>
                        <span class="service-status">${incident.fire_department_called ? 'YES' : 'NO'}</span>
                    </div>
                    <div class="service-item ${incident.paramedics_called ? 'active' : ''}">
                        <span class="service-label">PARAMEDICS:</span>
                        <span class="service-status">${incident.paramedics_called ? 'YES' : 'NO'}</span>
                    </div>
                </div>
                
                ${incident.police_notified && incident.police_file_number ? `
                    <div class="police-details">
                        <div class="detail-item">
                            <label>POLICE FILE NUMBER:</label>
                            <span>${incident.police_file_number}</span>
                        </div>
                    </div>
                ` : ''}
                
                ${incident.fire_department_called && (incident.fire_unit_number || incident.fire_personnel) ? `
                    <div class="fire-details">
                        ${incident.fire_unit_number ? `
                            <div class="detail-item">
                                <label>FIRE UNIT:</label>
                                <span>${incident.fire_unit_number}</span>
                            </div>
                        ` : ''}
                        ${incident.fire_personnel ? `
                            <div class="detail-item">
                                <label>FIRE PERSONNEL:</label>
                                <span>${incident.fire_personnel}</span>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
                
                ${incident.paramedics_called && (incident.ambulance_unit_number || incident.ems_personnel_names) ? `
                    <div class="ems-details">
                        ${incident.ambulance_unit_number ? `
                            <div class="detail-item">
                                <label>AMBULANCE UNIT:</label>
                                <span>${incident.ambulance_unit_number}</span>
                            </div>
                        ` : ''}
                        ${incident.ems_personnel_names ? `
                            <div class="detail-item">
                                <label>EMS PERSONNEL:</label>
                                <span>${incident.ems_personnel_names}</span>
                            </div>
                        ` : ''}
                        ${incident.ems_response_time ? `
                            <div class="detail-item">
                                <label>RESPONSE TIME:</label>
                                <span>${incident.ems_response_time}</span>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
            </div>
            
            ${(incident.services_offered && Array.isArray(incident.services_offered) && incident.services_offered.length > 0) || (incident.services_provided && Array.isArray(incident.services_provided) && incident.services_provided.length > 0) || (incident.resources_distributed && Array.isArray(incident.resources_distributed) && incident.resources_distributed.length > 0) ? `
                <div class="detail-section">
                    <h4>&gt; SERVICES & RESOURCES</h4>
                    ${incident.services_offered && Array.isArray(incident.services_offered) && incident.services_offered.length > 0 ? `
                        <div class="detail-item">
                            <label>SERVICES OFFERED:</label>
                            <div class="services-list">${incident.services_offered.join(', ')}</div>
                        </div>
                    ` : ''}
                    ${incident.services_provided && Array.isArray(incident.services_provided) && incident.services_provided.length > 0 ? `
                        <div class="detail-item">
                            <label>SERVICES PROVIDED:</label>
                            <div class="services-list">${incident.services_provided.join(', ')}</div>
                        </div>
                    ` : ''}
                    ${incident.resources_distributed && Array.isArray(incident.resources_distributed) && incident.resources_distributed.length > 0 ? `
                        <div class="detail-item">
                            <label>RESOURCES DISTRIBUTED:</label>
                            <div class="resources-list">${incident.resources_distributed.join(', ')}</div>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
            
            ${incident.referrals_made && Array.isArray(incident.referrals_made) && incident.referrals_made.length > 0 ? `
                <div class="detail-section">
                    <h4>&gt; REFERRALS MADE</h4>
                    <div class="referrals-display">${incident.referrals_made.join(', ')}</div>
                </div>
            ` : ''}
            
            ${incident.medical_assessment_notes ? `
                <div class="detail-section">
                    <h4>&gt; MEDICAL ASSESSMENT NOTES</h4>
                    <div class="medical-notes-display">${incident.medical_assessment_notes}</div>
                </div>
            ` : ''}
        </div>
    `,

    assessmentTab: (incident) => `
        <div class="assessment-content">
            ${incident.risk_level ? `
                <div class="risk-assessment">
                    <h4>RISK ASSESSMENT</h4>
                    <div class="risk-level">
                        <label>OVERALL RISK LEVEL:</label>
                        <span class="risk-indicator risk-${incident.risk_level}">${incident.risk_level?.toUpperCase()}</span>
                    </div>
                    
                    ${incident.safety_concerns ? `
                        <div class="safety-concerns">
                            <label>SAFETY CONCERNS:</label>
                            <div class="concerns-text">${incident.safety_concerns}</div>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
            
            ${incident.outcome_summary || incident.lessons_learned ? `
                <div class="outcome-assessment">
                    <h4>OUTCOME ASSESSMENT</h4>
                    
                    ${incident.outcome_summary ? `
                        <div class="outcome-summary">
                            <label>OUTCOME SUMMARY:</label>
                            <div class="summary-text">${incident.outcome_summary}</div>
                        </div>
                    ` : ''}
                    
                    ${incident.lessons_learned ? `
                        <div class="lessons-learned">
                            <label>LESSONS LEARNED:</label>
                            <div class="lessons-text">${incident.lessons_learned}</div>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
        </div>
    `,

    documentationTab: (incident) => `
        <div class="documentation-content">
            <div class="file-attachments">
                <h4>FILE ATTACHMENTS</h4>
                <div id="incident-files-list">
                    <!-- Files will be populated here -->
                </div>
            </div>
            
            ${incident.confidential_info ? `
                <div class="confidential-info">
                    <h4>CONFIDENTIAL INFORMATION</h4>
                    <div class="confidential-text">${incident.confidential_info}</div>
                </div>
            ` : ''}
        </div>
    `,

    propertyTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; PROPERTY INFORMATION</h4>
                <div class="property-info">
                    ${incident.property_involved ? `
                        <div class="detail-item">
                            <label>PROPERTY INVOLVED:</label>
                            <span class="detail-value">YES</span>
                        </div>
                        ${incident.property_description ? `
                            <div class="detail-item">
                                <label>DESCRIPTION:</label>
                                <span class="detail-value">${incident.property_description}</span>
                            </div>
                        ` : ''}
                        ${incident.property_value ? `
                            <div class="detail-item">
                                <label>ESTIMATED VALUE:</label>
                                <span class="detail-value">$${incident.property_value}</span>
                            </div>
                        ` : ''}
                    ` : `
                        <div class="detail-item">
                            <label>PROPERTY INVOLVED:</label>
                            <span class="detail-value">NO</span>
                        </div>
                    `}
                </div>
            </div>
        </div>
    `,

    agenciesTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; EMERGENCY SERVICES RESPONSE</h4>
                
                ${incident.police_notified ? `
                    <div class="agency-response">
                        <div class="agency-header">[POLICE NOTIFIED]</div>
                        ${incident.police_file_number ? `<div class="agency-detail">File Number: ${incident.police_file_number}</div>` : ''}
                        ${incident.officers_attending ? `<div class="agency-detail">Officers: ${incident.officers_attending}</div>` : ''}
                    </div>
                ` : ''}
                
                ${incident.fire_department_called ? `
                    <div class="agency-response">
                        <div class="agency-header">[FIRE DEPARTMENT CALLED]</div>
                        ${incident.fire_unit_number ? `<div class="agency-detail">Unit: ${incident.fire_unit_number}</div>` : ''}
                        ${incident.fire_personnel ? `<div class="agency-detail">Personnel: ${incident.fire_personnel}</div>` : ''}
                    </div>
                ` : ''}
                
                ${incident.paramedics_called ? `
                    <div class="agency-response">
                        <div class="agency-header">[PARAMEDICS/EMS CALLED]</div>
                        ${incident.ambulance_unit_number ? `<div class="agency-detail">Unit: ${incident.ambulance_unit_number}</div>` : ''}
                        ${incident.ems_personnel_names ? `<div class="agency-detail">Personnel: ${incident.ems_personnel_names}</div>` : ''}
                        ${incident.ems_response_time ? `<div class="agency-detail">Response Time: ${incident.ems_response_time}</div>` : ''}
                    </div>
                ` : ''}
                
                ${incident.bylaw_notified ? `
                    <div class="agency-response">
                        <div class="agency-header">[BYLAW NOTIFIED]</div>
                        ${incident.bylaw_file_number ? `<div class="agency-detail">File Number: ${incident.bylaw_file_number}</div>` : ''}
                        ${incident.bylaw_officers_attending ? `<div class="agency-detail">Officers: ${incident.bylaw_officers_attending}</div>` : ''}
                        ${incident.bylaw_response_time ? `<div class="agency-detail">Response Time: ${incident.bylaw_response_time}</div>` : ''}
                        ${incident.bylaw_enforcement_action ? `<div class="agency-detail">Enforcement Action: ${incident.bylaw_enforcement_action}</div>` : ''}
                        ${incident.bylaw_notes ? `<div class="agency-detail">Notes: ${incident.bylaw_notes}</div>` : ''}
                    </div>
                ` : ''}
                
                ${!incident.police_notified && !incident.fire_department_called && !incident.paramedics_called && !incident.bylaw_notified ? `
                    <div class="no-services">
                        [NO EMERGENCY SERVICES CONTACTED]
                    </div>
                ` : ''}
                
                ${incident.agency_response_notes ? `
                    <div class="detail-section">
                        <h4>&gt; AGENCY RESPONSE NOTES</h4>
                        <div class="agency-notes">
                            ${incident.agency_response_notes}
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `,

    safetyTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; SAFETY & ENVIRONMENT</h4>
                <div class="safety-info">
                    ${incident.scene_safety ? `
                        <div class="detail-item">
                            <label>SCENE SAFETY:</label>
                            <span class="detail-value">${incident.scene_safety}</span>
                        </div>
                    ` : ''}
                    
                    ${incident.safety_concerns ? `
                        <div class="detail-item">
                            <label>SAFETY CONCERNS:</label>
                            <div class="concerns-display">
                                ${incident.safety_concerns}
                            </div>
                        </div>
                    ` : ''}
                    
                    ${incident.substance_indicators && Array.isArray(incident.substance_indicators) && incident.substance_indicators.length > 0 ? `
                        <div class="detail-item">
                            <label>SUBSTANCE INDICATORS:</label>
                            <div class="substance-list">${incident.substance_indicators.join(', ')}</div>
                        </div>
                    ` : ''}
                    
                    ${incident.environmental_factors && Array.isArray(incident.environmental_factors) && incident.environmental_factors.length > 0 ? `
                        <div class="detail-item">
                            <label>ENVIRONMENTAL FACTORS:</label>
                            <div class="environmental-list">${incident.environmental_factors.join(', ')}</div>
                        </div>
                    ` : ''}
                    
                    ${incident.weather_conditions ? `
                        <div class="detail-item">
                            <label>WEATHER CONDITIONS:</label>
                            <span class="detail-value">${incident.weather_conditions}</span>
                        </div>
                    ` : ''}
                    
                    ${incident.scene_conditions ? `
                        <div class="detail-item">
                            <label>SCENE CONDITIONS:</label>
                            <span class="detail-value">${incident.scene_conditions}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `,

    followupTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; FOLLOW-UP STATUS</h4>
                <div class="followup-info">
                    <div class="detail-item">
                        <label>FOLLOW-UP REQUIRED:</label>
                        <span class="detail-value">${incident.follow_up_required ? 'YES' : 'NO'}</span>
                    </div>
                    
                    ${incident.follow_up_required && incident.follow_up_date ? `
                        <div class="detail-item">
                            <label>FOLLOW-UP DATE:</label>
                            <span class="detail-value">${new Date(incident.follow_up_date).toLocaleDateString()}</span>
                        </div>
                    ` : ''}
                    
                    ${incident.follow_up_notes ? `
                        <div class="detail-item">
                            <label>FOLLOW-UP NOTES:</label>
                            <div class="followup-display">
                                ${incident.follow_up_notes}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
            
            ${incident.hospital_transport_offered !== null ? `
                <div class="detail-section">
                    <h4>&gt; HOSPITAL TRANSPORT</h4>
                    <div class="transport-info">
                        <div class="detail-item">
                            <label>TRANSPORT OFFERED:</label>
                            <span class="detail-value">${incident.hospital_transport_offered ? 'YES' : 'NO'}</span>
                        </div>
                        
                        ${incident.transport_declined ? `
                            <div class="detail-item">
                                <label>TRANSPORT DECLINED:</label>
                                <span class="detail-value">YES</span>
                            </div>
                            ${incident.transport_decline_reason ? `
                                <div class="detail-item">
                                    <label>DECLINE REASON:</label>
                                    <span class="detail-value">${incident.transport_decline_reason}</span>
                                </div>
                            ` : ''}
                        ` : incident.hospital_destination ? `
                            <div class="detail-item">
                                <label>DESTINATION:</label>
                                <span class="detail-value">${incident.hospital_destination}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            ` : ''}
        </div>
    `,

    filesTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; FILE ATTACHMENTS</h4>
                <div id="incident-files-list" class="files-list">
                    <!-- Files will be populated here dynamically -->
                </div>
            </div>
            
            ${incident.confidential_info ? `
                <div class="detail-section">
                    <h4>&gt; CONFIDENTIAL INFORMATION</h4>
                    <div class="confidential-display">
                        ${incident.confidential_info}
                    </div>
                </div>
            ` : ''}
        </div>
    `,

    mapTab: (incident) => `
        <div class="map-content">
            <div id="incident-detail-map" class="detail-map-container">
                ${incident.coordinates ? `
                    <div class="map-placeholder">
                        MAP VIEW<br>
                        COORDINATES: ${incident.coordinates}
                    </div>
                ` : `
                    <div class="no-location">
                        NO LOCATION DATA AVAILABLE
                    </div>
                `}
            </div>
        </div>
    `,

    narrativeTab: (incident) => `
        <div class="detail-tab-content">
            <div class="detail-section">
                <h4>&gt; EVENTS & NARRATIVE</h4>
                <div class="narrative-container">
                    <div class="narrative-header">
                        <button class="action-btn add-narrative-btn" onclick="window.app.addNarrativeEntry('${incident.id}')">
                            📝 Add Entry
                        </button>
                    </div>
                    <div class="narrative-entries" id="narrative-entries-${incident.id}">
                        <!-- Narrative entries will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    `,

    // Update Status Modal
    updateStatusModal: (incident) => `
        <div class="modal-overlay" id="update-status-modal">
            <div class="modal-container">
                <div class="modal-header">
                    <h3>&gt; UPDATE INCIDENT STATUS</h3>
                    <button class="modal-close" onclick="window.app.closeUpdateStatusModal()">×</button>
                </div>
                
                <div class="modal-content">
                    <div class="current-status-display">
                        <div class="form-row">
                            <label>CURRENT STATUS:</label>
                            <span class="current-status status-${incident.status}">[${incident.status?.toUpperCase()}]</span>
                        </div>
                    </div>
                    
                    <form id="update-status-form" class="terminal-form">
                        <div class="form-row">
                            <label for="new-status">NEW STATUS:</label>
                            <select id="new-status" name="status" required>
                                <option value="">Select Status...</option>
                                <option value="draft" ${incident.status === 'draft' ? 'disabled' : ''}>Draft</option>
                                <option value="open" ${incident.status === 'open' ? 'disabled' : ''}>Open</option>
                                <option value="in_progress" ${incident.status === 'in_progress' ? 'disabled' : ''}>In Progress</option>
                                <option value="resolved" ${incident.status === 'resolved' ? 'disabled' : ''}>Resolved</option>
                                <option value="closed" ${incident.status === 'closed' ? 'disabled' : ''}>Closed</option>
                            </select>
                        </div>
                        
                        <div class="form-row">
                            <label for="status-notes">STATUS CHANGE NOTES:</label>
                            <textarea id="status-notes" name="notes" rows="3" placeholder="Optional notes about status change..."></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="action-button primary">UPDATE STATUS</button>
                            <button type="button" class="action-button" onclick="window.app.closeUpdateStatusModal()">CANCEL</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,

    // Print template for incident reports
    printReport: (incident) => `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Incident Report #${incident.incident_number || incident.id}</title>
            <style>
                @page {
                    margin: 0.75in;
                    size: letter;
                }
                
                body {
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    line-height: 1.4;
                    margin: 0;
                    padding: 0.5in;
                    color: #000;
                    background: #fff;
                    max-width: 100%;
                    box-sizing: border-box;
                }
                
                * {
                    box-sizing: border-box;
                    max-width: 100%;
                }
                
                .section {
                    width: 100%;
                    margin-bottom: 25px;
                    break-inside: avoid;
                    box-sizing: border-box;
                }
                
                .section-title {
                    width: 100%;
                    font-size: 14px;
                    font-weight: bold;
                    border-bottom: 1px solid #000;
                    padding-bottom: 3px;
                    margin-bottom: 10px;
                    text-transform: uppercase;
                    box-sizing: border-box;
                }
                
                .print-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border-bottom: 2px solid #000;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                    width: 100%;
                    box-sizing: border-box;
                    flex-wrap: wrap;
                    gap: 10px;
                }
                
                .logo-section {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    min-width: 0;
                }
                
                .logo-section img {
                    height: 60px;
                    margin-right: 15px;
                    max-width: 100%;
                    height: auto;
                }
                
                .org-info {
                    flex: 1;
                    min-width: 0;
                    word-wrap: break-word;
                }
                
                .org-name {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 2px;
                }
                
                .org-subtitle {
                    font-size: 10px;
                    color: #666;
                }
                
                .report-info {
                    text-align: right;
                    font-size: 10px;
                }
                
                .report-title {
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    margin: 20px 0;
                    text-decoration: underline;
                }
                
                .incident-number {
                    text-align: center;
                    font-size: 14px;
                    margin-bottom: 30px;
                }
                
                
                .field-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    margin-bottom: 15px;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .field-row {
                    margin-bottom: 8px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                
                .field-label {
                    font-weight: bold;
                    display: inline-block;
                    min-width: 120px;
                    word-wrap: break-word;
                }
                
                .field-value {
                    margin-left: 10px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                
                .full-width {
                    grid-column: 1 / -1;
                }
                
                .text-block {
                    background: #f9f9f9;
                    border: 1px solid #ddd;
                    padding: 10px;
                    margin-top: 5px;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .person-card {
                    border: 1px solid #ccc;
                    padding: 10px;
                    margin-bottom: 10px;
                    background: #f9f9f9;
                    width: 100%;
                    box-sizing: border-box;
                    word-wrap: break-word;
                }
                
                .person-header {
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                
                .medical-section {
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    padding: 8px;
                    margin-top: 8px;
                }
                
                .medical-title {
                    font-weight: bold;
                    color: #856404;
                    margin-bottom: 5px;
                }
                
                .services-grid {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 10px;
                    margin-bottom: 10px;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .service-item {
                    text-align: center;
                    padding: 5px;
                    border: 1px solid #ddd;
                    word-wrap: break-word;
                    box-sizing: border-box;
                }
                
                .service-item.active {
                    background: #d4edda;
                    border-color: #28a745;
                }
                
                .footer {
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #000;
                    font-size: 10px;
                    text-align: center;
                }
                
                .signature-section {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 40px;
                    margin-top: 30px;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .signature-box {
                    border-top: 1px solid #000;
                    padding-top: 5px;
                    text-align: center;
                    word-wrap: break-word;
                    box-sizing: border-box;
                }
                
                .narrative-entry-print {
                    border: 1px solid #ddd;
                    margin-bottom: 15px;
                    break-inside: avoid;
                    background: #f9f9f9;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .narrative-entry-header {
                    background: #e9e9e9;
                    padding: 8px 12px;
                    border-bottom: 1px solid #ddd;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .narrative-entry-meta {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 10px;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                .narrative-entry-meta strong {
                    color: #333;
                    font-weight: bold;
                    word-wrap: break-word;
                }
                
                .narrative-timestamp {
                    font-size: 11px;
                    color: #666;
                    word-wrap: break-word;
                }
                
                .narrative-user {
                    font-size: 11px;
                    color: #666;
                    font-style: italic;
                    word-wrap: break-word;
                }
                
                .narrative-entry-content {
                    padding: 12px;
                    line-height: 1.6;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                    width: 100%;
                    box-sizing: border-box;
                }
                
                @media print {
                    body { 
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                    
                    .narrative-entry-print {
                        page-break-inside: avoid;
                    }
                }
            </style>
        </head>
        <body>
            <!-- Header with IHARC Logo -->
            <div class="print-header">
                <div class="logo-section">
                    <img src="../assets/iharclogowithtag.png" alt="IHARC Logo">
                    <div class="org-info">
                        <div class="org-name">I.H.A.R.C.</div>
                        <div class="org-subtitle">Integrated Homelessness & Addictions Response Centre</div>
                    </div>
                </div>
                <div class="report-info">
                    <div>Report Generated: ${new Date().toLocaleDateString()}</div>
                    <div>Time: ${new Date().toLocaleTimeString()}</div>
                </div>
            </div>
            
            <!-- Report Title -->
            <div class="report-title">INCIDENT REPORT</div>
            <div class="incident-number">Incident #${incident.incident_number || incident.id}</div>
            
            <!-- Basic Information Section -->
            <div class="section">
                <div class="section-title">Basic Information</div>
                <div class="field-grid">
                    <div class="field-row">
                        <span class="field-label">Incident Type:</span>
                        <span class="field-value">${incident.incident_type?.replace('_', ' ').toUpperCase() || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Priority:</span>
                        <span class="field-value">${incident.priority?.toUpperCase() || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Status:</span>
                        <span class="field-value">${incident.status?.toUpperCase() || 'N/A'}</span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Date & Time:</span>
                        <span class="field-value">${incident.incident_date ? new Date(incident.incident_date).toLocaleDateString() : 'N/A'} ${incident.incident_time || ''}</span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Assigned Ranger:</span>
                        <span class="field-value">${incident.assigned_ranger || 'Unassigned'}</span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Created By:</span>
                        <span class="field-value">${incident.created_by || 'Unknown'}</span>
                    </div>
                </div>
            </div>
            
            <!-- Location Information -->
            <div class="section">
                <div class="section-title">Location Information</div>
                ${incident.street_address || incident.city || incident.location ? `
                    <div class="field-row">
                        <span class="field-label">Address:</span>
                        <span class="field-value">
                            ${incident.street_address || ''}
                            ${incident.street_address && (incident.city || incident.province) ? ', ' : ''}
                            ${incident.city || ''}${incident.province ? `, ${incident.province}` : ''}${incident.postal_code ? ` ${incident.postal_code}` : ''}
                            ${!incident.street_address && !incident.city && incident.location ? incident.location : ''}
                        </span>
                    </div>
                ` : '<div class="field-row">No specific location recorded</div>'}
                ${incident.coordinates ? `
                    <div class="field-row">
                        <span class="field-label">GPS Coordinates:</span>
                        <span class="field-value">${incident.coordinates}</span>
                    </div>
                ` : ''}
                ${incident.location_notes ? `
                    <div class="field-row full-width">
                        <span class="field-label">Location Notes:</span>
                        <div class="text-block">${incident.location_notes}</div>
                    </div>
                ` : ''}
            </div>
            
            <!-- Summary -->
            <div class="section">
                <div class="section-title">Summary</div>
                <div class="text-block">${incident.description || '[NO SUMMARY PROVIDED]'}</div>
            </div>
            
            <!-- Narrative Entries -->
            ${incident.narrativeEntries && Array.isArray(incident.narrativeEntries) && incident.narrativeEntries.length > 0 ? `
                <div class="section">
                    <div class="section-title">Events & Narrative</div>
                    ${incident.narrativeEntries.map((entry, index) => `
                        <div class="narrative-entry-print">
                            <div class="narrative-entry-header">
                                <div class="narrative-entry-meta">
                                    <strong>${entry.entry_type ? entry.entry_type.replace('_', ' ').toUpperCase() : 'ENTRY'}</strong>
                                    <span class="narrative-timestamp">
                                        ${entry.event_time ? new Date(entry.event_time).toLocaleString() : (entry.timestamp ? new Date(entry.timestamp).toLocaleString() : 'No timestamp')}
                                    </span>
                                    <span class="narrative-user">by ${entry.user || 'Unknown'}</span>
                                </div>
                            </div>
                            <div class="narrative-entry-content">
                                ${entry.content || '[No content]'}
                            </div>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
            
            <!-- People Involved -->
            ${incident.incident_people && Array.isArray(incident.incident_people) && incident.incident_people.length > 0 ? `
                <div class="section">
                    <div class="section-title">People Involved</div>
                    ${incident.incident_people.map(person => `
                        <div class="person-card">
                            <div class="person-header">
                                ${person.name || person.unnamed_description || 'Unnamed Person'}
                                ${person.age ? ` (Age: ${person.age})` : ''}
                                - ${(person.role || 'unknown').replace('_', ' ').toUpperCase()}
                            </div>
                            ${person.description ? `
                                <div class="field-row">
                                    <span class="field-label">Description:</span>
                                    <span class="field-value">${person.description}</span>
                                </div>
                            ` : ''}
                            ${person.has_medical_info ? `
                                <div class="medical-section">
                                    <div class="medical-title">Medical Information</div>
                                    ${person.medical_info.medical_issue_type ? `
                                        <div class="field-row">
                                            <span class="field-label">Medical Issue:</span>
                                            <span class="field-value">${person.medical_info.medical_issue_type}</span>
                                        </div>
                                    ` : ''}
                                    ${person.medical_info.medical_issue_description ? `
                                        <div class="field-row">
                                            <span class="field-label">Description:</span>
                                            <div class="text-block">${person.medical_info.medical_issue_description}</div>
                                        </div>
                                    ` : ''}
                                    ${person.medical_info.injury_severity ? `
                                        <div class="field-row">
                                            <span class="field-label">Severity:</span>
                                            <span class="field-value">${person.medical_info.injury_severity.toUpperCase()}</span>
                                        </div>
                                    ` : ''}
                                    ${person.medical_info.first_aid_provided ? `
                                        <div class="field-row">
                                            <span class="field-label">First Aid Provided:</span>
                                            <span class="field-value">YES</span>
                                        </div>
                                        ${person.medical_info.first_aid_details ? `
                                            <div class="text-block">${person.medical_info.first_aid_details}</div>
                                        ` : ''}
                                    ` : ''}
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            ` : ''}
            
            <!-- Emergency Services -->
            ${incident.police_notified || incident.fire_department_called || incident.paramedics_called ? `
                <div class="section">
                    <div class="section-title">Emergency Services Response</div>
                    <div class="services-grid">
                        <div class="service-item ${incident.police_notified ? 'active' : ''}">
                            <strong>POLICE</strong><br>
                            ${incident.police_notified ? 'NOTIFIED' : 'NOT NOTIFIED'}
                        </div>
                        <div class="service-item ${incident.fire_department_called ? 'active' : ''}">
                            <strong>FIRE DEPT</strong><br>
                            ${incident.fire_department_called ? 'CALLED' : 'NOT CALLED'}
                        </div>
                        <div class="service-item ${incident.paramedics_called ? 'active' : ''}">
                            <strong>PARAMEDICS</strong><br>
                            ${incident.paramedics_called ? 'CALLED' : 'NOT CALLED'}
                        </div>
                    </div>
                    ${incident.police_file_number ? `
                        <div class="field-row">
                            <span class="field-label">Police File Number:</span>
                            <span class="field-value">${incident.police_file_number}</span>
                        </div>
                    ` : ''}
                    ${incident.agency_response_notes ? `
                        <div class="field-row">
                            <span class="field-label">Agency Response Notes:</span>
                            <div class="text-block">${incident.agency_response_notes}</div>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
            
            <!-- Services & Resources -->
            ${(incident.services_offered && Array.isArray(incident.services_offered) && incident.services_offered.length > 0) || (incident.services_provided && Array.isArray(incident.services_provided) && incident.services_provided.length > 0) || (incident.resources_distributed && Array.isArray(incident.resources_distributed) && incident.resources_distributed.length > 0) ? `
                <div class="section">
                    <div class="section-title">Services & Resources</div>
                    ${incident.services_offered && Array.isArray(incident.services_offered) && incident.services_offered.length > 0 ? `
                        <div class="field-row">
                            <span class="field-label">Services Offered:</span>
                            <span class="field-value">${incident.services_offered.join(', ')}</span>
                        </div>
                    ` : ''}
                    ${incident.services_provided && Array.isArray(incident.services_provided) && incident.services_provided.length > 0 ? `
                        <div class="field-row">
                            <span class="field-label">Services Provided:</span>
                            <span class="field-value">${incident.services_provided.join(', ')}</span>
                        </div>
                    ` : ''}
                    ${incident.resources_distributed && Array.isArray(incident.resources_distributed) && incident.resources_distributed.length > 0 ? `
                        <div class="field-row">
                            <span class="field-label">Resources Distributed:</span>
                            <span class="field-value">${incident.resources_distributed.join(', ')}</span>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
            
            <!-- Safety & Environment -->
            ${incident.safety_concerns || (incident.substance_indicators && Array.isArray(incident.substance_indicators) && incident.substance_indicators.length > 0) || (incident.environmental_factors && Array.isArray(incident.environmental_factors) && incident.environmental_factors.length > 0) || incident.weather_conditions ? `
                <div class="section">
                    <div class="section-title">Safety & Environment</div>
                    ${incident.safety_concerns ? `
                        <div class="field-row">
                            <span class="field-label">Safety Concerns:</span>
                            <div class="text-block">${incident.safety_concerns}</div>
                        </div>
                    ` : ''}
                    ${incident.substance_indicators && Array.isArray(incident.substance_indicators) && incident.substance_indicators.length > 0 ? `
                        <div class="field-row">
                            <span class="field-label">Substance Indicators:</span>
                            <span class="field-value">${incident.substance_indicators.join(', ')}</span>
                        </div>
                    ` : ''}
                    ${incident.environmental_factors && Array.isArray(incident.environmental_factors) && incident.environmental_factors.length > 0 ? `
                        <div class="field-row">
                            <span class="field-label">Environmental Factors:</span>
                            <span class="field-value">${incident.environmental_factors.join(', ')}</span>
                        </div>
                    ` : ''}
                    ${incident.weather_conditions ? `
                        <div class="field-row">
                            <span class="field-label">Weather Conditions:</span>
                            <span class="field-value">${incident.weather_conditions}</span>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
            
            <!-- Follow-up Information -->
            ${incident.follow_up_required || incident.follow_up_notes ? `
                <div class="section">
                    <div class="section-title">Follow-up Information</div>
                    <div class="field-row">
                        <span class="field-label">Follow-up Required:</span>
                        <span class="field-value">${incident.follow_up_required ? 'YES' : 'NO'}</span>
                    </div>
                    ${incident.follow_up_date ? `
                        <div class="field-row">
                            <span class="field-label">Follow-up Date:</span>
                            <span class="field-value">${new Date(incident.follow_up_date).toLocaleDateString()}</span>
                        </div>
                    ` : ''}
                    ${incident.follow_up_notes ? `
                        <div class="field-row">
                            <span class="field-label">Follow-up Notes:</span>
                            <div class="text-block">${incident.follow_up_notes}</div>
                        </div>
                    ` : ''}
                </div>
            ` : ''}
            
            <!-- Additional Notes -->
            ${incident.additional_notes ? `
                <div class="section">
                    <div class="section-title">Additional Notes</div>
                    <div class="text-block">${incident.additional_notes}</div>
                </div>
            ` : ''}
            
            <!-- Audit Trail -->
            <div class="section">
                <div class="section-title">Audit Trail</div>
                <div class="field-grid">
                    <div class="field-row">
                        <span class="field-label">Created:</span>
                        <span class="field-value">${incident.created_at ? new Date(incident.created_at).toLocaleString() : 'N/A'}</span>
                    </div>
                    <div class="field-row">
                        <span class="field-label">Last Updated:</span>
                        <span class="field-value">${incident.updated_at ? new Date(incident.updated_at).toLocaleString() : 'N/A'}</span>
                    </div>
                </div>
            </div>
            
            <!-- Signature Section -->
            <div class="signature-section">
                <div class="signature-box">
                    <div>Ranger Signature</div>
                    <div style="margin-top: 10px; font-size: 10px;">Date: _____________</div>
                </div>
                <div class="signature-box">
                    <div>Supervisor Signature</div>
                    <div style="margin-top: 10px; font-size: 10px;">Date: _____________</div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="footer">
                <div>This report was generated electronically by the S.T.E.V.I Retro system</div>
                <div>I.H.A.R.C. - Integrated Homelessness & Addictions Response Centre</div>
                <div>Confidential - For Official Use Only</div>
            </div>
        </body>
        </html>
    `
};

