/**
 * People Search Manager
 * Handles people search functionality
 */

import { BaseManager } from '../../shared/base-manager.js';
import { SearchUtility } from '../../shared/search-utility.js';

export class PeopleSearchManager extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.searchUtility = new SearchUtility();
    }

    /**
     * Search people with query
     */
    async searchPeople(query) {
        try {
            // Get all people records first
            const allPeople = await this.data.search('people', {});

            if (!query || query.trim() === '') {
                return allPeople;
            }

            // Use the SearchUtility static method for proper search
            const searchFields = ['first_name', 'last_name', 'email', 'phone', 'housing_status', 'notes'];
            const searchResults = SearchUtility.search(allPeople, query, searchFields);

            return searchResults;
        } catch (error) {
            console.error('Error searching people:', error);
            throw error;
        }
    }

    /**
     * Search people by specific criteria
     */
    async searchPeopleByCriteria(criteria) {
        try {
            const results = await this.data.search('people', criteria);
            return results || [];
        } catch (error) {
            console.error('Error searching people by criteria:', error);
            throw error;
        }
    }

    /**
     * Show people search interface
     */
    async showPeopleSearchInterface() {
        try {
            // Create search modal
            const searchModal = document.createElement('div');
            searchModal.className = 'modal-overlay people-search-overlay';
            
            searchModal.innerHTML = `
                <div class="modal-dialog large-modal">
                    <div class="modal-header">
                        <h2>Search People</h2>
                        <button class="close-button" id="close-search-modal">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="search-section">
                            <div class="search-bar">
                                <input type="text" id="people-search-input" placeholder="Search by name, email, phone, housing status..." class="search-input" autofocus>
                                <button class="search-button" id="execute-search">🔍</button>
                            </div>
                            
                            <div class="advanced-search-toggle">
                                <button type="button" id="toggle-advanced-search" class="link-button">
                                    Advanced Search Options
                                </button>
                            </div>
                            
                            <div class="advanced-search-panel" id="advanced-search-panel" style="display: none;">
                                <div class="search-fields-grid">
                                    <div class="search-field">
                                        <label>First Name:</label>
                                        <input type="text" id="search-first-name" class="search-input small">
                                    </div>
                                    <div class="search-field">
                                        <label>Last Name:</label>
                                        <input type="text" id="search-last-name" class="search-input small">
                                    </div>
                                    <div class="search-field">
                                        <label>Email:</label>
                                        <input type="text" id="search-email" class="search-input small">
                                    </div>
                                    <div class="search-field">
                                        <label>Phone:</label>
                                        <input type="text" id="search-phone" class="search-input small">
                                    </div>
                                    <div class="search-field">
                                        <label>Housing Status:</label>
                                        <select id="search-housing-status" class="search-input small">
                                            <option value="">Any</option>
                                            <option value="Homeless">Homeless</option>
                                            <option value="Housed">Housed</option>
                                            <option value="At Risk">At Risk</option>
                                            <option value="Transitional">Transitional</option>
                                            <option value="Unknown">Unknown</option>
                                        </select>
                                    </div>
                                    <div class="search-field">
                                        <label>Date Range:</label>
                                        <div class="date-range">
                                            <input type="date" id="search-date-from" class="search-input small">
                                            <span>to</span>
                                            <input type="date" id="search-date-to" class="search-input small">
                                        </div>
                                    </div>
                                </div>
                                <div class="advanced-search-actions">
                                    <button type="button" id="clear-advanced-search" class="secondary-button">Clear All</button>
                                    <button type="button" id="execute-advanced-search" class="primary-button">Search</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="search-results-section">
                            <div class="search-results-header">
                                <span id="search-results-count">Enter search terms above</span>
                                <div class="search-results-controls">
                                    <select id="search-sort-by" class="search-input small">
                                        <option value="first_name">Sort by First Name</option>
                                        <option value="last_name">Sort by Last Name</option>
                                        <option value="created_at">Sort by Date Added</option>
                                        <option value="updated_at">Sort by Last Updated</option>
                                    </select>
                                </div>
                            </div>
                            <div class="search-results-container" id="search-results-container">
                                <div class="search-prompt">
                                    <div class="search-icon">🔍</div>
                                    <p>Use the search bar above to find people in the system.</p>
                                    <p><strong>Quick tips:</strong></p>
                                    <ul>
                                        <li>Search by partial names, email addresses, or phone numbers</li>
                                        <li>Use advanced search for more specific criteria</li>
                                        <li>Click on any result to view full details</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="secondary-button" id="close-search">Close</button>
                    </div>
                </div>
            `;

            document.body.appendChild(searchModal);

            // Set up event handlers
            this.setupSearchModalHandlers(searchModal);

        } catch (error) {
            console.error('Error showing people search interface:', error);
            this.ui.showDialog('Error', 'Failed to show search interface', 'error');
        }
    }

    /**
     * Set up search modal event handlers
     */
    setupSearchModalHandlers(modal) {
        // Close modal handlers
        const closeButtons = modal.querySelectorAll('#close-search-modal, #close-search');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });

        // Search input handlers
        const searchInput = modal.querySelector('#people-search-input');
        const executeSearchBtn = modal.querySelector('#execute-search');

        const performSearch = async () => {
            const query = searchInput.value.trim();
            await this.executeSearch(modal, query);
        };

        if (searchInput) {
            // Search on Enter key
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Live search with debounce
            let searchTimeout;
            searchInput.addEventListener('input', () => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const query = searchInput.value.trim();
                    if (query.length >= 2) {
                        this.executeSearch(modal, query);
                    } else if (query.length === 0) {
                        this.clearSearchResults(modal);
                    }
                }, 300);
            });
        }

        if (executeSearchBtn) {
            executeSearchBtn.addEventListener('click', performSearch);
        }

        // Advanced search toggle
        const toggleAdvanced = modal.querySelector('#toggle-advanced-search');
        const advancedPanel = modal.querySelector('#advanced-search-panel');
        
        if (toggleAdvanced && advancedPanel) {
            toggleAdvanced.addEventListener('click', () => {
                const isVisible = advancedPanel.style.display !== 'none';
                advancedPanel.style.display = isVisible ? 'none' : 'block';
                toggleAdvanced.textContent = isVisible ? 'Advanced Search Options' : 'Hide Advanced Options';
            });
        }

        // Advanced search handlers
        const executeAdvancedBtn = modal.querySelector('#execute-advanced-search');
        const clearAdvancedBtn = modal.querySelector('#clear-advanced-search');

        if (executeAdvancedBtn) {
            executeAdvancedBtn.addEventListener('click', () => {
                this.executeAdvancedSearch(modal);
            });
        }

        if (clearAdvancedBtn) {
            clearAdvancedBtn.addEventListener('click', () => {
                this.clearAdvancedSearch(modal);
            });
        }

        // Sort handler
        const sortSelect = modal.querySelector('#search-sort-by');
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.sortSearchResults(modal, sortSelect.value);
            });
        }

        // Close on overlay click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Execute search and display results
     */
    async executeSearch(modal, query) {
        try {
            const resultsContainer = modal.querySelector('#search-results-container');
            const resultsCount = modal.querySelector('#search-results-count');

            // Show loading state
            resultsContainer.innerHTML = '<div class="loading">Searching...</div>';
            resultsCount.textContent = 'Searching...';

            // Perform search
            const results = await this.searchPeople(query);

            // Display results
            this.displaySearchResults(modal, results, query);

        } catch (error) {
            console.error('Error executing search:', error);
            const resultsContainer = modal.querySelector('#search-results-container');
            resultsContainer.innerHTML = '<div class="error">Search failed. Please try again.</div>';
        }
    }

    /**
     * Execute advanced search
     */
    async executeAdvancedSearch(modal) {
        try {
            const criteria = this.getAdvancedSearchCriteria(modal);
            const results = await this.searchPeopleByCriteria(criteria);
            this.displaySearchResults(modal, results, 'Advanced Search');
        } catch (error) {
            console.error('Error executing advanced search:', error);
            const resultsContainer = modal.querySelector('#search-results-container');
            resultsContainer.innerHTML = '<div class="error">Advanced search failed. Please try again.</div>';
        }
    }

    /**
     * Get advanced search criteria from form
     */
    getAdvancedSearchCriteria(modal) {
        const criteria = {};

        const firstName = modal.querySelector('#search-first-name').value.trim();
        const lastName = modal.querySelector('#search-last-name').value.trim();
        const email = modal.querySelector('#search-email').value.trim();
        const phone = modal.querySelector('#search-phone').value.trim();
        const housingStatus = modal.querySelector('#search-housing-status').value.trim();
        const dateFrom = modal.querySelector('#search-date-from').value;
        const dateTo = modal.querySelector('#search-date-to').value;

        if (firstName) criteria.first_name = firstName;
        if (lastName) criteria.last_name = lastName;
        if (email) criteria.email = email;
        if (phone) criteria.phone = phone;
        if (housingStatus) criteria.housing_status = housingStatus;

        // Handle date range
        if (dateFrom || dateTo) {
            criteria.created_at = {};
            if (dateFrom) criteria.created_at.gte = dateFrom;
            if (dateTo) criteria.created_at.lte = dateTo;
        }

        return criteria;
    }

    /**
     * Clear advanced search form
     */
    clearAdvancedSearch(modal) {
        const inputs = modal.querySelectorAll('#advanced-search-panel input, #advanced-search-panel select');
        inputs.forEach(input => {
            input.value = '';
        });
    }

    /**
     * Display search results
     */
    displaySearchResults(modal, results, query) {
        const resultsContainer = modal.querySelector('#search-results-container');
        const resultsCount = modal.querySelector('#search-results-count');

        resultsCount.textContent = `${results.length} result${results.length !== 1 ? 's' : ''} found`;

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <div class="no-results-icon">🔍</div>
                    <h3>No Results Found</h3>
                    <p>No people match your search criteria.</p>
                    <p>Try adjusting your search terms or using different keywords.</p>
                </div>
            `;
            return;
        }

        // Render results
        resultsContainer.innerHTML = results.map(person => `
            <div class="search-result-item" data-person-id="${person.id}">
                <div class="result-header">
                    <h3 class="person-name">${person.first_name} ${person.last_name}</h3>
                    <div class="result-actions">
                        <button class="action-btn view-btn" data-action="view-person-detail" data-person-id="${person.id}">
                            👁️ View
                        </button>
                        <button class="action-btn edit-btn" data-action="edit-person" data-person-id="${person.id}">
                            ✏️ Edit
                        </button>
                    </div>
                </div>
                <div class="result-details">
                    ${person.email ? `<div class="detail-item"><span class="icon">📧</span> ${person.email}</div>` : ''}
                    ${person.phone ? `<div class="detail-item"><span class="icon">📱</span> ${person.phone}</div>` : ''}
                    ${person.housing_status ? `<div class="detail-item"><span class="icon">🏠</span> ${person.housing_status}</div>` : ''}
                    ${person.date_of_birth ? `<div class="detail-item"><span class="icon">🎂</span> ${new Date(person.date_of_birth).toLocaleDateString()}</div>` : ''}
                </div>
                <div class="result-meta">
                    <small>Added: ${new Date(person.created_at).toLocaleDateString()}</small>
                    ${person.updated_at ? `<small>Updated: ${new Date(person.updated_at).toLocaleDateString()}</small>` : ''}
                </div>
            </div>
        `).join('');

        // Set up result click handlers
        this.setupResultClickHandlers(modal);
    }

    /**
     * Set up click handlers for search results
     */
    setupResultClickHandlers(modal) {
        const actionButtons = modal.querySelectorAll('.result-actions .action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = btn.getAttribute('data-action');
                const personId = btn.getAttribute('data-person-id');
                
                // Close search modal
                document.body.removeChild(modal);
                
                // Execute the action
                if (window.app && action && personId) {
                    window.app.executeCommand(action, { personId });
                }
            });
        });

        // Handle result item clicks
        const resultItems = modal.querySelectorAll('.search-result-item');
        resultItems.forEach(item => {
            item.addEventListener('click', () => {
                const personId = item.getAttribute('data-person-id');
                if (personId) {
                    // Close search modal
                    document.body.removeChild(modal);
                    
                    // View person detail
                    if (window.app) {
                        window.app.executeCommand('view-person-detail', { personId });
                    }
                }
            });
        });
    }

    /**
     * Sort search results
     */
    sortSearchResults(modal, sortField) {
        // This would implement result sorting
        console.log('Sort search results by:', sortField);
    }

    /**
     * Clear search results
     */
    clearSearchResults(modal) {
        const resultsContainer = modal.querySelector('#search-results-container');
        const resultsCount = modal.querySelector('#search-results-count');
        
        resultsCount.textContent = 'Enter search terms above';
        resultsContainer.innerHTML = `
            <div class="search-prompt">
                <div class="search-icon">🔍</div>
                <p>Use the search bar above to find people in the system.</p>
            </div>
        `;
    }

    /**
     * Cleanup method
     */
    cleanup() {
        // Remove any event listeners if needed
    }
}