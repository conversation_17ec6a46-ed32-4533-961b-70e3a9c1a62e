// Status Ribbon View
// Read-only header showing current justice status snapshot

import { statusRibbonTemplates } from '../../templates/status-ribbon-templates.js';

export class StatusRibbon {
    constructor(justiceModule) {
        this.justice = justiceModule;
        this.container = null;
        this.episode = null;
    }
    
    async mount(container, episode) {
        this.container = container;
        this.episode = episode;
        
        try {
            // Get current status from API
            const status = await this.justice.api.getEpisode(episode.id);
            
            // Render the status ribbon
            this.render(status);
            
        } catch (error) {
            console.error('Failed to mount status ribbon:', error);
            this.renderError(error.message);
        }
    }
    
    render(status) {
        if (!this.container) return;
        
        this.container.innerHTML = statusRibbonTemplates.statusRibbon(status);
        
        // Add any interactive elements if needed
        this.setupEventHandlers();
    }
    
    renderError(message) {
        if (!this.container) return;
        
        this.container.innerHTML = statusRibbonTemplates.errorRibbon(message);
    }
    
    setupEventHandlers() {
        // Status ribbon is read-only, but we might add refresh functionality
        const refreshBtn = this.container.querySelector('[data-action="refresh-status"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                await this.refresh();
            });
        }
    }
    
    async refresh() {
        if (!this.episode) return;
        
        try {
            const status = await this.justice.api.getEpisode(this.episode.id);
            this.render(status);
        } catch (error) {
            console.error('Failed to refresh status:', error);
            this.justice.ui.showDialog('Error', `Failed to refresh status: ${error.message}`, 'error');
        }
    }
    
    unmount() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.container = null;
        this.episode = null;
    }
}
