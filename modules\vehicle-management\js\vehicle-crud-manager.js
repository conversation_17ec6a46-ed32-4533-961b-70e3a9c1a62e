/**
 * Vehicle CRUD Manager
 * Handles Create, Read, Update, Delete operations for vehicles
 * Extends BaseCrudManager for standardized CRUD operations
 */

import { BaseCrudManager } from '../../shared/base-crud-manager.js';

export class VehicleCrudManager extends BaseCrudManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super(dataManager, authManager, 'license_plates', 'vehicle', uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.onVehicleChanged = null; // Callback for when vehicles change
    }

    /**
     * Show add vehicle form
     */
    async showAddVehicleForm() {
        try {
            // Generate form fields for license plates
            const fields = this.data.schema.generateFormFields('license_plates');

            // Customize the province field to have proper options and default
            const provinceField = fields.find(f => f.name === 'province');
            if (provinceField) {
                provinceField.type = 'select';
                provinceField.defaultValue = 'ON';
                provinceField.options = [
                    { value: '', label: 'CANADA', disabled: true, isHeader: true },
                    { value: 'AB', label: 'Alberta' },
                    { value: 'BC', label: 'British Columbia' },
                    { value: 'MB', label: 'Manitoba' },
                    { value: 'NB', label: 'New Brunswick' },
                    { value: 'NL', label: 'Newfoundland and Labrador' },
                    { value: 'NS', label: 'Nova Scotia' },
                    { value: 'ON', label: 'Ontario', selected: true },
                    { value: 'PE', label: 'Prince Edward Island' },
                    { value: 'QC', label: 'Quebec' },
                    { value: 'SK', label: 'Saskatchewan' },
                    { value: 'NT', label: 'Northwest Territories' },
                    { value: 'NU', label: 'Nunavut' },
                    { value: 'YT', label: 'Yukon' },
                    { value: '', label: 'UNITED STATES', disabled: true, isHeader: true },
                    { value: 'AL', label: 'Alabama' },
                    { value: 'AK', label: 'Alaska' },
                    { value: 'AZ', label: 'Arizona' },
                    { value: 'AR', label: 'Arkansas' },
                    { value: 'CA', label: 'California' },
                    { value: 'CO', label: 'Colorado' },
                    { value: 'CT', label: 'Connecticut' },
                    { value: 'DE', label: 'Delaware' },
                    { value: 'FL', label: 'Florida' },
                    { value: 'GA', label: 'Georgia' },
                    { value: 'HI', label: 'Hawaii' },
                    { value: 'ID', label: 'Idaho' },
                    { value: 'IL', label: 'Illinois' },
                    { value: 'IN', label: 'Indiana' },
                    { value: 'IA', label: 'Iowa' },
                    { value: 'KS', label: 'Kansas' },
                    { value: 'KY', label: 'Kentucky' },
                    { value: 'LA', label: 'Louisiana' },
                    { value: 'ME', label: 'Maine' },
                    { value: 'MD', label: 'Maryland' },
                    { value: 'MA', label: 'Massachusetts' },
                    { value: 'MI', label: 'Michigan' },
                    { value: 'MN', label: 'Minnesota' },
                    { value: 'MS', label: 'Mississippi' },
                    { value: 'MO', label: 'Missouri' },
                    { value: 'MT', label: 'Montana' },
                    { value: 'NE', label: 'Nebraska' },
                    { value: 'NV', label: 'Nevada' },
                    { value: 'NH', label: 'New Hampshire' },
                    { value: 'NJ', label: 'New Jersey' },
                    { value: 'NM', label: 'New Mexico' },
                    { value: 'NY', label: 'New York' },
                    { value: 'NC', label: 'North Carolina' },
                    { value: 'ND', label: 'North Dakota' },
                    { value: 'OH', label: 'Ohio' },
                    { value: 'OK', label: 'Oklahoma' },
                    { value: 'OR', label: 'Oregon' },
                    { value: 'PA', label: 'Pennsylvania' },
                    { value: 'RI', label: 'Rhode Island' },
                    { value: 'SC', label: 'South Carolina' },
                    { value: 'SD', label: 'South Dakota' },
                    { value: 'TN', label: 'Tennessee' },
                    { value: 'TX', label: 'Texas' },
                    { value: 'UT', label: 'Utah' },
                    { value: 'VT', label: 'Vermont' },
                    { value: 'VA', label: 'Virginia' },
                    { value: 'WA', label: 'Washington' },
                    { value: 'WV', label: 'West Virginia' },
                    { value: 'WI', label: 'Wisconsin' },
                    { value: 'WY', label: 'Wyoming' },
                    { value: 'DC', label: 'District of Columbia' }
                ];
            }

            // Customize vehicle_type field
            const vehicleTypeField = fields.find(f => f.name === 'vehicle_type');
            if (vehicleTypeField) {
                vehicleTypeField.type = 'select';
                vehicleTypeField.options = [
                    '',
                    'sedan',
                    'suv',
                    'truck',
                    'pickup_truck',
                    'van',
                    'motorcycle',
                    'bicycle',
                    'rv',
                    'trailer',
                    'bus',
                    'commercial',
                    'other'
                ];
            }

            return new Promise((resolve) => {
                this.ui.showFullScreenForm('Add Vehicle Record', fields, async (formData) => {
                    try {
                        const result = await this.addVehicle(formData);
                        this.ui.showDialog(
                            'Vehicle Added',
                            `Vehicle record for ${formData.plate_number} has been created.`,
                            'success'
                        );
                        resolve(result);
                        return true; // Indicate success for form navigation
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add vehicle: ${error.message}`, 'error');
                        resolve(null);
                        return false; // Indicate failure to stay on form
                    }
                });
            });

        } catch (error) {
            console.error('Error showing add vehicle form:', error);
            throw error;
        }
    }

    /**
     * Add vehicle
     */
    async addVehicle(vehicleData) {
        try {
            // Map form data to database schema
            const dbVehicleData = {
                plate_number: vehicleData.plate_number,
                province: vehicleData.province || null,
                vehicle_make: vehicleData.vehicle_make || null,
                vehicle_model: vehicleData.vehicle_model || null,
                vehicle_year: vehicleData.vehicle_year ? parseInt(vehicleData.vehicle_year) : null,
                vehicle_color: vehicleData.vehicle_color || null,
                vehicle_type: vehicleData.vehicle_type || null,
                owner_name: vehicleData.owner_name || null,
                notes: vehicleData.notes || null,
                created_by: this.auth.getCurrentUser()?.email
            };

            // Insert into case_mgmt.license_plates
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: result, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .insert(dbVehicleData)
                .select()
                .single();

            if (error) {
                throw error;
            }

            // Trigger callback if set
            if (this.onVehicleChanged) {
                this.onVehicleChanged();
            }

            return result;
        } catch (error) {
            console.error('Error adding vehicle:', error);
            throw error;
        }
    }

    /**
     * Update vehicle
     */
    async updateVehicle(vehicleId, updateData) {
        try {
            const currentUser = this.auth.getCurrentUser();
            const dbUpdateData = {
                ...updateData,
                updated_by: currentUser?.email,
                updated_at: new Date().toISOString()
            };

            // Update in case_mgmt.license_plates
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: result, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .update(dbUpdateData)
                .eq('id', vehicleId)
                .select()
                .single();

            if (error) {
                throw error;
            }

            // Trigger callback if set
            if (this.onVehicleChanged) {
                this.onVehicleChanged();
            }

            return result;
        } catch (error) {
            console.error('Error updating vehicle:', error);
            throw error;
        }
    }

    /**
     * Delete vehicle
     */
    async deleteVehicle(vehicleId) {
        try {
            // Delete from case_mgmt.license_plates
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: result, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .delete()
                .eq('id', vehicleId)
                .select()
                .single();

            if (error) {
                throw error;
            }

            // Trigger callback if set
            if (this.onVehicleChanged) {
                this.onVehicleChanged();
            }

            return result;
        } catch (error) {
            console.error('Error deleting vehicle:', error);
            throw error;
        }
    }

    /**
     * Show edit vehicle form
     */
    async showEditVehicleForm(vehicleId) {
        try {
            // Load current vehicle data
            const supabase = await this.data.getSupabaseClient();
            if (!supabase) {
                throw new Error('Supabase client not available');
            }

            const { data: vehicle, error } = await supabase
                .schema('case_mgmt')
                .from('license_plates')
                .select('*')
                .eq('id', vehicleId)
                .single();

            if (error || !vehicle) {
                throw new Error('Vehicle not found');
            }

            // Generate form fields with current values
            const fields = this.data.schema.generateFormFields('license_plates');
            
            // Populate fields with current values
            fields.forEach(field => {
                if (vehicle[field.name] !== undefined) {
                    field.value = vehicle[field.name];
                }
            });

            // Customize fields (same as add form)
            const provinceField = fields.find(f => f.name === 'province');
            if (provinceField) {
                // Add province options (same as add form)
                // ... (省略，与添加表单相同的选项)
            }

            const vehicleTypeField = fields.find(f => f.name === 'vehicle_type');
            if (vehicleTypeField) {
                vehicleTypeField.type = 'select';
                vehicleTypeField.options = [
                    '',
                    'sedan',
                    'suv',
                    'truck',
                    'pickup_truck',
                    'van',
                    'motorcycle',
                    'bicycle',
                    'rv',
                    'trailer',
                    'bus',
                    'commercial',
                    'other'
                ];
            }

            return new Promise((resolve) => {
                this.ui.showFullScreenForm(`Edit Vehicle - ${vehicle.plate_number}`, fields, async (formData) => {
                    try {
                        const result = await this.updateVehicle(vehicleId, formData);
                        this.ui.showDialog(
                            'Vehicle Updated',
                            `Vehicle record for ${formData.plate_number} has been updated.`,
                            'success'
                        );
                        resolve(result);
                        return true; // Indicate success for form navigation
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to update vehicle: ${error.message}`, 'error');
                        resolve(null);
                        return false; // Indicate failure to stay on form
                    }
                });
            });

        } catch (error) {
            console.error('Error showing edit vehicle form:', error);
            throw error;
        }
    }

    /**
     * Cleanup method
     */
    cleanup() {
        this.onVehicleChanged = null;
    }
}
