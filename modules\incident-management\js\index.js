/**
 * Incident Management Module - Main Entry Point
 * Unifies all incident-related functionality into a single cohesive system
 * Replaces ~5800 lines of incident code from app.js with modular architecture
 */

import { BaseManager } from '../../shared/base-manager.js';
import { IncidentListManager } from './incident-list-manager.js';
import { IncidentSearchManager } from './incident-search-manager.js';
import { IncidentCrudManager } from './incident-crud-manager.js';
import { IncidentFormManager } from './incident-form-manager.js';
// Removed IncidentDraftManager - drafts are now just regular incidents with status 'draft'
import { IncidentPeopleManager } from './incident-people-manager.js';
import { IncidentAddressManager } from './incident-address-manager.js';
import { IncidentFileManager } from './incident-file-manager.js';
import { IncidentCommandsFactory } from './incident-commands.js';

export class IncidentManagement extends BaseManager {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement) {
        super(dataManager, authManager, uiManager);
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;

        // Initialize all sub-managers
        this.listManager = new IncidentListManager(dataManager, authManager, uiManager, uiUtilities);
        this.searchManager = new IncidentSearchManager(dataManager, authManager, uiManager, uiUtilities);
        this.crudManager = new IncidentCrudManager(dataManager, authManager, uiManager, uiUtilities, modalManagement);
        this.formManager = new IncidentFormManager(dataManager, authManager, uiManager, uiUtilities);
        // Removed draft manager - drafts are now just regular incidents with status 'draft'
        this.peopleManager = new IncidentPeopleManager(dataManager, authManager, uiManager, uiUtilities);
        this.addressManager = new IncidentAddressManager(dataManager, authManager, uiManager, uiUtilities);
        this.fileManager = new IncidentFileManager(dataManager, authManager, uiManager, uiUtilities);

        // Set up cross-manager communication
        this.setupManagerCommunication();

        // Initialize commands factory
        this.commandsFactory = new IncidentCommandsFactory(null, this);
    }

    setupManagerCommunication() {
        // Connect form manager with other managers for integrated functionality
        
        // Form manager needs access to other managers for complete functionality
        this.formManager.peopleManager = this.peopleManager;
        this.formManager.addressManager = this.addressManager;
        this.formManager.fileManager = this.fileManager;
        // Removed draft manager reference

        // People manager needs to communicate with form updates
        this.peopleManager.onPeopleChanged = () => {
            this.triggerFormUpdate();
        };

        // Address manager needs to communicate with form updates
        this.addressManager.onAddressChanged = () => {
            this.triggerFormUpdate();
        };

        // File manager needs to communicate with form updates
        this.fileManager.onFilesChanged = () => {
            this.updateFileCount();
        };

        // Note: Auto-save is started only during incident creation, not globally
    }

    // === LIST MANAGEMENT ===
    async loadIncidentsContent() {
        return await this.listManager.loadIncidentsContent();
    }

    async initializeUnifiedIncidents() {
        return await this.listManager.initializeUnifiedIncidents();
    }

    async loadIncidentsData() {
        return await this.listManager.loadIncidentsData();
    }

    async refreshIncidents() {
        return await this.listManager.refreshIncidents();
    }

    async selectIncident(incidentId) {
        return await this.listManager.selectIncident(incidentId);
    }

    startIncidentsUpdates() {
        return this.listManager.startIncidentsUpdates();
    }

    // === SEARCH MANAGEMENT ===
    async loadIncidentSearchContent() {
        return await this.searchManager.loadIncidentSearchContent();
    }

    setupIncidentSearch() {
        return this.searchManager.setupIncidentSearch();
    }

    async performIncidentSearch() {
        return await this.searchManager.performIncidentSearch();
    }

    async viewIncidentFromSearch(incidentId) {
        return await this.searchManager.viewIncidentFromSearch(incidentId);
    }

    async searchPeopleForIncident() {
        return await this.searchManager.searchPeopleForIncident();
    }

    applyQuickFilter(filterType) {
        return this.searchManager.applyQuickFilter(filterType);
    }

    async exportSearchResults() {
        return await this.searchManager.exportSearchResults();
    }

    // === CRUD OPERATIONS ===
    async closeIncident(incidentId) {
        return await this.crudManager.closeIncident(incidentId);
    }

    async deleteIncident(incidentId) {
        return await this.crudManager.deleteIncident(incidentId);
    }

    async updateIncidentStatus(incidentId, newStatus, statusReason = null) {
        return await this.crudManager.updateIncidentStatus(incidentId, newStatus, statusReason);
    }

    async assignRanger(incidentId, rangerName) {
        return await this.crudManager.assignRanger(incidentId, rangerName);
    }

    async addDispatchNote(incidentId, note) {
        return await this.crudManager.addDispatchNote(incidentId, note);
    }

    async updateIncidentPriority(incidentId, newPriority) {
        return await this.crudManager.updateIncidentPriority(incidentId, newPriority);
    }

    async duplicateIncident(incidentId) {
        return await this.crudManager.duplicateIncident(incidentId);
    }

    async bulkUpdateIncidents(incidentIds, updateData) {
        return await this.crudManager.bulkUpdateIncidents(incidentIds, updateData);
    }

    async showUpdateStatusModal(incidentId) {
        return await this.crudManager.showUpdateStatusModal(incidentId);
    }

    // === FORM MANAGEMENT ===
    async loadComprehensiveIncidentForm() {
        return await this.formManager.loadComprehensiveIncidentForm();
    }

    async loadEditIncidentContent(incidentId) {
        return await this.formManager.loadEditIncidentContent(incidentId);
    }

    async setupComprehensiveIncidentForm() {
        return await this.formManager.setupComprehensiveIncidentForm();
    }

    async setupEditIncidentForm() {
        return await this.formManager.setupEditIncidentForm();
    }

    async handleComprehensiveIncidentSubmit() {
        return await this.formManager.handleComprehensiveIncidentSubmit();
    }

    async handleEditIncidentFormSubmission() {
        return await this.formManager.handleEditIncidentFormSubmission();
    }

    async validateIncidentData(incidentData) {
        return await this.formManager.validateIncidentData(incidentData);
    }

    setupIncidentFormFunctionality() {
        return this.formManager.setupIncidentFormEventHandlers();
    }

    // === SIMPLIFIED DRAFT MANAGEMENT ===
    // Drafts are now just regular incidents with status 'draft'
    async saveDraftIncident() {
        return await this.formManager.saveIncidentAsDraft();
    }

    async deleteDraft(draftId) {
        return await this.crudManager.delete(draftId);
    }

    // === PEOPLE MANAGEMENT ===
    initializePeopleManagement() {
        return this.peopleManager.initializePeopleManagement();
    }

    async addPersonToIncident(personId) {
        return await this.peopleManager.addPersonToIncident(personId);
    }

    async removePersonFromIncident(index) {
        return await this.peopleManager.removePersonFromIncident(index);
    }

    async saveIncidentPeopleRelationships(incidentId) {
        return await this.peopleManager.saveIncidentPeopleRelationships(incidentId);
    }

    async updateIncidentPeopleRelationships(incidentId) {
        return await this.peopleManager.updateIncidentPeopleRelationships(incidentId);
    }

    async deleteIncidentPeopleRelationships(incidentId) {
        return await this.peopleManager.deleteIncidentPeopleRelationships(incidentId);
    }

    async loadExistingIncidentPeople() {
        return await this.peopleManager.loadExistingIncidentPeople();
    }

    updateInvolvedPeopleDisplay() {
        return this.peopleManager.updateInvolvedPeopleDisplay();
    }

    // === ADDRESS MANAGEMENT ===
    async setupIncidentAddressSearch() {
        return await this.addressManager.setupIncidentAddressSearch();
    }

    async geocodeIncidentAddress() {
        return await this.addressManager.geocodeIncidentAddress();
    }

    handleIncidentAddressSelection(addressData) {
        return this.addressManager.handleIncidentAddressSelection(addressData);
    }

    initializeIncidentMap() {
        return this.addressManager.initializeIncidentMap();
    }

    getCurrentLocationData() {
        return this.addressManager.getCurrentLocationData();
    }

    populateLocationFields(locationData) {
        return this.addressManager.populateLocationFields(locationData);
    }

    // === FILE MANAGEMENT ===
    setupFileUpload() {
        return this.fileManager.setupFileUpload();
    }

    async uploadIncidentFiles(incidentId) {
        return await this.fileManager.uploadIncidentFiles(incidentId);
    }

    async loadIncidentAttachments(incidentId) {
        return await this.fileManager.loadIncidentAttachments(incidentId);
    }

    async downloadIncidentAttachment(attachment) {
        return await this.fileManager.downloadIncidentAttachment(attachment);
    }

    getUploadedFiles() {
        return this.fileManager.getUploadedFiles();
    }

    clearUploadedFiles() {
        return this.fileManager.clearUploadedFiles();
    }

    hasUnuploadedFiles() {
        return this.fileManager.hasUnuploadedFiles();
    }

    // === INTEGRATED FUNCTIONALITY ===
    
    /**
     * Complete incident creation workflow
     */
    async createIncidentWorkflow() {
        try {
            // Load the comprehensive incident form HTML
            const formHtml = await this.loadComprehensiveIncidentForm();
            
            // Return HTML immediately - setup will happen after DOM insertion
            return formHtml;

        } catch (error) {
            console.error('Error in incident creation workflow:', error);
            throw error;
        }
    }

    /**
     * Initialize the incident creation form after HTML is in DOM
     */
    async initializeIncidentCreationForm() {
        try {
            console.log('Initializing incident creation form...');
            
            // Initialize all subsystems
            await this.setupComprehensiveIncidentForm();
            this.initializePeopleManagement();
            await this.setupIncidentAddressSearch();
            this.setupFileUpload();
            
            // Note: Draft incidents are now shown in the main incident list
            // No need to load drafts separately or show draft notifications

            // Start auto-save for all incident forms
            this.formManager.startAutoSave();
            
            console.log('Incident creation form initialized successfully');

        } catch (error) {
            console.error('Error initializing incident creation form:', error);
            throw error;
        }
    }

    /**
     * Complete incident editing workflow
     */
    async editIncidentWorkflow(incidentId, displayInCurrentTab = false) {
        try {
            // Validate incident ID
            if (!incidentId) {
                throw new Error('Incident ID is required for editing');
            }

            // Auto-save works for all incidents regardless of status

            // Store incident ID for retrieval by other methods
            sessionStorage.setItem('editIncidentId', incidentId);

            // Set incident ID for all managers
            this.peopleManager.setCurrentIncidentId(incidentId);

            // Load and setup the edit incident form - pass the incident ID
            const formHtml = await this.loadEditIncidentContent(incidentId);

            // If displayInCurrentTab is true, show the form in the current content area
            if (displayInCurrentTab) {
                const contentArea = document.querySelector('.tab-content.active .content-area');
                if (contentArea) {
                    contentArea.innerHTML = formHtml;
                    contentArea.classList.add('incident-edit-content');
                }
            }

            // Initialize all subsystems
            await this.setupEditIncidentForm();
            this.initializePeopleManagement();
            await this.setupIncidentAddressSearch();
            this.setupFileUpload();

            // Load existing data
            await this.loadExistingIncidentPeople();
            await this.loadIncidentAttachments(incidentId);

            return formHtml;

        } catch (error) {
            console.error('Error in incident editing workflow:', error);
            throw error;
        }
    }

    /**
     * Complete incident viewing workflow
     */
    async viewIncidentWorkflow(incidentId) {
        try {
            // Load incident details
            await this.selectIncident(incidentId);
            
            // Load related data
            await this.loadIncidentAttachments(incidentId);
            
        } catch (error) {
            console.error('Error in incident viewing workflow:', error);
            throw error;
        }
    }

    /**
     * Initialize the complete incidents system
     */
    async initializeIncidentSystem() {
        try {
            console.log('Initializing incident management system...');
            
            // Initialize the main incidents interface
            await this.initializeUnifiedIncidents();
            
            // Start auto-updates
            this.startIncidentsUpdates();
            
            console.log('Incident management system initialized successfully');

        } catch (error) {
            console.error('Error initializing incident system:', error);
            throw error;
        }
    }

    // === UTILITY METHODS ===
    
    triggerFormUpdate() {
        // Trigger auto-save when form data changes
        if (this.formManager) {
            this.formManager.autoSaveIncident();
        }

        // Emit event for other components
        const event = new CustomEvent('incidentFormUpdated', {
            detail: { timestamp: new Date() }
        });
        window.dispatchEvent(event);
    }

    updateFileCount() {
        const fileCount = this.fileManager.getFileCount();
        const fileCountElements = document.querySelectorAll('.files-count');
        
        fileCountElements.forEach(element => {
            element.textContent = fileCount;
        });
    }

    // === DATA ACCESS METHODS ===
    
    /**
     * Get comprehensive incident data for forms
     */
    collectIncidentData() {
        return {
            people: this.peopleManager.getInvolvedPeopleData(),
            location: this.addressManager.getCurrentLocationData(),
            files: this.fileManager.getUploadedFiles()
        };
    }

    /**
     * Populate form with comprehensive incident data
     */
    populateIncidentForm(incidentData) {
        if (incidentData.people) {
            this.peopleManager.involvedPeople = incidentData.people;
            this.peopleManager.updateInvolvedPeopleDisplay();
        }
        
        if (incidentData.location) {
            this.addressManager.populateLocationFields(incidentData.location);
        }
        
        if (incidentData.files) {
            this.fileManager.uploadedFiles = incidentData.files;
            this.fileManager.updateFileListDisplay();
        }
    }

    // === CLEANUP ===
    
    cleanup() {
        console.log('Cleaning up incident management system...');
        
        // Cleanup all managers
        if (this.listManager.cleanup) this.listManager.cleanup();
        if (this.formManager.cleanup) this.formManager.cleanup();

        // Clear any intervals or event listeners
        this.listManager.cleanup();
        this.formManager.stopAutoSave();
    }

    // === STATUS AND DEBUGGING ===
    
    getSystemStatus() {
        return {
            managers: {
                list: !!this.listManager,
                search: !!this.searchManager,
                crud: !!this.crudManager,
                form: !!this.formManager,
                // draft manager removed - drafts are regular incidents
                people: !!this.peopleManager,
                address: !!this.addressManager,
                file: !!this.fileManager
            },
            activeIncident: this.listManager?.selectedIncident?.id || null,
            involvedPeople: this.peopleManager?.involvedPeople?.length || 0,
            uploadedFiles: this.fileManager?.getFileCount() || 0,
            autoSaveActive: this.formManager?.autoSaveActive || false
        };
    }

    // === COMMAND INTEGRATION ===
    
    /**
     * Get incident commands for registration with command system
     */
    getCommands(commandManager) {
        // Update the factory with the actual command manager
        this.commandsFactory = new IncidentCommandsFactory(commandManager, this);
        return this.commandsFactory.createCommands();
    }

    // === LEGACY COMPATIBILITY METHODS ===
    // These methods maintain compatibility with existing app.js calls

    // Method aliases for backward compatibility
    populateIncidentTabContent(incident) {
        return this.listManager.populateIncidentTabContent(incident);
    }

    applyIncidentFilters(incidents) {
        return this.listManager.applyIncidentFilters(incidents);
    }

    filterIncidents() {
        return this.listManager.filterIncidents();
    }

    createIncidentPersonRelationship(relationshipData) {
        return this.peopleManager.createIncidentPersonRelationship(relationshipData);
    }

    createIncidentPersonMedical(incidentPersonId, medicalInfo) {
        return this.peopleManager.createIncidentPersonMedical(incidentPersonId, medicalInfo);
    }

    createIncidentActivity(incidentId, personId, involvementType) {
        return this.peopleManager.createIncidentActivity(incidentId, personId, involvementType);
    }

    deleteIncidentActivity(incidentId, personId) {
        return this.peopleManager.deleteIncidentActivity(incidentId, personId);
    }

    loadIncidentPeopleRelationships(incident) {
        return this.peopleManager.loadIncidentPeopleRelationships(incident);
    }

    saveIncidentCreationLinks(incidentId) {
        // This would be implemented if needed for backward compatibility
        console.log('saveIncidentCreationLinks called with incidentId:', incidentId);
    }
}

// Export individual managers and commands for direct access if needed
export {
    IncidentListManager,
    IncidentSearchManager,
    IncidentCrudManager,
    IncidentFormManager,
    // IncidentDraftManager removed - drafts are regular incidents
    IncidentPeopleManager,
    IncidentAddressManager,
    IncidentFileManager,
    IncidentCommandsFactory
};