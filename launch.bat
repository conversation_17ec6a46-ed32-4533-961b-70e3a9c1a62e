@echo off
title S.T.E.V.I Retro Launcher

REM Change to the directory where this batch file is located
cd /d "%~dp0"

echo.
echo ===============================================
echo  S.T.E.V.I Retro - Desktop Application Launcher
echo ===============================================
echo.
echo Starting S.T.E.V.I Retro...
echo Project Directory: %CD%
echo.

REM Kill any existing Electron processes to prevent file locking
taskkill /f /im electron.exe >nul 2>&1
timeout /t 1 >nul

REM Check if Node.js is installed
echo Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js is available
)

REM Check if dependencies are installed
echo Checking dependencies...
if not exist "node_modules" (
    echo ❌ Dependencies not found. Installing...
    npm install
    if errorlevel 1 (
        echo ❌ ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ✅ Dependencies are available
)

REM Launch the application using npm start (matches working PowerShell version)
echo Launching S.T.E.V.I Retro...
echo.
echo ✅ Application is starting...
echo ✅ If the app window doesn't appear, check for error messages below.
echo ✅ You can close this console window once the app is running.
echo.
npm start

REM If we get here, the app has closed
echo.
echo S.T.E.V.I Retro has closed.
echo Press any key to exit...
pause
